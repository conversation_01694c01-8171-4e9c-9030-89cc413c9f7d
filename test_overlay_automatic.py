#!/usr/bin/env python3
"""
Automatic Overlay Test Program
Loads both STEP files, triggers overlay, and diagnoses the issue automatically
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer_tdk_modular import StepViewerTDK

class AutomaticOverlayTest:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        # Test sequence timer
        self.timer = QTimer()
        self.timer.timeout.connect(self.run_test_sequence)
        self.step = 0
        
        print("🎯 AUTOMATIC OVERLAY TEST STARTING...")
        print("   This will automatically load files and test overlay")
        
        # Start test sequence after UI is ready
        QTimer.singleShot(1000, self.start_test)
    
    def start_test(self):
        print("🎯 Starting automatic test sequence...")
        self.timer.start(2000)  # Run every 2 seconds
    
    def run_test_sequence(self):
        try:
            if self.step == 0:
                print("\n📁 STEP 1: Loading TOP file...")
                self.viewer.set_active_viewer("top")
                success = self.viewer.step_loader_left.load_step_file("SOIC16P127_1270X940X610L89X51.STEP")
                if success:
                    self.viewer.vtk_renderer_left.display_polydata(self.viewer.step_loader_left.current_polydata)
                    self.viewer.top_file_label.setText("TOP: SOIC16P127_1270X940X610L89X51.STEP")
                    print("✅ TOP file loaded successfully")
                else:
                    print("❌ TOP file failed to load")
                
            elif self.step == 1:
                print("\n📁 STEP 2: Loading BOTTOM file...")
                self.viewer.set_active_viewer("bottom")
                success = self.viewer.step_loader_right.load_step_file("AMPHENOL_U77-A1118-200T.STEP")
                if success:
                    self.viewer.vtk_renderer_right.display_polydata(self.viewer.step_loader_right.current_polydata)
                    self.viewer.bottom_file_label.setText("BOTTOM: AMPHENOL_U77-A1118-200T.STEP")
                    print("✅ BOTTOM file loaded successfully")
                else:
                    print("❌ BOTTOM file failed to load")
                
            elif self.step == 2:
                print("\n🎯 STEP 3: Checking loaded actors...")
                self.check_loaded_actors()
                
            elif self.step == 3:
                print("\n🎯 STEP 4: Triggering overlay...")
                self.trigger_overlay()
                
            elif self.step == 4:
                print("\n🔍 STEP 5: Diagnosing overlay result...")
                self.diagnose_overlay()
                
            elif self.step == 5:
                print("\n✅ TEST COMPLETE - Stopping timer")
                self.timer.stop()
                return
                
            self.step += 1
            
        except Exception as e:
            print(f"❌ ERROR in test step {self.step}: {e}")
            import traceback
            traceback.print_exc()
            self.timer.stop()
    
    def check_loaded_actors(self):
        """Check what actors are loaded in both viewers"""
        print("🔍 Checking loaded actors...")
        
        # Check TOP viewer
        if hasattr(self.viewer.vtk_renderer_left, 'step_actors'):
            top_count = len(self.viewer.vtk_renderer_left.step_actors) if self.viewer.vtk_renderer_left.step_actors else 0
            print(f"   TOP viewer step_actors: {top_count}")
        else:
            print("   TOP viewer: No step_actors attribute")
            
        if hasattr(self.viewer.vtk_renderer_left, 'step_actor'):
            top_single = self.viewer.vtk_renderer_left.step_actor is not None
            print(f"   TOP viewer step_actor: {top_single}")
        else:
            print("   TOP viewer: No step_actor attribute")
        
        # Check BOTTOM viewer
        if hasattr(self.viewer.vtk_renderer_right, 'step_actors'):
            bottom_count = len(self.viewer.vtk_renderer_right.step_actors) if self.viewer.vtk_renderer_right.step_actors else 0
            print(f"   BOTTOM viewer step_actors: {bottom_count}")
        else:
            print("   BOTTOM viewer: No step_actors attribute")
            
        if hasattr(self.viewer.vtk_renderer_right, 'step_actor'):
            bottom_single = self.viewer.vtk_renderer_right.step_actor is not None
            print(f"   BOTTOM viewer step_actor: {bottom_single}")
        else:
            print("   BOTTOM viewer: No step_actor attribute")
    
    def trigger_overlay(self):
        """Trigger the overlay function"""
        print("🎯 Calling toggle_viewer_overlay()...")
        try:
            self.viewer.toggle_viewer_overlay()
            print("✅ toggle_viewer_overlay() completed without exception")
        except Exception as e:
            print(f"❌ Exception in toggle_viewer_overlay(): {e}")
            import traceback
            traceback.print_exc()
    
    def diagnose_overlay(self):
        """Diagnose the overlay result"""
        print("🔍 Diagnosing overlay result...")
        
        # Check overlay mode state
        overlay_mode = getattr(self.viewer, 'overlay_mode', False)
        print(f"   overlay_mode: {overlay_mode}")
        
        # Check overlay widget
        overlay_widget = getattr(self.viewer, 'overlay_widget', None)
        print(f"   overlay_widget exists: {overlay_widget is not None}")
        
        if overlay_widget:
            print(f"   overlay_widget visible: {overlay_widget.isVisible()}")
            
            # Check overlay VTK widget
            overlay_vtk = getattr(self.viewer, 'overlay_vtk_widget', None)
            print(f"   overlay_vtk_widget exists: {overlay_vtk is not None}")
            
            if overlay_vtk:
                render_window = overlay_vtk.GetRenderWindow()
                print(f"   render_window exists: {render_window is not None}")
                
                if render_window:
                    renderers = render_window.GetRenderers()
                    renderer_count = renderers.GetNumberOfItems()
                    print(f"   renderer count: {renderer_count}")
                    
                    if renderer_count > 0:
                        renderers.InitTraversal()
                        renderer = renderers.GetNextItem()
                        if renderer:
                            actors = renderer.GetActors()
                            actor_count = actors.GetNumberOfItems()
                            print(f"   actor count: {actor_count}")
                            
                            # Check each actor
                            actors.InitTraversal()
                            for i in range(actor_count):
                                actor = actors.GetNextItem()
                                if actor:
                                    color = actor.GetProperty().GetColor()
                                    visible = actor.GetVisibility()
                                    bounds = actor.GetBounds()
                                    print(f"   Actor {i}: Color={color}, Visible={visible}")
                                    print(f"            Bounds=({bounds[0]:.1f},{bounds[1]:.1f},{bounds[2]:.1f},{bounds[3]:.1f},{bounds[4]:.1f},{bounds[5]:.1f})")
    
    def run(self):
        return self.app.exec_()

if __name__ == "__main__":
    test = AutomaticOverlayTest()
    sys.exit(test.run())
