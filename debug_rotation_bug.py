#!/usr/bin/env python3
"""
Automated debug script to find the rotation save/load bug
"""

import sys
import os
import vtk
import math

# Add the current directory to path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from step_loader import <PERSON><PERSON><PERSON><PERSON><PERSON>

def test_rotation_save_load_cycle():
    """Test the complete rotation save/load cycle to find the bug"""
    
    print("\n" + "="*80)
    print("🔧 AUTOMATED ROTATION SAVE/LOAD DEBUG TEST")
    print("="*80)
    
    # Step 1: Find a STEP file to test with
    test_files = [
        "SOI16P127_1270X940X610L89X51.STEP",
        "test.step",
        "sample.step"
    ]
    
    test_file = None
    for filename in test_files:
        if os.path.exists(filename):
            test_file = filename
            break
    
    if not test_file:
        print("❌ No STEP file found for testing. Creating a simple test...")
        # We'll create a simple test without needing an actual STEP file
        test_simple_rotation_conversion()
        return
    
    print(f"✅ Using test file: {test_file}")
    
    # Step 2: Load the original file
    print(f"\n📋 STEP 2: Loading original file...")
    loader1 = STEPLoader()
    success, message = loader1.load_step_file(test_file)
    
    if not success:
        print(f"❌ Failed to load test file: {message}")
        return
        
    print(f"✅ Loaded original file: {message}")
    
    # Step 3: Create a known rotation transformation
    print(f"\n📋 STEP 3: Creating known rotation transformation...")
    
    # Test with simple rotations first
    test_rotations = [
        {"name": "Simple X rotation", "x": 45.0, "y": 0.0, "z": 0.0},
        {"name": "Simple Y rotation", "x": 0.0, "y": 30.0, "z": 0.0},
        {"name": "Simple Z rotation", "x": 0.0, "y": 0.0, "z": 60.0},
        {"name": "Combined rotation", "x": 30.0, "y": 45.0, "z": 60.0}
    ]
    
    for i, rotation in enumerate(test_rotations):
        print(f"\n🔍 TEST {i+1}: {rotation['name']}")
        print(f"   Input rotation: X={rotation['x']:.1f}° Y={rotation['y']:.1f}° Z={rotation['z']:.1f}°")
        
        # Create VTK transformation matrix
        transform = vtk.vtkTransform()
        transform.RotateX(rotation['x'])
        transform.RotateY(rotation['y'])
        transform.RotateZ(rotation['z'])
        transform_matrix = transform.GetMatrix()
        
        # Check what VTK GetOrientation() returns
        vtk_orientation = transform.GetOrientation()
        print(f"   VTK GetOrientation(): X={vtk_orientation[0]:.1f}° Y={vtk_orientation[1]:.1f}° Z={vtk_orientation[2]:.1f}°")
        
        # Step 4: Save with this transformation
        temp_filename = f"debug_test_{i+1}.step"
        print(f"   Saving to: {temp_filename}")
        
        save_success = loader1.save_step_file(temp_filename, transform_matrix)
        
        if not save_success:
            print(f"   ❌ Save failed!")
            continue
            
        print(f"   ✅ Save completed")
        
        # Step 5: Load the saved file
        print(f"   Loading saved file...")
        loader2 = STEPLoader()
        load_success, load_message = loader2.load_step_file(temp_filename)
        
        if not load_success:
            print(f"   ❌ Load failed: {load_message}")
            continue
            
        print(f"   ✅ Load completed: {load_message}")
        
        # Step 6: Compare the results
        print(f"   📊 COMPARISON:")
        print(f"      Original input:     X={rotation['x']:6.1f}° Y={rotation['y']:6.1f}° Z={rotation['z']:6.1f}°")
        print(f"      VTK GetOrientation: X={vtk_orientation[0]:6.1f}° Y={vtk_orientation[1]:6.1f}° Z={vtk_orientation[2]:6.1f}°")
        
        # Check if the loaded file has the same transformation
        # (This would require extracting the transformation from the loaded STEP file)
        
        # Clean up
        try:
            os.remove(temp_filename)
        except:
            pass
            
        print(f"   {'✅ PASSED' if abs(vtk_orientation[0] - rotation['x']) < 1.0 else '❌ FAILED'}")

def test_simple_rotation_conversion():
    """Test just the rotation conversion without STEP files"""
    print(f"\n📋 SIMPLE ROTATION CONVERSION TEST")
    
    test_cases = [
        {"x": 45.0, "y": 0.0, "z": 0.0},
        {"x": 0.0, "y": 30.0, "z": 0.0},
        {"x": 0.0, "y": 0.0, "z": 60.0},
        {"x": 30.0, "y": 45.0, "z": 60.0}
    ]
    
    for i, case in enumerate(test_cases):
        print(f"\n🔍 Case {i+1}: Input X={case['x']:.1f}° Y={case['y']:.1f}° Z={case['z']:.1f}°")
        
        # Method 1: VTK Transform with individual rotations
        transform1 = vtk.vtkTransform()
        transform1.RotateX(case['x'])
        transform1.RotateY(case['y'])
        transform1.RotateZ(case['z'])
        orientation1 = transform1.GetOrientation()
        
        print(f"   VTK GetOrientation(): X={orientation1[0]:6.1f}° Y={orientation1[1]:6.1f}° Z={orientation1[2]:6.1f}°")
        
        # Method 2: Create matrix and extract orientation
        matrix = transform1.GetMatrix()
        transform2 = vtk.vtkTransform()
        transform2.SetMatrix(matrix)
        orientation2 = transform2.GetOrientation()
        
        print(f"   Matrix->GetOrientation(): X={orientation2[0]:6.1f}° Y={orientation2[1]:6.1f}° Z={orientation2[2]:6.1f}°")
        
        # Check if they match
        match = (abs(orientation1[0] - orientation2[0]) < 0.1 and 
                abs(orientation1[1] - orientation2[1]) < 0.1 and 
                abs(orientation1[2] - orientation2[2]) < 0.1)
        
        print(f"   {'✅ CONSISTENT' if match else '❌ INCONSISTENT'}")

if __name__ == "__main__":
    test_rotation_save_load_cycle()
