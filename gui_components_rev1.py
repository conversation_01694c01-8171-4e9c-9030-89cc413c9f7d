from PyQt5.QtWidgets import (
    <PERSON>D<PERSON>Widget, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QGroupBox, QSpinBox, QSlider, QComboBox
)
from PyQt5.QtCore import Qt

def create_tool_dock(parent):
    """Create the complete tool dock with viewer selection and transform displays"""
    dock = QDockWidget("Tools", parent)
    dock.setAllowedAreas(Qt.LeftDockWidgetArea | Qt.RightDockWidgetArea)      
    panel = QWidget()
    layout = QVBoxLayout(panel)

    # Viewer Selection Group
    viewer_group = QGroupBox("Active Viewer")
    viewer_layout = QVBoxLayout(viewer_group)
    
    parent.top_btn = QPushButton("Top Viewer")
    parent.top_btn.clicked.connect(lambda: parent.set_active_viewer("top"))
    viewer_layout.addWidget(parent.top_btn)
    
    parent.bottom_btn = QPushButton("Bottom Viewer")
    parent.bottom_btn.clicked.connect(lambda: parent.set_active_viewer("bottom"))
    viewer_layout.addWidget(parent.bottom_btn)
    
    layout.addWidget(viewer_group)

    # File operations
    btn_open = QPushButton("Open STEP File")
    btn_open.clicked.connect(parent.load_step_file)
    layout.addWidget(btn_open)

    btn_save = QPushButton("Save STEP File")
    btn_save.clicked.connect(parent.save_transformed_step)
    layout.addWidget(btn_save)

    # View operations
    btn_clear = QPushButton("Clear Active View")
    btn_clear.clicked.connect(parent.clear_view)
    layout.addWidget(btn_clear)

    btn_fit = QPushButton("Fit Active View")
    btn_fit.clicked.connect(parent.fit_view)
    layout.addWidget(btn_fit)

    btn_reset = QPushButton("Reset to Original")
    btn_reset.clicked.connect(parent.reset_to_original)
    layout.addWidget(btn_reset)

    btn_align = QPushButton("Align Bottom-Center")
    btn_align.clicked.connect(parent.align_bottom_center)
    layout.addWidget(btn_align)

    # Original transformation info group
    original_group = QGroupBox("Original Transform")
    original_layout = QVBoxLayout(original_group)

    parent.lbl_orig_pos_x = QLabel("Position X: 0.0")
    parent.lbl_orig_pos_y = QLabel("Position Y: 0.0")
    parent.lbl_orig_pos_z = QLabel("Position Z: 0.0")
    parent.lbl_orig_rot_x = QLabel("Rotation X: 0.0 deg")
    parent.lbl_orig_rot_y = QLabel("Rotation Y: 0.0 deg")
    parent.lbl_orig_rot_z = QLabel("Rotation Z: 0.0 deg")
    
    original_layout.addWidget(parent.lbl_orig_pos_x)
    original_layout.addWidget(parent.lbl_orig_pos_y)
    original_layout.addWidget(parent.lbl_orig_pos_z)
    original_layout.addWidget(parent.lbl_orig_rot_x)
    original_layout.addWidget(parent.lbl_orig_rot_y)
    original_layout.addWidget(parent.lbl_orig_rot_z)
    
    layout.addWidget(original_group)

    # Current transformation info group
    current_group = QGroupBox("Current Transform")
    current_layout = QVBoxLayout(current_group)

    parent.lbl_curr_pos_x = QLabel("Position X: 0.0")
    parent.lbl_curr_pos_y = QLabel("Position Y: 0.0")
    parent.lbl_curr_pos_z = QLabel("Position Z: 0.0")
    parent.lbl_curr_rot_x = QLabel("Rotation X: 0.0 deg")
    parent.lbl_curr_rot_y = QLabel("Rotation Y: 0.0 deg")
    parent.lbl_curr_rot_z = QLabel("Rotation Z: 0.0 deg")
    
    current_layout.addWidget(parent.lbl_curr_pos_x)
    current_layout.addWidget(parent.lbl_curr_pos_y)
    current_layout.addWidget(parent.lbl_curr_pos_z)
    current_layout.addWidget(parent.lbl_curr_rot_x)
    current_layout.addWidget(parent.lbl_curr_rot_y)
    current_layout.addWidget(parent.lbl_curr_rot_z)
    
    layout.addWidget(current_group)

    # Rotation controls
    rotation_group = QGroupBox("Rotation Controls")
    rotation_layout = QVBoxLayout(rotation_group)

    # Rotation increment
    from PyQt5.QtWidgets import QSpinBox
    parent.rotation_increment = QSpinBox()
    parent.rotation_increment.setRange(1, 180)
    parent.rotation_increment.setValue(15)
    parent.rotation_increment.setSuffix(" deg")
    rotation_layout.addWidget(QLabel("Rotation Step:"))
    rotation_layout.addWidget(parent.rotation_increment)

    # Rotation buttons
    btn_rx = QPushButton("Rotate +X")
    btn_rx.clicked.connect(lambda: parent.rotate_shape('x', parent.rotation_increment.value()))
    rotation_layout.addWidget(btn_rx)

    btn_ry = QPushButton("Rotate +Y")
    btn_ry.clicked.connect(lambda: parent.rotate_shape('y', parent.rotation_increment.value()))
    rotation_layout.addWidget(btn_ry)

    btn_rz = QPushButton("Rotate +Z")
    btn_rz.clicked.connect(lambda: parent.rotate_shape('z', parent.rotation_increment.value()))
    rotation_layout.addWidget(btn_rz)
    
    layout.addWidget(rotation_group)

    # Model color controls
    color_group = QGroupBox("Model Color")
    color_layout = QVBoxLayout(color_group)
    
    parent.color_combo = QComboBox()
    parent.color_combo.addItems(["Original", "Red", "Green", "Blue", "Yellow", "Cyan", "Magenta", "White", "Gray"])
    parent.color_combo.currentTextChanged.connect(parent.change_model_color)
    color_layout.addWidget(parent.color_combo)
    
    layout.addWidget(color_group)

    btn_toggle_bbox = QPushButton("Toggle Bounding Box")
    btn_toggle_bbox.clicked.connect(parent.toggle_bbox_overlay)
    layout.addWidget(btn_toggle_bbox)

    # View sync button
    btn_sync_view = QPushButton("Update from Current View")
    btn_sync_view.clicked.connect(parent.force_view_update)
    layout.addWidget(btn_sync_view)

    layout.addStretch()
    dock.setWidget(panel)
    return dock
