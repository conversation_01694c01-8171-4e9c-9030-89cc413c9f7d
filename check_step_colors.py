#!/usr/bin/env python3

step_file = 'SOIC16P127_1270X940X610L89X51.STEP'

print("CHECKING STEP FILE FOR COLORS")
print("=" * 40)

with open(step_file, 'r', encoding='utf-8', errors='ignore') as f:
    content = f.read()

print(f"File size: {len(content)} characters")

# Search for color-related keywords
color_keywords = ['COLOUR', 'COLOR', 'RGB', 'SURFACE_STYLE']

for keyword in color_keywords:
    count = content.upper().count(keyword)
    print(f"{keyword}: {count} occurrences")

# Show actual color lines
lines = content.split('\n')
color_lines = []

for i, line in enumerate(lines):
    if 'COLOUR' in line.upper():
        color_lines.append(f"Line {i+1}: {line.strip()}")

print(f"\nActual COLOUR lines ({len(color_lines)}):")
for line in color_lines[:5]:  # Show first 5
    print(line)

print("=" * 40)
print("STEP FILE COLOR CHECK COMPLETE")
