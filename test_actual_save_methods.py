#!/usr/bin/env python3
"""
Test the actual save methods from the main program
"""

import os
import sys
import shutil
import time
from datetime import datetime

def get_file_info(filename):
    """Get file size and modification time"""
    if os.path.exists(filename):
        stat = os.stat(filename)
        return {
            'size': stat.st_size,
            'mtime': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
            'exists': True
        }
    else:
        return {'size': 0, 'mtime': 'N/A', 'exists': False}

def test_actual_save_methods():
    """Test the actual save methods from the main program"""
    print("=" * 80)
    print("🧪 TESTING ACTUAL SAVE METHODS FROM MAIN PROGRAM")
    print("=" * 80)
    
    # Check if test.step exists
    test_file = "test.step"
    if not os.path.exists(test_file):
        print(f"❌ ERROR: {test_file} not found!")
        return False
    
    original_info = get_file_info(test_file)
    print(f"📁 Original file: {test_file}")
    print(f"   Size: {original_info['size']} bytes")
    
    try:
        # Import the main program
        from step_viewer_tdk_modular import StepViewerTDK
        from PyQt5.QtWidgets import QApplication
        
        # Create QApplication if needed
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("✅ Successfully imported StepViewerTDK")
        
        # Create viewer instance
        viewer = StepViewerTDK()
        
        # Load the test file
        print(f"\n📝 Loading test file: {test_file}")
        success, msg = viewer.step_loader_left.load_step_file(test_file)
        
        if not success:
            print(f"❌ Failed to load {test_file}: {msg}")
            return False
        
        print(f"✅ Loaded {test_file} successfully")
        
        # Test files
        test_files = {
            'original_new': 'test_method_original_new.step',
            'original_existing': 'test_method_original_existing.step'
        }
        
        # Create existing file
        with open(test_files['original_existing'], 'w') as f:
            f.write("DUMMY EXISTING FILE FOR OVERWRITE TEST\n")
        
        print("\n" + "=" * 60)
        print("🧪 TESTING save_original_step() METHOD")
        print("=" * 60)
        
        # Test Original Save - New File
        print(f"\n📝 Test 1: save_original_step() to New File")
        print(f"   Target: {test_files['original_new']}")
        
        if os.path.exists(test_files['original_new']):
            os.remove(test_files['original_new'])
        
        # Mock the file dialog to return our test filename
        def mock_get_save_filename(title):
            return test_files['original_new']
        
        viewer._get_save_filename = mock_get_save_filename
        
        try:
            # Call the actual save method
            viewer.save_original_step()
            
            # Check if file was created
            new_info = get_file_info(test_files['original_new'])
            
            if new_info['exists'] and new_info['size'] == original_info['size']:
                print(f"   ✅ SUCCESS: {original_info['size']} → {new_info['size']} bytes")
                
                # Verify content is identical
                with open(test_file, 'rb') as f1, open(test_files['original_new'], 'rb') as f2:
                    if f1.read() == f2.read():
                        print(f"   ✅ PERFECT: Content is identical")
                    else:
                        print(f"   ❌ FAILED: Content is different")
            else:
                print(f"   ❌ FAILED: File not created or wrong size")
                print(f"      Exists: {new_info['exists']}")
                print(f"      Size: {new_info['size']} (expected {original_info['size']})")
                
        except Exception as e:
            print(f"   ❌ EXCEPTION: {e}")
        
        # Test Original Save - Existing File (Overwrite)
        print(f"\n📝 Test 2: save_original_step() to Existing File")
        print(f"   Target: {test_files['original_existing']}")
        
        before_info = get_file_info(test_files['original_existing'])
        print(f"   Before: {before_info['size']} bytes, {before_info['mtime']}")
        
        time.sleep(1)  # Ensure timestamp difference
        
        # Mock the file dialog to return existing filename
        def mock_get_save_filename_existing(title):
            return test_files['original_existing']
        
        viewer._get_save_filename = mock_get_save_filename_existing
        
        try:
            # Call the actual save method
            viewer.save_original_step()
            
            # Check if file was overwritten
            after_info = get_file_info(test_files['original_existing'])
            print(f"   After:  {after_info['size']} bytes, {after_info['mtime']}")
            
            if after_info['size'] == original_info['size'] and after_info['mtime'] != before_info['mtime']:
                print(f"   ✅ SUCCESS: File overwritten correctly")
                print(f"   ✅ SIZE: {before_info['size']} → {after_info['size']} bytes")
                print(f"   ✅ TIME: Updated")
                
                # Verify content is identical
                with open(test_file, 'rb') as f1, open(test_files['original_existing'], 'rb') as f2:
                    if f1.read() == f2.read():
                        print(f"   ✅ PERFECT: Content is identical")
                    else:
                        print(f"   ❌ FAILED: Content is different")
            else:
                print(f"   ❌ FAILED: File not overwritten correctly")
                print(f"      Size match: {after_info['size'] == original_info['size']}")
                print(f"      Time updated: {after_info['mtime'] != before_info['mtime']}")
                
        except Exception as e:
            print(f"   ❌ EXCEPTION: {e}")
        
        # Cleanup
        print(f"\n🧹 Cleaning up test files...")
        for filename in test_files.values():
            if os.path.exists(filename):
                os.remove(filename)
                print(f"   Removed: {filename}")
        
        print(f"\n✅ ACTUAL SAVE METHOD TEST COMPLETED!")
        return True
        
    except Exception as e:
        print(f"❌ FAILED TO TEST ACTUAL METHODS: {e}")
        return False

if __name__ == "__main__":
    success = test_actual_save_methods()
    if success:
        print(f"\n✅ ACTUAL SAVE METHODS TESTED!")
    else:
        print(f"\n❌ ACTUAL SAVE METHODS FAILED!")
