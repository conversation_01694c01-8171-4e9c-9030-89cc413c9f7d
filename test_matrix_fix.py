#!/usr/bin/env python3
"""
Test the matrix transformation fix
"""

import sys
import os
sys.path.append('.')

from step_viewer_tdk_modular import StepViewerTDK
from PyQt5.QtWidgets import QApplication
import time

def test_matrix_fix():
    """Test that the matrix transformation fix produces correct geometry"""
    print("🔧 TESTING MATRIX TRANSFORMATION FIX")
    print("=" * 60)
    
    app = QApplication([])
    viewer = StepViewerTDK()
    viewer.show()
    
    try:
        # STEP 1: Load original test.step
        print("\n📂 STEP 1: Loading original test.step...")
        viewer.active_viewer = "top"
        success = viewer.load_step_file_direct('test.step')
        
        if not success:
            print("❌ Failed to load original test.step")
            return
        
        print("✅ Original file loaded successfully")
        
        # STEP 2: Apply a simple rotation (just X=90° for easy verification)
        rotation_to_apply = {"x": 90.0, "y": 0.0, "z": 0.0}
        print(f"\n🔄 STEP 2: Applying simple X=90° rotation...")
        
        viewer._apply_3d_rotation_matrix("left", rotation_to_apply)
        print("✅ Rotation applied to model")
        
        # STEP 3: Save the rotated model with the FIXED matrix transformation
        save_filename = "test_matrix_fix.step"
        print(f"\n💾 STEP 3: Saving with FIXED matrix transformation...")
        
        loader = viewer.step_loader_left
        delta_pos = {"x": 0.0, "y": 0.0, "z": 0.0}
        delta_rot = rotation_to_apply
        orig_pos = {"x": 0.0, "y": 0.0, "z": 0.0}
        orig_rot = {"x": 0.0, "y": 0.0, "z": 0.0}
        
        success = viewer._save_step_text_transform(
            save_filename, loader, delta_pos, delta_rot, orig_pos, orig_rot
        )
        
        if not success:
            print("❌ Failed to save rotated file")
            return
        
        print(f"✅ File saved with FIXED matrix transformation: {save_filename}")
        
        # STEP 4: Load the saved file and compare
        print(f"\n📂 STEP 4: Loading saved file to verify geometry...")
        viewer.active_viewer = "bottom"
        success = viewer.load_step_file_direct(save_filename)
        
        if not success:
            print("❌ Failed to load saved file")
            return
        
        print("✅ Saved file loaded successfully")
        
        # STEP 5: Visual comparison
        print(f"\n👀 STEP 5: Visual comparison...")
        print(f"   📺 TOP viewer: Original test.step with X=90° rotation applied")
        print(f"   📺 BOTTOM viewer: Saved {save_filename} file")
        print(f"   ✅ Both should now look IDENTICAL with the matrix fix!")
        print(f"   🔍 Check that:")
        print(f"      - Geometry shapes are the same")
        print(f"      - Pin orientations match")
        print(f"      - No missing or distorted parts")
        print(f"      - Overall model proportions are identical")
        
        # Keep window open for inspection
        print(f"\n⏳ Keeping window open for 15 seconds for visual inspection...")
        time.sleep(15)
        
    except Exception as e:
        print(f"❌ Error during matrix fix test: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n🎯 MATRIX FIX TEST COMPLETE")
    app.quit()

if __name__ == "__main__":
    test_matrix_fix()
