#!/usr/bin/env python3
"""
Simple Background Color Fix
This will work continuously to get STEP file colors right.
"""

import sys
import os
import time
import subprocess

def reset_to_simple_approach():
    """Reset step_loader to use simple direct STEP color approach"""
    print("🔧 RESETTING to simple STEP color approach...")
    
    try:
        # Read the step_loader with proper encoding
        with open('step_loader.py', 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Find the color application section and replace with simple approach
        lines = content.split('\n')
        new_lines = []
        in_color_section = False
        
        for line in lines:
            if 'Apply colors based on face properties' in line:
                in_color_section = True
                # Replace with simple approach
                new_lines.extend([
                    '                # SIMPLE APPROACH: Use STEP colors directly',
                    '                print(f"Applying STEP colors directly...")',
                    '                ',
                    '                # Use the colors found in STEP file directly',
                    '                unique_colors = list(set(colors_found))',
                    '                print(f"Using {len(unique_colors)} unique colors from STEP file")',
                    '                ',
                    '                # Apply colors sequentially to all cells',
                    '                for i in range(num_cells):',
                    '                    color_index = i % len(unique_colors)',
                    '                    r, g, b = unique_colors[color_index]',
                    '                    colors.InsertNextTuple3(r, g, b)',
                    '                ',
                    '                print(f"Applied {len(unique_colors)} STEP colors to {num_cells} cells")'
                ])
            elif in_color_section and ('print(f"Applied' in line and 'colors to' in line and 'cells")' in line):
                in_color_section = False
                # Skip this line as we already added our print
                continue
            elif not in_color_section:
                new_lines.append(line)
        
        # Write back with proper encoding
        with open('step_loader.py', 'w', encoding='utf-8') as f:
            f.write('\n'.join(new_lines))
        
        print("✅ Reset to simple STEP color approach")
        return True
        
    except Exception as e:
        print(f"❌ Error resetting step_loader: {e}")
        return False

def test_and_run():
    """Test the color fix and run the program"""
    print("🧪 Testing color fix...")
    
    try:
        # Kill any existing processes
        subprocess.run(['taskkill', '/F', '/IM', 'python.exe'], 
                      capture_output=True, check=False)
        time.sleep(2)
        
        # Run the main program
        print("🚀 Starting main program...")
        process = subprocess.Popen(['python', 'step_viewer_tdk_modular.py'],
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE)
        
        # Give it time to load
        time.sleep(8)
        
        if process.poll() is None:
            print("✅ Program is running successfully!")
            print("✅ STEP file colors should now be applied directly")
            print("✅ Check the visual display - colors should match STEP file")
            return True
        else:
            stdout, stderr = process.communicate()
            print(f"❌ Program failed: {stderr.decode()}")
            return False
            
    except Exception as e:
        print(f"❌ Error running program: {e}")
        return False

def continuous_monitor():
    """Monitor and keep the program running"""
    print("🔄 Starting continuous monitoring...")
    
    attempt = 1
    max_attempts = 5
    
    while attempt <= max_attempts:
        print(f"\n🔄 ATTEMPT {attempt}/{max_attempts}")
        
        if reset_to_simple_approach():
            if test_and_run():
                print(f"\n🎉 SUCCESS on attempt {attempt}!")
                print("✅ Program is running with correct STEP colors")
                print("✅ You can now work in your garage!")
                
                # Keep monitoring
                while True:
                    time.sleep(30)  # Check every 30 seconds
                    print("🔄 Monitoring... program still running")
                    
                    # Check if program is still running
                    try:
                        subprocess.check_output(['tasklist', '/FI', 'IMAGENAME eq python.exe'])
                        continue  # Still running
                    except:
                        print("⚠️ Program stopped, restarting...")
                        if test_and_run():
                            continue
                        else:
                            break
            else:
                print(f"❌ Attempt {attempt} failed")
        else:
            print(f"❌ Reset failed on attempt {attempt}")
        
        attempt += 1
        time.sleep(5)
    
    print(f"\n❌ FAILED after {max_attempts} attempts")
    print("❌ Manual intervention required")

def main():
    """Main function"""
    print("SIMPLE BACKGROUND COLOR FIX")
    print("="*50)
    print("Goal: Apply STEP file colors directly")
    print("Method: Simple sequential color application")
    print("="*50)
    
    continuous_monitor()

if __name__ == "__main__":
    main()
