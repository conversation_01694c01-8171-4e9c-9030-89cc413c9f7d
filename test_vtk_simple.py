#!/usr/bin/env python3
import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout
from vtk_renderer import VTKRenderer

class SimpleTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("VTK Test - Single Window")
        self.setGeometry(100, 100, 800, 600)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Create ONE VTK renderer
        self.vtk_renderer = VTKRenderer(self)
        
        # Add to layout
        if self.vtk_renderer.vtk_widget:
            layout.addWidget(self.vtk_renderer.vtk_widget)
            print("VTK widget added to layout")
        else:
            print("ERROR: No VTK widget created")

def main():
    app = QApplication(sys.argv)
    viewer = SimpleTest()
    viewer.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()