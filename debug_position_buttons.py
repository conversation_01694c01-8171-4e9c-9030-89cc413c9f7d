#!/usr/bin/env python3
"""
Debug program to test position button functionality automatically
"""

import sys
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer_tdk_modular import StepViewerTDK

class PositionButtonDebugger:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        self.test_step = 0
        self.timer = QTimer()
        self.timer.timeout.connect(self.run_test_step)
        
        print("=== POSITION BUTTON DEBUG TEST ===")
        print("=== TESTING POSITION BUTTON FUNCTIONALITY ===")
        
        # Start the test after a short delay
        QTimer.singleShot(2000, self.start_test)
        
    def start_test(self):
        print("=== BEGINNING POSITION BUTTON TEST SEQUENCE ===")
        self.timer.start(2000)  # Run test every 2 seconds
        
    def run_test_step(self):
        if self.test_step == 0:
            print("STEP 1: Loading model and checking initial position...")
            # Auto-load the debug model
            filename = "E:/Python/3d-models/SOIC16P127_1270X940X610L89X51.STEP"
            self.viewer.step_loader_left.load_step_file(filename)
            self.viewer.vtk_renderer_left.display_polydata(self.viewer.step_loader_left.current_polydata)
            self.viewer.extract_step_transformation_data("top")
            self.viewer.setup_text_overlay_for_viewer("top")
            
            if hasattr(self.viewer, 'orig_pos_left'):
                print(f"Initial position: {self.viewer.orig_pos_left}")
            else:
                print("No orig_pos_left found")
                
        elif self.test_step == 1:
            print("STEP 2: Testing X+ button (should add positive value)...")
            # Simulate X+ button click
            increment = 1.0  # 1mm increment
            print(f"Calling move_shape('x', {increment})")
            self.viewer.move_shape('x', increment)
            
            if hasattr(self.viewer, 'orig_pos_left'):
                print(f"After X+ click: {self.viewer.orig_pos_left}")
            
        elif self.test_step == 2:
            print("STEP 3: Testing X- button (should subtract/add negative value)...")
            # Simulate X- button click
            increment = -1.0  # -1mm increment
            print(f"Calling move_shape('x', {increment})")
            self.viewer.move_shape('x', increment)
            
            if hasattr(self.viewer, 'orig_pos_left'):
                print(f"After X- click: {self.viewer.orig_pos_left}")
                
        elif self.test_step == 3:
            print("STEP 4: Testing Y+ button...")
            increment = 1.0
            print(f"Calling move_shape('y', {increment})")
            self.viewer.move_shape('y', increment)
            
            if hasattr(self.viewer, 'orig_pos_left'):
                print(f"After Y+ click: {self.viewer.orig_pos_left}")
                
        elif self.test_step == 4:
            print("STEP 5: Testing Y- button...")
            increment = -1.0
            print(f"Calling move_shape('y', {increment})")
            self.viewer.move_shape('y', increment)
            
            if hasattr(self.viewer, 'orig_pos_left'):
                print(f"After Y- click: {self.viewer.orig_pos_left}")
                
        elif self.test_step == 5:
            print("STEP 6: Testing Z+ button...")
            increment = 1.0
            print(f"Calling move_shape('z', {increment})")
            self.viewer.move_shape('z', increment)
            
            if hasattr(self.viewer, 'orig_pos_left'):
                print(f"After Z+ click: {self.viewer.orig_pos_left}")
                
        elif self.test_step == 6:
            print("STEP 7: Testing Z- button...")
            increment = -1.0
            print(f"Calling move_shape('z', {increment})")
            self.viewer.move_shape('z', increment)
            
            if hasattr(self.viewer, 'orig_pos_left'):
                print(f"After Z- click: {self.viewer.orig_pos_left}")
                
        elif self.test_step == 7:
            print("STEP 8: Final position check...")
            if hasattr(self.viewer, 'orig_pos_left'):
                print(f"Final position: {self.viewer.orig_pos_left}")
                
            # Check text overlay
            if hasattr(self.viewer, 'combined_text_actor_left'):
                text_content = self.viewer.combined_text_actor_left.GetInput()
                print(f"Text overlay shows: '{text_content}'")
                
            print("=== POSITION BUTTON TEST COMPLETE ===")
            print("Expected behavior:")
            print("  X+ should increase X value")
            print("  X- should decrease X value") 
            print("  Y+ should increase Y value")
            print("  Y- should decrease Y value")
            print("  Z+ should increase Z value")
            print("  Z- should decrease Z value")
            
            self.timer.stop()
            QTimer.singleShot(2000, self.app.quit)
            
        self.test_step += 1
        
    def run(self):
        return self.app.exec_()

if __name__ == "__main__":
    debugger = PositionButtonDebugger()
    sys.exit(debugger.run())
