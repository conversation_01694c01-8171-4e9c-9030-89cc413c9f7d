#!/usr/bin/env python3
"""
Test the main program's save functionality directly
"""

import sys
import os
import vtk

# Add current directory to path for imports
sys.path.append('.')

from step_loader import STEPLoader

def test_main_program_save_with_rotation():
    """Test the main program's save functionality with rotation"""
    print("🧪 TESTING MAIN PROGRAM SAVE WITH ROTATION")
    print("=" * 60)
    
    # Load the file using STEPLoader (same as main program)
    loader = STEPLoader()
    if not loader.load_step_file("test.step"):
        print("❌ Failed to load test.step")
        return False
    
    print("✅ Loaded test.step with STEPLoader")
    
    # Simulate GUI values (what the main program would send)
    current_pos = {'x': -4.19, 'y': -3.6673, 'z': 0.4914}
    current_rot = {'x': 0.0, 'y': 0.0, 'z': 54.0}  # Original 9° + user added 45°
    orig_pos = {'x': -4.19, 'y': -3.6673, 'z': 0.4914}
    orig_rot = {'x': 0.0, 'y': 0.0, 'z': 9.0}
    
    print(f"📝 Simulating GUI values:")
    print(f"   Current Position: {current_pos}")
    print(f"   Current Rotation: {current_rot}")
    print(f"   Original Position: {orig_pos}")
    print(f"   Original Rotation: {orig_rot}")
    
    # Calculate delta (what the main program should do)
    delta_pos = {
        'x': current_pos['x'] - orig_pos['x'],
        'y': current_pos['y'] - orig_pos['y'],
        'z': current_pos['z'] - orig_pos['z']
    }
    delta_rot = {
        'x': current_rot['x'] - orig_rot['x'],
        'y': current_rot['y'] - orig_rot['y'],
        'z': current_rot['z'] - orig_rot['z']
    }
    
    print(f"📝 Calculated deltas:")
    print(f"   Delta Position: {delta_pos}")
    print(f"   Delta Rotation: {delta_rot}")
    
    # Test the STEPTransformer approach directly
    print(f"\n🔧 Testing STEPTransformer approach...")
    
    try:
        from step_transformer import STEPTransformer
        
        transformer = STEPTransformer()
        if not transformer.load_step_file(loader.current_filename):
            print("❌ STEPTransformer failed to load file")
            return False
        
        # Apply delta transformations
        success = transformer.apply_transformation(
            rotation_x=delta_rot['x'],
            rotation_y=delta_rot['y'],
            rotation_z=delta_rot['z'],
            translation_x=delta_pos['x'],
            translation_y=delta_pos['y'],
            translation_z=delta_pos['z']
        )
        
        if not success:
            print("❌ STEPTransformer transformation failed")
            return False
        
        # Save the result
        output_file = "test_main_program_simulation.step"
        if transformer.save_step_file(output_file):
            print(f"✅ STEPTransformer saved: {output_file}")
            
            # Analyze the result
            analyze_result(output_file)
            return True
        else:
            print("❌ STEPTransformer save failed")
            return False
            
    except Exception as e:
        print(f"❌ STEPTransformer approach failed: {e}")
        return False

def analyze_result(filename):
    """Analyze the transformation result"""
    print(f"\n🔍 ANALYZING RESULT: {filename}")
    
    if not os.path.exists(filename):
        print("❌ File not found")
        return
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find coordinate points
        import re
        coord_pattern = r'CARTESIAN_POINT\s*\(\s*\'[^\']*\'\s*,\s*\(\s*([-\d.E+-]+)\s*,\s*([-\d.E+-]+)\s*,\s*([-\d.E+-]+)\s*\)'
        coords = re.findall(coord_pattern, content)
        
        print(f"   File size: {len(content)} chars, {len(coords)} coordinate points")
        print(f"   First 3 transformed coordinates:")
        
        # Original coordinates for comparison
        original_coords = [
            (0.000000, 0.000000, 0.000000),
            (-1.109997, -1.612300, 0.491400),
            (-0.889997, -1.612300, 0.491400)
        ]
        
        for i, (x, y, z) in enumerate(coords[:3]):
            new_x, new_y, new_z = float(x), float(y), float(z)
            orig_x, orig_y, orig_z = original_coords[i]
            
            print(f"     Point {i+1}: ({new_x:10.6f}, {new_y:10.6f}, {new_z:10.6f})")
            print(f"       Original: ({orig_x:10.6f}, {orig_y:10.6f}, {orig_z:10.6f})")
            
            # Check if transformation looks correct
            if i == 0:  # Origin
                if abs(new_x) < 0.001 and abs(new_y) < 0.001 and abs(new_z) < 0.001:
                    print(f"       ✅ Origin correctly unchanged")
                else:
                    print(f"       ❌ Origin moved unexpectedly")
            else:
                # Check if it's rotated (distance preserved)
                orig_dist = (orig_x**2 + orig_y**2 + orig_z**2)**0.5
                new_dist = (new_x**2 + new_y**2 + new_z**2)**0.5
                
                if abs(orig_dist - new_dist) < 0.01:
                    print(f"       ✅ Distance preserved: {orig_dist:.4f} → {new_dist:.4f}")
                else:
                    print(f"       ❌ Distance changed: {orig_dist:.4f} → {new_dist:.4f}")
                    
    except Exception as e:
        print(f"❌ Error analyzing file: {e}")

def test_why_main_program_fails():
    """Test why the main program falls back to STL"""
    print(f"\n🧪 TESTING WHY MAIN PROGRAM FALLS BACK TO STL")
    print("=" * 60)
    
    # Load file
    loader = STEPLoader()
    if not loader.load_step_file("test.step"):
        print("❌ Failed to load test.step")
        return
    
    print("✅ Loaded test.step")
    
    # Check what the main program's save method does
    print(f"📝 Testing loader.save_step_file with transformation matrix...")
    
    # Create a transformation matrix (like the main program does)
    transform = vtk.vtkTransform()
    transform.RotateZ(45.0)
    matrix = transform.GetMatrix()
    
    print(f"   Created VTK transformation matrix for 45° Z rotation")
    
    # Try the save method
    result = loader.save_step_file("test_main_program_debug.step", matrix)
    print(f"   Save result: {result}")
    
    # Check what files were created
    step_file = "test_main_program_debug.step"
    stl_file = "test_main_program_debug.stl"
    
    if os.path.exists(step_file):
        size = os.path.getsize(step_file)
        print(f"   ✅ STEP file created: {step_file} ({size} bytes)")
    else:
        print(f"   ❌ STEP file not created: {step_file}")
    
    if os.path.exists(stl_file):
        size = os.path.getsize(stl_file)
        print(f"   ⚠️ STL file created instead: {stl_file} ({size} bytes)")
        print(f"   💡 This explains why the main program creates STL files!")
    else:
        print(f"   ✅ No STL file created: {stl_file}")

def main():
    """Main test function"""
    print("🔧 MAIN PROGRAM SAVE TESTING")
    print("=" * 70)
    
    # Test 1: Simulate main program save with rotation
    test1_ok = test_main_program_save_with_rotation()
    
    # Test 2: Find out why main program falls back to STL
    test_why_main_program_fails()
    
    print("\n" + "=" * 70)
    print("🎯 MAIN PROGRAM SAVE TESTING COMPLETE")
    print(f"   STEPTransformer simulation: {'✅ OK' if test1_ok else '❌ FAILED'}")
    
    if test1_ok:
        print("\n💡 SOLUTION: The STEPTransformer works perfectly!")
        print("💡 PROBLEM: The main program is not using STEPTransformer correctly")
        print("💡 FIX NEEDED: Update main program to use STEPTransformer for transformations")

if __name__ == "__main__":
    main()
