#!/usr/bin/env python3
"""
Debug transformation order and coordinate system issues
Check what the GUI is actually sending vs what we're applying
"""

import sys
import os
import vtk
import math

# Add current directory to path for imports
sys.path.append('.')

from step_loader import <PERSON>EP<PERSON>oader
from step_transformer import STEPTransformer

def test_gui_transformation_values():
    """Test what transformation values the GUI would actually send"""
    print("🧪 TESTING GUI TRANSFORMATION VALUES")
    print("=" * 50)
    
    # Simulate what the GUI sends for a 45-degree Z rotation
    print("📝 Simulating GUI values for 45° Z rotation:")
    
    # Original values (from STEP file)
    orig_pos = {'x': -4.19, 'y': -3.6673, 'z': 0.4914}
    orig_rot = {'x': 0.0, 'y': 0.0, 'z': 9.0}
    
    # Current values (after user rotates 45 degrees more)
    current_pos = {'x': -4.19, 'y': -3.6673, 'z': 0.4914}  # Same position
    current_rot = {'x': 0.0, 'y': 0.0, 'z': 54.0}  # 9 + 45 = 54 degrees
    
    print(f"   Original Position: {orig_pos}")
    print(f"   Original Rotation: {orig_rot}")
    print(f"   Current Position: {current_pos}")
    print(f"   Current Rotation: {current_rot}")
    
    # Calculate the DELTA (what actually changed)
    delta_pos = {
        'x': current_pos['x'] - orig_pos['x'],
        'y': current_pos['y'] - orig_pos['y'], 
        'z': current_pos['z'] - orig_pos['z']
    }
    delta_rot = {
        'x': current_rot['x'] - orig_rot['x'],
        'y': current_rot['y'] - orig_rot['y'],
        'z': current_rot['z'] - orig_rot['z']
    }
    
    print(f"   Delta Position: {delta_pos}")
    print(f"   Delta Rotation: {delta_rot}")
    
    return orig_pos, orig_rot, current_pos, current_rot, delta_pos, delta_rot

def test_vtk_transformation_matrix():
    """Test VTK transformation matrix creation"""
    print("\n🧪 TESTING VTK TRANSFORMATION MATRIX")
    print("=" * 50)
    
    # Test the VTK transformation that the GUI creates
    current_pos = {'x': -4.19, 'y': -3.6673, 'z': 0.4914}
    current_rot = {'x': 0.0, 'y': 0.0, 'z': 54.0}
    
    print("📝 Creating VTK transformation matrix:")
    print(f"   Position: {current_pos}")
    print(f"   Rotation: {current_rot}")
    
    # This is what the GUI does
    transform = vtk.vtkTransform()
    transform.Translate(current_pos['x'], current_pos['y'], current_pos['z'])
    transform.RotateX(current_rot['x'])
    transform.RotateY(current_rot['y'])
    transform.RotateZ(current_rot['z'])
    
    matrix = transform.GetMatrix()
    
    print("   VTK Matrix:")
    for i in range(4):
        row = [matrix.GetElement(i, j) for j in range(4)]
        print(f"     [{row[0]:8.4f} {row[1]:8.4f} {row[2]:8.4f} {row[3]:8.4f}]")
    
    # Test a point transformation
    test_point = [0.0, 0.0, 0.0]
    transformed_point = [0.0, 0.0, 0.0, 1.0]
    
    # Apply matrix transformation manually
    for i in range(3):
        transformed_point[i] = (matrix.GetElement(i, 0) * test_point[0] +
                               matrix.GetElement(i, 1) * test_point[1] +
                               matrix.GetElement(i, 2) * test_point[2] +
                               matrix.GetElement(i, 3))
    
    print(f"   Test point (0,0,0) transforms to: ({transformed_point[0]:.4f}, {transformed_point[1]:.4f}, {transformed_point[2]:.4f})")
    
    return matrix

def test_step_transformer_with_different_approaches():
    """Test different transformation approaches"""
    print("\n🧪 TESTING DIFFERENT TRANSFORMATION APPROACHES")
    print("=" * 50)
    
    # Test 1: Use absolute values (what GUI sends)
    print("📝 Test 1: Using absolute values from GUI")
    transformer1 = STEPTransformer()
    if transformer1.load_step_file("test.step"):
        success1 = transformer1.apply_transformation(
            rotation_x=0.0,
            rotation_y=0.0,
            rotation_z=54.0,  # Absolute rotation
            translation_x=-4.19,  # Absolute position
            translation_y=-3.6673,
            translation_z=0.4914
        )
        if success1:
            transformer1.save_step_file("test_absolute_transform.step")
            print("   ✅ Saved: test_absolute_transform.step")
    
    # Test 2: Use delta values (what actually changed)
    print("📝 Test 2: Using delta values (changes only)")
    transformer2 = STEPTransformer()
    if transformer2.load_step_file("test.step"):
        success2 = transformer2.apply_transformation(
            rotation_x=0.0,
            rotation_y=0.0,
            rotation_z=45.0,  # Delta rotation (54 - 9 = 45)
            translation_x=0.0,  # Delta position (no change)
            translation_y=0.0,
            translation_z=0.0
        )
        if success2:
            transformer2.save_step_file("test_delta_transform.step")
            print("   ✅ Saved: test_delta_transform.step")
    
    # Test 3: Use only rotation around origin, then translate
    print("📝 Test 3: Rotate around origin, then translate")
    transformer3 = STEPTransformer()
    if transformer3.load_step_file("test.step"):
        # First apply rotation around origin
        success3a = transformer3.apply_transformation(
            rotation_x=0.0,
            rotation_y=0.0,
            rotation_z=45.0,  # Just the rotation change
            translation_x=0.0,
            translation_y=0.0,
            translation_z=0.0
        )
        # Then apply translation
        if success3a:
            success3b = transformer3.apply_transformation(
                rotation_x=0.0,
                rotation_y=0.0,
                rotation_z=0.0,  # No more rotation
                translation_x=-4.19,  # Final position
                translation_y=-3.6673,
                translation_z=0.4914
            )
            if success3b:
                transformer3.save_step_file("test_rotate_then_translate.step")
                print("   ✅ Saved: test_rotate_then_translate.step")

def analyze_coordinate_changes():
    """Analyze what actually changed in the coordinate files"""
    print("\n🧪 ANALYZING COORDINATE CHANGES")
    print("=" * 50)
    
    files_to_check = [
        ("test.step", "Original"),
        ("test_absolute_transform.step", "Absolute Transform"),
        ("test_delta_transform.step", "Delta Transform"),
        ("test_rotate_then_translate.step", "Rotate Then Translate")
    ]
    
    for filename, description in files_to_check:
        if os.path.exists(filename):
            print(f"\n📝 {description}: {filename}")
            
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Find first few coordinate points
                import re
                coord_pattern = r'CARTESIAN_POINT\s*\(\s*\'[^\']*\'\s*,\s*\(\s*([-\d.E+-]+)\s*,\s*([-\d.E+-]+)\s*,\s*([-\d.E+-]+)\s*\)'
                coords = re.findall(coord_pattern, content)
                
                print(f"   First 3 coordinates:")
                for i, (x, y, z) in enumerate(coords[:3]):
                    print(f"     Point {i+1}: ({float(x):10.6f}, {float(y):10.6f}, {float(z):10.6f})")
                    
            except Exception as e:
                print(f"   ❌ Error reading file: {e}")
        else:
            print(f"\n📝 {description}: {filename} - FILE NOT FOUND")

def main():
    """Main diagnostic function"""
    print("🔧 TRANSFORMATION ORDER DEBUG PROGRAM")
    print("=" * 60)
    
    # Test 1: Check GUI transformation values
    orig_pos, orig_rot, current_pos, current_rot, delta_pos, delta_rot = test_gui_transformation_values()
    
    # Test 2: Check VTK matrix creation
    matrix = test_vtk_transformation_matrix()
    
    # Test 3: Try different transformation approaches
    test_step_transformer_with_different_approaches()
    
    # Test 4: Analyze what actually changed
    analyze_coordinate_changes()
    
    print("\n" + "=" * 60)
    print("🎯 DIAGNOSIS COMPLETE")
    print("Check the coordinate changes above to see which approach works correctly!")

if __name__ == "__main__":
    main()
