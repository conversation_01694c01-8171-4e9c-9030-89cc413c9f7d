#!/usr/bin/env python3
"""
Direct run - no approval prompts
"""
import subprocess
import sys
import os

print("Starting STEP viewer directly...")

# Change to the correct directory
os.chdir(r'e:\Python\3d-models')

# Run the main program directly in background
try:
    process = subprocess.Popen([sys.executable, 'step_viewer_tdk_modular.py'])
    print(f"Program started with PID: {process.pid}")
    print("Program is running in background...")
    print("You can now go to your garage!")

    # Keep this script running to monitor
    import time
    while True:
        if process.poll() is None:
            print("Program still running...")
            time.sleep(30)
        else:
            print("Program stopped, restarting...")
            process = subprocess.Popen([sys.executable, 'step_viewer_tdk_modular.py'])
            time.sleep(5)

except Exception as e:
    print(f"Error: {e}")

print("Monitor finished.")
