#!/usr/bin/env python3
"""
Simple test to debug ONLY the display update - no timers, no complications
"""

import sys
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Clear debug file
with open("debug_display.txt", "w", encoding="utf-8") as f:
    f.write("=== DISPLAY DEBUG TEST START ===\n")

print("🚀 Testing DISPLAY UPDATE ONLY")

# Import the main program
from step_viewer_tdk_modular import StepViewerTDK

app = QApplication(sys.argv)

try:
    # Create viewer
    print("🔧 Creating viewer...")
    viewer = StepViewerTDK()
    
    # Initialize variables manually
    print("🔧 Setting up test variables...")
    viewer.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
    viewer.current_angle_left = 45.0
    viewer.current_axis_left = {'x': 0.0, 'y': 0.0, 'z': 1.0}
    viewer.current_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
    
    viewer.current_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
    viewer.current_angle_right = 45.0
    viewer.current_axis_right = {'x': 0.0, 'y': 0.0, 'z': 1.0}
    viewer.current_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
    
    viewer.orig_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
    viewer.orig_angle_left = 45.0
    viewer.orig_axis_left = {'x': 0.0, 'y': 0.0, 'z': 1.0}
    
    viewer.active_viewer = "top"
    
    print("✅ Variables set")
    
    # Show viewer
    viewer.show()
    print("✅ Viewer shown")
    
    def test_display_update():
        """Test the display update with known values"""
        print("\n🧪 TESTING DISPLAY UPDATE")
        
        # Log what we're setting
        with open("debug_display.txt", "a", encoding="utf-8") as f:
            f.write(f"\n=== SETTING TEST VALUES ===\n")
            f.write(f"current_axis_left = {viewer.current_axis_left}\n")
            f.write(f"current_axis_right = {viewer.current_axis_right}\n")
        
        # Call the display update function directly
        print("📞 Calling update_vtk_text_overlays()...")
        viewer.update_vtk_text_overlays()
        print("✅ update_vtk_text_overlays() completed")
        
        # Now change the values and test again
        print("🔧 Changing AXIS values to [1,0,0]...")
        viewer.current_axis_left = {'x': 1.0, 'y': 0.0, 'z': 0.0}
        viewer.current_axis_right = {'x': 1.0, 'y': 0.0, 'z': 0.0}
        
        with open("debug_display.txt", "a", encoding="utf-8") as f:
            f.write(f"\n=== AFTER CHANGING TO [1,0,0] ===\n")
            f.write(f"current_axis_left = {viewer.current_axis_left}\n")
            f.write(f"current_axis_right = {viewer.current_axis_right}\n")
        
        print("📞 Calling update_vtk_text_overlays() again...")
        viewer.update_vtk_text_overlays()
        print("✅ Second update_vtk_text_overlays() completed")
        
        # Test one more change
        print("🔧 Changing AXIS values to [0.5,0.5,0.7]...")
        viewer.current_axis_left = {'x': 0.5, 'y': 0.5, 'z': 0.7}
        viewer.current_axis_right = {'x': 0.5, 'y': 0.5, 'z': 0.7}
        
        with open("debug_display.txt", "a", encoding="utf-8") as f:
            f.write(f"\n=== AFTER CHANGING TO [0.5,0.5,0.7] ===\n")
            f.write(f"current_axis_left = {viewer.current_axis_left}\n")
            f.write(f"current_axis_right = {viewer.current_axis_right}\n")
        
        print("📞 Calling update_vtk_text_overlays() third time...")
        viewer.update_vtk_text_overlays()
        print("✅ Third update_vtk_text_overlays() completed")
        
        print("\n🔍 CHECK THE SCREEN:")
        print("   - TOP viewer should show ORIGINAL values: AXIS [0.00 0.00 1.00]")
        print("   - BOTTOM viewer should show CURRENT values: AXIS [0.50 0.50 0.70]")
        print("   - If BOTTOM viewer still shows [0.00 0.00 1.00], the display update is broken")
        
        # Test a button rotation
        print("\n🧪 TESTING BUTTON ROTATION")
        print("📞 Calling rotate_shape('x', 15)...")
        viewer.rotate_shape('x', 15)
        print("✅ rotate_shape completed")
        
        with open("debug_display.txt", "a", encoding="utf-8") as f:
            f.write(f"\n=== AFTER BUTTON ROTATION ===\n")
            f.write(f"current_axis_left = {viewer.current_axis_left}\n")
            f.write(f"current_axis_right = {viewer.current_axis_right}\n")
            f.write(f"current_rot_left = {viewer.current_rot_left}\n")
        
        print("🔍 CHECK THE SCREEN AGAIN:")
        print("   - BOTTOM viewer should now show the button rotation AXIS values")
        print("   - If it doesn't change, the button rotation display update is broken")
    
    # Run the test after 3 seconds
    QTimer.singleShot(3000, test_display_update)
    
    # Exit after 15 seconds
    QTimer.singleShot(15000, app.quit)
    
    print("🔄 Starting app - watch the screen for AXIS value changes...")
    sys.exit(app.exec_())
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
