import numpy as np
import math
from PyQt5.QtWidgets import QWidget

# Try different VTK import approaches
vtk = None
try:
    # Try importing specific VTK modules to avoid Qt dependency
    import vtkmodules.vtkCommonCore as vtk_core
    import vtkmodules.vtkRenderingCore as vtk_render
    import vtkmodules.vtkInteractionStyle as vtk_interact

    # Create a minimal vtk namespace
    class VTKNamespace:
        def __init__(self):
            # Import the essential VTK classes we need
            from vtkmodules.vtkCommonCore import vtkPoints, vtkCellArray
            from vtkmodules.vtkCommonDataModel import vtkPolyData, vtkTriangle
            from vtkmodules.vtkRenderingCore import vtkActor, vtkPolyDataMapper, vtkRenderer, vtkRenderWindow
            from vtkmodules.vtkInteractionStyle import vtkInteractorStyleTrackballCamera

            # Add them to this namespace
            self.vtkPoints = vtkPoints
            self.vtkCellArray = vtkCellArray
            self.vtkPolyData = vtkPolyData
            self.vtkTriangle = vtkTriangle
            self.vtkActor = vtkActor
            self.vtkPolyDataMapper = vtkPolyDataMapper
            self.vtkRenderer = vtkRenderer
            self.vtkRenderWindow = vtkRenderWindow
            self.vtkInteractorStyleTrackballCamera = vtkInteractorStyleTrackballCamera

    vtk = VTKNamespace()
    print("VTK imported successfully (modular)")
except ImportError as e1:
    try:
        # Fallback to regular VTK import
        import vtk
        print("VTK imported successfully (regular)")
    except ImportError as e2:
        print(f"VTK modular import failed: {e1}")
        print(f"VTK regular import failed: {e2}")
        raise ImportError("Cannot import VTK")

# Try to import VTK Qt components with multiple fallbacks
QVTKRenderWindowInteractor = None
vtk_qt_error = None

try:
    from vtkmodules.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor
    print("VTK Qt imported from vtkmodules.qt")
except ImportError as e1:
    vtk_qt_error = str(e1)
    try:
        from vtk.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor
        print("VTK Qt imported from vtk.qt")
    except ImportError as e2:
        try:
            # Try alternative import
            import vtk.qt4.QVTKRenderWindowInteractor as vtk_qt
            QVTKRenderWindowInteractor = vtk_qt.QVTKRenderWindowInteractor
            print("VTK Qt imported from vtk.qt4")
        except ImportError as e3:
            try:
                # Last resort - try direct vtk import
                from vtk import vtkRenderWindow, vtkRenderer
                print("Using basic VTK without Qt integration")
                # Create a minimal replacement
                class BasicVTKWidget(QWidget):
                    def __init__(self, parent=None):
                        super().__init__(parent)
                        self.render_window = vtkRenderWindow()
                        self.renderer = vtkRenderer()
                        self.render_window.AddRenderer(self.renderer)

                    def GetRenderWindow(self):
                        return self.render_window

                QVTKRenderWindowInteractor = BasicVTKWidget
            except ImportError as e4:
                print(f"All VTK Qt import attempts failed:")
                print(f"  vtkmodules.qt: {e1}")
                print(f"  vtk.qt: {e2}")
                print(f"  vtk.qt4: {e3}")
                print(f"  basic vtk: {e4}")
                raise ImportError("Cannot import VTK Qt components")
    except ImportError:
        QVTKRenderWindowInteractor = None

class VTKRenderer:
    def __init__(self, parent=None):
        self.parent = parent

        # Check if VTK is available
        if vtk is None:
            print("VTK not available - creating fallback renderer")
            self.renderer = None
            self.render_window = None
            self.vtk_widget = None
            self.interactor = None
            self.step_actor = None
            self.step_actors = []  # Initialize multi-actors list for each instance
            self.bbox_actor = None
            self.origin_actor = None
            return

        self.renderer = vtk.vtkRenderer()
        self.render_window = None  # Will be set by the widget
        self.vtk_widget = None
        self.interactor = None
        self.step_actor = None
        self.step_actors = []  # Initialize multi-actors list for each instance
        self.bbox_actor = None
        self.origin_actor = None

        # Origin overlay actors
        self.origin_actors = []
        self.origin_visible = True

        self.initialize()
    
    def initialize(self):
        try:
            print("🎯 Initializing VTK renderer with OpenGL crash fixes...")

            if QVTKRenderWindowInteractor:
                # Apply VTK OpenGL fixes BEFORE creating widgets
                self._apply_vtk_opengl_fixes()

                # Create the Qt VTK widget with minimal settings
                self.vtk_widget = QVTKRenderWindowInteractor()

                # Completely disable all VTK widget decorations and tools
                from PyQt5.QtCore import Qt
                self.vtk_widget.setWindowFlags(Qt.Widget)
                self.vtk_widget.setContextMenuPolicy(Qt.NoContextMenu)
                self.vtk_widget.setFocusPolicy(Qt.StrongFocus)

                # Get the render window
                self.render_window = self.vtk_widget.GetRenderWindow()

                # Apply OpenGL context fixes to render window
                self._configure_render_window_for_stability()

                # Set up our renderer with dark blue background
                self.renderer.SetBackground(0.1, 0.1, 0.2)  # Dark blue background

                # Add renderer to the render window
                self.render_window.AddRenderer(self.renderer)

                # Force dark blue background immediately
                self.renderer.SetBackground(0.1, 0.1, 0.2)

                # SAFE render with comprehensive error handling
                self._safe_render_with_fallbacks()

                # Set the widget background too
                self.vtk_widget.setStyleSheet("background-color: rgb(25, 25, 51);")

                # Get interactor and set up SAFE interaction
                self.interactor = self.vtk_widget.GetRenderWindow().GetInteractor()

                # Create SAFE custom interactor style that prevents crashes
                class SafeRotationStyle(vtk.vtkInteractorStyleTrackballCamera):
                    def __init__(self):
                        super().__init__()
                        self.SetMotionFactor(5.0)  # Reduced sensitivity for stability

                        # Mouse move events disabled per user request
                        # self.AddObserver("MouseMoveEvent", self._on_mouse_move_event)

                    def OnMouseMove(self):
                        # Override to add error handling and cursor position tracking
                        try:
                            super().OnMouseMove()

                            # ALWAYS track cursor position (not just during interaction)
                            self._update_cursor_position()

                        except Exception as e:
                            print(f"VTK mouse move error (handled): {e}")
                            pass

                    def _update_cursor_position(self):
                        """Update cursor position for display - called on every mouse move"""
                        try:
                            # Get cursor position in 3D world coordinates
                            interactor = self.GetInteractor()
                            if interactor:
                                x, y = interactor.GetEventPosition()

                                # Convert screen coordinates to world coordinates
                                picker = vtk.vtkWorldPointPicker()
                                picker.Pick(x, y, 0, interactor.GetRenderWindow().GetRenderers().GetFirstRenderer())
                                world_pos = picker.GetPickPosition()

                                # Store cursor position for display
                                if hasattr(interactor, 'cursor_callback'):
                                    interactor.cursor_callback(world_pos)

                        except Exception as e:
                            pass  # Silent fail for cursor tracking

                    def _on_mouse_move_event(self, obj, event):
                        """Handle mouse move events - minimal interference with rotation"""
                        try:
                            # Only update cursor position occasionally to avoid interfering with rotation
                            import time
                            current_time = time.time()
                            if not hasattr(self, '_last_cursor_update'):
                                self._last_cursor_update = 0

                            # Update cursor position only every 100ms to reduce interference
                            if current_time - self._last_cursor_update > 0.1:
                                self._update_cursor_position()
                                self._last_cursor_update = current_time
                        except Exception as e:
                            pass  # Silent fail for cursor tracking

                    def OnLeftButtonDown(self):
                        try:
                            super().OnLeftButtonDown()
                        except Exception as e:
                            print(f"VTK left button error (handled): {e}")
                            pass

                    def OnLeftButtonUp(self):
                        try:
                            super().OnLeftButtonUp()
                        except Exception as e:
                            print(f"VTK left button up error (handled): {e}")
                            pass

                style = SafeRotationStyle()
                self.interactor.SetInteractorStyle(style)
                print("DEBUG: VTK interactor ENABLED with SAFE rotation (crash protection)")

                # Aggressively disable ALL VTK decorations and toolbars
                self.render_window.SetBorders(0)
                self.render_window.SetOffScreenRendering(0)

                # Try to hide VTK toolbar without breaking display
                try:
                    from PyQt5.QtCore import Qt
                    # Only set basic widget flags to avoid display issues
                    self.vtk_widget.setWindowFlags(Qt.Widget)

                    # Hide toolbar children but don't hide the main widget
                    for child in self.vtk_widget.children():
                        if hasattr(child, 'hide') and hasattr(child, 'objectName'):
                            obj_name = child.objectName()
                            if 'toolbar' in obj_name.lower() or 'button' in obj_name.lower():
                                child.hide()
                                print(f"Hidden VTK child: {obj_name}")

                except Exception as e:
                    print(f"Could not disable VTK decorations: {e}")
                    # Continue anyway - display is more important than hiding toolbar

                # SAFE initialization with error handling
                try:
                    self.interactor.Initialize()
                    print("DEBUG: VTK interactor initialized successfully")
                except Exception as e:
                    print(f"VTK interactor init warning (continuing): {e}")
                    # Continue anyway - some systems have issues but still work

                # DON'T call Start() - it blocks the Qt event loop
                # self.interactor.Start()  # NEVER call this in Qt applications

                return True
            else:
                print("QVTKRenderWindowInteractor not available")
                self.vtk_widget = QWidget()
                return False
        except Exception as e:
            print(f"VTK init error: {e}")
            self.vtk_widget = QWidget()
            return False
    
    def display_polydata(self, polydata):
        print(f"DEBUG VTK DEBUG: display_polydata called with polydata: {polydata is not None}")
        if not polydata:
            print("DEBUG VTK DEBUG: No polydata provided to display")
            return False

        print(f"DEBUG VTK DEBUG: Polydata has {polydata.GetNumberOfPoints()} points, {polydata.GetNumberOfCells()} cells")

        try:
            print(f"DEBUG VTK DEBUG: step_actor BEFORE creation: {self.step_actor}")

            if self.step_actor:
                print("DEBUG VTK DEBUG: Removing existing step actor")
                self.renderer.RemoveActor(self.step_actor)

            print(f"DEBUG VTK DEBUG: Creating VTK mapper...")
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputData(polydata)

            print(f"DEBUG VTK DEBUG: Creating VTK actor...")
            self.step_actor = vtk.vtkActor()
            self.step_actor.SetMapper(mapper)

            print(f"DEBUG VTK DEBUG: step_actor AFTER creation: {self.step_actor}")
            print(f"DEBUG VTK DEBUG: step_actor type: {type(self.step_actor)}")

            # Check if polydata has color information
            cell_colors = polydata.GetCellData().GetScalars()
            point_colors = polydata.GetPointData().GetScalars()

            print(f"Cell colors: {cell_colors is not None}")
            print(f"Point colors: {point_colors is not None}")

            if cell_colors or point_colors:
                print("Using exact colors from STEP file")

                # CRITICAL FIX: Ensure colors are properly applied to VTK
                if cell_colors:
                    print("Using exact colors from STEP file")

                    # Ensure colors are set as active scalars BEFORE mapper setup
                    polydata.GetCellData().SetScalars(cell_colors)
                    polydata.GetCellData().SetActiveScalars("Colors")

                    print(f"OK Colors applied: {cell_colors.GetNumberOfTuples()} colors")
                    print(f"OK Color range: {cell_colors.GetRange()}")
                else:
                    print("FAIL No colors found in STEP file")

                # WORKING SOLUTION: Create separate actors for different colored parts
                # Since VTK ignores our RGB scalar data, we'll create multiple actors

                # Get the color data to determine which cells should be which color
                cell_colors = polydata.GetCellData().GetScalars("Colors")
                if cell_colors and cell_colors.GetNumberOfTuples() > 0:
                    print(f"DEBUG Creating separate actors for STEP file colors")
                    self._create_multi_color_actors(polydata, cell_colors)
                    # Hide the single actor since we're using multi-actors
                    if self.step_actor:
                        print(f"DEBUG: Hiding single actor since multi-actors are being used")
                        self.step_actor.SetVisibility(False)
                    return  # Skip single actor creation
                else:
                    # Fallback to single gray actor
                    mapper.SetScalarVisibility(False)
                    print(f"DEBUG VTK Fallback: Single gray actor")

                # Force mapper to use the color data
                mapper.SetInputData(polydata)
                mapper.Update()

                # FORCE BRIGHT VISIBLE COLOR
                self.step_actor.GetProperty().SetColor(1.0, 1.0, 0.0)  # BRIGHT YELLOW
                print(f"DEBUG VTK Using BRIGHT YELLOW actor color for visibility test")

                # Set lighting for maximum visibility
                self.step_actor.GetProperty().SetAmbient(0.8)  # High ambient
                self.step_actor.GetProperty().SetDiffuse(0.9)  # High diffuse
                self.step_actor.GetProperty().SetSpecular(0.5)  # Add shine

                # FORCE MODEL VISIBILITY
                self.step_actor.SetVisibility(True)
                self.step_actor.GetProperty().SetOpacity(1.0)  # Fully opaque
                self.step_actor.GetProperty().SetRepresentationToSurface()  # Force surface rendering
                print(f"DEBUG: FORCED model visibility ON, opacity 1.0, BRIGHT YELLOW color")

                print("OK VTK mapper configured for colors")
                if cell_colors:
                    print(f"Using cell colors: {cell_colors.GetNumberOfTuples()} colors")
                if point_colors:
                    print(f"Using point colors: {point_colors.GetNumberOfTuples()} colors")
                print("OK Applied exact STEP file colors")
            else:
                print("No colors found, using BRIGHT MAGENTA for visibility test")
                # Use BRIGHT MAGENTA color for visibility test
                self.step_actor.GetProperty().SetColor(1.0, 0.0, 1.0)  # BRIGHT MAGENTA
                mapper.SetScalarVisibility(False)

                # FORCE MODEL VISIBILITY
                self.step_actor.SetVisibility(True)
                self.step_actor.GetProperty().SetOpacity(1.0)  # Fully opaque
                self.step_actor.GetProperty().SetRepresentationToSurface()  # Force surface rendering
                print(f"DEBUG: FORCED model visibility ON, opacity 1.0, BRIGHT MAGENTA color")

            # Check if model is very small and scale it up
            bounds = polydata.GetBounds()
            max_dimension = max(bounds[1]-bounds[0], bounds[3]-bounds[2], bounds[5]-bounds[4])
            print(f"Model max dimension: {max_dimension}")

            if max_dimension < 0.01:  # Very small model
                scale_factor = 100.0  # Scale up 100x
                print(f"Model is very small ({max_dimension}), scaling up by {scale_factor}x")
                self.step_actor.SetScale(scale_factor, scale_factor, scale_factor)

            # Check if model is very small and scale it up
            bounds = polydata.GetBounds()
            max_dimension = max(bounds[1]-bounds[0], bounds[3]-bounds[2], bounds[5]-bounds[4])
            print(f"Model max dimension: {max_dimension}")

            if max_dimension < 0.01:  # Very small model
                scale_factor = 100.0  # Scale up 100x
                print(f"Model is very small ({max_dimension}), scaling up by {scale_factor}x")
                self.step_actor.SetScale(scale_factor, scale_factor, scale_factor)
                # Use scaled dimension for marker
                scaled_dimension = max_dimension * scale_factor
            else:
                scaled_dimension = max_dimension

            # DO NOT center the model - keep original STEP file coordinates
            bounds = polydata.GetBounds()
            print(f"Model bounds (original STEP coordinates): {bounds}")

            # Keep model at its original position from STEP file
            # The model should be displayed exactly as it was designed
            print(f"DEBUG VTK DEBUG: Adding step_actor to renderer: {self.step_actor}")
            self.renderer.AddActor(self.step_actor)
            print(f"DEBUG VTK DEBUG: step_actor added successfully")

            # VERIFY MODEL IS VISIBLE
            print(f"DEBUG: Model visibility: {self.step_actor.GetVisibility()}")
            print(f"DEBUG: Model opacity: {self.step_actor.GetProperty().GetOpacity()}")
            print(f"DEBUG: Model bounds: {self.step_actor.GetBounds()}")
            print(f"DEBUG: Renderer has {self.renderer.GetActors().GetNumberOfItems()} actors")

            # Position camera to show the model properly
            model_center = [(bounds[0] + bounds[1])/2, (bounds[2] + bounds[3])/2, (bounds[4] + bounds[5])/2]
            print(f"DEBUG: Model center: {model_center}")
            print(f"DEBUG: Model max dimension: {scaled_dimension}")

            # Force camera to show the full model
            self.fit_view()

            # Force multiple renders to ensure everything is displayed
            self.safe_render()
            self.renderer.Render()
            self.safe_render()

            print(f"DEBUG VTK DEBUG: display_polydata() COMPLETED SUCCESSFULLY")
            print(f"DEBUG VTK DEBUG: Final step_actor state: {self.step_actor}")
            return True
        except Exception as e:
            print(f"Display error: {e}")
            return False
    
    def fit_view(self):
        if self.renderer:
            # Get all actors bounds for better camera positioning
            bounds = [0, 0, 0, 0, 0, 0]
            if self.step_actor:
                bounds = self.step_actor.GetBounds()

            # Reset camera with proper bounds
            self.renderer.ResetCamera(bounds)

            # Add some padding around the model
            camera = self.renderer.GetActiveCamera()
            camera.Zoom(0.8)  # Zoom out a bit to show full model

            self.safe_render()


    
    def clear_view(self):
        if self.renderer:
            self.renderer.RemoveAllViewProps()
            # Reset actors
            self.step_actor = None
            self.step_actors = []  # Clear the multi-actors list
            self.bbox_actor = None
            self.origin_actor = None  # Reset origin actor so it can be re-added
            # Reset camera to default position
            camera = self.renderer.GetActiveCamera()
            camera.SetPosition(0, 0, 1)
            camera.SetFocalPoint(0, 0, 0)
            camera.SetViewUp(0, 1, 0)
            self.renderer.ResetCamera()
            self.safe_render()

    def toggle_bounding_box(self, show):
        """Toggle bounding box display"""
        if not self.renderer:
            return

        # Check if we have any actors to work with
        # Use the VISIBLE and TRANSFORMED actors for bounding box
        reference_actor = None
        if hasattr(self, 'step_actors') and self.step_actors:
            # Use multi-actors (these are the ones being transformed and visible)
            reference_actor = self.step_actors[0]  # Use first multi-actor
            print(f"DEBUG: Using multi-actor for bounding box (transformed geometry)")
        elif self.step_actor and self.step_actor.GetVisibility():
            # Only use single-actor if it's actually visible
            reference_actor = self.step_actor  # Use single actor
            print(f"DEBUG: Using single-actor for bounding box (visible)")
        else:
            print(f"DEBUG: No visible actors found for bounding box")

        if not reference_actor:
            print(f"DEBUG: No reference actor found for bounding box")
            return

        if show:
            # ALWAYS recreate bounding box to ensure it matches current actor state
            if self.bbox_actor:
                print(f"DEBUG: Removing existing bounding box before recreation")
                self.renderer.RemoveActor(self.bbox_actor)
                self.bbox_actor = None
                # Force render to ensure old bounding box is completely removed
                self.safe_render()

            # Get reference actor for transforms
            reference_actor = None
            if hasattr(self, 'step_actors') and self.step_actors:
                reference_actor = self.step_actors[0]
            elif hasattr(self, 'step_actor') and self.step_actor:
                reference_actor = self.step_actor

            # Use ORIGINAL geometry bounds (before transforms) to create properly sized bounding box
            if reference_actor and reference_actor.GetMapper():
                original_polydata = reference_actor.GetMapper().GetInput()
                if original_polydata:
                    bounds = original_polydata.GetBounds()
                    print(f"DEBUG: Using ORIGINAL geometry bounds for bounding box: {bounds}")
                else:
                    bounds = reference_actor.GetBounds()
                    print(f"DEBUG: Fallback to actor bounds: {bounds}")
            else:
                print("DEBUG: No reference actor found for bounding box")
                return

            # Create outline directly from original geometry
            outline = vtk.vtkOutlineFilter()
            outline.SetInputData(original_polydata)

            # Create mapper and actor for bounding box
            bbox_mapper = vtk.vtkPolyDataMapper()
            bbox_mapper.SetInputConnection(outline.GetOutputPort())

            self.bbox_actor = vtk.vtkActor()
            self.bbox_actor.SetMapper(bbox_mapper)
            self.bbox_actor.GetProperty().SetColor(1.0, 0.0, 0.0)  # Red color
            self.bbox_actor.GetProperty().SetLineWidth(2)
            self.bbox_actor.GetProperty().SetRepresentationToWireframe()

            # Apply the SAME transforms as the model actor (this is the key!)
            self.bbox_actor.SetPosition(reference_actor.GetPosition())
            self.bbox_actor.SetOrientation(reference_actor.GetOrientation())

            # If the model has a user transform, apply it to the bounding box too
            if reference_actor.GetUserTransform():
                self.bbox_actor.SetUserTransform(reference_actor.GetUserTransform())

            print(f"DEBUG: Bounding box synced with model transforms - Pos: {reference_actor.GetPosition()}, Orient: {reference_actor.GetOrientation()}")

            # FORCE the bounding box to actually apply the transformation
            self.bbox_actor.Modified()

            # Store original bounding box color for later restoration
            self.original_bbox_color = (1.0, 0.0, 0.0)  # Red
            print("DEBUG: Stored original bounding box color: Red")

            # Add bounding box to renderer
            self.renderer.AddActor(self.bbox_actor)
            print("DEBUG: Bounding box actor added to renderer")

            # Force render to ensure new bounding box is displayed
            self.safe_render()
            print("DEBUG: Forced render after bounding box creation")
        else:
            # Remove bounding box from renderer
            if self.bbox_actor:
                self.renderer.RemoveActor(self.bbox_actor)

        self.safe_render()



    def update_bounding_box(self):
        """Update bounding box to match current model position/rotation"""
        print("DEBUG: Updating bounding box using current transformed bounds")

        if self.bbox_actor:
            self.renderer.RemoveActor(self.bbox_actor)
            self.bbox_actor = None

        self.toggle_bounding_box(True)

    def _create_multi_color_actors(self, polydata, cell_colors):
        """Create separate actors for different colored parts since VTK ignores RGB scalar data"""
        try:
            import vtk

            # Analyze the color data to group cells by color
            color_groups = {}  # color_tuple -> list of cell_ids

            for cell_id in range(cell_colors.GetNumberOfTuples()):
                # Get RGB values for this cell
                r = int(cell_colors.GetComponent(cell_id, 0))
                g = int(cell_colors.GetComponent(cell_id, 1))
                b = int(cell_colors.GetComponent(cell_id, 2))
                color_key = (r, g, b)

                if color_key not in color_groups:
                    color_groups[color_key] = []
                color_groups[color_key].append(cell_id)

            print(f"DEBUG Found {len(color_groups)} color groups:")
            for color, cell_list in color_groups.items():
                print(f"   Color RGB{color}: {len(cell_list)} cells")

            # Create separate polydata and actor for each color group
            self.step_actors = []  # Store multiple actors

            for color_rgb, cell_ids in color_groups.items():
                # Create new polydata with only cells of this color
                color_polydata = vtk.vtkPolyData()
                color_polydata.SetPoints(polydata.GetPoints())  # Same points

                # Create new cell array with only the cells for this color
                color_cells = vtk.vtkCellArray()
                for cell_id in cell_ids:
                    cell = polydata.GetCell(cell_id)
                    color_cells.InsertNextCell(cell)

                color_polydata.SetPolys(color_cells)

                # Create mapper and actor for this color
                mapper = vtk.vtkPolyDataMapper()
                # Note: SetInputData will be called later after bounds correction check
                mapper.SetScalarVisibility(False)  # No scalar coloring

                actor = vtk.vtkActor()
                actor.SetMapper(mapper)

                # DEEP DEBUG: Check if this actor will have problematic bounds due to VTK's point inclusion bug
                actor_bounds = actor.GetBounds()
                x_min, x_max = actor_bounds[0], actor_bounds[1]
                y_min, y_max = actor_bounds[2], actor_bounds[3]
                z_max = actor_bounds[5]

                print(f"🔍 DEEP DEBUG: Actor for RGB{color_rgb} has bounds: {actor_bounds}")
                print(f"🔍 DEEP DEBUG: Checking if bounds match problematic pattern...")
                print(f"🔍 DEEP DEBUG: x_min={x_min:.3f}, x_max={x_max:.3f}, y_min={y_min:.3f}, y_max={y_max:.3f}, z_max={z_max:.3f}")

                # Fix VTK bounds bug: Create polydata with only the points actually used by cells
                # This prevents VTK from using ALL points when calculating bounds
                x_range = x_max - x_min
                y_range = y_max - y_min

                if x_range > 10.0 or y_range > 10.0:
                    print(f"🔧 Fixing VTK bounds bug for RGB{color_rgb}: {actor_bounds}")

                    # Create new polydata with only the points we actually use
                    corrected_polydata = vtk.vtkPolyData()
                    corrected_points = vtk.vtkPoints()
                    corrected_cells = vtk.vtkCellArray()

                    # Map old point IDs to new point IDs
                    point_map = {}
                    new_point_id = 0
                    points = polydata.GetPoints()

                    for cell_id in cell_ids:
                        cell = polydata.GetCell(cell_id)
                        new_cell_points = []

                        for j in range(cell.GetNumberOfPoints()):
                            old_point_id = cell.GetPointId(j)

                            if old_point_id not in point_map:
                                point = points.GetPoint(old_point_id)
                                corrected_points.InsertNextPoint(point)
                                point_map[old_point_id] = new_point_id
                                new_point_id += 1

                            new_cell_points.append(point_map[old_point_id])

                        # Create new cell with corrected point IDs
                        corrected_cells.InsertNextCell(len(new_cell_points), new_cell_points)

                    corrected_polydata.SetPoints(corrected_points)
                    corrected_polydata.SetPolys(corrected_cells)

                    # Use corrected polydata
                    mapper.SetInputData(corrected_polydata)
                    print(f"   ✅ Applied bounds correction - new bounds should be proper")
                else:
                    # Normal case - use original polydata
                    mapper.SetInputData(color_polydata)
                    print(f"✅ Normal bounds for RGB{color_rgb}: {len(cell_ids)} cells")

                # Set the actual STEP file color
                r, g, b = color_rgb
                actor.GetProperty().SetColor(r/255.0, g/255.0, b/255.0)
                actor.GetProperty().SetAmbient(0.3)
                actor.GetProperty().SetDiffuse(0.7)

                # Add to renderer
                self.renderer.AddActor(actor)
                self.step_actors.append(actor)

                print(f"DEBUG Created actor for RGB{color_rgb}: {len(cell_ids)} cells")

            print(f"OK Created {len(self.step_actors)} separate colored actors")

            # Store bounds for camera positioning
            bounds = polydata.GetBounds()
            max_dimension = max(bounds[1]-bounds[0], bounds[3]-bounds[2], bounds[5]-bounds[4])
            print(f"Model max dimension: {max_dimension}")

            # Position camera
            self._position_camera_for_model(bounds)

        except Exception as e:
            print(f"Error creating multi-color actors: {e}")
            import traceback
            traceback.print_exc()

    def _position_camera_for_model(self, bounds):
        """Position camera to view the model properly"""
        try:
            # Calculate model center and size
            center = [(bounds[1] + bounds[0]) / 2.0,
                     (bounds[3] + bounds[2]) / 2.0,
                     (bounds[5] + bounds[4]) / 2.0]

            max_dimension = max(bounds[1]-bounds[0], bounds[3]-bounds[2], bounds[5]-bounds[4])

            # Position camera
            camera = self.renderer.GetActiveCamera()
            camera.SetFocalPoint(center[0], center[1], center[2])

            # Set camera position for TOP-DOWN view (looking down Z-axis)
            distance = max_dimension * 2.5
            camera.SetPosition(center[0],           # X centered
                             center[1],           # Y centered
                             center[2] + distance) # Z above, looking down

            camera.SetViewUp(0, 1, 0)  # Y-up for top-down view
            self.renderer.ResetCameraClippingRange()

            print(f"Camera positioned for model bounds: {bounds}")

        except Exception as e:
            print(f"Error positioning camera: {e}")

    def create_origin_overlay(self):
        """Create origin overlay with red semicircle and XYZ arrows"""
        try:
            import vtk
            import math

            print("🎯 Creating origin overlay with red semicircle and XYZ arrows")

            # Clear existing origin actors
            self.clear_origin_overlay()

            # Create red semicircle at origin
            semicircle_actor = self._create_semicircle()
            if semicircle_actor:
                self.origin_actors.append(semicircle_actor)
                self.renderer.AddActor(semicircle_actor)
                print("✅ Red semicircle created at origin")

            # Create XYZ arrows
            x_arrow = self._create_axis_arrow([1, 0, 0], [1.0, 0.0, 0.0], "X")  # Red X-axis
            y_arrow = self._create_axis_arrow([0, 1, 0], [0.0, 1.0, 0.0], "Y")  # Green Y-axis
            z_arrow = self._create_axis_arrow([0, 0, 1], [0.0, 0.0, 1.0], "Z")  # Blue Z-axis

            for arrow in [x_arrow, y_arrow, z_arrow]:
                if arrow:
                    self.origin_actors.append(arrow)
                    self.renderer.AddActor(arrow)

            print("✅ XYZ arrows created")

            # Render the overlay
            if self.render_window:
                self.safe_render()
                print("✅ Origin overlay rendered")

            return True

        except Exception as e:
            print(f"ERROR creating origin overlay: {e}")
            return False

    def _create_semicircle(self):
        """Create a red semicircle at the origin"""
        try:
            import vtk
            import math

            # Create points for semicircle
            points = vtk.vtkPoints()
            lines = vtk.vtkCellArray()

            # Semicircle parameters
            radius = 1.0
            num_points = 20

            # Add center point
            points.InsertNextPoint(0.0, 0.0, 0.0)

            # Add semicircle points (half circle in XY plane)
            for i in range(num_points + 1):
                angle = math.pi * i / num_points  # 0 to π (180 degrees)
                x = radius * math.cos(angle)
                y = radius * math.sin(angle)
                z = 0.0
                points.InsertNextPoint(x, y, z)

            # Create lines for semicircle
            for i in range(num_points):
                line = vtk.vtkLine()
                line.GetPointIds().SetId(0, i + 1)
                line.GetPointIds().SetId(1, i + 2)
                lines.InsertNextCell(line)

            # Add radial lines to center
            for i in [1, num_points + 1]:  # Lines to start and end points
                line = vtk.vtkLine()
                line.GetPointIds().SetId(0, 0)  # Center
                line.GetPointIds().SetId(1, i)  # Edge point
                lines.InsertNextCell(line)

            # Create polydata
            polydata = vtk.vtkPolyData()
            polydata.SetPoints(points)
            polydata.SetLines(lines)

            # Create mapper and actor
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputData(polydata)

            actor = vtk.vtkActor()
            actor.SetMapper(mapper)
            actor.GetProperty().SetColor(1.0, 0.0, 0.0)  # Red
            actor.GetProperty().SetLineWidth(3)
            actor.GetProperty().SetOpacity(0.8)

            return actor

        except Exception as e:
            print(f"ERROR creating semicircle: {e}")
            return None

    def _create_axis_arrow(self, direction, color, label):
        """Create an arrow for axis direction"""
        try:
            import vtk

            # Create arrow source
            arrow_source = vtk.vtkArrowSource()
            arrow_source.SetTipResolution(16)
            arrow_source.SetShaftResolution(16)
            arrow_source.SetTipRadius(0.1)
            arrow_source.SetShaftRadius(0.03)
            arrow_source.SetTipLength(0.3)

            # Create transform to orient arrow
            transform = vtk.vtkTransform()

            # Scale arrow
            arrow_length = 2.0
            transform.Scale(arrow_length, arrow_length, arrow_length)

            # Orient arrow based on direction
            if direction == [1, 0, 0]:  # X-axis
                transform.RotateZ(-90)  # Point along +X
            elif direction == [0, 1, 0]:  # Y-axis
                pass  # Default orientation is +Y
            elif direction == [0, 0, 1]:  # Z-axis
                transform.RotateX(90)  # Point along +Z

            # Apply transform
            transform_filter = vtk.vtkTransformPolyDataFilter()
            transform_filter.SetInputConnection(arrow_source.GetOutputPort())
            transform_filter.SetTransform(transform)

            # Create mapper and actor
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputConnection(transform_filter.GetOutputPort())

            actor = vtk.vtkActor()
            actor.SetMapper(mapper)
            actor.GetProperty().SetColor(color[0], color[1], color[2])
            actor.GetProperty().SetOpacity(0.7)

            return actor

        except Exception as e:
            print(f"ERROR creating axis arrow: {e}")
            return None

    def clear_origin_overlay(self):
        """Remove origin overlay actors"""
        try:
            for actor in self.origin_actors:
                if actor and self.renderer:
                    self.renderer.RemoveActor(actor)
            self.origin_actors.clear()
            print("🗑️ Origin overlay cleared")
        except Exception as e:
            print(f"ERROR clearing origin overlay: {e}")

    def toggle_origin_overlay(self):
        """Toggle origin overlay visibility"""
        try:
            self.origin_visible = not self.origin_visible
            for actor in self.origin_actors:
                if actor:
                    actor.SetVisibility(self.origin_visible)

            if self.render_window:
                self.safe_render()

            status = "visible" if self.origin_visible else "hidden"
            print(f"🎯 Origin overlay {status}")
            return self.origin_visible

        except Exception as e:
            print(f"ERROR toggling origin overlay: {e}")
            return False

    def _apply_vtk_opengl_fixes(self):
        """Apply VTK OpenGL compatibility fixes to prevent crashes"""
        try:
            import vtk
            import os

            print("🔧 Applying VTK OpenGL compatibility fixes...")

            # Strategy 1: Force software rendering (most compatible)
            os.environ['VTK_USE_SOFTWARE_RENDERING'] = '1'
            os.environ['VTK_USE_OPENGL2'] = '0'  # Disable OpenGL2 backend

            # Strategy 2: Disable problematic OpenGL features
            os.environ['VTK_OPENGL_HAS_OSMESA'] = '0'  # Disable OSMesa
            os.environ['VTK_DEFAULT_RENDER_WINDOW_OFFSCREEN'] = '0'  # Force on-screen

            # Strategy 3: Set OpenGL compatibility mode
            os.environ['MESA_GL_VERSION_OVERRIDE'] = '3.3'  # Force OpenGL 3.3
            os.environ['MESA_GLSL_VERSION_OVERRIDE'] = '330'  # Force GLSL 3.30

            # Suppress VTK warnings to reduce noise
            vtk.vtkObject.GlobalWarningDisplayOff()

            print("✅ VTK OpenGL fixes applied")

        except Exception as e:
            print(f"🔧 Warning: Could not apply all OpenGL fixes: {e}")

    def _configure_render_window_for_stability(self):
        """Configure render window settings for maximum stability"""
        try:
            print("🔧 Configuring render window for stability...")

            if self.render_window:
                # Disable problematic features
                self.render_window.SetBorders(0)
                self.render_window.SetOffScreenRendering(0)  # Force on-screen
                self.render_window.SetMultiSamples(0)  # Disable anti-aliasing
                self.render_window.SetAlphaBitPlanes(0)  # Disable alpha
                self.render_window.SetStencilCapable(0)  # Disable stencil buffer

                # Set conservative buffer sizes
                self.render_window.SetDesiredUpdateRate(30)  # Limit frame rate

                print("✅ Render window configured for stability")

        except Exception as e:
            print(f"🔧 Warning: Could not configure all render window settings: {e}")

    def _safe_render_with_fallbacks(self):
        """Perform rendering with multiple fallback strategies"""
        try:
            print("🔧 Attempting safe render with fallbacks...")

            # Strategy 1: Try normal render
            try:
                self.render_window.Render()
                print("✅ Normal render successful")
                return True
            except Exception as e1:
                print(f"🔧 Normal render failed: {e1}")

            # Strategy 2: Try render with error suppression
            try:
                import vtk
                vtk.vtkObject.GlobalWarningDisplayOff()
                self.render_window.Render()
                print("✅ Render with error suppression successful")
                return True
            except Exception as e2:
                print(f"🔧 Render with error suppression failed: {e2}")

            # Strategy 3: Try delayed render (sometimes works after initialization)
            try:
                from PyQt5.QtCore import QTimer
                def delayed_render():
                    try:
                        self.render_window.Render()
                        print("✅ Delayed render successful")
                    except Exception as e3:
                        print(f"🔧 Delayed render failed: {e3}")

                QTimer.singleShot(100, delayed_render)  # Render after 100ms
                print("🔧 Scheduled delayed render")
                return True

            except Exception as e3:
                print(f"🔧 Could not schedule delayed render: {e3}")

            # Strategy 4: Continue without initial render (sometimes works)
            print("🔧 Continuing without initial render - may work when model is loaded")
            return True

        except Exception as e:
            print(f"🔧 All render strategies failed: {e}")
            return False

    def safe_render(self):
        """Safe rendering method that handles OpenGL errors gracefully"""
        try:
            if self.render_window:
                self.render_window.Render()
                return True
        except Exception as e:
            print(f"🔧 Render error handled gracefully: {e}")
            # Continue execution - don't crash the program
            return False

    def create_part_origin_overlay(self, part_origin_x, part_origin_y, part_origin_z):
        """Create a second origin marker at the actual part origin from STEP file"""
        try:
            import vtk

            print(f"🎯 Creating part origin marker at ({part_origin_x:.3f}, {part_origin_y:.3f}, {part_origin_z:.3f})")

            # Create a sphere at the part origin
            sphere_source = vtk.vtkSphereSource()
            sphere_source.SetCenter(part_origin_x, part_origin_y, part_origin_z)
            sphere_source.SetRadius(0.5)  # Small sphere
            sphere_source.SetPhiResolution(16)
            sphere_source.SetThetaResolution(16)

            # Create mapper and actor for sphere
            sphere_mapper = vtk.vtkPolyDataMapper()
            sphere_mapper.SetInputConnection(sphere_source.GetOutputPort())

            self.part_origin_sphere = vtk.vtkActor()
            self.part_origin_sphere.SetMapper(sphere_mapper)
            self.part_origin_sphere.GetProperty().SetColor(0.0, 1.0, 0.0)  # Green color
            self.part_origin_sphere.GetProperty().SetOpacity(0.8)

            # Create XYZ arrows at part origin
            # X-axis arrow (Red)
            x_arrow_source = vtk.vtkArrowSource()
            x_arrow_transform = vtk.vtkTransform()
            x_arrow_transform.Translate(part_origin_x, part_origin_y, part_origin_z)
            x_arrow_transform.RotateZ(-90)  # Point along X-axis
            x_arrow_transform.Scale(2.0, 2.0, 2.0)  # Make it bigger

            x_arrow_filter = vtk.vtkTransformPolyDataFilter()
            x_arrow_filter.SetInputConnection(x_arrow_source.GetOutputPort())
            x_arrow_filter.SetTransform(x_arrow_transform)

            x_arrow_mapper = vtk.vtkPolyDataMapper()
            x_arrow_mapper.SetInputConnection(x_arrow_filter.GetOutputPort())

            self.part_origin_x_arrow = vtk.vtkActor()
            self.part_origin_x_arrow.SetMapper(x_arrow_mapper)
            self.part_origin_x_arrow.GetProperty().SetColor(1.0, 0.0, 0.0)  # Red

            # Y-axis arrow (Green)
            y_arrow_source = vtk.vtkArrowSource()
            y_arrow_transform = vtk.vtkTransform()
            y_arrow_transform.Translate(part_origin_x, part_origin_y, part_origin_z)
            # Y-axis is default direction for arrow
            y_arrow_transform.Scale(2.0, 2.0, 2.0)

            y_arrow_filter = vtk.vtkTransformPolyDataFilter()
            y_arrow_filter.SetInputConnection(y_arrow_source.GetOutputPort())
            y_arrow_filter.SetTransform(y_arrow_transform)

            y_arrow_mapper = vtk.vtkPolyDataMapper()
            y_arrow_mapper.SetInputConnection(y_arrow_filter.GetOutputPort())

            self.part_origin_y_arrow = vtk.vtkActor()
            self.part_origin_y_arrow.SetMapper(y_arrow_mapper)
            self.part_origin_y_arrow.GetProperty().SetColor(0.0, 1.0, 0.0)  # Green

            # Z-axis arrow (Blue)
            z_arrow_source = vtk.vtkArrowSource()
            z_arrow_transform = vtk.vtkTransform()
            z_arrow_transform.Translate(part_origin_x, part_origin_y, part_origin_z)
            z_arrow_transform.RotateX(90)  # Point along Z-axis
            z_arrow_transform.Scale(2.0, 2.0, 2.0)

            z_arrow_filter = vtk.vtkTransformPolyDataFilter()
            z_arrow_filter.SetInputConnection(z_arrow_source.GetOutputPort())
            z_arrow_filter.SetTransform(z_arrow_transform)

            z_arrow_mapper = vtk.vtkPolyDataMapper()
            z_arrow_mapper.SetInputConnection(z_arrow_filter.GetOutputPort())

            self.part_origin_z_arrow = vtk.vtkActor()
            self.part_origin_z_arrow.SetMapper(z_arrow_mapper)
            self.part_origin_z_arrow.GetProperty().SetColor(0.0, 0.0, 1.0)  # Blue

            # Add all part origin actors to renderer
            self.renderer.AddActor(self.part_origin_sphere)
            self.renderer.AddActor(self.part_origin_x_arrow)
            self.renderer.AddActor(self.part_origin_y_arrow)
            self.renderer.AddActor(self.part_origin_z_arrow)

            # Initially visible
            self.part_origin_visible = True

            # Render
            if self.render_window:
                self.render_window.Render()

            print("✅ Part origin overlay created successfully")
            return True

        except Exception as e:
            print(f"❌ Error creating part origin overlay: {e}")
            return False

    def toggle_part_origin_overlay(self):
        """Toggle part origin overlay visibility"""
        try:
            if not hasattr(self, 'part_origin_sphere'):
                print("No part origin overlay to toggle")
                return False

            self.part_origin_visible = not self.part_origin_visible

            # Toggle visibility of all part origin actors
            self.part_origin_sphere.SetVisibility(self.part_origin_visible)
            self.part_origin_x_arrow.SetVisibility(self.part_origin_visible)
            self.part_origin_y_arrow.SetVisibility(self.part_origin_visible)
            self.part_origin_z_arrow.SetVisibility(self.part_origin_visible)

            # Render
            if self.render_window:
                self.render_window.Render()

            status = "visible" if self.part_origin_visible else "hidden"
            print(f"Part origin overlay {status}")
            return self.part_origin_visible

        except Exception as e:
            print(f"Error toggling part origin overlay: {e}")
            return False

