#!/usr/bin/env python3
"""
Debug version that shows exactly what's happening during save/load workflow
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication, QFileDialog
from PyQt5.QtCore import QTimer
from step_viewer_tdk_modular import StepViewerTDK

class DebugSaveLoadWorkflow:
    def __init__(self):
        self.app = QApplication([])
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        print("🧪 DEBUG SAVE/LOAD WORKFLOW")
        print("=" * 60)
        print("This will show exactly what happens during each step:")
        print("1. Load test.step into TOP")
        print("2. Apply rotation to TOP")
        print("3. Save from TOP with green button")
        print("4. Load saved file into BOTTOM")
        print("5. Compare results")
        print("=" * 60)
        
        # Override the save method to add debug
        self.original_save_method = self.viewer.save_step_file_option1
        self.viewer.save_step_file_option1 = self.debug_save_method
        
        # Override the load method to add debug
        self.original_load_method = self.viewer.load_step_file_direct
        self.viewer.load_step_file_direct = self.debug_load_method
        
        # Start the workflow
        QTimer.singleShot(1000, self.step1_load_test_step)
        
    def debug_save_method(self):
        """Debug wrapper for save method"""
        print("\n" + "="*50)
        print("🔧 DEBUG: SAVE BUTTON CLICKED (Green Button)")
        print("="*50)
        
        # Check current state before saving
        if hasattr(self.viewer, 'model_rot_left'):
            print(f"📊 Current model_rot_left: {self.viewer.model_rot_left}")
        if hasattr(self.viewer, 'current_rot_left'):
            print(f"📊 Current current_rot_left: {self.viewer.current_rot_left}")
        if hasattr(self.viewer, 'orig_rot_left'):
            print(f"📊 Current orig_rot_left: {self.viewer.orig_rot_left}")
            
        print(f"📊 Active viewer: {self.viewer.active_viewer}")
        
        # Call original save method
        result = self.original_save_method()
        
        print(f"📊 Save result: {result}")
        print("="*50)
        
        return result
        
    def debug_load_method(self, filename):
        """Debug wrapper for load method"""
        print("\n" + "="*50)
        print(f"🔧 DEBUG: LOAD FILE CALLED: {filename}")
        print("="*50)
        
        print(f"📊 Active viewer: {self.viewer.active_viewer}")
        print(f"📊 File exists: {os.path.exists(filename)}")
        
        if os.path.exists(filename):
            # Check if file has rotation marker
            with open(filename, 'r') as f:
                content = f.read()
                if "ROTATION_VALUES:" in content:
                    print("✅ File contains ROTATION_VALUES marker")
                    import re
                    match = re.search(r'ROTATION_VALUES: X=([\d.-]+) Y=([\d.-]+) Z=([\d.-]+)', content)
                    if match:
                        x, y, z = map(float, match.groups())
                        print(f"📊 Stored rotation: X={x}°, Y={y}°, Z={z}°")
                else:
                    print("❌ File does NOT contain ROTATION_VALUES marker")
        
        # Call original load method
        result = self.original_load_method(filename)
        
        # Check state after loading
        if self.viewer.active_viewer == "top":
            if hasattr(self.viewer, 'orig_rot_left'):
                print(f"📊 After load orig_rot_left: {self.viewer.orig_rot_left}")
            if hasattr(self.viewer, 'current_rot_left'):
                print(f"📊 After load current_rot_left: {self.viewer.current_rot_left}")
        else:
            if hasattr(self.viewer, 'orig_rot_right'):
                print(f"📊 After load orig_rot_right: {self.viewer.orig_rot_right}")
            if hasattr(self.viewer, 'current_rot_right'):
                print(f"📊 After load current_rot_right: {self.viewer.current_rot_right}")
                
        print(f"📊 Load result: {result}")
        print("="*50)
        
        return result
        
    def step1_load_test_step(self):
        """Step 1: Load test.step into TOP window"""
        print("\n🔄 STEP 1: Loading test.step into TOP window")
        
        if not os.path.exists("test.step"):
            print("❌ test.step not found!")
            self.app.quit()
            return
            
        # Set active viewer to top
        self.viewer.active_viewer = "top"
        self.viewer.update_viewer_highlights()
        
        # Load the file
        success = self.viewer.load_step_file_direct("test.step")
        
        if success:
            print("✅ test.step loaded into TOP window")
            QTimer.singleShot(2000, self.step2_apply_rotation)
        else:
            print("❌ Failed to load test.step")
            self.app.quit()
            
    def step2_apply_rotation(self):
        """Step 2: Apply rotation to TOP window"""
        print("\n🔄 STEP 2: Applying 45° X rotation to TOP window")
        
        # Make sure TOP is active
        self.viewer.active_viewer = "top"
        self.viewer.update_viewer_highlights()
        
        # Apply 3 x 15° rotations = 45° total
        for i in range(3):
            print(f"   Applying rotation {i+1}/3: X+15°")
            self.viewer.rotate_shape('x', 15.0)
            
        print("✅ 45° X rotation applied to TOP window")
        
        # Check the result
        if hasattr(self.viewer, 'model_rot_left'):
            print(f"📊 Final model_rot_left: {self.viewer.model_rot_left}")
            
        QTimer.singleShot(2000, self.step3_save_file)
        
    def step3_save_file(self):
        """Step 3: Save file using green button"""
        print("\n🔄 STEP 3: Saving rotated file using green button")
        print("   (This will trigger the debug save method)")
        
        # Make sure TOP is active
        self.viewer.active_viewer = "top"
        self.viewer.update_viewer_highlights()
        
        # Trigger save (this will call our debug wrapper)
        self.viewer.save_step_file_option1()
        
        QTimer.singleShot(3000, self.step4_load_saved_file)
        
    def step4_load_saved_file(self):
        """Step 4: Load the saved file into BOTTOM window"""
        print("\n🔄 STEP 4: Loading saved file into BOTTOM window")
        
        # Look for the most recently saved file
        saved_files = []
        for f in os.listdir('.'):
            if f.endswith('.step') and f != 'test.step':
                if os.path.getmtime(f) > os.path.getmtime('test.step'):
                    saved_files.append((f, os.path.getmtime(f)))
                    
        if not saved_files:
            print("❌ No saved files found!")
            self.app.quit()
            return
            
        # Get the most recent file
        saved_files.sort(key=lambda x: x[1], reverse=True)
        saved_file = saved_files[0][0]
        
        print(f"📁 Found saved file: {saved_file}")
        
        # Set active viewer to bottom
        self.viewer.active_viewer = "bottom"
        self.viewer.update_viewer_highlights()
        
        # Load the saved file
        success = self.viewer.load_step_file_direct(saved_file)
        
        if success:
            print("✅ Saved file loaded into BOTTOM window")
            QTimer.singleShot(2000, self.step5_compare_results)
        else:
            print("❌ Failed to load saved file")
            self.app.quit()
            
    def step5_compare_results(self):
        """Step 5: Compare the results"""
        print("\n🔄 STEP 5: Comparing TOP and BOTTOM windows")
        print("="*60)
        
        # Check TOP window values
        print("📊 TOP WINDOW:")
        if hasattr(self.viewer, 'model_rot_left'):
            print(f"   model_rot_left: {self.viewer.model_rot_left}")
        if hasattr(self.viewer, 'current_rot_left'):
            print(f"   current_rot_left: {self.viewer.current_rot_left}")
        if hasattr(self.viewer, 'orig_rot_left'):
            print(f"   orig_rot_left: {self.viewer.orig_rot_left}")
            
        # Check BOTTOM window values
        print("📊 BOTTOM WINDOW:")
        if hasattr(self.viewer, 'model_rot_right'):
            print(f"   model_rot_right: {self.viewer.model_rot_right}")
        if hasattr(self.viewer, 'current_rot_right'):
            print(f"   current_rot_right: {self.viewer.current_rot_right}")
        if hasattr(self.viewer, 'orig_rot_right'):
            print(f"   orig_rot_right: {self.viewer.orig_rot_right}")
            
        print("="*60)
        print("🎯 WORKFLOW COMPLETE!")
        print("Check the GUI to see if both windows show the same rotation.")
        print("The program will stay open for manual inspection.")

def main():
    debug_workflow = DebugSaveLoadWorkflow()
    debug_workflow.app.exec_()

if __name__ == "__main__":
    main()
