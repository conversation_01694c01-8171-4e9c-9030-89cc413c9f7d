# AI FOCUS RULES - MANDATORY CHECKLIST

## BEFORE MAKING ANY CODE CHANGES:

### 1. SYNTAX CHECK FIRST - ALWAYS
- [ ] Run: `python -c "import ast; ast.parse(open('filename.py').read()); print('✅ SYNTAX OK')"`
- [ ] NEVER skip this step
- [ ] Fix ALL syntax errors before proceeding

### 2. TEST IMPORTS BEFORE USING
- [ ] Test actual import statements that will be used
- [ ] Don't assume import paths - verify them
- [ ] Example: `python -c "from OCC.Core.STEPControl import STEP<PERSON><PERSON><PERSON>_Reader; print('✅ IMPORT OK')"`

### 3. VERIFY WHAT'S ACTUALLY RUNNING
- [ ] Use `list-processes` to check what's actually running
- [ ] Don't claim something is running without verification
- [ ] If nothing is running, say "NOTHING IS RUNNING" - don't pretend

## WHEN MAKING CHANGES:

### 4. ONE CHANGE AT A TIME
- [ ] Make ONE specific change
- [ ] Test that ONE change
- [ ] Don't make multiple changes simultaneously

### 5. VERIFY RESULTS IMMEDIATELY
- [ ] After each change, verify it worked
- [ ] Don't assume success
- [ ] Show actual test results

### 6. NO GUESSING OR ASSUMPTIONS
- [ ] Don't say "should work" or "might work"
- [ ] Test everything
- [ ] Only state facts based on actual test results

## WHEN DEBUGGING:

### 7. FOCUS ON THE SPECIFIC ISSUE
- [ ] Address the exact problem stated
- [ ] Don't go off on tangents
- [ ] Don't change unrelated things

### 8. SHOW YOUR WORK
- [ ] Show the actual commands run
- [ ] Show the actual output received
- [ ] Explain what the output means

### 9. WHEN STUCK - ASK FOR HELP
- [ ] If repeating the same failed approach 3 times, STOP
- [ ] Ask user for guidance
- [ ] Don't keep trying the same thing

## COMMUNICATION RULES:

### 10. NO FALSE CLAIMS
- [ ] Don't say "it's running" unless verified
- [ ] Don't say "it's fixed" unless tested
- [ ] Don't say "should work" - test it

### 11. BE DIRECT AND HONEST
- [ ] If something failed, say it failed
- [ ] If you don't know, say you don't know
- [ ] If you made an error, acknowledge it

### 12. FOLLOW USER INSTRUCTIONS EXACTLY
- [ ] Do what the user asks, nothing more
- [ ] Don't add extra features or changes
- [ ] Ask before doing anything beyond the request

## MANDATORY WORKFLOW:

```
1. READ user request carefully
2. CHECK syntax of any files to be modified
3. TEST any imports/modules to be used
4. MAKE one specific change
5. TEST the change immediately
6. VERIFY the result
7. REPORT actual results (not assumptions)
8. If not working, STOP and ask for help
```

## FORBIDDEN ACTIONS:

❌ Making changes without syntax checking first
❌ Claiming something is running without verification
❌ Making multiple changes at once
❌ Assuming imports work without testing
❌ Saying "should work" instead of testing
❌ Going off on tangents from the specific request
❌ Repeating failed approaches more than 3 times

## SUCCESS CRITERIA:

✅ Every change is tested immediately
✅ Every claim is backed by actual evidence
✅ Syntax is always checked before changes
✅ Imports are always tested before use
✅ Only make claims that are verified facts
✅ Stay focused on the specific user request

---

**THESE RULES ARE MANDATORY - NO EXCEPTIONS**
**REFER TO THIS FILE BEFORE EVERY ACTION**
