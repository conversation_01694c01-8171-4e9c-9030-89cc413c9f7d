import subprocess
import sys
import threading
import time

def run_program():
    """Run the program and capture real-time output"""
    print("Starting step_viewer_tdk_modular.py...")
    
    # Start the process
    process = subprocess.Popen(
        [sys.executable, 'step_viewer_tdk_modular.py'],
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        text=True,
        bufsize=1,
        universal_newlines=True
    )
    
    print("Program started, capturing output...")
    
    # Read output in real-time
    try:
        for line in iter(process.stdout.readline, ''):
            if line:
                print(f"OUTPUT: {line.strip()}")
                
                # Look for specific debug messages
                if "DEBUG: Camera update timer working" in line:
                    print("🎯 FOUND: Camera update timer is working!")
                elif "DEBUG: Timer working" in line:
                    print("⚠️  FOUND: Old timer message (wrong version)")
                elif "Current TOP" in line:
                    print("📊 FOUND: Current TOP update")
                elif "ERROR" in line or "Error" in line:
                    print(f"❌ ERROR: {line.strip()}")
                    
    except KeyboardInterrupt:
        print("Stopping capture...")
    finally:
        process.terminate()
        print("Program terminated")

if __name__ == "__main__":
    run_program()
