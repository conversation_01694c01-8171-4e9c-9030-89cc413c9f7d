#!/usr/bin/env python3
"""
Test cumulative rotation fix
"""

import sys
import os
sys.path.append('.')

from step_viewer_tdk_modular import StepViewerTDK
from PyQt5.QtWidgets import QApplication
import time

def test_cumulative_fix():
    """Test the correct way to do cumulative rotations"""
    print("🧪 TESTING CUMULATIVE ROTATION FIX")
    print("=" * 50)
    
    app = QApplication([])
    viewer = StepViewerTDK()
    viewer.show()
    
    try:
        # STEP 1: Load a pre-rotated file (X=90°)
        print("\n📂 STEP 1: Loading test_rotation_1.step (X=90°)...")
        viewer.active_viewer = "top"
        success = viewer.load_step_file_direct('test_rotation_1.step')
        
        if not success:
            print("❌ Failed to load pre-rotated file")
            return
        
        print("✅ Pre-rotated file loaded")
        
        # Get the original rotation values from the loaded file
        if hasattr(viewer, 'orig_rot_left'):
            orig_rot = viewer.orig_rot_left.copy()
            print(f"📊 Original rotation from file: {orig_rot}")
        else:
            print("❌ No original rotation values found")
            return
        
        # STEP 2: Apply additional rotation (Y=45°)
        additional_rot = {"x": 0.0, "y": 45.0, "z": 0.0}
        print(f"\n🔄 STEP 2: Applying additional rotation: {additional_rot}")
        viewer._apply_3d_rotation_matrix("left", additional_rot)
        print("✅ Additional rotation applied")
        
        # STEP 3: Save with CORRECT parameters
        print(f"\n💾 STEP 3: Saving with CORRECT cumulative approach...")
        filename = "test_cumulative_fix.step"
        
        loader = viewer.step_loader_left
        delta_pos = {"x": 0.0, "y": 0.0, "z": 0.0}
        delta_rot = additional_rot  # Just the additional rotation!
        orig_pos = {"x": 0.0, "y": 0.0, "z": 0.0}
        # orig_rot should be the rotation from the loaded file
        
        print(f"   Using parameters:")
        print(f"   - orig_rot: {orig_rot} (from loaded file)")
        print(f"   - delta_rot: {delta_rot} (additional rotation)")
        print(f"   - Expected result: X={orig_rot['x'] + delta_rot['x']:.1f}°, Y={orig_rot['y'] + delta_rot['y']:.1f}°, Z={orig_rot['z'] + delta_rot['z']:.1f}°")
        
        success = viewer._save_step_text_transform(
            filename, loader, delta_pos, delta_rot, orig_pos, orig_rot
        )
        
        if success:
            print(f"✅ Cumulative file saved: {filename}")
            
            # STEP 4: Load and verify
            print(f"\n📂 STEP 4: Loading and verifying saved file...")
            viewer.active_viewer = "bottom"
            load_success = viewer.load_step_file_direct(filename)
            
            if load_success and hasattr(viewer, 'current_rot_right'):
                loaded_rot = viewer.current_rot_right
                expected_x = orig_rot['x'] + delta_rot['x']
                expected_y = orig_rot['y'] + delta_rot['y']
                expected_z = orig_rot['z'] + delta_rot['z']
                
                print(f"📊 Expected: X={expected_x:.1f}°, Y={expected_y:.1f}°, Z={expected_z:.1f}°")
                print(f"📊 Loaded:   X={loaded_rot['x']:.1f}°, Y={loaded_rot['y']:.1f}°, Z={loaded_rot['z']:.1f}°")
                
                x_diff = abs(loaded_rot['x'] - expected_x)
                y_diff = abs(loaded_rot['y'] - expected_y)
                z_diff = abs(loaded_rot['z'] - expected_z)
                
                if x_diff < 0.1 and y_diff < 0.1 and z_diff < 0.1:
                    print(f"✅ SUCCESS: Cumulative rotation preserved correctly!")
                    print(f"   Differences: ΔX={x_diff:.3f}°, ΔY={y_diff:.3f}°, ΔZ={z_diff:.3f}°")
                else:
                    print(f"❌ FAILED: Cumulative rotation not preserved")
                    print(f"   Differences: ΔX={x_diff:.3f}°, ΔY={y_diff:.3f}°, ΔZ={z_diff:.3f}°")
            else:
                print(f"❌ Failed to load or verify saved file")
        else:
            print(f"❌ Failed to save cumulative file")
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
    
    time.sleep(2)
    app.quit()

if __name__ == "__main__":
    test_cumulative_fix()
