#!/usr/bin/env python3
"""
SIMPLE RUNTIME DEBUG - Test variable initialization and method execution
"""

import sys
import os
import subprocess

def test_variable_initialization():
    """Test if the required variables are initialized in a fresh instance"""
    print("🔧 TESTING VARIABLE INITIALIZATION")
    print("=" * 50)
    
    # Create a script to check variable initialization
    init_test_script = '''
import sys
import os
sys.path.insert(0, ".")

try:
    # Set offscreen rendering to prevent window from showing
    os.environ['QT_QPA_PLATFORM'] = 'offscreen'
    
    from PyQt5.QtWidgets import QApplication
    import step_viewer_tdk_modular
    
    app = QApplication([])
    
    # Create instance
    print("Creating StepViewerTDK instance...")
    viewer = step_viewer_tdk_modular.StepViewerTDK()
    print("✅ Instance created successfully")
    
    # Check if required variables exist
    required_vars = [
        'model_rot_left', 'model_rot_right',
        'current_rot_left', 'current_rot_right', 
        'current_axis_left', 'current_axis_right',
        'current_angle_left', 'current_angle_right'
    ]
    
    print("\\nVariable initialization check:")
    initialized_count = 0
    for var in required_vars:
        if hasattr(viewer, var):
            value = getattr(viewer, var)
            print(f"✅ {var}: {value}")
            initialized_count += 1
        else:
            print(f"❌ {var}: NOT INITIALIZED")
    
    print(f"\\nInitialized: {initialized_count}/{len(required_vars)} variables")
    
    # Check if timer exists
    if hasattr(viewer, 'rotation_timer'):
        timer = viewer.rotation_timer
        print(f"✅ rotation_timer: interval={timer.interval()}ms, active={timer.isActive()}")
    else:
        print("❌ rotation_timer: NOT INITIALIZED")
    
    # Test if rotate_shape method works
    print("\\nTesting rotate_shape method...")
    try:
        viewer.rotate_shape('x', 15)
        print("✅ rotate_shape method executed without error")
        
        # Check if variables were updated
        if hasattr(viewer, 'current_rot_left'):
            print(f"✅ current_rot_left after rotation: {viewer.current_rot_left}")
        if hasattr(viewer, 'current_axis_left'):
            print(f"✅ current_axis_left after rotation: {viewer.current_axis_left}")
            
    except Exception as e:
        print(f"❌ rotate_shape method error: {e}")
    
    app.quit()
    print("✅ Test completed successfully")
    
except Exception as e:
    print(f"❌ Variable initialization test error: {e}")
    import traceback
    traceback.print_exc()
'''
    
    try:
        # Write and run the test script
        with open('test_simple_init.py', 'w') as f:
            f.write(init_test_script)
        
        print("Running variable initialization test...")
        result = subprocess.run([sys.executable, 'test_simple_init.py'], 
                              capture_output=True, text=True, timeout=30)
        
        print("Test output:")
        print(result.stdout)
        if result.stderr:
            print("Test errors:")
            print(result.stderr)
            
        # Clean up
        if os.path.exists('test_simple_init.py'):
            os.remove('test_simple_init.py')
            
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ Test execution error: {e}")
        return False

def test_method_source_code():
    """Check the actual source code of key methods"""
    print("\n📋 CHECKING METHOD SOURCE CODE")
    print("=" * 50)
    
    try:
        import step_viewer_tdk_modular
        import inspect
        
        # Check rotate_shape method
        if hasattr(step_viewer_tdk_modular.StepViewerTDK, 'rotate_shape'):
            method = getattr(step_viewer_tdk_modular.StepViewerTDK, 'rotate_shape')
            source_lines = inspect.getsourcelines(method)
            source_text = ''.join(source_lines[0])
            
            print("🔍 rotate_shape method analysis:")
            
            # Check for key components
            checks = [
                ('current_axis_left calculation', 'current_axis_left' in source_text and 'rot_mag' in source_text),
                ('current_axis_right calculation', 'current_axis_right' in source_text and 'rot_mag' in source_text),
                ('Math.sqrt usage', 'math.sqrt' in source_text),
                ('update_vtk_text_overlays call', 'update_vtk_text_overlays' in source_text),
                ('Rotation value increment', '+= degrees' in source_text)
            ]
            
            for check_name, check_result in checks:
                status = "✅" if check_result else "❌"
                print(f"   {status} {check_name}")
            
            # Show critical lines
            print("\\n📝 Critical lines from rotate_shape:")
            lines = source_lines[0]
            for i, line in enumerate(lines):
                if any(keyword in line for keyword in ['current_axis', 'rot_mag', 'math.sqrt']):
                    print(f"   Line {source_lines[1] + i}: {line.rstrip()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Source code analysis error: {e}")
        return False

def test_update_vtk_text_overlays():
    """Check the update_vtk_text_overlays method"""
    print("\\n📝 CHECKING update_vtk_text_overlays METHOD")
    print("=" * 50)
    
    try:
        import step_viewer_tdk_modular
        import inspect
        
        if hasattr(step_viewer_tdk_modular.StepViewerTDK, 'update_vtk_text_overlays'):
            method = getattr(step_viewer_tdk_modular.StepViewerTDK, 'update_vtk_text_overlays')
            source_lines = inspect.getsourcelines(method)
            source_text = ''.join(source_lines[0])
            
            print("🔍 update_vtk_text_overlays method analysis:")
            
            # Check for key components
            checks = [
                ('Uses current_axis_left', 'current_axis_left' in source_text),
                ('Uses current_axis_right', 'current_axis_right' in source_text),
                ('Uses current_rot_left', 'current_rot_left' in source_text),
                ('Uses current_rot_right', 'current_rot_right' in source_text),
                ('Creates text content', 'ROT:' in source_text or 'AXIS:' in source_text),
                ('Updates VTK text actor', 'SetInput' in source_text or 'text_actor' in source_text)
            ]
            
            for check_name, check_result in checks:
                status = "✅" if check_result else "❌"
                print(f"   {status} {check_name}")
            
            return True
        else:
            print("❌ update_vtk_text_overlays method not found")
            return False
        
    except Exception as e:
        print(f"❌ Text overlay method analysis error: {e}")
        return False

def main():
    """Run all simple runtime debug tests"""
    print("SIMPLE RUNTIME DEBUGGING")
    print("=" * 60)
    
    tests = [
        ("Variable Initialization", test_variable_initialization),
        ("Method Source Code", test_method_source_code),
        ("Text Overlay Method", test_update_vtk_text_overlays)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\\n🧪 Running {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\\n" + "=" * 60)
    print("SIMPLE RUNTIME DEBUG SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\\nOverall: {passed}/{len(results)} tests passed")
    
    if passed < len(results):
        print("\\n⚠️ ISSUES IDENTIFIED:")
        print("Check the specific test outputs above for details")
    else:
        print("\\n🎉 All runtime tests passed!")

if __name__ == "__main__":
    main()
