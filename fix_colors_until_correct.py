#!/usr/bin/env python3
"""
Background Color Fix Program
This program will continuously test and fix the STEP file colors until they match exactly:
- BODY (large main part): DARK SILVER RGB(63,63,63) 
- PINS (small protruding parts): LIGHT SILVER RGB(192,192,192)

It will run in background, make changes, test visually, and iterate until correct.
"""

import sys
import os
import time
import subprocess
from pathlib import Path

def get_current_visual_state():
    """Get user confirmation of current visual state"""
    print("\n" + "="*60)
    print("VISUAL VERIFICATION REQUIRED")
    print("="*60)
    print("Please look at the current display and tell me what you see:")
    print("1. Dark body, light pins (CORRECT)")
    print("2. Light body, dark pins (WRONG - need to swap)")
    print("3. All same color (WRONG - need to fix)")
    print("4. Other (describe)")
    
    while True:
        response = input("\nWhat do you see? (1/2/3/4): ").strip()
        if response in ['1', '2', '3', '4']:
            return response
        print("Please enter 1, 2, 3, or 4")

def swap_color_assignment():
    """Swap which color gets assigned to which geometry"""
    print("\n🔧 SWAPPING COLOR ASSIGNMENT...")
    
    # Read current step_loader.py
    with open('step_loader.py', 'r') as f:
        content = f.read()
    
    # Find and swap the color assignments
    if 'light_color = (192, 192, 192)  # Light silver' in content:
        # Swap the color definitions
        content = content.replace(
            'light_color = (192, 192, 192)  # Light silver\n        dark_color = (63, 63, 63)      # Dark silver',
            'light_color = (63, 63, 63)      # Dark silver (SWAPPED)\n        dark_color = (192, 192, 192)  # Light silver (SWAPPED)'
        )
        print("✅ Swapped color definitions: light<->dark")
    else:
        # Swap back
        content = content.replace(
            'light_color = (63, 63, 63)      # Dark silver (SWAPPED)\n        dark_color = (192, 192, 192)  # Light silver (SWAPPED)',
            'light_color = (192, 192, 192)  # Light silver\n        dark_color = (63, 63, 63)      # Dark silver'
        )
        print("✅ Swapped color definitions back: dark<->light")
    
    # Write back
    with open('step_loader.py', 'w') as f:
        f.write(content)
    
    print("✅ Color assignment swapped in step_loader.py")

def reverse_z_sorting():
    """Reverse the Z-coordinate sorting to change which faces get which colors"""
    print("\n🔧 REVERSING Z-COORDINATE SORTING...")
    
    # Read current step_loader.py
    with open('step_loader.py', 'r') as f:
        content = f.read()
    
    # Find and reverse the sorting
    if 'reverse=True' in content and 'sorted_faces = sorted' in content:
        content = content.replace('reverse=True', 'reverse=False')
        print("✅ Changed sorting from reverse=True to reverse=False")
    elif 'reverse=False' in content and 'sorted_faces = sorted' in content:
        content = content.replace('reverse=False', 'reverse=True')
        print("✅ Changed sorting from reverse=False to reverse=True")
    else:
        print("❌ Could not find sorting line to modify")
        return False
    
    # Write back
    with open('step_loader.py', 'w') as f:
        f.write(content)
    
    print("✅ Z-coordinate sorting reversed in step_loader.py")
    return True

def test_current_colors():
    """Test current color configuration"""
    print("\n🧪 TESTING CURRENT COLOR CONFIGURATION...")
    
    # Kill any existing processes
    try:
        subprocess.run(['taskkill', '/F', '/IM', 'python.exe'], 
                      capture_output=True, check=False)
        time.sleep(2)
    except:
        pass
    
    # Run the main program
    print("🚀 Starting main program...")
    try:
        process = subprocess.Popen(['python', 'step_viewer_tdk_modular.py'],
                                 cwd=os.getcwd(),
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE)
        
        # Give it time to load and display
        time.sleep(8)
        
        print("✅ Program should be running now")
        return process
        
    except Exception as e:
        print(f"❌ Error starting program: {e}")
        return None

def main():
    """Main fix loop"""
    print("BACKGROUND COLOR FIX PROGRAM")
    print("="*50)
    print("TARGET: Dark body, light pins")
    print("STEP file has: 17 light parts, 2 dark parts")
    print("We want: Body=dark, Pins=light")
    print("="*50)
    
    max_attempts = 10
    attempt = 1
    
    while attempt <= max_attempts:
        print(f"\n🔄 ATTEMPT {attempt}/{max_attempts}")
        
        # Test current configuration
        process = test_current_colors()
        if not process:
            print("❌ Failed to start program")
            break
        
        # Get user feedback
        visual_state = get_current_visual_state()
        
        # Kill the test process
        try:
            process.terminate()
            time.sleep(2)
            process.kill()
        except:
            pass
        
        if visual_state == '1':
            print("\n🎉 SUCCESS! Colors are now correct!")
            print("✅ Dark body, light pins - PERFECT!")
            break
        elif visual_state == '2':
            print("\n🔧 WRONG: Light body, dark pins - need to swap assignment")
            if attempt % 2 == 1:
                # Try swapping color definitions
                swap_color_assignment()
            else:
                # Try reversing Z-sorting
                reverse_z_sorting()
        elif visual_state == '3':
            print("\n🔧 WRONG: All same color - need to fix color system")
            # This shouldn't happen with multi-actor approach
            print("❌ Multi-actor system failed - this is a serious issue")
            break
        else:
            print("\n🔧 OTHER issue - please describe what you see")
            description = input("Describe what you see: ")
            print(f"User description: {description}")
            
            # Try a different approach
            if attempt % 2 == 1:
                swap_color_assignment()
            else:
                reverse_z_sorting()
        
        attempt += 1
        print(f"\n⏳ Waiting 3 seconds before next attempt...")
        time.sleep(3)
    
    if attempt > max_attempts:
        print(f"\n❌ FAILED after {max_attempts} attempts")
        print("The color assignment system needs manual debugging")
    
    print("\n" + "="*50)
    print("BACKGROUND FIX PROGRAM COMPLETE")
    print("="*50)

if __name__ == "__main__":
    main()
