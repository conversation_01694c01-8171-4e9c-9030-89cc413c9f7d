#!/usr/bin/env python3

print("DEBUG SHAPE ORDER IN STEP FILE VS DISPLAY")

# 1. Check STEP file shape order
print("=== STEP FILE SHAPE ORDER ===")
try:
    from OCC.Core.STEPControl import ST<PERSON><PERSON><PERSON>rol_Reader
    from OCC.Core.TopExp import TopExp_Explorer
    from OCC.Core.TopAbs import TopAbs_FACE
    
    reader = STEPControl_Reader()
    reader.ReadFile('SOIC16P127_1270X940X610L89X51.STEP')
    reader.TransferRoots()
    shape = reader.OneShape()
    
    explorer = TopExp_Explorer(shape, TopAbs_FACE)
    step_face_order = []
    face_index = 0
    
    while explorer.More():
        face = explorer.Current()
        # Get some identifier for this face (hash or memory address)
        face_id = str(face)[-8:]  # Last 8 chars of string representation
        step_face_order.append(face_id)
        
        if face_index < 10:  # Show first 10
            print(f"STEP Face {face_index}: {face_id}")
        
        face_index += 1
        explorer.Next()
    
    print(f"STEP file: {len(step_face_order)} faces")
    
except Exception as e:
    print(f"Error reading STEP shapes: {e}")
    step_face_order = []

# 2. Check display shape order (from step_loader)
print("\n=== DISPLAY SHAPE ORDER ===")
try:
    from step_loader import STEPLoader
    
    # Modify step_loader temporarily to track face order
    loader = STEPLoader()
    
    # Read the same STEP file
    reader = STEPControl_Reader()
    reader.ReadFile('SOIC16P127_1270X940X610L89X51.STEP')
    reader.TransferRoots()
    shape = reader.OneShape()
    
    explorer = TopExp_Explorer(shape, TopAbs_FACE)
    display_face_order = []
    face_index = 0
    
    while explorer.More():
        face = explorer.Current()
        face_id = str(face)[-8:]  # Same identifier method
        display_face_order.append(face_id)
        
        if face_index < 10:  # Show first 10
            print(f"Display Face {face_index}: {face_id}")
        
        face_index += 1
        explorer.Next()
    
    print(f"Display: {len(display_face_order)} faces")
    
except Exception as e:
    print(f"Error reading display shapes: {e}")
    display_face_order = []

# 3. Compare orders
print("\n=== ORDER COMPARISON ===")
if step_face_order and display_face_order:
    if step_face_order == display_face_order:
        print("SUCCESS: Shape orders are identical")
    else:
        print("FAILURE: Shape orders are different")
        
        # Show first few differences
        for i in range(min(10, len(step_face_order), len(display_face_order))):
            if step_face_order[i] != display_face_order[i]:
                print(f"Difference at index {i}: STEP {step_face_order[i]} vs Display {display_face_order[i]}")
else:
    print("Cannot compare - missing data")

print("\nSHAPE ORDER DEBUG COMPLETE")
