#!/usr/bin/env python3

print("ANALYZING THE 18 EXTRA STYLED_ITEM ENTRIES")

# Read STEP file as text
with open('SOIC16P127_1270X940X610L89X51.STEP', 'r', encoding='utf-8', errors='ignore') as f:
    step_text = f.read()

lines = step_text.split('\n')

# Get all STYLED_ITEM entries
styled_items = []
for line in lines:
    if 'STYLED_ITEM' in line:
        import re
        match = re.search(r'STYLED_ITEM.*?#(\d+).*?#(\d+)', line)
        if match:
            color_ref = match.group(1)
            geometry_id = int(match.group(2))
            styled_items.append([geometry_id, color_ref, line])

print(f"Found {len(styled_items)} STYLED_ITEM entries")

# Sort by geometry ID
styled_items.sort(key=lambda x: x[0])

# Show the last 18 entries (the ones that don't match OpenCASCADE faces)
print("\nLAST 18 STYLED_ITEM ENTRIES (the extra ones):")
for i, entry in enumerate(styled_items[-18:]):
    geometry_id, color_ref, full_line = entry
    print(f"{i+1}: STEP ID #{geometry_id}, Color ref #{color_ref}")
    print(f"   Full line: {full_line}")
    
    # Check what this geometry ID refers to
    for line in lines:
        if f'#{geometry_id} =' in line and 'STYLED_ITEM' not in line:
            print(f"   Geometry definition: {line}")
            break
    print()

# Check if these are different types of geometry
print("\nCHECKING GEOMETRY TYPES FOR EXTRA ENTRIES:")
geometry_types = {}
for entry in styled_items[-18:]:
    geometry_id = entry[0]
    for line in lines:
        if f'#{geometry_id} =' in line and 'STYLED_ITEM' not in line:
            # Extract the type (word after =)
            match = re.search(r'#\d+\s*=\s*(\w+)', line)
            if match:
                geo_type = match.group(1)
                if geo_type in geometry_types:
                    geometry_types[geo_type] += 1
                else:
                    geometry_types[geo_type] = 1
            break

print("Geometry types in extra entries:")
for geo_type, count in geometry_types.items():
    print(f"  {geo_type}: {count}")

print("\nANALYZING EXTRA STYLED_ITEM ENTRIES COMPLETE")
