#!/usr/bin/env python3

print("TEST VTK COLOR DISPLAY")

from step_loader import STEPLoader
from vtk_renderer import VTKRenderer

# Load file with step_loader
loader = STEPLoader()
success, msg = loader.load_step_file('SOIC16P127_1270X940X610L89X51.STEP')

if success and loader.current_polydata:
    polydata = loader.current_polydata
    
    # Check colors in polydata
    colors_array = polydata.GetCellData().GetScalars("Colors")
    if colors_array:
        dark_count = 0
        light_count = 0
        
        for i in range(colors_array.GetNumberOfTuples()):
            r = int(colors_array.GetComponent(i, 0))
            g = int(colors_array.GetComponent(i, 1))
            b = int(colors_array.GetComponent(i, 2))
            color = (r, g, b)
            
            if color == (63, 63, 63):
                dark_count += 1
            elif color == (192, 192, 192):
                light_count += 1
        
        print(f"Polydata colors: {dark_count} dark, {light_count} light")
        
        # Test VTK renderer
        print("Testing VTK renderer...")
        
        # Create a simple Qt application for VTK
        from PyQt5.QtWidgets import QApplication
        import sys
        
        app = QApplication(sys.argv)
        
        # Create VTK renderer
        renderer = VTKRenderer(None)
        
        # Try to display the polydata
        print("Calling display_polydata...")
        result = renderer.display_polydata(polydata)
        print(f"Display result: {result}")
        
        app.quit()
    else:
        print("No colors in polydata")
else:
    print("Failed to load")

print("VTK COLOR TEST COMPLETE")
