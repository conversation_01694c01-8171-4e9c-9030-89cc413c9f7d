import vtk
import numpy as np
import math
from PyQt5.QtWidgets import QWidget

try:
    from vtk.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor
except ImportError:
    try:
        from vtkmodules.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor
    except ImportError:
        QVTKRenderWindowInteractor = None

class VTKRenderer:
    def __init__(self, parent=None):
        self.parent = parent
        self.renderer = vtk.vtkRenderer()
        self.render_window = vtk.vtkRenderWindow()
        self.vtk_widget = None
        self.interactor = None
        self.step_actor = None
        self.initialize()
    
    def initialize(self):
        try:
            self.renderer.SetBackground(0.1, 0.1, 0.2)
            self.render_window.AddRenderer(self.renderer)
            
            if QVTKRenderWindowInteractor:
                self.vtk_widget = QVTKRenderWindowInteractor()
                self.vtk_widget.SetRenderWindow(self.render_window)
                self.interactor = self.vtk_widget.GetRenderWindow().GetInteractor()
                style = vtk.vtkInteractorStyleTrackballCamera()
                self.interactor.SetInteractorStyle(style)
                self.interactor.Initialize()
            else:
                self.vtk_widget = QWidget()
            return True
        except Exception as e:
            print(f"VTK init error: {e}")
            self.vtk_widget = QWidget()
            return False
    
    def display_polydata(self, polydata):
        if not polydata:
            return False
        try:
            if self.step_actor:
                self.renderer.RemoveActor(self.step_actor)
            
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputData(polydata)
            
            self.step_actor = vtk.vtkActor()
            self.step_actor.SetMapper(mapper)
            self.step_actor.GetProperty().SetColor(0.8, 0.8, 0.9)
            
            self.renderer.AddActor(self.step_actor)
            self.render_window.Render()
            return True
        except Exception as e:
            print(f"Display error: {e}")
            return False
    
    def fit_view(self):
        try:
            self.renderer.ResetCamera()
            self.render_window.Render()
        except:
            pass
    
    def get_camera_orientation(self):
        return {'x': 0.0, 'y': 0.0, 'z': 0.0}
    
    def clear_view(self):
        try:
            self.renderer.RemoveAllViewProps()
            self.render_window.Render()
        except:
            pass