#!/usr/bin/env python3
"""
Trace the contamination issue - find where BOTTOM viewer gets TOP viewer actors
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from step_viewer_tdk_modular import StepViewerTDK

def trace_step_actors(viewer, label):
    """Trace step_actors in both viewers"""
    print(f"\n🔍 {label} - STEP ACTORS TRACE:")
    
    # TOP viewer
    if hasattr(viewer, 'vtk_renderer_left') and hasattr(viewer.vtk_renderer_left, 'step_actors'):
        print(f"   TOP step_actors list ID: {id(viewer.vtk_renderer_left.step_actors)}")
        print(f"   TOP step_actors count: {len(viewer.vtk_renderer_left.step_actors)}")
        for i, actor in enumerate(viewer.vtk_renderer_left.step_actors):
            bounds = actor.GetBounds()
            color = actor.GetProperty().GetColor()
            print(f"      TOP actor {i}: bounds={bounds}, color={color}")
    else:
        print("   TOP step_actors: NOT FOUND")
    
    # BOTTOM viewer  
    if hasattr(viewer, 'vtk_renderer_right') and hasattr(viewer.vtk_renderer_right, 'step_actors'):
        print(f"   BOTTOM step_actors list ID: {id(viewer.vtk_renderer_right.step_actors)}")
        print(f"   BOTTOM step_actors count: {len(viewer.vtk_renderer_right.step_actors)}")
        for i, actor in enumerate(viewer.vtk_renderer_right.step_actors):
            bounds = actor.GetBounds()
            color = actor.GetProperty().GetColor()
            print(f"      BOTTOM actor {i}: bounds={bounds}, color={color}")
    else:
        print("   BOTTOM step_actors: NOT FOUND")
    
    # Check if they're the same list (contamination)
    if (hasattr(viewer, 'vtk_renderer_left') and hasattr(viewer.vtk_renderer_left, 'step_actors') and
        hasattr(viewer, 'vtk_renderer_right') and hasattr(viewer.vtk_renderer_right, 'step_actors')):
        
        same_list = id(viewer.vtk_renderer_left.step_actors) == id(viewer.vtk_renderer_right.step_actors)
        if same_list:
            print("   ⚠️  WARNING: TOP and BOTTOM are sharing the SAME step_actors list!")
        else:
            print("   ✅ TOP and BOTTOM have separate step_actors lists")

def main():
    """Trace contamination step by step"""
    print("🔬 TRACING STEP_ACTORS CONTAMINATION")
    print("=" * 60)
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    
    # Initial state
    trace_step_actors(viewer, "INITIAL STATE")
    
    # Load 16-pin into TOP viewer
    print("\n" + "🔴" * 30 + " LOADING 16-PIN INTO TOP " + "🔴" * 30)
    viewer.active_viewer = "top"
    viewer.update_viewer_highlights()
    
    if os.path.exists("SOIC16P127_1270X940X610L89X51.STEP"):
        success1 = viewer.load_step_file_direct("SOIC16P127_1270X940X610L89X51.STEP")
        print(f"   16-pin load result: {success1}")
        trace_step_actors(viewer, "AFTER LOADING 16-PIN")
    else:
        print("   ❌ 16-pin STEP file not found")
        return 1
    
    # Load 8-pin into BOTTOM viewer
    print("\n" + "🔵" * 30 + " LOADING 8-PIN INTO BOTTOM " + "🔵" * 30)
    viewer.active_viewer = "bottom"
    viewer.update_viewer_highlights()
    
    if os.path.exists("test.step"):
        success2 = viewer.load_step_file_direct("test.step")
        print(f"   8-pin load result: {success2}")
        trace_step_actors(viewer, "AFTER LOADING 8-PIN")
    else:
        print("   ❌ 8-pin STEP file not found")
        return 1
    
    # Final analysis
    print("\n" + "=" * 60)
    print("🎯 CONTAMINATION ANALYSIS:")
    
    if (hasattr(viewer, 'vtk_renderer_left') and hasattr(viewer.vtk_renderer_left, 'step_actors') and
        hasattr(viewer, 'vtk_renderer_right') and hasattr(viewer.vtk_renderer_right, 'step_actors')):
        
        top_count = len(viewer.vtk_renderer_left.step_actors)
        bottom_count = len(viewer.vtk_renderer_right.step_actors)
        
        print(f"   TOP viewer has {top_count} actors")
        print(f"   BOTTOM viewer has {bottom_count} actors")
        
        # Expected: TOP=2 (16-pin has 2 parts), BOTTOM=1 (8-pin has 1 part)
        if top_count == 2 and bottom_count == 1:
            print("   ✅ CORRECT: No contamination detected")
        elif bottom_count > 1:
            print("   ❌ CONTAMINATION: BOTTOM viewer has too many actors!")
            print("   🔍 BOTTOM viewer should only have 1 actor (8-pin)")
            print("   🔍 Extra actors are likely contamination from TOP viewer")
        elif top_count != 2:
            print("   ❌ UNEXPECTED: TOP viewer doesn't have expected 2 actors")
        
        # Check for shared list
        same_list = id(viewer.vtk_renderer_left.step_actors) == id(viewer.vtk_renderer_right.step_actors)
        if same_list:
            print("   ❌ ROOT CAUSE: Both viewers share the same step_actors list!")
        else:
            print("   ✅ Viewers have separate step_actors lists")
    
    print("\n🏁 CONTAMINATION TRACE COMPLETE")
    return 0

if __name__ == "__main__":
    sys.exit(main())
