#!/usr/bin/env python3
"""
Visual rotation test - displays original rotated model vs saved rotated model
for direct visual comparison
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from step_loader import <PERSON>EPLoader
from vtk_renderer import VTKRenderer
import vtk
from PyQt5.QtWidgets import QApp<PERSON>, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, Q<PERSON>abel, QPushButton
from PyQt5.QtCore import Qt
import time

class VisualRotationTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🔍 VISUAL ROTATION TEST - Compare Original vs Saved")
        self.setGeometry(100, 100, 1400, 800)
        
        # Create main widget and layout
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QVBoxLayout(main_widget)
        
        # Title
        title = QLabel("🔍 VISUAL ROTATION TEST")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # Instructions
        instructions = QLabel(
            "LEFT: Original test.step with X=90° rotation applied\n"
            "RIGHT: Saved rotated file loaded from disk\n"
            "✅ SUCCESS: Both models should look IDENTICAL\n"
            "❌ FAILURE: Models look different (rotation bug still exists)"
        )
        instructions.setAlignment(Qt.AlignCenter)
        instructions.setStyleSheet("font-size: 12px; margin: 10px; background-color: #f0f0f0; padding: 10px;")
        layout.addWidget(instructions)
        
        # Create side-by-side viewers
        viewer_layout = QHBoxLayout()
        
        # Left viewer (original + rotation)
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_label = QLabel("LEFT: Original + X=90° Rotation")
        left_label.setAlignment(Qt.AlignCenter)
        left_label.setStyleSheet("font-weight: bold; background-color: #e6f3ff; padding: 5px;")
        left_layout.addWidget(left_label)
        
        self.left_renderer = VTKRenderer()
        left_layout.addWidget(self.left_renderer.vtk_widget)
        viewer_layout.addWidget(left_widget)
        
        # Right viewer (saved file)
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_label = QLabel("RIGHT: Saved Rotated File")
        right_label.setAlignment(Qt.AlignCenter)
        right_label.setStyleSheet("font-weight: bold; background-color: #ffe6e6; padding: 5px;")
        right_layout.addWidget(right_label)
        
        self.right_renderer = VTKRenderer()
        right_layout.addWidget(self.right_renderer.vtk_widget)
        viewer_layout.addWidget(right_widget)
        
        layout.addLayout(viewer_layout)
        
        # Status and button
        self.status_label = QLabel("🔄 Loading models...")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("font-size: 14px; margin: 10px;")
        layout.addWidget(self.status_label)
        
        close_button = QPushButton("Close Test")
        close_button.clicked.connect(self.close)
        close_button.setStyleSheet("font-size: 14px; padding: 10px; margin: 10px;")
        layout.addWidget(close_button)
        
        # Initialize loaders
        self.left_loader = STEPLoader()
        self.right_loader = STEPLoader()
        
        # Start the test
        self.run_test()
    
    def run_test(self):
        """Run the visual rotation test"""
        try:
            print("🔍 VISUAL ROTATION TEST STARTING...")
            
            # Step 1: Load original file in left viewer
            print("📂 STEP 1: Loading original test.step...")
            self.status_label.setText("📂 Loading original test.step...")
            QApplication.processEvents()
            
            if not self.left_loader.load_step_file("test.step"):
                self.status_label.setText("❌ Failed to load test.step")
                return
            
            self.left_renderer.display_polydata(self.left_loader.current_polydata)
            print("✅ Original file loaded in LEFT viewer")

            # Debug: Check what actors are available
            print(f"🔍 DEBUG: step_actor = {getattr(self.left_renderer, 'step_actor', 'None')}")
            print(f"🔍 DEBUG: multi_actors = {getattr(self.left_renderer, 'multi_actors', 'None')}")
            if hasattr(self.left_renderer, 'multi_actors') and self.left_renderer.multi_actors:
                print(f"🔍 DEBUG: multi_actors count = {len(self.left_renderer.multi_actors)}")
            
            # Step 2: Apply X=90° rotation to left viewer
            print("🔄 STEP 2: Applying X=90° rotation to LEFT viewer...")
            self.status_label.setText("🔄 Applying X=90° rotation...")
            QApplication.processEvents()
            
            # Create rotation transform
            transform = vtk.vtkTransform()
            transform.RotateX(90.0)
            
            # Apply to left renderer
            if hasattr(self.left_renderer, 'step_actor') and self.left_renderer.step_actor:
                self.left_renderer.step_actor.SetUserTransform(transform)
                self.left_renderer.render_window.Render()
                print("✅ X=90° rotation applied to LEFT viewer")
            else:
                print("❌ No step_actor found in left renderer")
                # Try to find the actor in multi-actors
                if hasattr(self.left_renderer, 'multi_actors') and self.left_renderer.multi_actors:
                    for actor in self.left_renderer.multi_actors:
                        actor.SetUserTransform(transform)
                    self.left_renderer.render_window.Render()
                    print("✅ X=90° rotation applied to LEFT viewer multi-actors")
                else:
                    print("❌ No actors found to rotate in left renderer")
            
            # Step 3: Save the rotated model
            print("💾 STEP 3: Saving rotated model...")
            self.status_label.setText("💾 Saving rotated model...")
            QApplication.processEvents()
            
            # Use the same save logic as the main viewer
            success = self.save_rotated_model()
            if not success:
                self.status_label.setText("❌ Failed to save rotated model")
                return
            
            print("✅ Rotated model saved as visual_test_rotated.step")
            
            # Step 4: Load saved file in right viewer
            print("📂 STEP 4: Loading saved file in RIGHT viewer...")
            self.status_label.setText("📂 Loading saved rotated file...")
            QApplication.processEvents()
            
            if not self.right_loader.load_step_file("visual_test_rotated.step"):
                self.status_label.setText("❌ Failed to load saved file")
                return
            
            self.right_renderer.display_polydata(self.right_loader.current_polydata)
            print("✅ Saved file loaded in RIGHT viewer")
            
            # Step 5: Final comparison
            print("👀 STEP 5: Visual comparison ready!")
            self.status_label.setText("👀 VISUAL COMPARISON READY! Do the models look identical?")
            
            print("\n" + "="*60)
            print("🎯 VISUAL ROTATION TEST COMPLETE")
            print("="*60)
            print("LEFT viewer:  Original test.step with X=90° rotation applied")
            print("RIGHT viewer: Saved rotated file loaded from disk")
            print("")
            print("✅ SUCCESS: Both models should look IDENTICAL")
            print("❌ FAILURE: Models look different (rotation bug still exists)")
            print("")
            print("Please visually compare the two models and report:")
            print("1. Do they have the same orientation?")
            print("2. Do the pins point in the same direction?")
            print("3. Are there any visible differences?")
            print("="*60)
            
        except Exception as e:
            print(f"❌ Test failed with error: {e}")
            self.status_label.setText(f"❌ Test failed: {e}")
    
    def save_rotated_model(self):
        """Save the rotated model using the same logic as main viewer"""
        try:
            from simple_step_modifier import SimpleSTEPModifier
            
            # Create modifier and load original file
            modifier = SimpleSTEPModifier()
            if not modifier.load_step_file("test.step"):
                print("❌ Failed to load original file for saving")
                return False
            
            # Apply X=90° rotation to coordinate system
            if not modifier.modify_placement(0, 0, 0, 90, 0, 0):
                print("❌ Failed to modify coordinate system")
                return False
            
            # Save with rotation comment
            rotation_values = {'x': 90.0, 'y': 0.0, 'z': 0.0}
            if not modifier.save_step_file("visual_test_rotated.step", rotation_values):
                print("❌ Failed to save rotated file")
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ Save failed: {e}")
            return False

def main():
    app = QApplication(sys.argv)
    
    # Check if test.step exists
    if not os.path.exists("test.step"):
        print("❌ test.step not found! Please make sure test.step is in the current directory.")
        return
    
    test_window = VisualRotationTest()
    test_window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
