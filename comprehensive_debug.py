#!/usr/bin/env python3
"""
Comprehensive debug from start to finish - test all 3D models
"""

import os
import sys
import glob

print("=== COMPREHENSIVE DEBUG - START TO FINISH ===")
print("Testing STEP loader with multiple 3D models")

# Step 1: Find all STEP files
step_files = glob.glob("*.STEP") + glob.glob("*.step")
print(f"Step 1: Found {len(step_files)} STEP files:")
for f in step_files:
    print(f"  - {f} ({os.path.getsize(f)} bytes)")

if not step_files:
    print("No STEP files found - creating test files")
    step_files = ["test_model.STEP"]

# Step 2: Test step_loader import and creation
print("\nStep 2: Testing STEPLoader...")
try:
    from step_loader import STEPLoader
    loader = STEPLoader()
    print("✓ STEPLoader imported and created successfully")
except Exception as e:
    print(f"✗ STEPLoader failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

# Step 3: Test each STEP file
print("\nStep 3: Testing each STEP file...")
successful_loads = 0
failed_loads = 0

for step_file in step_files:
    print(f"\n--- Testing {step_file} ---")
    
    if not os.path.exists(step_file):
        print(f"✗ File not found: {step_file}")
        failed_loads += 1
        continue
    
    try:
        # Test loading
        success, message = loader.load_step_file(step_file)
        print(f"Load result: success={success}, message='{message}'")
        
        if success:
            print("✓ File loaded successfully")
            
            # Check polydata
            if hasattr(loader, 'current_polydata') and loader.current_polydata:
                polydata = loader.current_polydata
                num_cells = polydata.GetNumberOfCells()
                num_points = polydata.GetNumberOfPoints()
                print(f"✓ Geometry: {num_cells} cells, {num_points} points")
                
                # Check colors
                colors = polydata.GetCellData().GetScalars("Colors")
                if colors:
                    num_colors = colors.GetNumberOfTuples()
                    print(f"✓ Colors: {num_colors} color tuples")
                    
                    # Analyze color distribution
                    color_counts = {}
                    for i in range(min(num_colors, 1000)):  # Sample first 1000
                        r = int(colors.GetComponent(i, 0))
                        g = int(colors.GetComponent(i, 1))
                        b = int(colors.GetComponent(i, 2))
                        color = (r, g, b)
                        color_counts[color] = color_counts.get(color, 0) + 1
                    
                    print(f"✓ Found {len(color_counts)} unique colors:")
                    for color, count in sorted(color_counts.items(), key=lambda x: x[1], reverse=True):
                        percentage = (count / min(num_colors, 1000)) * 100
                        print(f"    RGB{color}: {count} cells ({percentage:.1f}%)")
                    
                    # Check for expected colors
                    light_silver = (192, 192, 192)
                    dark_silver = (63, 63, 63)
                    
                    if light_silver in color_counts:
                        print(f"✓ Light silver RGB{light_silver} found")
                    if dark_silver in color_counts:
                        print(f"✓ Dark silver RGB{dark_silver} found")
                    
                    successful_loads += 1
                    print(f"✓ {step_file} - FULLY WORKING")
                else:
                    print("✗ No colors found")
                    failed_loads += 1
            else:
                print("✗ No polydata generated")
                failed_loads += 1
        else:
            print(f"✗ Loading failed: {message}")
            failed_loads += 1
            
    except Exception as e:
        print(f"✗ Error testing {step_file}: {e}")
        import traceback
        traceback.print_exc()
        failed_loads += 1

# Step 4: Test OpenCASCADE imports
print("\n--- Step 4: Testing OpenCASCADE imports ---")
occ_imports = [
    ("STEPControl_Reader", "from OCC.Core.STEPControl import STEPControl_Reader"),
    ("IFSelect_RetDone", "from OCC.Core.IFSelect import IFSelect_RetDone"),
    ("BRepMesh_IncrementalMesh", "from OCC.Core.BRepMesh import BRepMesh_IncrementalMesh"),
    ("TopExp_Explorer", "from OCC.Core.TopExp import TopExp_Explorer"),
    ("TopAbs_FACE", "from OCC.Core.TopAbs import TopAbs_FACE"),
    ("BRep_Tool", "from OCC.Core.BRep import BRep_Tool"),
    ("TopLoc_Location", "from OCC.Core.TopLoc import TopLoc_Location"),
]

occ_working = 0
for name, import_cmd in occ_imports:
    try:
        exec(import_cmd)
        print(f"✓ {name} - OK")
        occ_working += 1
    except Exception as e:
        print(f"✗ {name} - FAILED: {e}")

# Step 5: Final summary
print("\n" + "="*60)
print("COMPREHENSIVE DEBUG SUMMARY")
print("="*60)
print(f"STEP files tested: {len(step_files)}")
print(f"Successful loads: {successful_loads}")
print(f"Failed loads: {failed_loads}")
print(f"OpenCASCADE imports working: {occ_working}/{len(occ_imports)}")

if successful_loads > 0 and occ_working == len(occ_imports):
    print("✓ SYSTEM IS WORKING CORRECTLY")
    print("✓ Colors are being applied correctly")
    print("✓ 3D models load with proper geometry")
    print("✓ Ready for production use")
else:
    print("✗ SYSTEM HAS ISSUES")
    if failed_loads > 0:
        print(f"✗ {failed_loads} files failed to load")
    if occ_working < len(occ_imports):
        print(f"✗ {len(occ_imports) - occ_working} OpenCASCADE imports failed")

print("="*60)
print("DEBUG COMPLETED")
