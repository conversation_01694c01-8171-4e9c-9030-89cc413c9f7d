#!/usr/bin/env python3
"""
TEST CURRENT VERSION VS WORKING VERSION
Compare what we have now vs a known working version
"""

import os
import sys

def test_current_version():
    """Test the current version to see what issues we have"""
    print("🔍 TESTING CURRENT VERSION")
    print("=" * 40)
    
    # Check what files we have
    current_files = [
        "step_viewer_tdk_modular.py",
        "vtk_renderer.py", 
        "step_loader.py"
    ]
    
    for file in current_files:
        if os.path.exists(file):
            print(f"✅ {file} exists")
        else:
            print(f"❌ {file} missing")
    
    print("\n📋 AVAILABLE NUMBERED VERSIONS:")
    
    # List numbered versions
    for file in os.listdir('.'):
        if 'rev' in file and file.endswith('.py'):
            print(f"  📁 {file}")
    
    print("\n🎯 WHAT WE NEED TO TEST:")
    print("1. Load a working version (like step_viewer_tdk_modular_working.py)")
    print("2. Test rotation buttons - do they increment correctly?")
    print("3. Test STEP file colors - do they display correctly?")
    print("4. Compare with current version behavior")
    
    print("\n💡 RECOMMENDATION:")
    print("1. Copy step_viewer_tdk_modular_working.py to step_viewer_tdk_modular_test.py")
    print("2. Run the test version to see if rotation/colors work")
    print("3. If they work, apply our fixes to that working version")
    print("4. If they don't work, try step_viewer_tdk_modular_rev1.py")

def main():
    print("🧪 VERSION COMPARISON TEST")
    print("=" * 50)
    
    test_current_version()
    
    print("\n🔧 NEXT STEPS:")
    print("1. Test a known working version")
    print("2. Identify what rotation/color behavior should be")
    print("3. Apply our fixes to the working version")
    print("4. Verify both fixes work correctly")

if __name__ == "__main__":
    main()
