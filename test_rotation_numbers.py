#!/usr/bin/env python3
"""
Test Rotation Numbers - Check if the rotation save actually preserves the angles
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

def main():
    app = QApplication(sys.argv)
    
    # Import viewer
    from step_viewer_tdk_modular_fixed import StepViewerTDK
    viewer = StepViewerTDK()
    viewer.show()
    viewer.setWindowTitle("Testing Rotation Numbers")
    
    # Find test file
    test_file = 'test.step'
    if not os.path.exists(test_file):
        test_file = 'SOIC16P127_1270X940X610L89X51.STEP'
    
    if not os.path.exists(test_file):
        print("No test file found")
        return
    
    def step1_load():
        print("Step 1: Loading file...")
        viewer.active_viewer = 'top'
        success = viewer.load_step_file_direct(test_file)
        if success:
            print(f"✓ File loaded")
            QTimer.singleShot(1000, step2_rotate)
        else:
            print("✗ Load failed")
    
    def step2_rotate():
        print("Step 2: Applying rotations...")
        viewer.rotate_shape('x', 15.0)
        viewer.rotate_shape('y', 30.0)
        viewer.rotate_shape('z', 45.0)
        
        # Check the rotation values in TOP viewer
        top_rot = viewer.current_rot_left
        print(f"TOP viewer rotations after applying: {top_rot}")
        
        QTimer.singleShot(1000, step3_save)
    
    def step3_save():
        print("Step 3: Saving with rotations...")
        output_file = "test_numbers_output.step"
        
        delta_pos = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        delta_rot = {'x': 15.0, 'y': 30.0, 'z': 45.0}
        
        success = viewer._save_step_opencascade_transform(
            output_file,
            viewer.step_loader_left,
            delta_pos,
            delta_rot
        )
        
        if success and os.path.exists(output_file):
            print(f"✓ File saved: {output_file}")
            QTimer.singleShot(1000, lambda: step4_load_saved(output_file))
        else:
            print("✗ Save failed")
    
    def step4_load_saved(saved_file):
        print("Step 4: Loading saved file in BOTTOM viewer...")
        viewer.active_viewer = 'bottom'
        success = viewer.load_step_file_direct(saved_file)
        
        if success:
            print("✓ Saved file loaded")
            QTimer.singleShot(1000, step5_check_numbers)
        else:
            print("✗ Load saved file failed")
    
    def step5_check_numbers():
        print("Step 5: Checking rotation numbers...")
        
        # Get the rotation values from both viewers
        top_rot = viewer.current_rot_left
        bottom_rot = viewer.current_rot_right
        
        print("=" * 50)
        print("ROTATION NUMBER TEST RESULTS:")
        print("=" * 50)
        print(f"TOP viewer rotations:    {top_rot}")
        print(f"BOTTOM viewer rotations: {bottom_rot}")
        print()
        
        # Calculate total angles
        top_total = (top_rot['x']**2 + top_rot['y']**2 + top_rot['z']**2)**0.5
        bottom_total = (bottom_rot['x']**2 + bottom_rot['y']**2 + bottom_rot['z']**2)**0.5
        
        print(f"TOP total angle:    {top_total:.1f}°")
        print(f"BOTTOM total angle: {bottom_total:.1f}°")
        print()
        
        # Check if rotations match (within tolerance)
        tolerance = 1.0  # 1 degree tolerance
        x_match = abs(top_rot['x'] - bottom_rot['x']) < tolerance
        y_match = abs(top_rot['y'] - bottom_rot['y']) < tolerance
        z_match = abs(top_rot['z'] - bottom_rot['z']) < tolerance
        
        if x_match and y_match and z_match:
            print("✅ ROTATION SAVE FIX IS WORKING!")
            print("✅ Rotations are preserved in saved file")
            print("✅ Both viewers show matching rotation angles")
        else:
            print("❌ ROTATION SAVE FIX IS NOT WORKING!")
            print("❌ Rotations are lost when saving file")
            print("❌ Saved file shows different angles than original")
            print()
            print("Expected rotations: X=15°, Y=30°, Z=45°")
            print(f"Actual saved rotations: X={bottom_rot['x']:.1f}°, Y={bottom_rot['y']:.1f}°, Z={bottom_rot['z']:.1f}°")
        
        print("=" * 50)
        
        # Exit after showing results
        QTimer.singleShot(3000, app.quit)
    
    # Start the test
    QTimer.singleShot(1000, step1_load)
    
    # Run the application
    app.exec_()

if __name__ == "__main__":
    main()
