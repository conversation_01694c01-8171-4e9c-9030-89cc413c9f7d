#!/usr/bin/env python3
"""
Correct workflow test that follows user's exact instructions:
1. Load test.step in TOP viewer
2. Rotate it
3. Save it with green button
4. <PERSON>ad saved file in BOTTOM viewer
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

def main():
    print("🔧 Starting correct workflow test...")
    
    # Import the viewer
    try:
        from step_viewer_tdk_modular_fixed import StepViewerTDK
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create viewer
    viewer = StepViewerTDK()
    
    # Show the viewer
    viewer.show()
    
    def run_workflow():
        print("\n📋 STEP 1: Load test.step in TOP viewer")
        print("-" * 50)
        
        # Load test.step in TOP viewer (original file)
        viewer.active_viewer = "top"
        success = viewer.step_loader_left.load_step_file("test.step")
        if not success:
            print("❌ Failed to load test.step")
            return
        print("✅ Loaded test.step in TOP viewer")
        
        # Wait a moment for loading to complete
        QTimer.singleShot(2000, apply_rotations)
    
    def apply_rotations():
        print("\n📋 STEP 2: Rotate it")
        print("-" * 50)
        
        # Apply rotations to the TOP viewer
        viewer.active_viewer = "top"
        viewer.rotate_shape('x', 25)
        print("✅ Applied X rotation: 25°")
        
        viewer.rotate_shape('y', 40) 
        print("✅ Applied Y rotation: 40°")
        
        viewer.rotate_shape('z', 60)
        print("✅ Applied Z rotation: 60°")
        
        print(f"✅ TOP viewer now shows: X={viewer.current_rot_left['x']}°, Y={viewer.current_rot_left['y']}°, Z={viewer.current_rot_left['z']}°")
        
        # Wait a moment then save
        QTimer.singleShot(1000, save_file)
    
    def save_file():
        print("\n📋 STEP 3: Save it with green button")
        print("-" * 50)
        
        # Save with green button
        save_filename = "e:\\python\\viewer\\save\\test_rotated_REV001.step"
        viewer.active_viewer = "top"
        success = viewer.save_step_file_option1_direct(save_filename)
        
        if success:
            print(f"✅ Saved with green button to: {save_filename}")
        else:
            print("❌ Save failed!")
            return
        
        # Wait a moment then load in bottom
        QTimer.singleShot(1000, load_bottom)
    
    def load_bottom():
        print("\n📋 STEP 4: Load saved file in BOTTOM viewer")
        print("-" * 50)
        
        # Load saved file in BOTTOM viewer using the correct method
        save_filename = "e:\\python\\viewer\\save\\test_rotated_REV001.step"
        viewer.active_viewer = "bottom"
        success = viewer.load_step_file_direct(save_filename)
        
        if success:
            print(f"✅ Loaded saved file in BOTTOM viewer")
            print(f"✅ BOTTOM viewer now shows: X={viewer.current_rot_right['x']}°, Y={viewer.current_rot_right['y']}°, Z={viewer.current_rot_right['z']}°")
        else:
            print("❌ Failed to load saved file in BOTTOM viewer")
            return
        
        print("\n🎉 WORKFLOW COMPLETE!")
        print("Both viewers should now be visible with models loaded.")
        print("You can now visually inspect if they match.")
    
    # Run the workflow after a short delay to let GUI initialize
    QTimer.singleShot(1000, run_workflow)
    
    # Start the application
    app.exec_()

if __name__ == "__main__":
    main()
