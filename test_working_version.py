#!/usr/bin/env python3
"""
TEST THE WORKING VERSION
Test step_viewer_tdk_modular_working.py to see baseline behavior
"""

import os
import shutil

def test_working_version():
    """Test the working version to establish baseline"""
    print("🧪 TESTING WORKING VERSION")
    print("=" * 40)
    
    working_file = "step_viewer_tdk_modular_working.py"
    
    if not os.path.exists(working_file):
        print(f"❌ {working_file} not found")
        return False
    
    # Backup current version
    if os.path.exists("step_viewer_tdk_modular.py"):
        shutil.copy("step_viewer_tdk_modular.py", "step_viewer_tdk_modular_current.py")
        print("✅ Backed up current version to step_viewer_tdk_modular_current.py")
    
    # Copy working version to main name
    shutil.copy(working_file, "step_viewer_tdk_modular.py")
    print(f"✅ Copied {working_file} to main program")
    
    print("\n🎯 NOW TEST MANUALLY:")
    print("1. Run the program")
    print("2. Load debug_auto_saved.step")
    print("3. Test X+15° button multiple times")
    print("4. Record what happens:")
    print("   - Does it increment: 0° → 15° → 30° → 45°?")
    print("   - Or does it reset: 0° → 15° → 15° → 15°?")
    print("5. Check colors:")
    print("   - Do STEP file colors display correctly?")
    print("   - Are they the right silver colors?")
    
    print("\n📝 RECORD RESULTS:")
    print("- Rotation behavior: _______________")
    print("- Color behavior: _________________")
    
    return True

def restore_current():
    """Restore the current version"""
    if os.path.exists("step_viewer_tdk_modular_current.py"):
        shutil.copy("step_viewer_tdk_modular_current.py", "step_viewer_tdk_modular.py")
        print("✅ Restored current version")
    else:
        print("❌ No current version backup found")

if __name__ == "__main__":
    print("🔧 WORKING VERSION TESTER")
    print("=" * 50)
    
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "restore":
        restore_current()
    else:
        test_working_version()
        print("\n💡 To restore current version later:")
        print("python test_working_version.py restore")
