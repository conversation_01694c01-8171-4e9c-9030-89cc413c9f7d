#!/usr/bin/env python3
"""
Test Displayed Values - Verify the actual numbers shown in the text display
"""

import sys
import time
import re
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QTextEdit
from PyQt5.QtCore import QTimer
from step_viewer_tdk_modular import StepViewerTDK

class DisplayedValuesTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Test Displayed Values")
        self.setGeometry(100, 100, 800, 600)
        
        # Create main widget
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QVBoxLayout(main_widget)
        
        # Status label
        self.status_label = QLabel("Testing displayed values...")
        layout.addWidget(self.status_label)
        
        # Results text area
        self.results_text = QTextEdit()
        self.results_text.setReadOnly(True)
        layout.addWidget(self.results_text)
        
        # Create the actual viewer
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        # Test step counter
        self.test_step = 0
        self.passed_tests = 0
        self.failed_tests = 0
        
        # Start tests after a delay
        self.timer = QTimer()
        self.timer.timeout.connect(self.run_next_test)
        self.timer.start(3000)  # Start after 3 seconds
        
    def log_result(self, message):
        """Log a test result"""
        print(message)
        self.results_text.append(message)
        
    def get_displayed_position_values(self, viewer_name):
        """Get the actual position values displayed in the text overlay"""
        if viewer_name == "TOP":
            # Calculate displayed position from internal values
            if hasattr(self.viewer, 'movement_delta_left') and hasattr(self.viewer, 'orig_pos_left'):
                pos_delta = self.viewer.movement_delta_left
                orig_pos = self.viewer.orig_pos_left
                # This is how the text display calculates it
                displayed_pos = {
                    'x': orig_pos['x'] + pos_delta['x'],
                    'y': orig_pos['y'] + pos_delta['y'],
                    'z': orig_pos['z'] + pos_delta['z']
                }
                return displayed_pos
        else:  # BOTTOM
            if hasattr(self.viewer, 'movement_delta_right') and hasattr(self.viewer, 'orig_pos_right'):
                pos_delta = self.viewer.movement_delta_right
                orig_pos = self.viewer.orig_pos_right
                # This is how the text display calculates it
                displayed_pos = {
                    'x': orig_pos['x'] + pos_delta['x'],
                    'y': orig_pos['y'] + pos_delta['y'],
                    'z': orig_pos['z'] + pos_delta['z']
                }
                return displayed_pos
        
        return {'x': 0, 'y': 0, 'z': 0}
        
    def run_next_test(self):
        """Run tests step by step"""
        
        if self.test_step == 0:
            self.log_result("🔧 TESTING DISPLAYED VALUES")
            self.log_result("=" * 50)
            
        elif self.test_step == 1:
            self.log_result("\n📋 LOADING STEP FILE")
            try:
                self.viewer.active_viewer = "top"
                self.viewer.load_step_file_direct("test.step")
                QApplication.processEvents()
                time.sleep(2)
                self.log_result("   ✅ STEP file loaded")
            except Exception as e:
                self.log_result(f"   ❌ STEP file loading error: {e}")
                
        elif self.test_step == 2:
            self.log_result("\n📋 TEST: TOP VIEWER X+ BUTTON - DISPLAYED VALUES")
            
            # Set TOP viewer active
            self.viewer.active_viewer = "top"
            
            # Get DISPLAYED position values BEFORE button press
            pos_before = self.get_displayed_position_values("TOP")
            self.log_result(f"   BEFORE X+ button:")
            self.log_result(f"     Displayed X: {pos_before['x']:.3f}")
            self.log_result(f"     Displayed Y: {pos_before['y']:.3f}")
            self.log_result(f"     Displayed Z: {pos_before['z']:.3f}")
            
            # Press X+ button (should INCREASE displayed X value by 1.0)
            self.log_result(f"   Pressing X+ button (should ADD +1.0 to displayed X value)...")
            self.viewer.move_shape('x', 1.0)
            QApplication.processEvents()
            time.sleep(0.5)
            
            # Get DISPLAYED position values AFTER button press
            pos_after = self.get_displayed_position_values("TOP")
            self.log_result(f"   AFTER X+ button:")
            self.log_result(f"     Displayed X: {pos_after['x']:.3f}")
            self.log_result(f"     Displayed Y: {pos_after['y']:.3f}")
            self.log_result(f"     Displayed Z: {pos_after['z']:.3f}")
            
            # Check the change in DISPLAYED X value
            x_change = pos_after['x'] - pos_before['x']
            self.log_result(f"   DISPLAYED X CHANGE: {x_change:.3f}")
            self.log_result(f"   EXPECTED CHANGE: +1.000")
            
            if abs(x_change - 1.0) < 0.01:
                self.log_result("   ✅ PASS: X+ button INCREASES displayed X value by 1.0")
                self.passed_tests += 1
            else:
                self.log_result("   ❌ FAIL: X+ button should INCREASE displayed X value by 1.0")
                self.failed_tests += 1
                
        elif self.test_step == 3:
            self.log_result("\n📋 TEST: TOP VIEWER X- BUTTON - DISPLAYED VALUES")
            
            # Get DISPLAYED position values BEFORE button press
            pos_before = self.get_displayed_position_values("TOP")
            self.log_result(f"   BEFORE X- button:")
            self.log_result(f"     Displayed X: {pos_before['x']:.3f}")
            
            # Press X- button (should DECREASE displayed X value by 1.0)
            self.log_result(f"   Pressing X- button (should SUBTRACT -1.0 from displayed X value)...")
            self.viewer.move_shape('x', -1.0)
            QApplication.processEvents()
            time.sleep(0.5)
            
            # Get DISPLAYED position values AFTER button press
            pos_after = self.get_displayed_position_values("TOP")
            self.log_result(f"   AFTER X- button:")
            self.log_result(f"     Displayed X: {pos_after['x']:.3f}")
            
            # Check the change in DISPLAYED X value
            x_change = pos_after['x'] - pos_before['x']
            self.log_result(f"   DISPLAYED X CHANGE: {x_change:.3f}")
            self.log_result(f"   EXPECTED CHANGE: -1.000")
            
            if abs(x_change - (-1.0)) < 0.01:
                self.log_result("   ✅ PASS: X- button DECREASES displayed X value by 1.0")
                self.passed_tests += 1
            else:
                self.log_result("   ❌ FAIL: X- button should DECREASE displayed X value by 1.0")
                self.failed_tests += 1
                
        elif self.test_step == 4:
            self.log_result("\n📋 TEST: BOTTOM VIEWER X+ BUTTON - DISPLAYED VALUES")
            
            # Set BOTTOM viewer active
            self.viewer.active_viewer = "bottom"
            
            # Load STEP file into BOTTOM viewer
            self.viewer.load_step_file_direct("test.step")
            QApplication.processEvents()
            time.sleep(1)
            
            # Get DISPLAYED position values BEFORE button press
            pos_before = self.get_displayed_position_values("BOTTOM")
            self.log_result(f"   BEFORE X+ button:")
            self.log_result(f"     Displayed X: {pos_before['x']:.3f}")
            
            # Press X+ button (should INCREASE displayed X value by 1.0)
            self.log_result(f"   Pressing X+ button (should ADD +1.0 to displayed X value)...")
            self.viewer.move_shape('x', 1.0)
            QApplication.processEvents()
            time.sleep(0.5)
            
            # Get DISPLAYED position values AFTER button press
            pos_after = self.get_displayed_position_values("BOTTOM")
            self.log_result(f"   AFTER X+ button:")
            self.log_result(f"     Displayed X: {pos_after['x']:.3f}")
            
            # Check the change in DISPLAYED X value
            x_change = pos_after['x'] - pos_before['x']
            self.log_result(f"   DISPLAYED X CHANGE: {x_change:.3f}")
            self.log_result(f"   EXPECTED CHANGE: +1.000")
            
            if abs(x_change - 1.0) < 0.01:
                self.log_result("   ✅ PASS: BOTTOM X+ button INCREASES displayed X value by 1.0")
                self.passed_tests += 1
            else:
                self.log_result("   ❌ FAIL: BOTTOM X+ button should INCREASE displayed X value by 1.0")
                self.failed_tests += 1
                
        elif self.test_step == 5:
            self.log_result("\n📋 TEST: BOTTOM VIEWER X- BUTTON - DISPLAYED VALUES")
            
            # Get DISPLAYED position values BEFORE button press
            pos_before = self.get_displayed_position_values("BOTTOM")
            self.log_result(f"   BEFORE X- button:")
            self.log_result(f"     Displayed X: {pos_before['x']:.3f}")
            
            # Press X- button (should DECREASE displayed X value by 1.0)
            self.log_result(f"   Pressing X- button (should SUBTRACT -1.0 from displayed X value)...")
            self.viewer.move_shape('x', -1.0)
            QApplication.processEvents()
            time.sleep(0.5)
            
            # Get DISPLAYED position values AFTER button press
            pos_after = self.get_displayed_position_values("BOTTOM")
            self.log_result(f"   AFTER X- button:")
            self.log_result(f"     Displayed X: {pos_after['x']:.3f}")
            
            # Check the change in DISPLAYED X value
            x_change = pos_after['x'] - pos_before['x']
            self.log_result(f"   DISPLAYED X CHANGE: {x_change:.3f}")
            self.log_result(f"   EXPECTED CHANGE: -1.000")
            
            if abs(x_change - (-1.0)) < 0.01:
                self.log_result("   ✅ PASS: BOTTOM X- button DECREASES displayed X value by 1.0")
                self.passed_tests += 1
            else:
                self.log_result("   ❌ FAIL: BOTTOM X- button should DECREASE displayed X value by 1.0")
                self.failed_tests += 1
                
        else:
            # Test complete
            self.log_result("\n" + "=" * 50)
            self.log_result("🏁 DISPLAYED VALUES TESTS COMPLETE")
            self.log_result("=" * 50)
            
            total_tests = self.passed_tests + self.failed_tests
            self.log_result(f"📊 RESULTS: {self.passed_tests}/{total_tests} PASSED, {self.failed_tests}/{total_tests} FAILED")
            
            if self.failed_tests == 0:
                self.log_result("🎉 ALL DISPLAYED VALUES CORRECT!")
                self.status_label.setText("✅ ALL DISPLAYED VALUES CORRECT!")
            else:
                self.log_result("⚠️  DISPLAYED VALUES STILL WRONG - BUTTONS NOT WORKING CORRECTLY")
                self.status_label.setText(f"❌ {self.failed_tests} DISPLAYED VALUES WRONG")
                
            self.timer.stop()
            return
            
        self.test_step += 1

if __name__ == "__main__":
    app = QApplication(sys.argv)
    tester = DisplayedValuesTest()
    tester.show()
    sys.exit(app.exec_())
