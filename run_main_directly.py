#!/usr/bin/env python3
"""
Direct launcher for the main STEP viewer program.
This bypasses any PowerShell function/alias issues.
"""

import subprocess
import sys
import os

def main():
    print("🔧 DIRECT LAUNCHER: Starting main STEP viewer program...")
    print("🔧 Bypassing PowerShell function/alias issues...")
    
    # Get the current directory
    current_dir = os.path.dirname(os.path.abspath(__file__))
    main_program = os.path.join(current_dir, "step_viewer_tdk_modular.py")
    
    print(f"🔧 Running: {main_program}")
    
    # Run the main program directly
    try:
        # Use sys.executable to get the current Python interpreter
        result = subprocess.run([sys.executable, main_program], 
                              cwd=current_dir,
                              check=False)
        print(f"🔧 Program exited with code: {result.returncode}")
    except Exception as e:
        print(f"❌ Error running main program: {e}")
        input("Press Enter to continue...")

if __name__ == "__main__":
    main()
