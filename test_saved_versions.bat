@echo off
echo ========================================
echo TESTING SAVED STEP VIEWER VERSIONS
echo ========================================
echo.
echo Available versions from yesterday (8/25/2025):
echo.
echo 23. step_viewer_tdk_modular_rev23.py (6:30 AM)
echo 24. step_viewer_tdk_modular_rev24.py (7:14 AM) 
echo 25. step_viewer_tdk_modular_rev25.py (8:44 AM)
echo 26. step_viewer_tdk_modular_rev26.py (8:44 AM)
echo 27. step_viewer_tdk_modular_rev27.py (8:44 AM)
echo 28. step_viewer_tdk_modular_rev28.py (8:44 AM)
echo.
echo Also from 8/24/2025 evening:
echo 22. step_viewer_tdk_modular_rev22.py (8:51 PM)
echo.
echo ========================================
echo INSTRUCTIONS:
echo 1. Type a number (22-28) to test that version
echo 2. Test rotation numbers with mouse and buttons
echo 3. If it works, tell me the number!
echo 4. If not, press Ctrl+C and try another
echo ========================================
echo.

set /p version="Enter version number to test (22-28): "

if "%version%"=="22" goto test22
if "%version%"=="23" goto test23
if "%version%"=="24" goto test24
if "%version%"=="25" goto test25
if "%version%"=="26" goto test26
if "%version%"=="27" goto test27
if "%version%"=="28" goto test28

echo Invalid version number!
pause
goto :eof

:test22
echo Testing rev22...
copy "e:\python\save\step_viewer_tdk_modular_rev22.py" "step_viewer_tdk_modular_fixed.py"
python -B step_viewer_tdk_modular_fixed.py
goto :eof

:test23
echo Testing rev23...
copy "e:\python\save\step_viewer_tdk_modular_rev23.py" "step_viewer_tdk_modular_fixed.py"
python -B step_viewer_tdk_modular_fixed.py
goto :eof

:test24
echo Testing rev24...
copy "e:\python\save\step_viewer_tdk_modular_rev24.py" "step_viewer_tdk_modular_fixed.py"
python -B step_viewer_tdk_modular_fixed.py
goto :eof

:test25
echo Testing rev25...
copy "e:\python\save\step_viewer_tdk_modular_rev25.py" "step_viewer_tdk_modular_fixed.py"
python -B step_viewer_tdk_modular_fixed.py
goto :eof

:test26
echo Testing rev26...
copy "e:\python\save\step_viewer_tdk_modular_rev26.py" "step_viewer_tdk_modular_fixed.py"
python -B step_viewer_tdk_modular_fixed.py
goto :eof

:test27
echo Testing rev27...
copy "e:\python\save\step_viewer_tdk_modular_rev27.py" "step_viewer_tdk_modular_fixed.py"
python -B step_viewer_tdk_modular_fixed.py
goto :eof

:test28
echo Testing rev28...
copy "e:\python\save\step_viewer_tdk_modular_rev28.py" "step_viewer_tdk_modular_fixed.py"
python -B step_viewer_tdk_modular_fixed.py
goto :eof
