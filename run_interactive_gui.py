#!/usr/bin/env python3
"""
Run the interactive 3D STEP viewer GUI (not the automated test)
This will open the GUI and keep it open for manual use
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the main viewer class
from step_viewer_tdk_modular_fixed import StepViewerTDK
from PyQt5.QtWidgets import QApplication

def main():
    """Run the interactive GUI"""
    print("🚀 Starting Interactive 3D STEP Viewer GUI...")
    print("This will open the GUI for manual use (not automated test)")
    
    # Create Qt application
    app = QApplication(sys.argv)
    app.setApplicationName("TDK STEP Viewer Dual - Interactive")
    app.setApplicationVersion("3.0")
    
    # Create and show the viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    print("✅ GUI opened! You can now:")
    print("   - Load STEP files using the File menu")
    print("   - Use mouse to rotate/zoom the 3D models")
    print("   - Use the view buttons (front, top, etc.)")
    print("   - Save rotated models")
    print("   - Close the window when done")
    
    # Run the application event loop
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
