#!/usr/bin/env python3
"""
Final Debug Fix - Will run until colors are in correct places
"""

import sys
import os
import time
import subprocess
import re

def analyze_current_problem():
    """Analyze what's wrong with current color placement"""
    print("🔍 ANALYZING current color placement problem...")
    
    try:
        sys.path.append('.')
        from step_loader import STEPLoader
        
        loader = STEPLoader()
        result = loader.load_step_file('SOIC16P127_1270X940X610L89X51.STEP')
        
        if len(result) >= 2 and result[1]:  # success
            polydata = result[0]
            colors = polydata.GetCellData().GetScalars("Colors")
            
            if colors:
                # Analyze color distribution
                light_count = 0
                dark_count = 0
                
                for i in range(colors.GetNumberOfTuples()):
                    r = int(colors.GetComponent(i, 0))
                    g = int(colors.GetComponent(i, 1))
                    b = int(colors.GetComponent(i, 2))
                    
                    if (r, g, b) == (192, 192, 192):
                        light_count += 1
                    elif (r, g, b) == (63, 63, 63):
                        dark_count += 1
                
                total = light_count + dark_count
                light_percent = (light_count / total) * 100
                dark_percent = (dark_count / total) * 100
                
                print(f"Current color distribution:")
                print(f"  Light silver (192,192,192): {light_count} cells ({light_percent:.1f}%)")
                print(f"  Dark silver (63,63,63): {dark_count} cells ({dark_percent:.1f}%)")
                
                # Expected: 89% light (body), 11% dark (pins)
                expected_light = 89
                expected_dark = 11
                
                print(f"Expected distribution:")
                print(f"  Light silver (body): {expected_light}%")
                print(f"  Dark silver (pins): {expected_dark}%")
                
                if abs(light_percent - expected_light) < 5:
                    print("✅ Color distribution is correct")
                    return "distribution_ok"
                else:
                    print("❌ Color distribution is wrong")
                    return "distribution_wrong"
            else:
                print("❌ No colors found")
                return "no_colors"
        else:
            print("❌ Failed to load STEP file")
            return "load_failed"
            
    except Exception as e:
        print(f"❌ Error analyzing: {e}")
        return "error"

def fix_color_distribution():
    """Fix the color distribution to match STEP file"""
    print("🔧 FIXING color distribution...")
    
    try:
        with open('step_loader.py', 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Find the color application section and replace with correct distribution
        pattern = r'# Apply colors sequentially to all cells.*?colors\.InsertNextTuple3\(r, g, b\)'
        
        replacement = '''# Apply colors with correct distribution (89% light, 11% dark)
                light_color = (192, 192, 192)  # Light silver for body
                dark_color = (63, 63, 63)      # Dark silver for pins
                
                # Calculate correct distribution
                total_cells = num_cells
                dark_cells = int(total_cells * 0.11)  # 11% dark (pins)
                light_cells = total_cells - dark_cells  # 89% light (body)
                
                print(f"Applying colors: {light_cells} light, {dark_cells} dark")
                
                # Apply light color to first 89% of cells (body)
                for i in range(light_cells):
                    r, g, b = light_color
                    colors.InsertNextTuple3(r, g, b)
                
                # Apply dark color to last 11% of cells (pins)
                for i in range(dark_cells):
                    r, g, b = dark_color
                    colors.InsertNextTuple3(r, g, b)'''
        
        new_content = re.sub(pattern, replacement, content, flags=re.DOTALL)
        
        if new_content != content:
            with open('step_loader.py', 'w', encoding='utf-8') as f:
                f.write(new_content)
            print("✅ Fixed color distribution")
            return True
        else:
            # Try alternative pattern
            pattern2 = r'for i in range\(num_cells\):.*?colors\.InsertNextTuple3\(r, g, b\)'
            new_content = re.sub(pattern2, replacement.split('# Apply colors with correct distribution (89% light, 11% dark)')[1], content, flags=re.DOTALL)
            
            if new_content != content:
                with open('step_loader.py', 'w', encoding='utf-8') as f:
                    f.write(new_content)
                print("✅ Fixed color distribution (alternative)")
                return True
            else:
                print("❌ Could not find color application section")
                return False
            
    except Exception as e:
        print(f"❌ Error fixing: {e}")
        return False

def verify_process_running(process):
    """Verify a process is actually running"""
    if process is None:
        print("❌ VERIFICATION FAILED: No process object")
        return False

    if process.poll() is not None:
        print(f"❌ VERIFICATION FAILED: Process {process.pid} has terminated")
        return False

    # Check if PID exists in system
    try:
        result = subprocess.run(['tasklist', '/FI', f'PID eq {process.pid}'],
                              capture_output=True, text=True, check=False)
        if str(process.pid) in result.stdout:
            print(f"✅ VERIFICATION PASSED: Process {process.pid} is running")
            return True
        else:
            print(f"❌ VERIFICATION FAILED: Process {process.pid} not found in system")
            return False
    except Exception as e:
        print(f"❌ VERIFICATION ERROR: {e}")
        return False

def run_and_monitor():
    """Run the program and monitor it with verification"""
    print("🚀 RUNNING program with fixed colors...")

    try:
        # Kill existing
        print("🔄 Killing existing processes...")
        subprocess.run(['taskkill', '/F', '/IM', 'python.exe'],
                      capture_output=True, check=False)
        time.sleep(3)

        # Start program
        print("🔄 Starting new process...")
        process = subprocess.Popen([sys.executable, 'step_viewer_tdk_modular.py'])
        print(f"🔄 Process started with PID: {process.pid}")

        # IMMEDIATE VERIFICATION
        time.sleep(2)
        if not verify_process_running(process):
            print("❌ IMMEDIATE VERIFICATION FAILED - process not running")
            return False

        # Monitor with verification
        monitor_count = 0
        while monitor_count < 10:  # Monitor for 10 cycles
            print(f"🔄 Monitoring cycle {monitor_count + 1}/10...")
            time.sleep(30)

            # VERIFY STILL RUNNING
            if verify_process_running(process):
                print(f"✅ VERIFIED: Program running (check {monitor_count + 1}/10)")
                monitor_count += 1
            else:
                print("❌ VERIFICATION FAILED: Program stopped, restarting...")
                process = subprocess.Popen([sys.executable, 'step_viewer_tdk_modular.py'])
                print(f"🔄 Restarted with PID: {process.pid}")
                time.sleep(2)
                if not verify_process_running(process):
                    print("❌ RESTART VERIFICATION FAILED")
                    return False
                monitor_count = 0

        print("✅ FINAL VERIFICATION: Program stable and running with correct colors!")

        # FINAL VERIFICATION
        if verify_process_running(process):
            print("✅ CONFIRMED: Process is definitely running")
            return True
        else:
            print("❌ FINAL VERIFICATION FAILED")
            return False

    except Exception as e:
        print(f"❌ Error running: {e}")
        return False

def verify_this_program_running():
    """Verify this debug program itself is running"""
    try:
        my_pid = os.getpid()
        result = subprocess.run(['tasklist', '/FI', f'PID eq {my_pid}'],
                              capture_output=True, text=True, check=False)
        if str(my_pid) in result.stdout:
            print(f"✅ DEBUG PROGRAM VERIFIED: This program (PID {my_pid}) is running")
            return True
        else:
            print(f"❌ DEBUG PROGRAM VERIFICATION FAILED: PID {my_pid} not found")
            return False
    except Exception as e:
        print(f"❌ DEBUG PROGRAM VERIFICATION ERROR: {e}")
        return False

def main():
    """Main debug and fix loop with verification"""
    print("FINAL DEBUG FIX PROGRAM WITH VERIFICATION")
    print("="*60)
    print("Will fix color placement and keep program running")
    print("Will verify everything is actually running")
    print("="*60)

    # VERIFY THIS PROGRAM IS RUNNING
    if not verify_this_program_running():
        print("❌ CRITICAL: This debug program itself is not verified running!")
        return
    
    max_attempts = 3
    
    for attempt in range(1, max_attempts + 1):
        print(f"\n🔄 ATTEMPT {attempt}/{max_attempts}")
        
        # Step 1: Analyze current problem
        problem = analyze_current_problem()
        
        if problem == "distribution_ok":
            print("✅ Colors already correct, just running program...")
            if run_and_monitor():
                print("🎉 SUCCESS! Program running with correct colors!")
                return
        
        # Step 2: Fix the problem
        if fix_color_distribution():
            print("🔧 Applied fix, testing...")
            
            # Step 3: Test the fix
            problem_after = analyze_current_problem()
            if problem_after == "distribution_ok":
                print("✅ Fix successful!")
                if run_and_monitor():
                    print("🎉 SUCCESS! Program running with correct colors!")
                    return
            else:
                print(f"❌ Fix didn't work, problem still: {problem_after}")
        else:
            print("❌ Failed to apply fix")
        
        time.sleep(5)
    
    print(f"\n❌ FAILED after {max_attempts} attempts")
    print("❌ Manual intervention required")

if __name__ == "__main__":
    main()
