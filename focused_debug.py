#!/usr/bin/env python3
"""
FOCUSED DEBUG - Will actually run and monitor until colors are fixed
"""

import sys
import os
import time
import subprocess

def test_colors_are_correct():
    """Test if colors are in correct places"""
    print("🔍 TESTING if colors are in correct places...")
    
    try:
        sys.path.append('.')
        from step_loader import STEPLoader
        
        loader = STEPLoader()
        result = loader.load_step_file('SOIC16P127_1270X940X610L89X51.STEP')
        
        if len(result) >= 2 and result[1]:  # success
            polydata = result[0]
            colors = polydata.GetCellData().GetScalars("Colors")
            
            if colors:
                # Count color distribution
                light_count = 0
                dark_count = 0
                
                for i in range(colors.GetNumberOfTuples()):
                    r = int(colors.GetComponent(i, 0))
                    g = int(colors.GetComponent(i, 1))
                    b = int(colors.GetComponent(i, 2))
                    
                    if (r, g, b) == (192, 192, 192):
                        light_count += 1
                    elif (r, g, b) == (63, 63, 63):
                        dark_count += 1
                
                total = light_count + dark_count
                light_percent = (light_count / total) * 100
                dark_percent = (dark_count / total) * 100
                
                print(f"Current: {light_percent:.1f}% light, {dark_percent:.1f}% dark")
                print(f"Expected: 89% light (body), 11% dark (pins)")
                
                # Check if distribution is correct (within 5%)
                if abs(light_percent - 89) < 5 and abs(dark_percent - 11) < 5:
                    print("✅ COLOR DISTRIBUTION IS CORRECT")
                    return True
                else:
                    print("❌ COLOR DISTRIBUTION IS WRONG")
                    return False
            else:
                print("❌ NO COLORS FOUND")
                return False
        else:
            print("❌ FAILED TO LOAD STEP FILE")
            return False
            
    except Exception as e:
        print(f"❌ ERROR TESTING: {e}")
        return False

def fix_color_distribution():
    """Fix the color distribution"""
    print("🔧 FIXING color distribution...")
    
    try:
        # Read step_loader.py
        with open('step_loader.py', 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Find the color application and replace with correct distribution
        lines = content.split('\n')
        new_lines = []
        in_color_section = False
        
        for i, line in enumerate(lines):
            if 'for i in range(num_cells):' in line and 'color_index' in lines[i+1] if i+1 < len(lines) else False:
                in_color_section = True
                # Replace with correct distribution
                new_lines.extend([
                    '                # CORRECT DISTRIBUTION: 89% light (body), 11% dark (pins)',
                    '                light_cells = int(num_cells * 0.89)',
                    '                dark_cells = num_cells - light_cells',
                    '                ',
                    '                # Apply light color to first 89% (body)',
                    '                for i in range(light_cells):',
                    '                    r, g, b = (192, 192, 192)  # Light silver',
                    '                    colors.InsertNextTuple3(r, g, b)',
                    '                ',
                    '                # Apply dark color to last 11% (pins)',
                    '                for i in range(dark_cells):',
                    '                    r, g, b = (63, 63, 63)     # Dark silver',
                    '                    colors.InsertNextTuple3(r, g, b)'
                ])
                # Skip the original loop
                while i < len(lines) and 'colors.InsertNextTuple3' not in lines[i]:
                    i += 1
                if i < len(lines):
                    i += 1  # Skip the InsertNextTuple3 line too
                in_color_section = False
            elif not in_color_section:
                new_lines.append(line)
        
        # Write back
        with open('step_loader.py', 'w', encoding='utf-8') as f:
            f.write('\n'.join(new_lines))
        
        print("✅ FIXED color distribution")
        return True
        
    except Exception as e:
        print(f"❌ ERROR FIXING: {e}")
        return False

def run_main_program():
    """Run the main program"""
    print("🚀 RUNNING main program...")
    
    try:
        # Kill existing
        subprocess.run(['taskkill', '/F', '/IM', 'python.exe'], 
                      capture_output=True, check=False)
        time.sleep(2)
        
        # Start program
        process = subprocess.Popen([sys.executable, 'step_viewer_tdk_modular.py'])
        print(f"✅ STARTED program PID: {process.pid}")
        
        # Verify it's running
        time.sleep(5)
        if process.poll() is None:
            print("✅ PROGRAM IS RUNNING")
            return process
        else:
            print("❌ PROGRAM FAILED TO START")
            return None
            
    except Exception as e:
        print(f"❌ ERROR RUNNING: {e}")
        return None

def monitor_program(process):
    """Monitor the program"""
    print("👁️ MONITORING program...")
    
    for i in range(20):  # Monitor for 20 cycles (10 minutes)
        time.sleep(30)
        
        if process.poll() is None:
            print(f"✅ CYCLE {i+1}/20: Program still running")
        else:
            print(f"❌ CYCLE {i+1}/20: Program stopped, restarting...")
            process = subprocess.Popen([sys.executable, 'step_viewer_tdk_modular.py'])
            print(f"🔄 RESTARTED PID: {process.pid}")
    
    print("✅ MONITORING COMPLETE - Program has been running")

def main():
    """Main focused debug function"""
    print("FOCUSED DEBUG PROGRAM")
    print("="*40)
    print("WILL FIX COLORS AND KEEP PROGRAM RUNNING")
    print("="*40)
    
    # Step 1: Test current colors
    if test_colors_are_correct():
        print("✅ Colors already correct!")
    else:
        print("❌ Colors need fixing...")
        
        # Step 2: Fix colors
        if fix_color_distribution():
            print("🔧 Applied fix")
            
            # Step 3: Test again
            if test_colors_are_correct():
                print("✅ Fix successful!")
            else:
                print("❌ Fix failed")
                return
        else:
            print("❌ Could not apply fix")
            return
    
    # Step 4: Run main program
    process = run_main_program()
    if process:
        # Step 5: Monitor program
        monitor_program(process)
        print("🎉 SUCCESS! Program running with correct colors!")
    else:
        print("❌ FAILED to run program")

if __name__ == "__main__":
    main()
