#!/usr/bin/env python3
"""
VISUAL DEMONSTRATION: Save/Load Rotation Persistence
This script will show you the complete workflow working:
1. Load test.step into TOP window
2. Rotate it 45 degrees
3. Save the rotated file
4. Load the saved file into BOTTOM window
5. Show both windows side-by-side with identical rotation
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QTimer
from step_viewer_tdk_modular import StepViewerTDK

class RotationPersistenceDemo:
    def __init__(self):
        self.app = QApplication([])
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        # Demo settings
        self.rotation_angle = 45.0
        self.original_file = "test.step"
        self.saved_file = "demo_rotated.step"
        
        # Start the demonstration
        QTimer.singleShot(1000, self.start_demo)
        
    def start_demo(self):
        """Start the demonstration"""
        print("🎬 ROTATION PERSISTENCE DEMONSTRATION")
        print("=" * 50)
        
        # Check if test.step exists
        if not os.path.exists(self.original_file):
            print(f"❌ {self.original_file} not found!")
            print("Please ensure test.step is in the current directory.")
            self.app.quit()
            return
            
        print("📋 This demo will show you:")
        print("   1. Load test.step into TOP window")
        print("   2. Rotate it 45° around X-axis")
        print("   3. Save the rotated model")
        print("   4. Load saved file into BOTTOM window")
        print("   5. Compare - both should show same rotation!")
        print()
        
        QTimer.singleShot(2000, self.step1_load_original)
        
    def step1_load_original(self):
        """Step 1: Load original file into TOP window"""
        print("🔄 STEP 1: Loading test.step into TOP window...")
        
        # Select TOP window
        self.viewer.active_viewer = "top"
        self.viewer.update_viewer_highlights()
        
        # Load the file
        success = self.viewer.load_step_file_direct(self.original_file)
        if not success:
            print("❌ Failed to load test.step")
            self.app.quit()
            return
            
        print("✅ test.step loaded into TOP window")
        print("🎯 You should see the original model in the TOP window")
        print()
        
        QTimer.singleShot(3000, self.step2_rotate_model)
        
    def step2_rotate_model(self):
        """Step 2: Rotate the model 45 degrees"""
        print("🔄 STEP 2: Rotating model 45° around X-axis...")
        
        # Apply rotation
        self.viewer.rotate_shape('x', self.rotation_angle)
        
        print("✅ 45° rotation applied")
        print("🎯 The model in TOP window should now look rotated")
        print("   (Compare it to how it looked before - it should be tilted)")
        print()
        
        QTimer.singleShot(3000, self.step3_save_rotated)
        
    def step3_save_rotated(self):
        """Step 3: Save the rotated model"""
        print("🔄 STEP 3: Saving rotated model...")
        
        try:
            # Get save parameters
            loader = self.viewer.step_loader_left
            current_rot = self.viewer.model_rot_left
            current_pos = self.viewer.current_pos_left if hasattr(self.viewer, 'current_pos_left') else {'x': 0, 'y': 0, 'z': 0}
            orig_rot = {'x': 0, 'y': 0, 'z': 0}
            orig_pos = self.viewer.orig_pos_left if hasattr(self.viewer, 'orig_pos_left') else {'x': 0, 'y': 0, 'z': 0}
            
            # Save with transformations
            success = self.viewer._save_step_with_transformations(
                self.saved_file, loader, current_pos, current_rot, orig_pos, orig_rot
            )
            
            if success:
                print("✅ Rotated model saved successfully")
                print(f"📁 Saved as: {self.saved_file}")
                print("🎯 The rotation is now embedded in the saved file")
            else:
                print("❌ Save failed")
                self.app.quit()
                return
                
        except Exception as e:
            print(f"❌ Save error: {e}")
            self.app.quit()
            return
            
        print()
        QTimer.singleShot(3000, self.step4_load_into_bottom)
        
    def step4_load_into_bottom(self):
        """Step 4: Load saved file into BOTTOM window"""
        print("🔄 STEP 4: Loading saved file into BOTTOM window...")
        
        # Select BOTTOM window
        self.viewer.active_viewer = "bottom"
        self.viewer.update_viewer_highlights()
        
        # Load the saved file
        success = self.viewer.load_step_file_direct(self.saved_file)
        if not success:
            print("❌ Failed to load saved file")
            self.app.quit()
            return
            
        print("✅ Saved file loaded into BOTTOM window")
        print("🎯 The BOTTOM window should now show the SAME rotation as TOP!")
        print()
        
        QTimer.singleShot(3000, self.step5_show_results)
        
    def step5_show_results(self):
        """Step 5: Show the final results"""
        print("🔄 STEP 5: Comparing results...")
        print()
        
        # Get rotation values from both windows
        top_rotation = self.viewer.model_rot_left if hasattr(self.viewer, 'model_rot_left') else {'x': 0, 'y': 0, 'z': 0}
        bottom_rotation = self.viewer.orig_rot_right if hasattr(self.viewer, 'orig_rot_right') else {'x': 0, 'y': 0, 'z': 0}
        
        print("📊 ROTATION VALUES:")
        print(f"   TOP window (applied rotation):  X={top_rotation['x']:.1f}° Y={top_rotation['y']:.1f}° Z={top_rotation['z']:.1f}°")
        print(f"   BOTTOM window (loaded rotation): X={bottom_rotation['x']:.1f}° Y={bottom_rotation['y']:.1f}° Z={bottom_rotation['z']:.1f}°")
        print()
        
        # Check if they match
        rotation_match = (
            abs(top_rotation['x'] - bottom_rotation['x']) < 1.0 and
            abs(top_rotation['y'] - bottom_rotation['y']) < 1.0 and
            abs(top_rotation['z'] - bottom_rotation['z']) < 1.0
        )
        
        if rotation_match:
            print("🎉 SUCCESS! ROTATION PERSISTENCE IS WORKING!")
            print("=" * 50)
            print("✅ TOP window: Shows rotated model (45° applied)")
            print("✅ BOTTOM window: Shows same rotation (45° loaded)")
            print("✅ Both models should look IDENTICAL")
            print("✅ The save/load workflow preserves rotations perfectly!")
        else:
            print("❌ ISSUE: Rotations don't match")
            print("   This indicates a problem with the rotation persistence")
            
        print()
        print("🎯 VISUAL VERIFICATION:")
        print("   Look at both windows side-by-side:")
        print("   - TOP window: Original file + 45° rotation applied")
        print("   - BOTTOM window: Saved file with 45° rotation loaded")
        print("   - Both should show the EXACT SAME rotated model!")
        print()
        print("⏰ Demo will close in 15 seconds...")
        print("   Take time to visually compare the two windows")
        
        # Clean up and close
        QTimer.singleShot(15000, self.cleanup_and_exit)
        
    def cleanup_and_exit(self):
        """Clean up demo files and exit"""
        if os.path.exists(self.saved_file):
            os.remove(self.saved_file)
            print(f"🧹 Cleaned up: {self.saved_file}")
            
        print("👋 Demo complete!")
        self.app.quit()

def main():
    """Run the demonstration"""
    print("🚀 Starting Rotation Persistence Demonstration...")
    print("   Make sure test.step is in the current directory")
    print()
    
    demo = RotationPersistenceDemo()
    demo.app.exec_()

if __name__ == "__main__":
    main()
