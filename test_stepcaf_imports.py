#!/usr/bin/env python3

print("TESTING STEPCAF IMPORT PATTERNS")

# Test different import patterns for STEPCAFControl_Reader
import_patterns = [
    "from OCC.Core import STEPCAFControl_Reader",
    "from OCC.Core.STEPCAFControl import STEPCAFControl_Reader", 
    "from OCC import STEPCAFControl_Reader",
    "import OCC.Core.STEPCAFControl_Reader",
    "from OCC.STEPCAFControl_Reader import STEPCAFControl_Reader",
]

for pattern in import_patterns:
    try:
        exec(pattern)
        print(f"✓ SUCCESS: {pattern}")
        break
    except Exception as e:
        print(f"✗ FAILED: {pattern} - {e}")

# Check what STEP-related modules are available
try:
    import OCC.Core
    step_modules = [item for item in dir(OCC.Core) if 'STEP' in item]
    print(f"\nAvailable STEP modules in OCC.Core:")
    for module in step_modules:
        print(f"  {module}")
        
    # Check if STEPCAFControl_Reader is in any of these modules
    for module_name in step_modules:
        try:
            module = getattr(OCC.Core, module_name)
            if hasattr(module, 'STEPCAFControl_Reader'):
                print(f"Found STEPCAFControl_Reader in {module_name}")
        except:
            pass
            
except Exception as e:
    print(f"Cannot check OCC.Core: {e}")

print("\nTESTING STEPCAF IMPORT PATTERNS COMPLETE")
