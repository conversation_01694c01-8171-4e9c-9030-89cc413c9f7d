#!/usr/bin/env python3
"""
DEBUG MAIN PROGRAM METHODS - Test the actual methods from the main program
This will import and test the actual methods from step_viewer_tdk_modular.py
to see exactly why they're not working.
"""

import sys
import os
import time
import math

def test_main_program_imports():
    """Test if we can import the main program components"""
    print("🔧 TESTING MAIN PROGRAM IMPORTS")
    print("=" * 50)
    
    try:
        # Test step_loader import
        from step_loader import STEPLoader
        print("✅ step_loader imported successfully")
        
        # Test vtk_renderer import  
        from vtk_renderer import VTKRenderer
        print("✅ vtk_renderer imported successfully")
        
        # Test gui_components import
        from gui_components import create_tool_dock
        print("✅ gui_components imported successfully")
        
        # Test main program import
        sys.path.insert(0, '.')
        import step_viewer_tdk_modular
        print("✅ step_viewer_tdk_modular imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_rotation_calculation():
    """Test the rotation calculation logic directly"""
    print("\n🧮 TESTING ROTATION CALCULATION LOGIC")
    print("=" * 50)
    
    try:
        # Test the exact calculation from the main program
        current_rot = {'x': 15.0, 'y': 15.0, 'z': 0.0}
        
        print(f"Input rotation: {current_rot}")
        
        # Calculate axis and angle (same as main program)
        rot_mag = math.sqrt(current_rot['x']**2 + current_rot['y']**2 + current_rot['z']**2)
        print(f"Rotation magnitude: {rot_mag:.3f}")
        
        if rot_mag > 0.001:
            current_axis = {
                'x': current_rot['x'] / rot_mag,
                'y': current_rot['y'] / rot_mag,
                'z': current_rot['z'] / rot_mag
            }
            current_angle = rot_mag
            print(f"✅ Calculated axis: {current_axis}")
            print(f"✅ Calculated angle: {current_angle:.1f}°")
            return True
        else:
            print("❌ Rotation magnitude too small")
            return False
            
    except Exception as e:
        print(f"❌ Calculation error: {e}")
        return False

def test_vtk_text_overlay_method():
    """Test if the update_vtk_text_overlays method exists and works"""
    print("\n📝 TESTING VTK TEXT OVERLAY METHOD")
    print("=" * 50)
    
    try:
        import step_viewer_tdk_modular
        
        # Check if the method exists
        if hasattr(step_viewer_tdk_modular.StepViewerTDK, 'update_vtk_text_overlays'):
            print("✅ update_vtk_text_overlays method exists")
            
            # Try to inspect the method
            import inspect
            method = getattr(step_viewer_tdk_modular.StepViewerTDK, 'update_vtk_text_overlays')
            source_lines = inspect.getsourcelines(method)
            print(f"✅ Method has {len(source_lines[0])} lines of code")
            
            # Show first few lines
            print("📋 Method preview:")
            for i, line in enumerate(source_lines[0][:5]):
                print(f"   {i+1}: {line.rstrip()}")
            
            return True
        else:
            print("❌ update_vtk_text_overlays method does not exist!")
            return False
            
    except Exception as e:
        print(f"❌ Method test error: {e}")
        return False

def test_mouse_tracking_timer():
    """Test if the mouse tracking timer is set up correctly"""
    print("\n⏰ TESTING MOUSE TRACKING TIMER SETUP")
    print("=" * 50)
    
    try:
        import step_viewer_tdk_modular
        
        # Check if update_rotation_from_mouse method exists
        if hasattr(step_viewer_tdk_modular.StepViewerTDK, 'update_rotation_from_mouse'):
            print("✅ update_rotation_from_mouse method exists")
            
            # Try to inspect the method
            import inspect
            method = getattr(step_viewer_tdk_modular.StepViewerTDK, 'update_rotation_from_mouse')
            source_lines = inspect.getsourcelines(method)
            print(f"✅ Method has {len(source_lines[0])} lines of code")
            
            # Look for key components
            source_text = ''.join(source_lines[0])
            
            if 'GetOrientation' in source_text:
                print("✅ Method calls GetOrientation()")
            else:
                print("❌ Method does NOT call GetOrientation()")
                
            if 'current_axis' in source_text:
                print("✅ Method updates current_axis")
            else:
                print("❌ Method does NOT update current_axis")
                
            if 'update_vtk_text_overlays' in source_text:
                print("✅ Method calls update_vtk_text_overlays()")
            else:
                print("❌ Method does NOT call update_vtk_text_overlays()")
            
            return True
        else:
            print("❌ update_rotation_from_mouse method does not exist!")
            return False
            
    except Exception as e:
        print(f"❌ Timer test error: {e}")
        return False

def test_button_rotation_method():
    """Test if the rotate_shape method has axis calculation"""
    print("\n🔘 TESTING BUTTON ROTATION METHOD")
    print("=" * 50)
    
    try:
        import step_viewer_tdk_modular
        
        # Check if rotate_shape method exists
        if hasattr(step_viewer_tdk_modular.StepViewerTDK, 'rotate_shape'):
            print("✅ rotate_shape method exists")
            
            # Try to inspect the method
            import inspect
            method = getattr(step_viewer_tdk_modular.StepViewerTDK, 'rotate_shape')
            source_lines = inspect.getsourcelines(method)
            print(f"✅ Method has {len(source_lines[0])} lines of code")
            
            # Look for key components
            source_text = ''.join(source_lines[0])
            
            if 'current_axis_left' in source_text:
                print("✅ Method updates current_axis_left")
            else:
                print("❌ Method does NOT update current_axis_left")
                
            if 'current_axis_right' in source_text:
                print("✅ Method updates current_axis_right")
            else:
                print("❌ Method does NOT update current_axis_right")
                
            if 'math.sqrt' in source_text:
                print("✅ Method has axis calculation (math.sqrt)")
            else:
                print("❌ Method does NOT have axis calculation")
                
            if 'rot_mag' in source_text:
                print("✅ Method calculates rotation magnitude")
            else:
                print("❌ Method does NOT calculate rotation magnitude")
            
            return True
        else:
            print("❌ rotate_shape method does not exist!")
            return False
            
    except Exception as e:
        print(f"❌ Button rotation test error: {e}")
        return False

def test_color_handling():
    """Test the color handling in vtk_renderer"""
    print("\n🎨 TESTING COLOR HANDLING")
    print("=" * 50)
    
    try:
        from vtk_renderer import VTKRenderer
        
        # Check if VTKRenderer can be instantiated
        print("✅ VTKRenderer can be imported")
        
        # Look at the display_polydata method
        import inspect
        if hasattr(VTKRenderer, 'display_polydata'):
            method = getattr(VTKRenderer, 'display_polydata')
            source_lines = inspect.getsourcelines(method)
            source_text = ''.join(source_lines[0])
            
            if 'exact colors from STEP file' in source_text:
                print("✅ Method uses exact STEP file colors")
            else:
                print("❌ Method does NOT use exact STEP file colors")
                
            if 'SetScalarVisibility(True)' in source_text:
                print("✅ Method enables scalar visibility")
            else:
                print("❌ Method does NOT enable scalar visibility")
                
            return True
        else:
            print("❌ display_polydata method not found")
            return False
            
    except Exception as e:
        print(f"❌ Color handling test error: {e}")
        return False

def main():
    """Run all debug tests"""
    print("MAIN PROGRAM METHOD DEBUGGING")
    print("=" * 60)
    
    tests = [
        ("Import Test", test_main_program_imports),
        ("Rotation Calculation", test_rotation_calculation),
        ("VTK Text Overlay Method", test_vtk_text_overlay_method),
        ("Mouse Tracking Timer", test_mouse_tracking_timer),
        ("Button Rotation Method", test_button_rotation_method),
        ("Color Handling", test_color_handling)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("DEBUG TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed < len(results):
        print("\n⚠️ ISSUES FOUND - These need to be fixed:")
        for test_name, result in results:
            if not result:
                print(f"   - {test_name}")
    else:
        print("\n🎉 All tests passed - Methods should be working!")

if __name__ == "__main__":
    main()
