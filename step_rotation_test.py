#!/usr/bin/env python3
"""
STEP File Rotation Save Test
Tests the specific issues:
1. Double overwrite dialog problem
2. Rotation reading incorrect in saved STEP files

Only works with STEP files, no STL conversion.
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular_fixed import StepViewerTDK

def test_step_rotation_save():
    """Test STEP file rotation save functionality"""
    print("🔧 STEP FILE ROTATION SAVE TEST")
    print("=" * 50)
    
    # Find a STEP file
    step_files = [f for f in os.listdir('.') if f.endswith('.STEP') or f.endswith('.step')]
    if not step_files:
        print("❌ No STEP files found")
        return False
        
    test_file = step_files[0]
    test_save_file = "test_step_rotation.step"
    
    print(f"📁 Original STEP file: {test_file}")
    print(f"💾 Will save as: {test_save_file}")
    
    # Remove existing test file
    if os.path.exists(test_save_file):
        os.remove(test_save_file)
        print(f"🗑️  Removed existing {test_save_file}")
    
    # Create viewer
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    
    try:
        print(f"\n📂 Step 1: Loading {test_file} into TOP viewer...")
        
        # Load file into TOP viewer
        viewer.active_viewer = "top"
        success = viewer.step_loader_left.load_step_file(test_file)
        
        if not success:
            print("❌ Failed to load STEP file")
            return False
            
        # Display in TOP viewer
        if hasattr(viewer.step_loader_left, 'current_polydata') and viewer.step_loader_left.current_polydata:
            viewer.vtk_renderer_left.display_polydata(viewer.step_loader_left.current_polydata)
            print("✅ STEP file loaded and displayed in TOP viewer")
        else:
            print("❌ No polydata available from STEP file")
            return False
        
        print(f"\n🔄 Step 2: Applying rotations to STEP model...")
        print("   - Applying X+15°, Y+30°, Z+45°")
        
        # Apply rotations
        viewer.rotate_shape('x', 15.0)
        viewer.rotate_shape('y', 30.0) 
        viewer.rotate_shape('z', 45.0)
        
        # Verify rotation values (check current_rot_left which is where button rotations are stored)
        current_rot = viewer.current_rot_left
        print(f"   - Current rotations: X={current_rot['x']:.1f}°, Y={current_rot['y']:.1f}°, Z={current_rot['z']:.1f}°")

        if not (abs(current_rot['x'] - 15.0) < 0.1 and
                abs(current_rot['y'] - 30.0) < 0.1 and
                abs(current_rot['z'] - 45.0) < 0.1):
            print("❌ Rotation values are incorrect")
            print(f"   Expected: X=15.0°, Y=30.0°, Z=45.0°")
            print(f"   Actual: X={current_rot['x']:.1f}°, Y={current_rot['y']:.1f}°, Z={current_rot['z']:.1f}°")
            return False
        
        print(f"\n💾 Step 3: Saving rotated STEP file using OpenCASCADE...")
        
        # Save using OpenCASCADE transformation (direct method to avoid dialogs)
        loader = viewer.step_loader_left
        delta_pos = viewer.current_pos_left
        delta_rot = viewer.model_rot_left
        
        print(f"   - Using OpenCASCADE transformation")
        print(f"   - Delta rotations: X={delta_rot['x']:.1f}°, Y={delta_rot['y']:.1f}°, Z={delta_rot['z']:.1f}°")
        
        success = viewer._save_step_opencascade_transform(
            test_save_file, loader, delta_pos, delta_rot
        )
        
        if not success:
            print("❌ OpenCASCADE transformation save failed")
            return False
            
        # Verify saved STEP file
        if not os.path.exists(test_save_file):
            print("❌ Saved STEP file not created")
            return False
            
        original_size = os.path.getsize(test_file)
        saved_size = os.path.getsize(test_save_file)
        print(f"   - Original STEP: {original_size:,} bytes")
        print(f"   - Saved STEP: {saved_size:,} bytes")
        
        if saved_size < 100:
            print("❌ Saved STEP file too small")
            return False
            
        print("✅ STEP file saved successfully with OpenCASCADE transformation")
        
        print(f"\n📂 Step 4: Loading saved STEP file into BOTTOM viewer...")
        
        # Load saved STEP file into BOTTOM viewer
        viewer.active_viewer = "bottom"
        success = viewer.step_loader_right.load_step_file(test_save_file)
        
        if not success:
            print("❌ Failed to load saved STEP file")
            return False
            
        # Display in BOTTOM viewer
        if hasattr(viewer.step_loader_right, 'current_polydata') and viewer.step_loader_right.current_polydata:
            viewer.vtk_renderer_right.display_polydata(viewer.step_loader_right.current_polydata)
            print("✅ Saved STEP file loaded and displayed in BOTTOM viewer")
        else:
            print("❌ No polydata available from saved STEP file")
            return False
            
        print(f"\n🔍 Step 5: Analyzing STEP transformation results...")
        
        # Check if both viewers have models
        top_has_model = (hasattr(viewer.vtk_renderer_left, 'step_actor') and 
                        viewer.vtk_renderer_left.step_actor is not None) or \
                       (hasattr(viewer.vtk_renderer_left, 'multi_actors') and 
                        len(viewer.vtk_renderer_left.multi_actors) > 0)
                        
        bottom_has_model = (hasattr(viewer.vtk_renderer_right, 'step_actor') and 
                           viewer.vtk_renderer_right.step_actor is not None) or \
                          (hasattr(viewer.vtk_renderer_right, 'multi_actors') and 
                           len(viewer.vtk_renderer_right.multi_actors) > 0)
        
        print(f"   - TOP viewer has model: {top_has_model}")
        print(f"   - BOTTOM viewer has model: {bottom_has_model}")
        
        if not (top_has_model and bottom_has_model):
            print("❌ One or both viewers missing models")
            return False
            
        # Show viewer for manual inspection
        viewer.show()
        print(f"\n👀 STEP ROTATION TEST RESULTS:")
        print(f"   - TOP viewer: Shows original STEP file with applied rotations")
        print(f"   - BOTTOM viewer: Shows saved STEP file")
        print(f"   - CRITICAL TEST: Both should show the SAME rotated geometry")
        print(f"   - If BOTTOM shows different rotation, the STEP save is broken")
        print(f"\n🔧 Manual verification required:")
        print(f"   1. Check if both models look identical")
        print(f"   2. Check if rotations are preserved in saved STEP file")
        print(f"   3. Close window when done inspecting")
        
        # Keep application running for manual inspection
        app.exec_()
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Cleanup test file
        if os.path.exists(test_save_file):
            try:
                os.remove(test_save_file)
                print(f"🗑️  Cleaned up {test_save_file}")
            except:
                pass

if __name__ == "__main__":
    print("🚀 Starting STEP File Rotation Save Test...")
    print("   This test will:")
    print("   1. Load a STEP file into TOP viewer")
    print("   2. Apply rotations (X+15°, Y+30°, Z+45°)")
    print("   3. Save using OpenCASCADE transformation")
    print("   4. Load saved STEP file into BOTTOM viewer")
    print("   5. Show both for visual comparison")
    print()
    
    success = test_step_rotation_save()
    
    if success:
        print("\n🎉 STEP rotation test completed!")
        print("   Check the visual results to verify if rotation save works correctly.")
    else:
        print("\n❌ STEP rotation test failed!")
    
    sys.exit(0 if success else 1)
