#!/usr/bin/env python3
"""
Debug Reset Visual Issue - Find exactly why reset doesn't move model back visually
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular import Step<PERSON><PERSON><PERSON>TD<PERSON>

def debug_reset_visual_issue():
    """Debug why reset doesn't work visually"""
    
    print("🔧 DEBUG RESET VISUAL ISSUE")
    print("=" * 60)
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    # Process events to ensure GUI is ready
    app.processEvents()
    time.sleep(1)
    
    # Find a STEP file to test with
    test_files = [
        "AMPHENOL_U77-A1118-200T.STEP",
        "test.step",
        "sample.stp"
    ]
    
    test_file = None
    for filename in test_files:
        if os.path.exists(filename):
            test_file = filename
            break
    
    if not test_file:
        print("❌ No STEP file found for testing")
        return False
    
    print(f"📁 Using test file: {test_file}")
    
    # Step 1: Load STEP file
    print(f"\n📋 STEP 1: LOADING {test_file}...")
    success = viewer.load_step_file_direct(test_file)
    app.processEvents()
    time.sleep(2)
    
    if not success:
        print("❌ Failed to load STEP file")
        return False
    
    renderer = viewer.vtk_renderer_left
    
    # Get initial state
    print(f"\n🔍 INITIAL STATE AFTER LOADING:")
    if hasattr(renderer, 'step_actors') and renderer.step_actors:
        for i, actor in enumerate(renderer.step_actors):
            pos = actor.GetPosition()
            orient = actor.GetOrientation()
            bounds = actor.GetBounds()
            print(f"  Multi-actor {i}: Pos={pos}, Orient={orient}")
            print(f"    Bounds: {bounds}")
    
    if hasattr(renderer, 'step_actor') and renderer.step_actor:
        pos = renderer.step_actor.GetPosition()
        orient = renderer.step_actor.GetOrientation()
        bounds = renderer.step_actor.GetBounds()
        print(f"  Single-actor: Pos={pos}, Orient={orient}")
        print(f"    Bounds: {bounds}")
    
    # Check original transforms storage
    print(f"\n🔍 ORIGINAL TRANSFORMS STORED:")
    if hasattr(viewer, 'original_actor_transforms_left'):
        print(f"  Stored transforms: {len(viewer.original_actor_transforms_left)}")
        for i, orig_state in enumerate(viewer.original_actor_transforms_left):
            print(f"    Original {i}:")
            print(f"      Position: {orig_state['position']}")
            print(f"      Orientation: {orig_state['orientation']}")
            print(f"      Transform: {orig_state['transform']}")
    else:
        print("  ❌ No original transforms stored!")
    
    print(f"\n👁️ INITIAL MODEL LOADED - Note the position (5 seconds)")
    time.sleep(5)
    
    # Step 2: Apply a LARGE, OBVIOUS transformation
    print(f"\n📋 STEP 2: APPLYING LARGE TRANSFORMATION...")
    viewer.active_viewer = "top"
    
    print("🔧 Applying LARGE movement: +100mm X")
    viewer.move_shape("x", 100)
    app.processEvents()
    time.sleep(1)
    
    print("🔧 Applying LARGE rotation: +90° Z")
    viewer.rotate_shape("z", 90)
    app.processEvents()
    time.sleep(1)
    
    # Check state after transformation
    print(f"\n🔍 STATE AFTER TRANSFORMATION:")
    if hasattr(renderer, 'step_actors') and renderer.step_actors:
        for i, actor in enumerate(renderer.step_actors):
            pos = actor.GetPosition()
            orient = actor.GetOrientation()
            bounds = actor.GetBounds()
            print(f"  Multi-actor {i}: Pos={pos}, Orient={orient}")
            print(f"    Bounds: {bounds}")
    
    if hasattr(renderer, 'step_actor') and renderer.step_actor:
        pos = renderer.step_actor.GetPosition()
        orient = renderer.step_actor.GetOrientation()
        bounds = renderer.step_actor.GetBounds()
        print(f"  Single-actor: Pos={pos}, Orient={orient}")
        print(f"    Bounds: {bounds}")
    
    print(f"\n👁️ MODEL AFTER TRANSFORMATION - Note the new position (5 seconds)")
    time.sleep(5)
    
    # Step 3: Manual reset test
    print(f"\n📋 STEP 3: MANUAL RESET TEST...")
    
    print("🔧 Manually resetting actors to (0,0,0) position and orientation...")
    
    # Manual reset of multi-actors
    if hasattr(renderer, 'step_actors') and renderer.step_actors:
        for i, actor in enumerate(renderer.step_actors):
            print(f"  Manually resetting multi-actor {i}")
            actor.SetPosition(0, 0, 0)
            actor.SetOrientation(0, 0, 0)
            actor.SetUserTransform(None)
            actor.Modified()
    
    # Manual reset of single-actor
    if hasattr(renderer, 'step_actor') and renderer.step_actor:
        print(f"  Manually resetting single-actor")
        renderer.step_actor.SetPosition(0, 0, 0)
        renderer.step_actor.SetOrientation(0, 0, 0)
        renderer.step_actor.SetUserTransform(None)
        renderer.step_actor.Modified()
    
    # Force render
    renderer.render_window.Render()
    app.processEvents()
    time.sleep(1)
    
    # Check state after manual reset
    print(f"\n🔍 STATE AFTER MANUAL RESET:")
    if hasattr(renderer, 'step_actors') and renderer.step_actors:
        for i, actor in enumerate(renderer.step_actors):
            pos = actor.GetPosition()
            orient = actor.GetOrientation()
            bounds = actor.GetBounds()
            print(f"  Multi-actor {i}: Pos={pos}, Orient={orient}")
            print(f"    Bounds: {bounds}")
    
    if hasattr(renderer, 'step_actor') and renderer.step_actor:
        pos = renderer.step_actor.GetPosition()
        orient = renderer.step_actor.GetOrientation()
        bounds = renderer.step_actor.GetBounds()
        print(f"  Single-actor: Pos={pos}, Orient={orient}")
        print(f"    Bounds: {bounds}")
    
    print(f"\n👁️ MODEL AFTER MANUAL RESET - Did it move back? (5 seconds)")
    time.sleep(5)
    
    # Step 4: Re-apply transformation and test GUI reset
    print(f"\n📋 STEP 4: RE-APPLY TRANSFORMATION AND TEST GUI RESET...")
    
    print("🔧 Re-applying transformation...")
    viewer.move_shape("x", 100)
    viewer.rotate_shape("z", 90)
    app.processEvents()
    time.sleep(2)
    
    print(f"\n👁️ MODEL RE-TRANSFORMED - Note position again (3 seconds)")
    time.sleep(3)
    
    print("🔧 Calling GUI reset_to_original()...")
    viewer.reset_to_original()
    app.processEvents()
    time.sleep(2)
    
    # Check final state
    print(f"\n🔍 STATE AFTER GUI RESET:")
    if hasattr(renderer, 'step_actors') and renderer.step_actors:
        for i, actor in enumerate(renderer.step_actors):
            pos = actor.GetPosition()
            orient = actor.GetOrientation()
            bounds = actor.GetBounds()
            print(f"  Multi-actor {i}: Pos={pos}, Orient={orient}")
            print(f"    Bounds: {bounds}")
    
    if hasattr(renderer, 'step_actor') and renderer.step_actor:
        pos = renderer.step_actor.GetPosition()
        orient = renderer.step_actor.GetOrientation()
        bounds = renderer.step_actor.GetBounds()
        print(f"  Single-actor: Pos={pos}, Orient={orient}")
        print(f"    Bounds: {bounds}")
    
    print(f"\n👁️ MODEL AFTER GUI RESET - Did it move back? (10 seconds)")
    time.sleep(10)
    
    # Step 5: Analysis
    print(f"\n📋 STEP 5: ANALYSIS")
    print("=" * 60)
    
    print(f"🔍 KEY QUESTIONS:")
    print(f"1. Did the manual reset (Step 3) move the model back visually?")
    print(f"2. Did the GUI reset (Step 4) move the model back visually?")
    print(f"3. Are the actor positions/orientations actually changing in the debug output?")
    print(f"4. Are the bounds changing when transformations are applied?")
    
    print(f"\n🎯 DIAGNOSIS:")
    print(f"- If manual reset worked but GUI reset didn't: GUI reset logic issue")
    print(f"- If neither worked: VTK rendering or actor issue")
    print(f"- If positions change but model doesn't move: Camera or display issue")
    print(f"- If bounds don't change: Transformations not being applied")
    
    # Keep window open
    print(f"\n👁️ Window will stay open for final inspection...")
    QTimer.singleShot(15000, app.quit)
    app.exec_()
    
    return True

if __name__ == "__main__":
    debug_reset_visual_issue()
