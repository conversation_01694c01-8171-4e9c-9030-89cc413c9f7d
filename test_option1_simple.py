#!/usr/bin/env python3
"""
Simple test for Option 1 save - just test the save method directly
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

# Import the main program
sys.path.append('.')
from step_viewer_tdk_modular import Step<PERSON>iewerTDK

def test_option1_save():
    """Test Option 1 save method directly"""
    print("TESTING OPTION 1 SAVE METHOD DIRECTLY")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    
    # Load test file
    print("1. Loading test.step...")
    success = viewer.load_step_file_direct('test.step')
    if not success:
        print("❌ Failed to load test.step")
        return
    
    print("✅ test.step loaded")
    
    # Set up test values
    loader = viewer.step_loader_left
    current_pos = {'x': 0.0, 'y': 0.0, 'z': 0.0}
    current_rot = {'x': 0.0, 'y': 0.0, 'z': 174.0}  # Test rotation
    orig_pos = {'x': 0.0, 'y': 0.0, 'z': 0.0}
    orig_rot = {'x': 0.0, 'y': 0.0, 'z': 0.0}
    
    print(f"2. Test values:")
    print(f"   Current rotation: {current_rot}")
    print(f"   Original rotation: {orig_rot}")
    
    # Test the save method directly
    test_filename = "test_option1_direct.step"
    
    print(f"3. Calling _save_step_with_transformations...")
    try:
        success = viewer._save_step_with_transformations(
            test_filename, loader, current_pos, current_rot, orig_pos, orig_rot
        )
        
        if success and os.path.exists(test_filename):
            file_size = os.path.getsize(test_filename)
            print(f"✅ SUCCESS: {test_filename} created ({file_size} bytes)")
            
            # Quick check if it's a STEP file
            with open(test_filename, 'r') as f:
                first_line = f.readline().strip()
            
            if first_line.startswith('ISO-10303'):
                print(f"✅ File is a valid STEP file")
            else:
                print(f"❌ File is NOT a STEP file, first line: {first_line}")
                
        else:
            print(f"❌ FAILED: File not created or save method returned False")
            
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")
        import traceback
        traceback.print_exc()
    
    app.quit()

if __name__ == "__main__":
    test_option1_save()
