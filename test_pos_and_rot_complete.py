#!/usr/bin/env python3
"""
Complete test showing BOTH position (X,Y,Z) AND rotation transformations:
1. Original POS/ROT in STEP file
2. Apply BOTH position translation AND rotation
3. What POS/ROT gets written after transformation
4. What POS/ROT gets read back from saved file
5. Verification they match
"""

import re
import numpy as np
from simple_step_modifier import SimpleSTEPModifier
import vtk
import math

def extract_pos_rot_from_step(filename):
    """Extract POS and ROT values from STEP file root coordinate system"""
    print(f"🔍 EXTRACTING POS/ROT FROM: {filename}")
    
    with open(filename, 'r') as f:
        content = f.read()
    
    # Extract position from #12 (root CARTESIAN_POINT)
    point_match = re.search(r'#12\s*=\s*CARTESIAN_POINT\s*\([^;]+\)\s*;', content)
    position = [0.0, 0.0, 0.0]
    if point_match:
        point_line = point_match.group(0)
        coords_match = re.search(r'\(\s*[^,]+,\s*\(\s*([^,]+),\s*([^,]+),\s*([^)]+)\s*\)', point_line)
        if coords_match:
            position = [float(coords_match.group(1)), float(coords_match.group(2)), float(coords_match.group(3))]
    
    # Extract Z direction from #13
    z_dir_match = re.search(r'#13\s*=\s*DIRECTION\s*\([^;]+\)\s*;', content)
    z_direction = [0.0, 0.0, 1.0]
    if z_dir_match:
        z_line = z_dir_match.group(0)
        z_coords_match = re.search(r'\(\s*[^,]+,\s*\(\s*([^,]+),\s*([^,]+),\s*([^)]+)\s*\)', z_line)
        if z_coords_match:
            z_direction = [float(z_coords_match.group(1)), float(z_coords_match.group(2)), float(z_coords_match.group(3))]
    
    # Extract X direction from #14
    x_dir_match = re.search(r'#14\s*=\s*DIRECTION\s*\([^;]+\)\s*;', content)
    x_direction = [1.0, 0.0, 0.0]
    if x_dir_match:
        x_line = x_dir_match.group(0)
        x_coords_match = re.search(r'\(\s*[^,]+,\s*\(\s*([^,]+),\s*([^,]+),\s*([^)]+)\s*\)', x_line)
        if x_coords_match:
            x_direction = [float(x_coords_match.group(1)), float(x_coords_match.group(2)), float(x_coords_match.group(3))]
    
    # Convert direction vectors to Euler angles (rotation)
    x_vec = np.array(x_direction)
    z_vec = np.array(z_direction)
    
    # Calculate Y vector as cross product of Z x X
    y_vec = np.cross(z_vec, x_vec)
    
    # Create rotation matrix from direction vectors
    rotation_matrix = np.array([
        [x_vec[0], y_vec[0], z_vec[0]],
        [x_vec[1], y_vec[1], z_vec[1]],
        [x_vec[2], y_vec[2], z_vec[2]]
    ])
    
    # Convert rotation matrix to Euler angles (XYZ order)
    def matrix_to_euler_xyz(R):
        """Convert rotation matrix to Euler angles (XYZ order) in degrees"""
        sy = math.sqrt(R[0,0] * R[0,0] + R[1,0] * R[1,0])
        
        singular = sy < 1e-6
        
        if not singular:
            x = math.atan2(R[2,1], R[2,2])
            y = math.atan2(-R[2,0], sy)
            z = math.atan2(R[1,0], R[0,0])
        else:
            x = math.atan2(-R[1,2], R[1,1])
            y = math.atan2(-R[2,0], sy)
            z = 0
        
        return [math.degrees(x), math.degrees(y), math.degrees(z)]
    
    rotation = matrix_to_euler_xyz(rotation_matrix)
    
    return {
        'position': position,
        'rotation': rotation,
        'raw_vectors': {
            'x_direction': x_direction,
            'z_direction': z_direction
        }
    }

def show_step_file_lines(filename, title):
    """Show the exact lines from STEP file for root coordinate system"""
    print(f"\n📄 {title}: {filename}")
    print("-" * 60)
    
    with open(filename, 'r') as f:
        content = f.read()
    
    lines = content.split('\n')
    
    for i, line in enumerate(lines):
        if '#12 = CARTESIAN_POINT' in line:
            print(f"   Line {i+1}: {line}")
        elif '#13 = DIRECTION' in line:
            print(f"   Line {i+1}: {line}")
        elif '#14 = DIRECTION' in line:
            print(f"   Line {i+1}: {line}")

def main():
    print("=" * 80)
    print("🔧 COMPLETE POSITION AND ROTATION TEST")
    print("=" * 80)
    
    original_file = "test.step"
    test_file = "complete_pos_rot_test.step"
    
    # STEP 1: Show original POS/ROT
    print("📋 STEP 1: ORIGINAL POS/ROT VALUES")
    print("-" * 60)
    
    original_data = extract_pos_rot_from_step(original_file)
    
    print(f"📊 ORIGINAL POS/ROT:")
    print(f"   POS: X={original_data['position'][0]:8.3f}mm Y={original_data['position'][1]:8.3f}mm Z={original_data['position'][2]:8.3f}mm")
    print(f"   ROT: X={original_data['rotation'][0]:8.3f}° Y={original_data['rotation'][1]:8.3f}° Z={original_data['rotation'][2]:8.3f}°")
    
    show_step_file_lines(original_file, "ORIGINAL STEP FILE LINES")
    
    # STEP 2: Apply BOTH position translation AND rotation
    print(f"\n📋 STEP 2: APPLYING POSITION TRANSLATION + ROTATION")
    print("-" * 60)
    
    # Define test transformation: Move to (10, 20, 5) and rotate 30° around Z
    test_pos_x = 10.0
    test_pos_y = 20.0
    test_pos_z = 5.0
    test_rot_x = 0.0
    test_rot_y = 0.0
    test_rot_z = 30.0
    
    print(f"🔧 Input transformation:")
    print(f"   Position: X={test_pos_x:.1f}mm Y={test_pos_y:.1f}mm Z={test_pos_z:.1f}mm")
    print(f"   Rotation: X={test_rot_x:.1f}° Y={test_rot_y:.1f}° Z={test_rot_z:.1f}°")
    print(f"🔧 Expected result:")
    print(f"   POS should become: X={test_pos_x:.1f}mm Y={test_pos_y:.1f}mm Z={test_pos_z:.1f}mm")
    print(f"   ROT should become: X={test_rot_x:.1f}° Y={test_rot_y:.1f}° Z={test_rot_z:.1f}°")
    
    modifier = SimpleSTEPModifier()
    modifier.load_step_file(original_file)
    
    # Create transformation matrix with BOTH translation and rotation
    transform = vtk.vtkTransform()
    transform.Translate(test_pos_x, test_pos_y, test_pos_z)  # First translate
    transform.RotateX(test_rot_x)  # Then rotate
    transform.RotateY(test_rot_y)
    transform.RotateZ(test_rot_z)
    matrix = transform.GetMatrix()
    
    # Transform geometry
    print(f"🔧 Applying geometry transformation...")
    modifier.transform_geometry_coordinates(matrix)
    
    # Set coordinate system with BOTH position and rotation
    print(f"🔧 Setting coordinate system...")
    modifier.modify_placement(test_pos_x, test_pos_y, test_pos_z, test_rot_x, test_rot_y, test_rot_z)
    
    # Save file
    modifier.save_step_file(test_file)
    
    # STEP 3: Show what was written
    print(f"\n📋 STEP 3: WHAT WAS WRITTEN TO SAVED FILE")
    print("-" * 60)
    
    saved_data = extract_pos_rot_from_step(test_file)
    
    print(f"📊 SAVED POS/ROT:")
    print(f"   POS: X={saved_data['position'][0]:8.3f}mm Y={saved_data['position'][1]:8.3f}mm Z={saved_data['position'][2]:8.3f}mm")
    print(f"   ROT: X={saved_data['rotation'][0]:8.3f}° Y={saved_data['rotation'][1]:8.3f}° Z={saved_data['rotation'][2]:8.3f}°")
    
    show_step_file_lines(test_file, "SAVED STEP FILE LINES")
    
    # STEP 4: Read back verification
    print(f"\n📋 STEP 4: READING BACK FROM SAVED FILE")
    print("-" * 60)
    
    readback_data = extract_pos_rot_from_step(test_file)
    
    print(f"📊 READ BACK POS/ROT:")
    print(f"   POS: X={readback_data['position'][0]:8.3f}mm Y={readback_data['position'][1]:8.3f}mm Z={readback_data['position'][2]:8.3f}mm")
    print(f"   ROT: X={readback_data['rotation'][0]:8.3f}° Y={readback_data['rotation'][1]:8.3f}° Z={readback_data['rotation'][2]:8.3f}°")
    
    # STEP 5: Verification
    print(f"\n📋 STEP 5: VERIFICATION - DO ALL VALUES MATCH?")
    print("-" * 60)
    
    print(f"📊 COMPLETE COMPARISON:")
    print(f"   ORIGINAL POS: X={original_data['position'][0]:8.3f}mm Y={original_data['position'][1]:8.3f}mm Z={original_data['position'][2]:8.3f}mm")
    print(f"   SAVED    POS: X={saved_data['position'][0]:8.3f}mm Y={saved_data['position'][1]:8.3f}mm Z={saved_data['position'][2]:8.3f}mm")
    print(f"   READ BACK POS: X={readback_data['position'][0]:8.3f}mm Y={readback_data['position'][1]:8.3f}mm Z={readback_data['position'][2]:8.3f}mm")
    print()
    print(f"   ORIGINAL ROT: X={original_data['rotation'][0]:8.3f}° Y={original_data['rotation'][1]:8.3f}° Z={original_data['rotation'][2]:8.3f}°")
    print(f"   SAVED    ROT: X={saved_data['rotation'][0]:8.3f}° Y={saved_data['rotation'][1]:8.3f}° Z={saved_data['rotation'][2]:8.3f}°")
    print(f"   READ BACK ROT: X={readback_data['rotation'][0]:8.3f}° Y={readback_data['rotation'][1]:8.3f}° Z={readback_data['rotation'][2]:8.3f}°")
    
    # Expected values
    expected_pos = [test_pos_x, test_pos_y, test_pos_z]
    expected_rot = [test_rot_x, test_rot_y, test_rot_z]
    
    print(f"\n📊 EXPECTED VALUES:")
    print(f"   EXPECTED POS: X={expected_pos[0]:8.3f}mm Y={expected_pos[1]:8.3f}mm Z={expected_pos[2]:8.3f}mm")
    print(f"   EXPECTED ROT: X={expected_rot[0]:8.3f}° Y={expected_rot[1]:8.3f}° Z={expected_rot[2]:8.3f}°")
    
    # Check matches
    pos_match = np.allclose(readback_data['position'], expected_pos, atol=1e-3)
    rot_match = np.allclose(readback_data['rotation'], expected_rot, atol=1.0)  # 1 degree tolerance
    
    print(f"\n🔍 MATCH VERIFICATION:")
    print(f"   Position matches expected: {'✅ YES' if pos_match else '❌ NO'}")
    print(f"   Rotation matches expected: {'✅ YES' if rot_match else '❌ NO'}")
    
    # Check if saved and read back match
    pos_consistent = np.allclose(saved_data['position'], readback_data['position'], atol=1e-6)
    rot_consistent = np.allclose(saved_data['rotation'], readback_data['rotation'], atol=1e-3)
    
    print(f"   Saved/ReadBack consistent: {'✅ YES' if (pos_consistent and rot_consistent) else '❌ NO'}")
    
    # Show the transformation magnitude
    pos_change = np.linalg.norm(np.array(readback_data['position']) - np.array(original_data['position']))
    rot_change = np.linalg.norm(np.array(readback_data['rotation']) - np.array(original_data['rotation']))
    
    print(f"\n📊 TRANSFORMATION MAGNITUDE:")
    print(f"   Position change: {pos_change:.3f}mm")
    print(f"   Rotation change: {rot_change:.3f}°")
    
    if pos_match and rot_match and pos_consistent and rot_consistent:
        print(f"\n🎉 PERFECT SUCCESS: BOTH POSITION AND ROTATION WORK!")
        print(f"   ✅ Original POS/ROT were read correctly")
        print(f"   ✅ Position translation was applied and written correctly")
        print(f"   ✅ Rotation transformation was applied and written correctly")
        print(f"   ✅ Saved POS/ROT were read back correctly")
        print(f"   ✅ All values match expected transformation")
        print(f"   ✅ The complete coordinate transformation system is working!")
    else:
        print(f"\n❌ ISSUES DETECTED:")
        if not pos_match:
            print(f"   ❌ Position doesn't match expected values")
            print(f"      Expected: {expected_pos}")
            print(f"      Got:      {readback_data['position']}")
        if not rot_match:
            print(f"   ❌ Rotation doesn't match expected values")
            print(f"      Expected: {expected_rot}")
            print(f"      Got:      {readback_data['rotation']}")
        if not pos_consistent:
            print(f"   ❌ Position not consistent between save/read")
        if not rot_consistent:
            print(f"   ❌ Rotation not consistent between save/read")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
