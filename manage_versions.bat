@echo off
echo ========================================
echo    STEP Viewer Version Manager
echo ========================================
echo.

:MENU
echo Available version groups in E:\Python\save:
echo.

REM Change to save directory and list versions
pushd E:\Python\save
for /f "tokens=2 delims=_" %%i in ('dir /b *_rev*.py 2^>nul ^| findstr /r "_rev[0-9]*\.py$"') do (
    for /f "tokens=1 delims=." %%j in ("%%i") do (
        echo   %%j
    )
)
popd

echo.
echo Enter the revision number you want to restore (e.g., rev13):
set /p REVISION=

if "%REVISION%"=="" (
    echo No revision specified. Exiting.
    pause
    exit /b
)

echo.
echo Files found for %REVISION%:
pushd E:\Python\save
dir /b *_%REVISION%.py 2>nul
if errorlevel 1 (
    echo No files found for %REVISION%
    popd
    pause
    goto MENU
)
popd

echo.
echo Do you want to copy these files to E:\Python\3d-models? (Y/N)
set /p CONFIRM=

if /i "%CONFIRM%"=="Y" (
    echo.
    echo Copying files...
    
    pushd E:\Python\save
    REM Copy each file, removing the revision suffix
    for %%f in (*_%REVISION%.py) do (
        set "filename=%%f"
        setlocal enabledelayedexpansion
        set "newname=!filename:_%REVISION%=!"
        echo Copying %%f to !newname!
        copy "%%f" "E:\Python\3d-models\!newname!" >nul
        endlocal
    )
    popd
    
    echo.
    echo ✅ Files copied successfully!
    echo You can now test the restored version.
    echo.
) else (
    echo Copy cancelled.
)

echo.
echo Press any key to return to menu or Ctrl+C to exit...
pause >nul
goto MENU
