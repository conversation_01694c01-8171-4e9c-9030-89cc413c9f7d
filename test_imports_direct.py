#!/usr/bin/env python3

import sys

print("Testing imports directly...")

try:
    print("Testing BRepMesh import...")
    from OCC.Core.BRepMesh import BRepMesh_IncrementalMesh
    print("BRepMesh import OK")
    
    print("Testing other OCC imports...")
    from OCC.Core.TopExp import TopExp_Explorer
    from OCC.Core.TopAbs import TopAbs_FACE
    from OCC.Core.BRep import BR<PERSON>_Tool
    from OCC.Core.TopLoc import TopLoc_Location
    print("All OCC imports OK")
    
    print("Testing STEPControl imports...")
    from OCC.Core.STEPControl import STEP<PERSON>ontrol_Reader
    from OCC.Core.IFSelect import IFSelect_RetDone
    print("STEPControl imports OK")
    
    print("All imports successful!")
    
except Exception as e:
    print(f"Import failed: {e}")
    import traceback
    traceback.print_exc()

print("Import test completed")
