#!/usr/bin/env python3

import sys
import time
from PyQt5.QtWidgets import <PERSON>A<PERSON><PERSON>, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QLabel
from PyQt5.QtCore import QTimer
from step_viewer_tdk_modular import StepViewerTDK

class CoordinateSystemDebugger(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Coordinate System Debugger")
        self.setGeometry(100, 100, 800, 600)
        
        # Create main widget and layout
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QVBoxLayout(main_widget)
        
        # Create the viewer
        self.viewer = StepViewerTDK()
        layout.addWidget(self.viewer)
        
        # Create debug controls
        controls_layout = QHBoxLayout()
        
        # Load button
        btn_load = QPushButton("Load test.step")
        btn_load.clicked.connect(self.load_test_file)
        controls_layout.addWidget(btn_load)
        
        # Position test buttons
        btn_x_plus = QPushButton("X+ (should increase X)")
        btn_x_plus.clicked.connect(lambda: self.test_move('x', 1.0))
        controls_layout.addWidget(btn_x_plus)
        
        btn_x_minus = QPushButton("X- (should decrease X)")
        btn_x_minus.clicked.connect(lambda: self.test_move('x', -1.0))
        controls_layout.addWidget(btn_x_minus)
        
        btn_y_plus = QPushButton("Y+ (should increase Y)")
        btn_y_plus.clicked.connect(lambda: self.test_move('y', 1.0))
        controls_layout.addWidget(btn_y_plus)
        
        btn_y_minus = QPushButton("Y- (should decrease Y)")
        btn_y_minus.clicked.connect(lambda: self.test_move('y', -1.0))
        controls_layout.addWidget(btn_y_minus)
        
        layout.addLayout(controls_layout)
        
        # Status display
        self.status_label = QLabel("Ready")
        layout.addWidget(self.status_label)
        
    def load_test_file(self):
        """Load the test STEP file"""
        self.viewer.active_viewer = "top"
        success = self.viewer.load_step_file_direct("test.step")
        if success:
            self.status_label.setText("✅ test.step loaded successfully")
            # Wait a moment then show initial values
            QTimer.singleShot(1000, self.show_initial_values)
        else:
            self.status_label.setText("❌ Failed to load test.step")
    
    def show_initial_values(self):
        """Show the initial position values"""
        if hasattr(self.viewer, 'current_pos_left'):
            pos = self.viewer.current_pos_left
            self.status_label.setText(f"Initial position: X={pos['x']:.3f}, Y={pos['y']:.3f}, Z={pos['z']:.3f}")
        else:
            self.status_label.setText("No position data available")
    
    def test_move(self, axis, amount):
        """Test a movement and show before/after values"""
        # Get position before
        if hasattr(self.viewer, 'current_pos_left'):
            pos_before = self.viewer.current_pos_left.copy()
        else:
            pos_before = {'x': 0, 'y': 0, 'z': 0}
        
        print(f"\n🔧 COORDINATE TEST: Moving {axis} by {amount}")
        print(f"   Before: X={pos_before['x']:.3f}, Y={pos_before['y']:.3f}, Z={pos_before['z']:.3f}")
        
        # Set active viewer and move
        self.viewer.active_viewer = "top"
        self.viewer.move_shape(axis, amount)
        
        # Allow time for updates
        QApplication.processEvents()
        time.sleep(0.1)
        
        # Get position after
        if hasattr(self.viewer, 'current_pos_left'):
            pos_after = self.viewer.current_pos_left.copy()
        else:
            pos_after = {'x': 0, 'y': 0, 'z': 0}
        
        print(f"   After:  X={pos_after['x']:.3f}, Y={pos_after['y']:.3f}, Z={pos_after['z']:.3f}")
        
        # Calculate actual change
        actual_change = pos_after[axis] - pos_before[axis]
        expected_change = amount
        
        print(f"   Expected change in {axis.upper()}: {expected_change}")
        print(f"   Actual change in {axis.upper()}: {actual_change}")
        
        if abs(actual_change - expected_change) < 0.01:
            result = "✅ CORRECT"
        else:
            result = "❌ WRONG"
        
        print(f"   Result: {result}")
        
        # Update status
        self.status_label.setText(f"{axis.upper()}{'+' if amount > 0 else '-'}: Expected {expected_change}, Got {actual_change} - {result}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    debugger = CoordinateSystemDebugger()
    debugger.show()
    sys.exit(app.exec_())
