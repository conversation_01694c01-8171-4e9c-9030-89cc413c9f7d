#!/usr/bin/env python3
"""
Script to remove all debug print statements from step_viewer_tdk_modular.py
"""

import re

def remove_debug_prints(filename):
    """Remove all debug print statements from the file"""
    
    with open(filename, 'r', encoding='utf-8', errors='replace') as f:
        content = f.read()
    
    # Remove lines that contain debug print statements
    lines = content.split('\n')
    cleaned_lines = []
    
    for line in lines:
        # Skip lines that contain debug print statements
        if 'print(' in line and any(keyword in line.upper() for keyword in [
            'DEBUG', 'TEMP DEBUG', 'DEEP DEBUG', 'FORCE DEBUG'
        ]):
            continue
        
        # Skip lines with emoji characters that cause encoding issues
        if any(emoji in line for emoji in ['🎯', '🔧', '✅', '🔍', '⚠️', '🔴', '🔵']):
            continue
            
        cleaned_lines.append(line)
    
    # Write the cleaned content back
    cleaned_content = '\n'.join(cleaned_lines)
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(cleaned_content)
    
    print(f"Removed debug prints from {filename}")
    print(f"Original lines: {len(lines)}")
    print(f"Cleaned lines: {len(cleaned_lines)}")
    print(f"Removed: {len(lines) - len(cleaned_lines)} lines")

if __name__ == "__main__":
    remove_debug_prints('step_viewer_tdk_modular.py')
