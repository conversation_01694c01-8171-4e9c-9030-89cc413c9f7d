#!/usr/bin/env python3
"""
Detailed Reset Debug - Find out exactly why the visual reset isn't working
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular import StepViewerTDK

def print_detailed_actor_state(viewer, label=""):
    """Print extremely detailed actor state"""
    print(f"\n🔍 DETAILED ACTOR STATE - {label}")
    print("=" * 60)
    
    renderer = viewer.vtk_renderer_left
    
    # Check multi-actor models
    if hasattr(renderer, 'step_actors') and renderer.step_actors:
        print(f"📊 Multi-actor model: {len(renderer.step_actors)} actors")
        for i, actor in enumerate(renderer.step_actors):
            pos = actor.GetPosition()
            orient = actor.GetOrientation()
            transform = actor.GetUserTransform()
            matrix = actor.GetMatrix()
            
            print(f"   🎭 Actor {i}:")
            print(f"     Position: {pos}")
            print(f"     Orientation: {orient}")
            print(f"     UserTransform: {transform}")
            print(f"     Actor Matrix: {matrix}")
            print(f"     Visibility: {actor.GetVisibility()}")
            print(f"     Modified Time: {actor.GetMTime()}")
            
            if transform:
                transform_matrix = transform.GetMatrix()
                print(f"     Transform Matrix: {transform_matrix}")
    
    # Check single-actor model
    if hasattr(renderer, 'step_actor') and renderer.step_actor:
        print(f"📊 Single-actor model")
        actor = renderer.step_actor
        pos = actor.GetPosition()
        orient = actor.GetOrientation()
        transform = actor.GetUserTransform()
        matrix = actor.GetMatrix()
        
        print(f"   🎭 Single Actor:")
        print(f"     Position: {pos}")
        print(f"     Orientation: {orient}")
        print(f"     UserTransform: {transform}")
        print(f"     Actor Matrix: {matrix}")
        print(f"     Visibility: {actor.GetVisibility()}")
        print(f"     Modified Time: {actor.GetMTime()}")
        
        if transform:
            transform_matrix = transform.GetMatrix()
            print(f"     Transform Matrix: {transform_matrix}")
    
    # Check bounding box
    if hasattr(renderer, 'bounding_box_actor') and renderer.bounding_box_actor:
        bbox_actor = renderer.bounding_box_actor
        bbox_pos = bbox_actor.GetPosition()
        bbox_orient = bbox_actor.GetOrientation()
        print(f"📦 Bounding Box Actor:")
        print(f"     Position: {bbox_pos}")
        print(f"     Orientation: {bbox_orient}")
        print(f"     Visibility: {bbox_actor.GetVisibility()}")
    
    # Check display values
    print(f"📊 Display Values:")
    print(f"   Position: {viewer.current_pos_left}")
    print(f"   Rotation: {viewer.current_rot_left}")
    
    # Check original transforms storage
    if hasattr(viewer, 'original_actor_transforms_left'):
        print(f"📊 Original Transforms Stored: {len(viewer.original_actor_transforms_left)}")
        for i, orig_state in enumerate(viewer.original_actor_transforms_left):
            print(f"   Original {i}:")
            print(f"     Position: {orig_state['position']}")
            print(f"     Orientation: {orig_state['orientation']}")
            print(f"     Transform: {orig_state['transform']}")

def debug_reset_step_by_step():
    """Debug reset with step-by-step analysis"""
    
    print("🔧 STEP-BY-STEP RESET DEBUG")
    print("=" * 60)
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    # Process events to ensure GUI is ready
    app.processEvents()
    time.sleep(1)
    
    # Find a STEP file to test with
    test_files = [
        "AMPHENOL_U77-A1118-200T.STEP",
        "test.step",
        "sample.stp"
    ]
    
    test_file = None
    for filename in test_files:
        if os.path.exists(filename):
            test_file = filename
            break
    
    if not test_file:
        print("❌ No STEP file found for testing")
        return False
    
    print(f"📁 Using test file: {test_file}")
    
    # Step 1: Load STEP file
    print(f"\n📋 STEP 1: LOADING {test_file}...")
    success = viewer.load_step_file_direct(test_file)
    app.processEvents()
    time.sleep(2)
    
    if not success:
        print("❌ Failed to load STEP file")
        return False
    
    print_detailed_actor_state(viewer, "AFTER LOADING")
    
    # Step 2: Apply ONE transformation to see what happens
    print(f"\n📋 STEP 2: APPLYING SINGLE TRANSFORMATION...")
    print("🔧 Applying 45° Z rotation...")
    
    viewer.active_viewer = "top"
    
    # Get BEFORE state
    renderer = viewer.vtk_renderer_left
    if hasattr(renderer, 'step_actors') and renderer.step_actors:
        before_pos = renderer.step_actors[0].GetPosition()
        before_orient = renderer.step_actors[0].GetOrientation()
        print(f"BEFORE rotation: Pos={before_pos}, Orient={before_orient}")
    
    # Apply rotation
    viewer.rotate_shape("z", 45)
    app.processEvents()
    time.sleep(1)
    
    # Get AFTER state
    if hasattr(renderer, 'step_actors') and renderer.step_actors:
        after_pos = renderer.step_actors[0].GetPosition()
        after_orient = renderer.step_actors[0].GetOrientation()
        print(f"AFTER rotation: Pos={after_pos}, Orient={after_orient}")
        
        # Check if transformation actually happened
        if after_orient[2] != before_orient[2]:
            print("✅ Rotation was applied successfully!")
        else:
            print("❌ Rotation was NOT applied!")
    
    print_detailed_actor_state(viewer, "AFTER ROTATION")
    
    # Step 3: Manual reset test
    print(f"\n📋 STEP 3: MANUAL RESET TEST...")
    
    # Try manual reset
    if hasattr(renderer, 'step_actors') and renderer.step_actors:
        actor = renderer.step_actors[0]
        print("🔧 Manually setting actor to (0,0,0) position and orientation...")
        
        # Store current state
        current_pos = actor.GetPosition()
        current_orient = actor.GetOrientation()
        print(f"Current before manual reset: Pos={current_pos}, Orient={current_orient}")
        
        # Manual reset
        actor.SetPosition(0, 0, 0)
        actor.SetOrientation(0, 0, 0)
        actor.Modified()
        
        # Force render
        renderer.render_window.Render()
        app.processEvents()
        time.sleep(1)
        
        # Check result
        new_pos = actor.GetPosition()
        new_orient = actor.GetOrientation()
        print(f"After manual reset: Pos={new_pos}, Orient={new_orient}")
        
        if new_pos == (0.0, 0.0, 0.0) and new_orient == (0.0, 0.0, 0.0):
            print("✅ Manual reset worked!")
        else:
            print("❌ Manual reset failed!")
    
    print_detailed_actor_state(viewer, "AFTER MANUAL RESET")
    
    # Step 4: Test the actual reset button
    print(f"\n📋 STEP 4: TESTING ACTUAL RESET BUTTON...")
    
    # Re-apply transformation
    viewer.rotate_shape("z", 45)
    app.processEvents()
    time.sleep(1)
    
    print("🔧 Calling viewer.reset_to_original()...")
    viewer.reset_to_original()
    app.processEvents()
    time.sleep(2)
    
    print_detailed_actor_state(viewer, "AFTER RESET BUTTON")
    
    # Keep window open for visual inspection
    print(f"\n👁️ Window will stay open for 15 seconds for visual inspection...")
    print("🔍 Check if you can see any visual changes!")
    
    QTimer.singleShot(15000, app.quit)
    app.exec_()
    
    return True

if __name__ == "__main__":
    debug_reset_step_by_step()
