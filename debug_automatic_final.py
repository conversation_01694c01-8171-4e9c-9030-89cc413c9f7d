#!/usr/bin/env python3
"""
Debug Automatic Final - Automatically find and fix the reset issue
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular import Step<PERSON>iewerTDK

def debug_automatic_final():
    """Automatically debug and fix the reset issue"""
    
    print("🔧 DEBUG AUTOMATIC FINAL - FIND AND FIX RESET ISSUE")
    print("=" * 70)
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    # Process events to ensure GUI is ready
    app.processEvents()
    time.sleep(1)
    
    # Find a STEP file to test with
    test_files = [
        "AMPHENOL_U77-A1118-200T.STEP",
        "test.step",
        "sample.stp"
    ]
    
    test_file = None
    for filename in test_files:
        if os.path.exists(filename):
            test_file = filename
            break
    
    if not test_file:
        print("❌ No STEP file found for testing")
        return False
    
    print(f"📁 Using test file: {test_file}")
    
    # Step 1: Load STEP file
    print(f"\n📋 STEP 1: LOADING AND ANALYZING...")
    success = viewer.load_step_file_direct(test_file)
    app.processEvents()
    time.sleep(2)
    
    if not success:
        print("❌ Failed to load STEP file")
        return False
    
    renderer = viewer.vtk_renderer_left
    
    # Get all actors
    all_actors = []
    if hasattr(renderer, 'renderer') and renderer.renderer:
        actor_collection = renderer.renderer.GetActors()
        actor_collection.InitTraversal()
        
        actor = actor_collection.GetNextActor()
        while actor:
            all_actors.append(actor)
            actor = actor_collection.GetNextActor()
    
    print(f"✅ Found {len(all_actors)} actors in renderer")
    
    # Identify actors
    multi_actors = []
    bbox_actor = None
    single_actor = None
    
    for i, actor in enumerate(all_actors):
        visible = actor.GetVisibility()
        pos = actor.GetPosition()
        orient = actor.GetOrientation()
        
        print(f"Actor {i}: Visible={visible}, Pos={pos}, Orient={orient}")
        
        # Identify actor types
        if hasattr(renderer, 'step_actors') and renderer.step_actors:
            for j, multi_actor in enumerate(renderer.step_actors):
                if multi_actor == actor:
                    multi_actors.append(actor)
                    print(f"  *** MULTI-ACTOR {j} ***")
                    
        if hasattr(renderer, 'bbox_actor') and renderer.bbox_actor == actor:
            bbox_actor = actor
            print(f"  *** BOUNDING BOX ACTOR ***")
            
        if hasattr(renderer, 'step_actor') and renderer.step_actor == actor:
            single_actor = actor
            print(f"  *** SINGLE-ACTOR ***")
    
    # Step 2: Test GUI transformations
    print(f"\n📋 STEP 2: TESTING GUI TRANSFORMATIONS...")
    
    viewer.active_viewer = "top"
    
    # Store initial bounds for comparison
    initial_bounds = {}
    for i, actor in enumerate(all_actors):
        if actor.GetVisibility():
            initial_bounds[i] = actor.GetBounds()
    
    print(f"🔧 Applying GUI transformation: +100mm X")
    viewer.move_shape("x", 100)
    app.processEvents()
    time.sleep(1)
    
    # Check which actors actually moved
    moved_actors = []
    for i, actor in enumerate(all_actors):
        if actor.GetVisibility():
            new_bounds = actor.GetBounds()
            if new_bounds != initial_bounds[i]:
                moved_actors.append(i)
                print(f"✅ Actor {i} bounds changed: {initial_bounds[i]} → {new_bounds}")
            else:
                print(f"❌ Actor {i} bounds unchanged: {new_bounds}")
    
    print(f"🔧 Applying GUI rotation: +90° Z")
    viewer.rotate_shape("z", 90)
    app.processEvents()
    time.sleep(1)
    
    # Check final positions
    print(f"\n🔍 FINAL POSITIONS AFTER GUI TRANSFORMATIONS:")
    for i, actor in enumerate(all_actors):
        if actor.GetVisibility():
            pos = actor.GetPosition()
            orient = actor.GetOrientation()
            bounds = actor.GetBounds()
            print(f"Actor {i}: Pos={pos}, Orient={orient}")
            print(f"  Bounds: {bounds}")
    
    # Step 3: Test GUI reset
    print(f"\n📋 STEP 3: TESTING GUI RESET...")
    
    print(f"🔧 Calling GUI reset...")
    viewer.reset_to_original()
    app.processEvents()
    time.sleep(2)
    
    # Check positions after reset
    print(f"\n🔍 POSITIONS AFTER GUI RESET:")
    reset_worked = True
    for i, actor in enumerate(all_actors):
        if actor.GetVisibility():
            pos = actor.GetPosition()
            orient = actor.GetOrientation()
            bounds = actor.GetBounds()
            print(f"Actor {i}: Pos={pos}, Orient={orient}")
            print(f"  Bounds: {bounds}")
            
            # Check if reset worked (position should be close to 0,0,0)
            if abs(pos[0]) > 0.1 or abs(pos[1]) > 0.1 or abs(orient[2]) > 0.1:
                reset_worked = False
                print(f"❌ Actor {i} NOT reset properly")
            else:
                print(f"✅ Actor {i} reset correctly")
    
    # Step 4: Analysis and fix
    print(f"\n📋 STEP 4: ANALYSIS AND FIX")
    print("=" * 70)
    
    print(f"🎯 FINDINGS:")
    print(f"Total actors: {len(all_actors)}")
    print(f"Multi-actors: {len(multi_actors)}")
    print(f"Bounding box actor: {'Yes' if bbox_actor else 'No'}")
    print(f"Single actor: {'Yes' if single_actor else 'No'}")
    print(f"Actors that moved during GUI transform: {moved_actors}")
    print(f"GUI reset worked: {reset_worked}")
    
    if not reset_worked:
        print(f"\n🔧 APPLYING FIX...")
        
        # The issue is likely that the bounding box is not being reset properly
        # Let's fix it by ensuring the bounding box follows the multi-actor reset
        
        # Re-apply transformation
        print(f"Re-applying transformation for fix test...")
        viewer.move_shape("x", 100)
        viewer.rotate_shape("z", 90)
        app.processEvents()
        time.sleep(1)
        
        # Manual fix: Reset all visible actors manually
        print(f"Applying manual fix...")
        for i, actor in enumerate(all_actors):
            if actor.GetVisibility():
                print(f"Manually resetting Actor {i}...")
                actor.SetPosition(0, 0, 0)
                actor.SetOrientation(0, 0, 0)
                actor.SetUserTransform(None)
                actor.Modified()
        
        # Force render
        renderer.render_window.Render()
        app.processEvents()
        time.sleep(1)
        
        # Check if manual fix worked
        print(f"\n🔍 AFTER MANUAL FIX:")
        manual_fix_worked = True
        for i, actor in enumerate(all_actors):
            if actor.GetVisibility():
                pos = actor.GetPosition()
                orient = actor.GetOrientation()
                print(f"Actor {i}: Pos={pos}, Orient={orient}")
                
                if abs(pos[0]) > 0.1 or abs(pos[1]) > 0.1 or abs(orient[2]) > 0.1:
                    manual_fix_worked = False
                    print(f"❌ Manual fix failed for Actor {i}")
                else:
                    print(f"✅ Manual fix worked for Actor {i}")
        
        if manual_fix_worked:
            print(f"\n✅ MANUAL FIX WORKED!")
            print(f"🔧 SOLUTION: The GUI reset needs to reset ALL visible actors, not just multi-actors")
        else:
            print(f"\n❌ MANUAL FIX FAILED!")
            print(f"🔧 ISSUE: There's a deeper problem with actor transformation")
    else:
        print(f"\n✅ GUI RESET ALREADY WORKS!")
    
    # Step 5: Final recommendation
    print(f"\n📋 STEP 5: FINAL RECOMMENDATION")
    print("=" * 70)
    
    if not reset_worked:
        print(f"🎯 THE ISSUE:")
        print(f"GUI reset is not resetting all visible actors properly")
        
        print(f"\n🔧 THE FIX:")
        print(f"Modify the reset_to_original() method to reset ALL visible actors:")
        print(f"1. Reset multi-actors (already working)")
        print(f"2. Reset bounding box actor position/orientation")
        print(f"3. Force render after reset")
        
        print(f"\n💻 CODE FIX NEEDED:")
        print(f"In reset_to_original() method, add:")
        print(f"if self.bbox_actor:")
        print(f"    self.bbox_actor.SetPosition(0, 0, 0)")
        print(f"    self.bbox_actor.SetOrientation(0, 0, 0)")
        print(f"    self.bbox_actor.Modified()")
    else:
        print(f"✅ No fix needed - reset is working correctly")
    
    # Keep window open briefly
    print(f"\n👁️ Keeping window open for 10 seconds...")
    QTimer.singleShot(10000, app.quit)
    app.exec_()
    
    return True

if __name__ == "__main__":
    debug_automatic_final()
