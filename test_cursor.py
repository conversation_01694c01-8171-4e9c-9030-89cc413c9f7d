#!/usr/bin/env python3

import sys
try:
    from step_viewer_tdk_modular_fixed import StepViewerTDK
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    
    # Check cursor labels after GUI creation
    print(f"lbl_cursor_x exists: {hasattr(viewer, 'lbl_cursor_x')}")
    if hasattr(viewer, 'lbl_cursor_x'):
        print(f"lbl_cursor_x is not None: {viewer.lbl_cursor_x is not None}")
        if viewer.lbl_cursor_x:
            print(f"Current text: {viewer.lbl_cursor_x.text()}")
    
    # Manually update cursor position to test
    viewer.cursor_pos = {'x': 1.234, 'y': 5.678, 'z': 9.012}
    if hasattr(viewer, 'lbl_cursor_x') and viewer.lbl_cursor_x:
        viewer.lbl_cursor_x.setText(f"X: {viewer.cursor_pos['x']:.3f}mm")
        viewer.lbl_cursor_y.setText(f"Y: {viewer.cursor_pos['y']:.3f}mm")
        viewer.lbl_cursor_z.setText(f"Z: {viewer.cursor_pos['z']:.3f}mm")
        print("Cursor labels updated manually")
    
    viewer.show()
    app.exec_()
    
except Exception as e:
    import traceback
    print(f'ERROR: {e}')
    traceback.print_exc()
