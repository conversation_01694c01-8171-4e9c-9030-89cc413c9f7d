#!/usr/bin/env python3
"""
Visual Demo: Load test.step, rotate it, save it, display in bottom viewer
This keeps the GUI open so you can see both viewers side by side
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer_tdk_modular_fixed import StepViewerTDK

def main():
    print("🚀 Starting Visual Demo...")
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create the dual viewer
    viewer = StepViewerTDK()
    viewer.show()
    viewer.raise_()
    viewer.activateWindow()
    
    print("📋 STEP 1: Loading test.step in TOP viewer...")
    
    # Load test.step in TOP viewer
    viewer.set_active_viewer('top')
    success = viewer.load_step_file_direct('test.step')
    
    if not success:
        print("❌ Failed to load test.step")
        return False
    
    print("✅ test.step loaded in TOP viewer")
    
    # Wait a moment for display
    app.processEvents()
    time.sleep(1)
    
    print("📋 STEP 2: Applying rotations to TOP viewer...")
    print("   X=25°, Y=40°, Z=60°")
    
    # Apply rotations
    viewer.rotate_shape('x', 25)
    app.processEvents()
    time.sleep(0.5)
    
    viewer.rotate_shape('y', 40) 
    app.processEvents()
    time.sleep(0.5)
    
    viewer.rotate_shape('z', 60)
    app.processEvents()
    time.sleep(0.5)
    
    print("✅ Rotations applied to TOP viewer")
    
    print("📋 STEP 3: Saving with GREEN BUTTON...")
    
    # Save with green button
    save_file = r"e:\python\viewer\save\visual_test_REV001.step"
    os.makedirs(os.path.dirname(save_file), exist_ok=True)
    
    success = viewer.save_step_file_option1_direct(save_file)
    
    if not success:
        print("❌ Failed to save file")
        return False
        
    print(f"✅ File saved: {save_file}")
    
    # Wait a moment
    app.processEvents()
    time.sleep(1)
    
    print("📋 STEP 4: Loading saved file in BOTTOM viewer...")
    
    # Switch to bottom viewer and load the saved file
    viewer.set_active_viewer('bottom')
    success = viewer.load_step_file_direct(save_file)
    
    if not success:
        print("❌ Failed to load saved file in BOTTOM viewer")
        return False
        
    print("✅ Saved file loaded in BOTTOM viewer")
    
    # Final update
    app.processEvents()
    time.sleep(1)
    
    print("\n" + "="*60)
    print("🎉 VISUAL DEMO COMPLETE!")
    print("="*60)
    print("You should now see:")
    print("• TOP viewer: Original test.step with rotations applied")
    print("• BOTTOM viewer: Saved file with same rotations")
    print("• Both viewers should show IDENTICAL models")
    print("• Both viewers should show MATCHING rotation numbers")
    print("\nClose the window when you're done inspecting.")
    print("="*60)
    
    # Keep GUI open
    app.exec_()
    
    # Clean up
    try:
        if os.path.exists(save_file):
            os.unlink(save_file)
            print(f"🧹 Cleaned up: {save_file}")
    except:
        pass
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
