#!/usr/bin/env python3
"""
Find the problematic Unicode character
"""

filename = 'vtk_renderer.py'

try:
    with open(filename, 'rb') as f:
        content = f.read()
    
    print(f"File size: {len(content)} bytes")
    
    # Check around position 9402
    start = max(0, 9402 - 50)
    end = min(len(content), 9402 + 50)
    
    print(f"Checking bytes {start} to {end}")
    
    for i in range(start, end):
        byte_val = content[i]
        if byte_val > 127:  # Non-ASCII
            print(f"Position {i}: byte 0x{byte_val:02x} ({byte_val})")
            
            # Show context
            context_start = max(0, i - 20)
            context_end = min(len(content), i + 20)
            context = content[context_start:context_end]
            
            try:
                context_str = context.decode('utf-8', errors='replace')
                print(f"Context: {repr(context_str)}")
            except:
                print(f"Context (raw): {context}")
                
except Exception as e:
    print(f"Error: {e}")
