#!/usr/bin/env python3
"""
Standalone STEP Color Verification Program
This program will definitively determine what colors should be displayed
and verify the final VTK output matches the original STEP file.
"""

import sys
import os
import re
from pathlib import Path

def parse_step_file_colors(step_file_path):
    """Parse STEP file to extract exact color definitions and their usage"""
    print(f"=== PARSING STEP FILE: {step_file_path} ===")
    
    try:
        with open(step_file_path, 'r') as f:
            content = f.read()
        
        # Find all COLOUR_RGB definitions
        colour_rgb_pattern = r'#(\d+)\s*=\s*COLOUR_RGB\s*\(\s*\'[^\']*\'\s*,\s*([\d\.]+)\s*,\s*([\d\.]+)\s*,\s*([\d\.]+)\s*\)'
        colour_matches = re.findall(colour_rgb_pattern, content)
        
        colors_defined = {}
        for match in colour_matches:
            color_id = match[0]
            r = int(float(match[1]) * 255)
            g = int(float(match[2]) * 255) 
            b = int(float(match[3]) * 255)
            colors_defined[color_id] = (r, g, b)
            print(f"Color #{color_id}: RGB({r}, {g}, {b})")
        
        # Find SURFACE_STYLE_USAGE and SURFACE_STYLE_FILL_AREA references
        surface_style_pattern = r'#(\d+)\s*=\s*SURFACE_STYLE_FILL_AREA\s*\(\s*#(\d+)\s*\)'
        surface_matches = re.findall(surface_style_pattern, content)
        
        color_usage = {}
        for match in surface_matches:
            style_id = match[0]
            color_id = match[1]
            if color_id in colors_defined:
                color_usage[style_id] = colors_defined[color_id]
                print(f"Surface Style #{style_id} uses Color #{color_id}: RGB{colors_defined[color_id]}")
        
        # Count how many times each color is referenced
        color_counts = {}
        for color in colors_defined.values():
            color_counts[color] = color_counts.get(color, 0) + 1
        
        print(f"\n=== COLOR USAGE SUMMARY ===")
        for color, count in color_counts.items():
            print(f"RGB{color}: used {count} times")
        
        return colors_defined, color_usage, color_counts
        
    except Exception as e:
        print(f"Error parsing STEP file: {e}")
        return {}, {}, {}

def load_and_verify_vtk_output():
    """Load the STEP file with our loader and verify VTK output"""
    print(f"\n=== LOADING WITH OUR STEP LOADER ===")
    
    try:
        # Import our step loader
        sys.path.append('.')
        from step_loader import STEPLoader
        
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        loader = STEPLoader()
        
        # Load the file
        polydata, success, message = loader.load_step_file(step_file)
        
        if not success:
            print(f"Failed to load STEP file: {message}")
            return None
        
        print(f"Loaded polydata: {polydata.GetNumberOfCells()} cells, {polydata.GetNumberOfPoints()} points")
        
        # Get the color data
        cell_colors = polydata.GetCellData().GetScalars("Colors")
        if not cell_colors:
            print("No color data found in polydata")
            return None
        
        print(f"Color data: {cell_colors.GetNumberOfTuples()} tuples, {cell_colors.GetNumberOfComponents()} components")
        
        # Analyze the color distribution
        color_counts = {}
        for i in range(cell_colors.GetNumberOfTuples()):
            r = int(cell_colors.GetComponent(i, 0))
            g = int(cell_colors.GetComponent(i, 1))
            b = int(cell_colors.GetComponent(i, 2))
            color = (r, g, b)
            color_counts[color] = color_counts.get(color, 0) + 1
        
        print(f"\n=== VTK COLOR DISTRIBUTION ===")
        total_cells = sum(color_counts.values())
        for color, count in color_counts.items():
            percentage = (count / total_cells) * 100
            print(f"RGB{color}: {count} cells ({percentage:.1f}%)")
        
        return polydata, color_counts
        
    except Exception as e:
        print(f"Error loading with STEP loader: {e}")
        import traceback
        traceback.print_exc()
        return None

def verify_geometry_assignment():
    """Verify which geometry parts get which colors"""
    print(f"\n=== VERIFYING GEOMETRY ASSIGNMENT ===")
    
    try:
        # Import our step loader
        sys.path.append('.')
        from step_loader import STEPLoader
        
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        loader = STEPLoader()
        
        # Load the file
        polydata, success, message = loader.load_step_file(step_file)
        
        if not success:
            print(f"Failed to load STEP file: {message}")
            return
        
        # Get face properties if available
        if hasattr(loader, 'face_properties') and loader.face_properties:
            print(f"Found {len(loader.face_properties)} faces with properties")
            
            # Sort faces by Z coordinate
            sorted_faces = sorted(loader.face_properties, key=lambda f: f['z_coord'])
            
            print(f"\n=== FACE Z-COORDINATE ANALYSIS ===")
            for i, face in enumerate(sorted_faces[:5]):  # Show first 5 faces
                print(f"Face {i}: Z={face['z_coord']:.3f}, triangles={face['triangle_count']}")
            print("...")
            for i, face in enumerate(sorted_faces[-5:], len(sorted_faces)-5):  # Show last 5 faces
                print(f"Face {i}: Z={face['z_coord']:.3f}, triangles={face['triangle_count']}")
            
            # Calculate which faces should be body vs pins based on STEP file colors
            step_colors = [(192, 192, 192), (63, 63, 63)]  # Light, Dark
            light_count = 17  # From STEP file analysis
            dark_count = 2    # From STEP file analysis
            
            total_triangles = sum(f['triangle_count'] for f in loader.face_properties)
            dark_ratio = dark_count / (light_count + dark_count)
            dark_triangles_needed = int(total_triangles * dark_ratio)
            
            print(f"\n=== EXPECTED COLOR ASSIGNMENT ===")
            print(f"Total triangles: {total_triangles}")
            print(f"Dark ratio from STEP: {dark_ratio:.3f}")
            print(f"Dark triangles needed: {dark_triangles_needed}")
            print(f"Light triangles needed: {total_triangles - dark_triangles_needed}")
            
            # Show which faces should get which colors
            dark_assigned = 0
            print(f"\n=== FACE COLOR ASSIGNMENT (Z-sorted) ===")
            for i, face in enumerate(sorted_faces):
                if dark_assigned < dark_triangles_needed:
                    color = "DARK (63,63,63)"
                    dark_assigned += face['triangle_count']
                else:
                    color = "LIGHT (192,192,192)"
                
                if i < 3 or i >= len(sorted_faces) - 3:  # Show first and last 3
                    print(f"Face {i}: Z={face['z_coord']:.3f}, triangles={face['triangle_count']}, color={color}")
                elif i == 3:
                    print("...")
        
    except Exception as e:
        print(f"Error verifying geometry assignment: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main verification function"""
    print("STEP COLOR VERIFICATION PROGRAM")
    print("=" * 50)
    
    step_file = "SOIC16P127_1270X940X610L89X51.STEP"
    
    if not os.path.exists(step_file):
        print(f"STEP file not found: {step_file}")
        return
    
    # Step 1: Parse original STEP file colors
    step_colors, color_usage, color_counts = parse_step_file_colors(step_file)
    
    # Step 2: Load with our loader and check VTK output
    vtk_result = load_and_verify_vtk_output()
    
    # Step 3: Verify geometry assignment
    verify_geometry_assignment()
    
    # Step 4: Compare and provide recommendations
    print(f"\n=== FINAL VERIFICATION RESULTS ===")
    
    if step_colors and vtk_result:
        polydata, vtk_color_counts = vtk_result
        
        print(f"STEP file defines {len(step_colors)} colors:")
        for color_id, rgb in step_colors.items():
            print(f"  Color #{color_id}: RGB{rgb}")
        
        print(f"\nVTK output has {len(vtk_color_counts)} colors:")
        for rgb, count in vtk_color_counts.items():
            print(f"  RGB{rgb}: {count} cells")
        
        # Check if colors match
        step_rgb_values = set(step_colors.values())
        vtk_rgb_values = set(vtk_color_counts.keys())
        
        if step_rgb_values == vtk_rgb_values:
            print(f"\n✅ COLOR VALUES MATCH!")
        else:
            print(f"\n❌ COLOR VALUES DON'T MATCH!")
            print(f"STEP colors: {step_rgb_values}")
            print(f"VTK colors: {vtk_rgb_values}")
        
        # Check color distribution
        print(f"\n=== COLOR DISTRIBUTION ANALYSIS ===")
        print(f"From STEP file color usage:")
        for color, count in color_counts.items():
            print(f"  RGB{color}: {count} references")
        
        print(f"From VTK output:")
        total_vtk = sum(vtk_color_counts.values())
        for color, count in vtk_color_counts.items():
            percentage = (count / total_vtk) * 100
            print(f"  RGB{color}: {count} cells ({percentage:.1f}%)")

if __name__ == "__main__":
    main()
