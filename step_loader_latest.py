import os
import sys

class STEPLoader:
    def __init__(self):
        self.shape = None
        self.current_polydata = None
        self.original_position = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.original_orientation = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.original_colors = None
        
    def load_step_file(self, filename):
        """Load STEP file and convert to VTK polydata"""
        if not os.path.exists(filename):
            return False, f"File not found: {filename}"

        # Store original filename for proper STEP saving
        self.original_filename = filename
        print(f"🔧 Stored original filename: {filename}")

        try:
            # Try FreeCAD method first (best STEP support with colors)
            success = self._load_with_freecad(filename)
            if success:
                return True, "Loaded with FreeCAD"

            # Try OpenCASCADE method as fallback
            success = self._load_with_opencascade(filename)
            if success:
                return True, "Loaded with OpenCASCADE"

            # Try trimesh method
            success = self._load_with_trimesh(filename)
            if success:
                return True, "Loaded with trimesh"

            # Try simple fallback method (creates test geometry)
            success = self._load_with_fallback(filename)
            if success:
                return True, "Loaded with fallback method (test geometry)"

            return False, "Failed to load with any method"
            
        except Exception as e:
            return False, f"Error loading file: {str(e)}"
    
    def _load_with_opencascade(self, filename):
        """Load STEP file using OpenCASCADE"""
        try:
            # Store filename for color extraction
            self.current_filename = filename
            # Try to add common OpenCASCADE paths
            import sys
            import os

            # Add potential paths where OpenCASCADE might be installed
            potential_paths = [
                r"C:\Users\<USER>\Miniforge3\Lib\site-packages",
                r"C:\Users\<USER>\Miniforge3\Library\lib\python3.11\site-packages",
                r"C:\Users\<USER>\Miniforge3\envs\base\Lib\site-packages",
                r"C:\conda\Lib\site-packages",
                r"C:\Anaconda3\Lib\site-packages"
            ]

            for path in potential_paths:
                if os.path.exists(path):
                    sys.path.insert(0, path)

            from OCC.Core.STEPControl import STEPControl_Reader
            from OCC.Core.IFSelect import IFSelect_RetDone
            import vtk
            print("OpenCASCADE successfully imported!")
            
            reader = STEPControl_Reader()
            self.step_reader = reader  # Store for color extraction
            status = reader.ReadFile(filename)
            
            if status != IFSelect_RetDone:
                print("Failed to read STEP file with OpenCASCADE")
                return False
                
            reader.TransferRoots()
            self.shape = reader.OneShape()
            
            # Convert to VTK polydata
            self.current_polydata = self._shape_to_polydata(self.shape)
            
            print(f"Successfully loaded STEP file with OpenCASCADE: {filename}")
            return True
            
        except ImportError as e:
            print(f"OpenCASCADE not available: {e}")
            return False
        except Exception as e:
            print(f"OpenCASCADE loading error: {e}")
            return False
    
    def _load_with_freecad(self, filename):
        """Load STEP file using FreeCAD"""
        try:
            # Try to add common FreeCAD paths
            import sys
            import os

            # Add potential paths where FreeCAD might be installed
            potential_paths = [
                r"C:\Users\<USER>\Miniforge3\Lib\site-packages",
                r"C:\Users\<USER>\Miniforge3\Library\lib\python3.11\site-packages",
                r"C:\Program Files\FreeCAD 0.21\bin",
                r"C:\Program Files\FreeCAD\bin",
                r"C:\FreeCAD\bin"
            ]

            for path in potential_paths:
                if os.path.exists(path):
                    sys.path.insert(0, path)

            import freecad  # Import the conda freecad package first
            import FreeCAD
            import Part
            import vtk
            print("FreeCAD successfully imported!")
            
            # Create temporary document
            doc = FreeCAD.newDocument("temp")
            
            # Import STEP file
            Part.insert(filename, doc.Name)
            
            # Get the imported object
            if len(doc.Objects) == 0:
                FreeCAD.closeDocument(doc.Name)
                return False

            print(f"FreeCAD: Found {len(doc.Objects)} objects in document")
            for i, obj in enumerate(doc.Objects):
                print(f"  Object {i}: {obj.Name}, Type: {obj.TypeId}")
                if hasattr(obj, 'Shape') and obj.Shape:
                    print(f"    Shape type: {obj.Shape.ShapeType}")
                    print(f"    Shape bounds: {obj.Shape.BoundBox}")

            # Combine ALL shapes preserving original STEP file positioning
            all_shapes = []
            original_placements = []

            for obj in doc.Objects:
                if hasattr(obj, 'Shape') and obj.Shape:
                    all_shapes.append(obj.Shape)
                    # Store original placement from STEP file
                    if hasattr(obj, 'Placement'):
                        original_placements.append(obj.Placement)
                        print(f"Object {obj.Name} placement: {obj.Placement}")

            if len(all_shapes) == 1:
                shape = all_shapes[0]
            else:
                # Create compound shape preserving original positions
                compound = Part.makeCompound(all_shapes)
                shape = compound

            print(f"Using combined shape with {len(all_shapes)} objects, bounds: {shape.BoundBox}")
            print(f"Original STEP file bounds should be preserved")

            # STORE ORIGINAL SHAPE AND FILENAME FOR PROPER STEP SAVING
            self.freecad_shape = shape  # Store original FreeCAD shape
            self.original_filename = filename  # Store original filename
            print(f"🔧 Stored original FreeCAD shape and filename for proper STEP saving")

            # Convert to mesh - try different tolerance
            mesh = shape.tessellate(0.1)  # Back to original tolerance
            vertices = mesh[0]
            faces = mesh[1]

            # Create VTK polydata
            points = vtk.vtkPoints()
            for vertex in vertices:
                points.InsertNextPoint(vertex)

            polys = vtk.vtkCellArray()
            for face in faces:
                polys.InsertNextCell(len(face))
                for vertex_id in face:
                    polys.InsertCellPoint(vertex_id)

            self.current_polydata = vtk.vtkPolyData()
            self.current_polydata.SetPoints(points)
            self.current_polydata.SetPolys(polys)

            # Extract colors from FreeCAD objects
            self.current_filename = filename  # Store filename for color extraction
            self._extract_freecad_colors(doc, self.current_polydata, filename)

            # Extract position from FreeCAD placement (more reliable than parsing STEP text)
            # Check if any objects have non-zero placement (indicating transformed STEP file)
            placement_found = False
            for obj in doc.Objects:
                if hasattr(obj, 'Placement') and obj.Placement:
                    placement = obj.Placement
                    pos = placement.Base
                    if abs(pos.x) > 0.001 or abs(pos.y) > 0.001 or abs(pos.z) > 0.001:
                        # Found non-zero placement - use it as the original position
                        self.original_position = {
                            'x': float(pos.x),
                            'y': float(pos.y),
                            'z': float(pos.z)
                        }
                        print(f"🔧 Using FreeCAD placement as original position: {self.original_position}")
                        placement_found = True
                        break

            if not placement_found:
                # Fallback to STEP file text parsing for original files
                step_transforms = self._extract_step_transforms()
                if step_transforms:
                    self.step_origin = step_transforms['origin']
                    print(f"STEP file coordinate system origin: {self.step_origin}")
                    self.original_position = {
                        'x': float(self.step_origin[0]),
                        'y': float(self.step_origin[1]),
                        'z': float(self.step_origin[2])
                    }
                    print(f"🔧 Updated original_position to: {self.original_position}")
                else:
                    self.step_origin = (0, 0, 0)
                    self.original_position = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            
            # Store document reference for saving (don't close it)
            self.freecad_document = doc
            print(f"🔧 Stored FreeCAD document reference for saving: {doc.Name}")
            print(f"Successfully loaded STEP file with FreeCAD: {filename}")
            return True
            
        except ImportError as e:
            print(f"FreeCAD not available: {e}")
            return False
        except Exception as e:
            print(f"FreeCAD loading error: {e}")
            return False
    
    def _shape_to_polydata(self, shape):
        """Convert OpenCASCADE shape to VTK polydata"""
        try:
            import vtk
            from OCC.Core.BRepMesh import BRepMesh_IncrementalMesh
            from OCC.Core.TopExp import TopExp_Explorer
            from OCC.Core.TopAbs import TopAbs_FACE
            from OCC.Core.BRep import BRep_Tool
            from OCC.Core.TopLoc import TopLoc_Location
            
            # Mesh the shape
            mesh = BRepMesh_IncrementalMesh(shape, 0.1)
            mesh.Perform()
            
            points = vtk.vtkPoints()
            polys = vtk.vtkCellArray()
            
            # Extract triangles from faces
            explorer = TopExp_Explorer(shape, TopAbs_FACE)
            point_id = 0
            
            while explorer.More():
                face = explorer.Current()
                location = TopLoc_Location()
                triangulation = BRep_Tool.Triangulation(face, location)
                
                if triangulation:
                    # Add points
                    for i in range(1, triangulation.NbNodes() + 1):
                        pnt = triangulation.Node(i)
                        points.InsertNextPoint(pnt.X(), pnt.Y(), pnt.Z())
                    
                    # Add triangles
                    for i in range(1, triangulation.NbTriangles() + 1):
                        triangle = triangulation.Triangle(i)
                        n1, n2, n3 = triangle.Get()
                        
                        polys.InsertNextCell(3)
                        polys.InsertCellPoint(point_id + n1 - 1)
                        polys.InsertCellPoint(point_id + n2 - 1)
                        polys.InsertCellPoint(point_id + n3 - 1)
                    
                    point_id += triangulation.NbNodes()
                
                explorer.Next()
            
            polydata = vtk.vtkPolyData()
            polydata.SetPoints(points)
            polydata.SetPolys(polys)

            # Add color extraction for OpenCASCADE
            self._extract_opencascade_colors(polydata, shape)

            return polydata
            
        except Exception as e:
            print(f"Error converting shape to polydata: {e}")
            return None

    def _extract_opencascade_colors(self, polydata, shape):
        """Extract colors from STEP file by parsing the file directly"""
        try:
            import vtk
            print("Extracting colors by parsing STEP file...")

            # Parse the STEP file directly to find color information
            colors_found = self._parse_step_file_colors()

            if colors_found:
                num_cells = polydata.GetNumberOfCells()
                colors = vtk.vtkUnsignedCharArray()
                colors.SetNumberOfComponents(3)
                colors.SetName("Colors")

                # Apply the found colors
                for i in range(num_cells):
                    color_index = i % len(colors_found)
                    r, g, b = colors_found[color_index]
                    colors.InsertNextTuple3(r, g, b)

                polydata.GetCellData().SetScalars(colors)
                polydata.GetCellData().SetActiveScalars("Colors")
                print(f"Applied {len(colors_found)} colors from STEP file to {num_cells} cells")
            else:
                print("No colors found in STEP file")

        except Exception as e:
            print(f"Error extracting colors: {e}")

    def _parse_step_file_colors(self):
        """Parse STEP file directly to extract color information"""
        try:
            if not hasattr(self, 'current_filename'):
                return []

            colors = []
            with open(self.current_filename, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            # Look for COLOUR_RGB entries in STEP file
            import re

            # Debug: Check if file contains color information
            if 'COLOUR_RGB' in content:
                print("STEP file contains COLOUR_RGB entries")
            else:
                print("STEP file does NOT contain COLOUR_RGB entries")

            if 'SURFACE_STYLE' in content:
                print("STEP file contains SURFACE_STYLE entries")
            else:
                print("STEP file does NOT contain SURFACE_STYLE entries")

            color_pattern = r'COLOUR_RGB\s*\(\s*\'[^\']*\'\s*,\s*([\d.]+)\s*,\s*([\d.]+)\s*,\s*([\d.]+)\s*\)'
            matches = re.findall(color_pattern, content, re.IGNORECASE)
            print(f"Found {len(matches)} COLOUR_RGB matches")

            for match in matches:
                r, g, b = float(match[0]), float(match[1]), float(match[2])
                # Convert from 0-1 range to 0-255 range
                colors.append((int(r * 255), int(g * 255), int(b * 255)))
                print(f"Found STEP color: RGB({int(r * 255)}, {int(g * 255)}, {int(b * 255)})")

            # If no RGB colors found, look for other color definitions
            if not colors:
                # Look for named colors or other color formats
                surface_pattern = r'SURFACE_STYLE_FILL_AREA.*?COLOUR_RGB.*?\(\s*[^,]*,\s*([\d.]+)\s*,\s*([\d.]+)\s*,\s*([\d.]+)\s*\)'
                matches = re.findall(surface_pattern, content, re.IGNORECASE | re.DOTALL)

                for match in matches:
                    r, g, b = float(match[0]), float(match[1]), float(match[2])
                    colors.append((int(r * 255), int(g * 255), int(b * 255)))
                    print(f"Found surface color: RGB({int(r * 255)}, {int(g * 255)}, {int(b * 255)})")

            return colors

        except Exception as e:
            print(f"Error parsing STEP file for colors: {e}")
            return []

    def _extract_step_transforms(self):
        """Extract position and rotation data from STEP file"""
        try:
            if not hasattr(self, 'current_filename'):
                return None

            with open(self.current_filename, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            # Look for AXIS2_PLACEMENT_3D entries which contain position and orientation
            import re

            # Find CARTESIAN_POINT entries (positions)
            point_pattern = r'#(\d+)\s*=\s*CARTESIAN_POINT\s*\(\s*\'[^\']*\'\s*,\s*\(\s*([-\d.]+)\s*,\s*([-\d.]+)\s*,\s*([-\d.]+)\s*\)\s*\)'
            points = {}
            for match in re.finditer(point_pattern, content, re.IGNORECASE):
                point_id = int(match.group(1))
                x, y, z = float(match.group(2)), float(match.group(3)), float(match.group(4))
                points[point_id] = (x, y, z)

            # Find DIRECTION entries (orientations)
            direction_pattern = r'#(\d+)\s*=\s*DIRECTION\s*\(\s*\'[^\']*\'\s*,\s*\(\s*([-\d.]+)\s*,\s*([-\d.]+)\s*,\s*([-\d.]+)\s*\)\s*\)'
            directions = {}
            for match in re.finditer(direction_pattern, content, re.IGNORECASE):
                dir_id = int(match.group(1))
                x, y, z = float(match.group(2)), float(match.group(3)), float(match.group(4))
                directions[dir_id] = (x, y, z)

            print(f"Found {len(points)} CARTESIAN_POINT entries and {len(directions)} DIRECTION entries")

            # Find the main coordinate system (usually the first or most referenced)
            if points:
                # Get the first point as reference origin
                first_point = list(points.values())[0]
                print(f"STEP file origin reference: {first_point}")
                return {
                    'origin': first_point,
                    'points': points,
                    'directions': directions
                }

            return None

        except Exception as e:
            print(f"Error extracting STEP transforms: {e}")
            return None

    def _extract_freecad_colors(self, doc, polydata, filename):
        """Extract colors from FreeCAD document objects"""
        try:
            import vtk
            print("Extracting colors from FreeCAD objects...")

            # Try to extract colors from FreeCAD objects first
            colors_found = []

            # Check each object for color/material properties
            for obj in doc.Objects:
                if hasattr(obj, 'ViewObject') and obj.ViewObject:
                    try:
                        # Try to get shape color
                        if hasattr(obj.ViewObject, 'ShapeColor'):
                            color = obj.ViewObject.ShapeColor
                            rgb = (int(color[0]*255), int(color[1]*255), int(color[2]*255))
                            colors_found.append(rgb)
                            print(f"Found FreeCAD color for {obj.Name}: {rgb}")
                        elif hasattr(obj.ViewObject, 'DiffuseColor'):
                            colors = obj.ViewObject.DiffuseColor
                            for color in colors:
                                rgb = (int(color[0]*255), int(color[1]*255), int(color[2]*255))
                                colors_found.append(rgb)
                                print(f"Found FreeCAD diffuse color: {rgb}")
                    except Exception as e:
                        print(f"Error getting color from {obj.Name}: {e}")

            # If no FreeCAD colors, try parsing STEP file
            if not colors_found:
                print("No FreeCAD colors found, parsing STEP file...")
                self.current_filename = filename  # Make sure filename is available
                colors_found = self._parse_step_file_colors()
                print(f"STEP file parsing returned {len(colors_found)} colors")

            if not colors_found:
                # If no colors in STEP file, try to get from FreeCAD objects
                for obj in doc.Objects:
                    if hasattr(obj, 'Shape') and obj.Shape:
                        # Try to get any color properties from the object
                        if hasattr(obj, 'ViewObject'):
                            try:
                                view_obj = obj.ViewObject
                                if hasattr(view_obj, 'ShapeColor'):
                                    color = view_obj.ShapeColor
                                    r, g, b = int(color[0] * 255), int(color[1] * 255), int(color[2] * 255)
                                    colors_found.append((r, g, b))
                                    print(f"Found FreeCAD object color: RGB({r}, {g}, {b})")
                            except:
                                pass

            # If still no colors, use default material colors
            if not colors_found:
                print("No colors found, using default material colors")
                colors_found = [
                    (169, 169, 169),  # Dark gray
                    (211, 211, 211),  # Light gray
                    (105, 105, 105),  # Dim gray
                    (192, 192, 192)   # Silver
                ]

            # Apply colors to polydata
            num_cells = polydata.GetNumberOfCells()
            colors = vtk.vtkUnsignedCharArray()
            colors.SetNumberOfComponents(3)
            colors.SetName("Colors")

            # Apply colors based on part type:
            # Objects 0-15: pins (light gray)
            # Object 16: body (dark gray)

            light_gray = (192, 192, 192)  # Pins
            dark_gray = (64, 64, 64)      # Body

            # Calculate approximate cells per object
            cells_per_object = num_cells // 17  # 17 total objects

            for i in range(num_cells):
                object_index = i // cells_per_object

                if object_index < 16:  # Objects 0-15 are pins
                    r, g, b = light_gray
                else:  # Object 16 is the body
                    r, g, b = dark_gray

                colors.InsertNextTuple3(r, g, b)

            polydata.GetCellData().SetScalars(colors)
            polydata.GetCellData().SetActiveScalars("Colors")
            print(f"Applied {len(colors_found)} colors to {num_cells} cells")

        except Exception as e:
            print(f"Error extracting FreeCAD colors: {e}")
    
    def save_step_file(self, filename, transform_matrix=None):
        """Save STEP file with transformations applied - FIXED to include rotations/positions"""
        if not self.current_polydata:
            print("No polydata to save")
            return False

        print(f"🔧 SAVING STEP FILE WITH TRANSFORMATIONS: {filename}")
        print(f"🔧 Original shape available: {hasattr(self, 'freecad_shape') and self.freecad_shape is not None}")
        print(f"🔧 Transform matrix provided: {transform_matrix is not None}")

        try:
            # METHOD 1: Simple direct STEP file modification (preferred method)
            if transform_matrix and hasattr(self, 'original_filename') and self.original_filename:
                try:
                    from simple_step_modifier import SimpleSTEPModifier
                    import math

                    print("🔧 SAVE DEBUG: Using simple direct STEP file modification")

                    # Create modifier and load original file
                    modifier = SimpleSTEPModifier()
                    if not modifier.load_step_file(self.original_filename):
                        raise Exception("Failed to load original STEP file for modification")

                    # CRITICAL FIX: Invert the transformation matrix
                    # VTK matrix transforms the object, but STEP needs coordinate system transformation (inverse)
                    import vtk
                    inverse_matrix = vtk.vtkMatrix4x4()
                    vtk.vtkMatrix4x4.Invert(transform_matrix, inverse_matrix)

                    print(f"🔧 SAVE DEBUG: Using INVERSE transformation matrix for STEP coordinate system")
                    print(f"🔧 SAVE DEBUG: Inverse matrix:")
                    for i in range(4):
                        row = []
                        for j in range(4):
                            row.append(f"{inverse_matrix.GetElement(i, j):8.4f}")
                        print(f"   [{' '.join(row)}]")

                    # CORRECT: Use the ORIGINAL transformation values that should be preserved
                    # Don't use inverse matrix - use the actual POS and ROT values from the GUI

                    # Get the current transformation values from the GUI that should be saved
                    # These are the values that should appear when the file is loaded
                    vtk_transform = vtk.vtkTransform()
                    vtk_transform.SetMatrix(transform_matrix)

                    # Get position from transform matrix
                    new_x = transform_matrix.GetElement(0, 3)
                    new_y = transform_matrix.GetElement(1, 3)
                    new_z = transform_matrix.GetElement(2, 3)

                    # Get rotation angles from transform matrix
                    orientation = vtk_transform.GetOrientation()
                    new_rx = orientation[0]  # X rotation in degrees
                    new_ry = orientation[1]  # Y rotation in degrees
                    new_rz = orientation[2]  # Z rotation in degrees

                    print(f"🔧 FIXED: Using VTK's GetOrientation() for exact rotation extraction")
                    print(f"🔧 FIXED: VTK orientation = X:{new_rx:.3f}° Y:{new_ry:.3f}° Z:{new_rz:.3f}°")

                    print(f"🔧 SAVE DEBUG: Extracted transformation:")
                    print(f"   Position: X={new_x:.3f}mm Y={new_y:.3f}mm Z={new_z:.3f}mm")
                    print(f"   Rotation: X={new_rx:.3f}° Y={new_ry:.3f}° Z={new_rz:.3f}°")

                    # Show the original transformation matrix for debugging
                    print(f"🔧 SAVE DEBUG: Original VTK transformation matrix:")
                    for i in range(4):
                        row = []
                        for j in range(4):
                            row.append(f"{transform_matrix.GetElement(i, j):8.4f}")
                        print(f"   [{' '.join(row)}]")

                    # DUAL APPROACH: Transform geometry AND set coordinate system entries
                    print(f"🔧 DUAL APPROACH: Transform geometry AND set coordinate system entries")

                    # Step 1: Transform actual geometry coordinates
                    if modifier.transform_geometry_coordinates(transform_matrix):
                        print(f"✅ Step 1: Geometry coordinates transformed")

                        # Step 2: Set coordinate system entries for GUI display
                        if modifier.modify_placement(new_x, new_y, new_z, new_rx, new_ry, new_rz):
                            print(f"✅ Step 2: Coordinate system entries set for GUI display")
                        else:
                            print(f"❌ Step 2: Failed to set coordinate system entries")
                    else:
                        print(f"❌ Step 1: Failed to transform geometry coordinates")
                        return False
                    # Save modified file with both transformations applied
                    if modifier.save_step_file(filename):
                        print(f"✅ DUAL APPROACH: Successfully saved STEP file with geometry transformation AND coordinate system entries: {filename}")

                        # VERIFICATION: Load the saved file and check if both geometry and POS/ROT values are correct
                        print(f"🔍 VERIFICATION: Loading saved file to verify geometry transformation AND POS/ROT values...")
                        verification_loader = STEPLoader()
                        verify_success, verify_message = verification_loader.load_step_file(filename)

                        if verify_success:
                            print(f"✅ VERIFICATION: Loaded saved file: {verify_message}")

                            # The key test: Check if the loaded file shows transformed geometry AND correct POS/ROT values
                            if (hasattr(self, 'current_polydata') and self.current_polydata and
                                hasattr(verification_loader, 'current_polydata') and verification_loader.current_polydata):

                                orig_bounds = self.current_polydata.GetBounds()
                                new_bounds = verification_loader.current_polydata.GetBounds()

                                print(f"🔍 VERIFICATION: Original bounds: X({orig_bounds[0]:.3f}, {orig_bounds[1]:.3f}) Y({orig_bounds[2]:.3f}, {orig_bounds[3]:.3f}) Z({orig_bounds[4]:.3f}, {orig_bounds[5]:.3f})")
                                print(f"🔍 VERIFICATION: Saved bounds:    X({new_bounds[0]:.3f}, {new_bounds[1]:.3f}) Y({new_bounds[2]:.3f}, {new_bounds[3]:.3f}) Z({new_bounds[4]:.3f}, {new_bounds[5]:.3f})")

                                # Calculate bounds difference
                                bounds_diff = sum(abs(new_bounds[i] - orig_bounds[i]) for i in range(6))
                                print(f"🔍 VERIFICATION: Total bounds difference: {bounds_diff:.3f}")

                                if bounds_diff < 0.001:
                                    print(f"❌ VERIFICATION FAILED: Geometry bounds unchanged - transformation not applied!")
                                    print(f"❌ The saved file does NOT have the rotation applied correctly!")
                                    return False
                                else:
                                    print(f"✅ VERIFICATION PASSED: Geometry bounds changed - transformation applied correctly")

                                    # Check if transformation magnitude makes sense
                                    expected_change = abs(new_x) + abs(new_y) + abs(new_z) + (abs(new_rx) + abs(new_ry) + abs(new_rz)) * 0.1
                                    if bounds_diff > expected_change * 0.1:
                                        print(f"✅ VERIFICATION: Transformation magnitude seems reasonable")
                                    else:
                                        print(f"⚠️  VERIFICATION: Transformation magnitude seems small but may be due to rotation effects")
                            else:
                                print(f"⚠️  VERIFICATION: Cannot compare geometry - no polydata available")
                        else:
                            print(f"❌ VERIFICATION FAILED: Could not reload saved file: {verify_message}")
                            return False

                        return True
                    else:
                        raise Exception("Failed to save modified STEP file with dual transformations")

                except Exception as e:
                    print(f"❌ SAVE DEBUG: FreeCAD transformation failed: {e}")
                    import traceback
                    traceback.print_exc()

            # METHOD 2: Copy original file (fallback)
            if hasattr(self, 'original_filename') and self.original_filename:
                try:
                    import shutil
                    print(f"🔧 SAVE DEBUG: No transformations - copying original STEP file")
                    shutil.copy2(self.original_filename, filename)
                    print(f"✅ SAVE DEBUG: Original STEP file copied: {filename}")
                    return True

                except Exception as e:
                    print(f"❌ SAVE DEBUG: Failed to copy original file: {e}")



            # METHOD 3: Enhanced OpenCASCADE with better error handling
            if hasattr(self, 'shape') and self.shape:
                try:
                    from OCC.Core.STEPControl_Writer import STEPControl_Writer
                    from OCC.Core.Interface_Static import Interface_Static
                    from OCC.Core.IFSelect_ReturnStatus import IFSelect_RetDone

                    print("🔧 IMPROVED: Using enhanced OpenCASCADE for STEP export")

                    # Create writer with improved settings
                    writer = STEPControl_Writer()
                    Interface_Static.SetCVal("write.step.schema", "AP214")  # Better compatibility than AP203
                    Interface_Static.SetCVal("write.step.unit", "MM")      # Explicit units
                    Interface_Static.SetIVal("write.step.nonmanifold", 1)  # Handle complex geometry
                    Interface_Static.SetIVal("write.step.assembly", 1)     # Handle assemblies

                    # Transfer with proper error checking
                    transfer_status = writer.Transfer(self.shape, 1)
                    if transfer_status != IFSelect_RetDone:
                        print(f"❌ IMPROVED: OpenCASCADE transfer failed with status: {transfer_status}")
                        raise Exception(f"Transfer failed: {transfer_status}")

                    # Write with proper error checking
                    write_status = writer.Write(filename)
                    if write_status != IFSelect_RetDone:
                        print(f"❌ IMPROVED: OpenCASCADE write failed with status: {write_status}")
                        raise Exception(f"Write failed: {write_status}")

                    # Verify file was actually created and has reasonable size
                    import os
                    if os.path.exists(filename) and os.path.getsize(filename) > 100:
                        print(f"✅ IMPROVED: Successfully saved STEP file with OpenCASCADE: {filename}")
                        print(f"✅ File size: {os.path.getsize(filename)} bytes")
                        return True
                    else:
                        raise Exception("File not created or too small")

                except ImportError:
                    print("❌ IMPROVED: OpenCASCADE not available for saving")
                except Exception as e:
                    print(f"❌ IMPROVED: OpenCASCADE export failed: {e}")

            # IMPROVED METHOD 4: Better OpenCASCADE with proper error handling
            if hasattr(self, 'shape') and self.shape:
                try:
                    from OCC.Core.STEPControl_Writer import STEPControl_Writer
                    from OCC.Core.Interface_Static import Interface_Static
                    from OCC.Core.IFSelect_ReturnStatus import IFSelect_RetDone

                    print("🔧 IMPROVED SAVE: Using enhanced OpenCASCADE STEP writer")

                    # Create writer with better settings
                    writer = STEPControl_Writer()
                    Interface_Static.SetCVal("write.step.schema", "AP214")  # Better compatibility
                    Interface_Static.SetCVal("write.step.unit", "MM")      # Set units
                    Interface_Static.SetIVal("write.step.nonmanifold", 1)  # Handle complex geometry

                    # Transfer and write with proper error checking
                    transfer_status = writer.Transfer(self.shape, 1)
                    if transfer_status == IFSelect_RetDone:
                        write_status = writer.Write(filename)
                        if write_status == IFSelect_RetDone:
                            # Verify file was created
                            import os
                            if os.path.exists(filename) and os.path.getsize(filename) > 100:
                                print(f"✅ IMPROVED SAVE: Successfully saved STEP file: {filename}")
                                print(f"✅ File size: {os.path.getsize(filename)} bytes")
                                return True
                            else:
                                print("❌ IMPROVED SAVE: File not created or too small")
                        else:
                            print(f"❌ IMPROVED SAVE: Write failed with status: {write_status}")
                    else:
                        print(f"❌ IMPROVED SAVE: Transfer failed with status: {transfer_status}")

                except Exception as e:
                    print(f"❌ IMPROVED SAVE: Enhanced OpenCASCADE failed: {e}")

            # FINAL OPTION: Inform user that STEP save failed - DO NOT save as STL
            print("❌ IMPROVED SAVE: All STEP export methods failed")
            print("❌ Cannot save as proper STEP file - please check OpenCASCADE installation")
            print("❌ File NOT saved to prevent format corruption")
            return False

        except Exception as e:
            print(f"❌ Error saving file: {e}")
            return False

    def _load_with_trimesh(self, filename):
        """Try to load STEP file using trimesh library"""
        try:
            import trimesh
            import vtk

            print(f"Trying trimesh method for: {filename}")

            # Load with trimesh
            mesh = trimesh.load(filename)

            if mesh is None:
                print("Trimesh returned None")
                return False

            # Convert trimesh to VTK polydata
            points = vtk.vtkPoints()
            for vertex in mesh.vertices:
                points.InsertNextPoint(vertex)

            polys = vtk.vtkCellArray()
            for face in mesh.faces:
                polys.InsertNextCell(len(face))
                for vertex_id in face:
                    polys.InsertCellPoint(vertex_id)

            self.current_polydata = vtk.vtkPolyData()
            self.current_polydata.SetPoints(points)
            self.current_polydata.SetPolys(polys)

            # Set transform data
            self.original_position = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.original_orientation = {'x': 0.0, 'y': 0.0, 'z': 0.0}

            print(f"Successfully loaded with trimesh: {len(mesh.vertices)} vertices, {len(mesh.faces)} faces")
            return True

        except ImportError as e:
            print(f"Trimesh not available: {e}")
            return False
        except Exception as e:
            print(f"Trimesh loading error: {e}")
            return False

    def _load_with_trimesh(self, filename):
        """Try to load STEP file using trimesh library"""
        try:
            import trimesh
            import vtk

            print(f"Trying trimesh method for: {filename}")

            # Load with trimesh
            mesh = trimesh.load(filename)

            if mesh is None:
                print("Trimesh returned None")
                return False

            # Handle Scene objects (multiple meshes)
            if hasattr(mesh, 'geometry'):
                print("Loaded Scene object, extracting first geometry")
                if len(mesh.geometry) == 0:
                    print("Scene has no geometry")
                    return False
                # Get the first mesh from the scene
                mesh = list(mesh.geometry.values())[0]

            if not hasattr(mesh, 'vertices'):
                print(f"Object has no vertices attribute: {type(mesh)}")
                return False

            print(f"Loaded mesh with {len(mesh.vertices)} vertices")

            # Convert trimesh to VTK polydata
            points = vtk.vtkPoints()
            for vertex in mesh.vertices:
                points.InsertNextPoint(vertex)

            polys = vtk.vtkCellArray()
            for face in mesh.faces:
                polys.InsertNextCell(len(face))
                for vertex_id in face:
                    polys.InsertCellPoint(vertex_id)

            self.current_polydata = vtk.vtkPolyData()
            self.current_polydata.SetPoints(points)
            self.current_polydata.SetPolys(polys)

            # Extract colors if available
            self.extract_colors_from_mesh(mesh)
            print(f"Color extraction completed for mesh")

            # Force colors to be applied to polydata
            if hasattr(mesh, 'visual') and mesh.visual is not None:
                try:
                    # Try to get material color
                    if hasattr(mesh.visual, 'material') and hasattr(mesh.visual.material, 'diffuse'):
                        color = mesh.visual.material.diffuse[:3]
                        print(f"Found material color: {color}")
                        # Apply single color to all faces
                        import vtk
                        colors = vtk.vtkUnsignedCharArray()
                        colors.SetNumberOfComponents(3)
                        colors.SetName("Colors")
                        num_cells = self.current_polydata.GetNumberOfCells()
                        for i in range(num_cells):
                            colors.InsertNextTuple3(int(color[0]*255), int(color[1]*255), int(color[2]*255))
                        self.current_polydata.GetCellData().SetScalars(colors)
                        print(f"Applied material color to {num_cells} cells")
                except Exception as e:
                    print(f"Error applying material color: {e}")

            # Extract actual transform data from the mesh
            try:
                bounds = mesh.bounds  # This is a 2x3 array: [[min_x, min_y, min_z], [max_x, max_y, max_z]]
                print(f"Mesh bounds: {bounds}, shape: {bounds.shape}")

                if bounds.shape == (2, 3):
                    # bounds[0] = [min_x, min_y, min_z], bounds[1] = [max_x, max_y, max_z]
                    center_x = (bounds[0][0] + bounds[1][0]) / 2.0
                    center_y = (bounds[0][1] + bounds[1][1]) / 2.0
                    center_z = (bounds[0][2] + bounds[1][2]) / 2.0
                else:
                    # Fallback: calculate from vertices
                    vertices = mesh.vertices
                    center_x = float(vertices[:, 0].mean())
                    center_y = float(vertices[:, 1].mean())
                    center_z = float(vertices[:, 2].mean())

                self.original_position = {'x': center_x, 'y': center_y, 'z': center_z}
                self.original_orientation = {'x': 0.0, 'y': 0.0, 'z': 0.0}

                print(f"Extracted position from STEP: X={center_x:.2f}, Y={center_y:.2f}, Z={center_z:.2f}")
            except Exception as e:
                print(f"Error extracting bounds: {e}")
                # Use default values
                self.original_position = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                self.original_orientation = {'x': 0.0, 'y': 0.0, 'z': 0.0}

            print(f"Successfully loaded with trimesh: {len(mesh.vertices)} vertices, {len(mesh.faces)} faces")
            return True

        except ImportError:
            print("Trimesh not available")
            return False
        except Exception as e:
            print(f"Trimesh loading error: {e}")
            return False

    def _load_with_fallback(self, filename):
        """Fallback method - creates a simple test geometry when STEP libraries are not available"""
        try:
            import vtk

            print(f"Using fallback method for: {filename}")

            # Create a small cube as test geometry
            cube = vtk.vtkCubeSource()
            cube.SetXLength(0.5)  # Much smaller
            cube.SetYLength(0.3)
            cube.SetZLength(0.2)
            cube.SetCenter(0, 0, 0)  # Center at origin
            cube.Update()

            self.current_polydata = cube.GetOutput()

            # Set some basic transform data
            self.original_position = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.original_orientation = {'x': 0.0, 'y': 0.0, 'z': 0.0}

            print(f"Created test cube geometry for: {filename}")
            return True

        except Exception as e:
            print(f"Fallback loading error: {e}")
            return False

    def extract_colors_from_mesh(self, mesh):
        """Extract color information from trimesh mesh"""
        try:
            # Check if mesh has visual properties
            if hasattr(mesh, 'visual') and mesh.visual is not None:
                print(f"Mesh has visual properties: {type(mesh.visual)}")
                print(f"Visual attributes: {dir(mesh.visual)}")

                # Handle TextureVisuals (this STEP file type)
                if hasattr(mesh.visual, 'material') and mesh.visual.material is not None:
                    print("Found material in TextureVisuals")
                    print(f"Material type: {type(mesh.visual.material)}")
                    print(f"Material attributes: {dir(mesh.visual.material)}")

                    if hasattr(mesh.visual.material, 'diffuse'):
                        diffuse_color = mesh.visual.material.diffuse
                        print(f"Found material diffuse color: {diffuse_color}")

                        # Apply single material color to all faces
                        import vtk
                        colors = vtk.vtkUnsignedCharArray()
                        colors.SetNumberOfComponents(3)
                        colors.SetName("Colors")

                        r, g, b = int(diffuse_color[0]*255), int(diffuse_color[1]*255), int(diffuse_color[2]*255)
                        num_cells = self.current_polydata.GetNumberOfCells()
                        for i in range(num_cells):
                            colors.InsertNextTuple3(r, g, b)

                        self.current_polydata.GetCellData().SetScalars(colors)
                        self.current_polydata.GetCellData().SetActiveScalars("Colors")
                        print(f"Applied material color ({r}, {g}, {b}) to {num_cells} cells")
                        return
                    else:
                        print("Material has no diffuse property, checking other properties...")
                        # Handle MultiMaterial - check individual materials
                        if hasattr(mesh.visual.material, 'materials'):
                            materials = mesh.visual.material.materials
                            print(f"Found {len(materials)} materials in MultiMaterial")

                            # Get colors from individual materials
                            material_colors = []
                            for i, mat in enumerate(materials):
                                print(f"Material {i}: {type(mat)}, attributes: {dir(mat)}")
                                if hasattr(mat, 'diffuse'):
                                    color = mat.diffuse
                                    material_colors.append(color)
                                    print(f"Material {i} diffuse: {color}")
                                elif hasattr(mat, 'main_color'):
                                    color = mat.main_color
                                    material_colors.append(color)
                                    print(f"Material {i} main_color: {color}")

                            if material_colors:
                                # Apply different colors to different parts of the model
                                import vtk
                                colors = vtk.vtkUnsignedCharArray()
                                colors.SetNumberOfComponents(3)
                                colors.SetName("Colors")

                                num_cells = self.current_polydata.GetNumberOfCells()
                                cells_per_material = num_cells // len(material_colors)

                                print(f"Applying {len(material_colors)} different colors to {num_cells} cells")

                                for i in range(num_cells):
                                    # Determine which material to use based on cell index
                                    material_index = min(i // cells_per_material, len(material_colors) - 1)
                                    color = material_colors[material_index]

                                    if color is not None:
                                        # Color values are already 0-255, not 0-1
                                        r, g, b = int(color[0]), int(color[1]), int(color[2])
                                        colors.InsertNextTuple3(r, g, b)
                                    else:
                                        # Default gray if no color
                                        colors.InsertNextTuple3(128, 128, 128)

                                self.current_polydata.GetCellData().SetScalars(colors)
                                self.current_polydata.GetCellData().SetActiveScalars("Colors")
                                print(f"Applied multi-material colors to {num_cells} cells")
                                return

                        # Try other color properties
                        if hasattr(mesh.visual.material, 'ambient'):
                            color = mesh.visual.material.ambient
                            print(f"Using ambient color: {color}")
                        elif hasattr(mesh.visual.material, 'main_color'):
                            color = mesh.visual.material.main_color
                            print(f"Using main color: {color}")
                        else:
                            print("No color properties found in material")

                # Check for face colors (ColorVisuals)
                elif hasattr(mesh.visual, 'face_colors') and mesh.visual.face_colors is not None:
                    face_colors = mesh.visual.face_colors
                    print(f"Found face colors: {face_colors.shape}")

                    # Add colors to VTK polydata
                    import vtk
                    colors = vtk.vtkUnsignedCharArray()
                    colors.SetNumberOfComponents(3)
                    colors.SetName("Colors")

                    # Check if all colors are the same
                    all_same = all(
                        color[0] == face_colors[0][0] and
                        color[1] == face_colors[0][1] and
                        color[2] == face_colors[0][2]
                        for color in face_colors[:10]  # Check first 10
                    )

                    # Check for variety in colors
                    unique_colors = set()
                    for color in face_colors[:50]:  # Check more colors
                        unique_colors.add((int(color[0]), int(color[1]), int(color[2])))

                    print(f"Found {len(unique_colors)} unique colors in first 50 faces")
                    print(f"Sample colors: {list(unique_colors)[:5]}")

                    if len(unique_colors) <= 2 and all_same:
                        print(f"Limited color variety, adding palette for better visibility")
                        # Add some color variety for better visibility
                        color_palette = [
                            (255, 100, 100),  # Red
                            (100, 255, 100),  # Green
                            (100, 100, 255),  # Blue
                            (255, 255, 100),  # Yellow
                            (255, 100, 255),  # Magenta
                            (100, 255, 255),  # Cyan
                        ]

                        for i, color in enumerate(face_colors):
                            palette_color = color_palette[i % len(color_palette)]
                            colors.InsertNextTuple3(palette_color[0], palette_color[1], palette_color[2])

                            # Debug first few colors
                            if i < 5:
                                print(f"Face {i} color: {palette_color}")
                    else:
                        # Use original colors
                        for i, color in enumerate(face_colors):
                            # Handle RGBA or RGB colors with proper precision
                            r, g, b = color[0], color[1], color[2]

                            # Convert to 0-255 range if needed
                            if r <= 1.0 and g <= 1.0 and b <= 1.0:
                                r, g, b = int(r * 255), int(g * 255), int(b * 255)
                            else:
                                r, g, b = int(r), int(g), int(b)

                            colors.InsertNextTuple3(r, g, b)

                            # Debug first few colors
                            if i < 5:
                                print(f"Face {i} color: ({r}, {g}, {b})")

                    self.current_polydata.GetCellData().SetScalars(colors)
                    self.current_polydata.GetCellData().SetActiveScalars("Colors")
                    print("Applied face colors to polydata and set as active scalars")

                # Check for vertex colors
                elif hasattr(mesh.visual, 'vertex_colors') and mesh.visual.vertex_colors is not None:
                    vertex_colors = mesh.visual.vertex_colors
                    print(f"Found vertex colors: {vertex_colors.shape}")

                    # Add colors to VTK polydata
                    import vtk
                    colors = vtk.vtkUnsignedCharArray()
                    colors.SetNumberOfComponents(3)
                    colors.SetName("Colors")

                    for color in vertex_colors:
                        # Handle RGBA or RGB colors with proper precision
                        r, g, b = color[0], color[1], color[2]

                        # Convert to 0-255 range if needed
                        if r <= 1.0 and g <= 1.0 and b <= 1.0:
                            r, g, b = int(r * 255), int(g * 255), int(b * 255)
                        else:
                            r, g, b = int(r), int(g), int(b)

                        colors.InsertNextTuple3(r, g, b)

                    self.current_polydata.GetPointData().SetScalars(colors)
                    self.current_polydata.GetPointData().SetActiveScalars("Colors")
                    print("Applied vertex colors to polydata and set as active scalars")

                # Check for material properties
                elif hasattr(mesh.visual, 'material'):
                    material = mesh.visual.material
                    if hasattr(material, 'diffuse'):
                        diffuse_color = material.diffuse
                        print(f"Found material diffuse color: {diffuse_color}")
                        self.default_color = diffuse_color[:3]  # Store RGB values

                else:
                    print("No color information found in visual properties")
                    self.default_color = [0.8, 0.8, 0.9]  # Default light gray
            else:
                print("No visual properties found")
                self.default_color = [0.8, 0.8, 0.9]  # Default light gray

        except Exception as e:
            print(f"Error extracting colors: {e}")
            self.default_color = [0.8, 0.8, 0.9]  # Default light gray