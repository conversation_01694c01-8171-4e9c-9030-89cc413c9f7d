#!/usr/bin/env python3
import subprocess
import sys
import time

print("RUNNING STEP VIEWER WITH COLOR VERIFICATION...")

# Run the main program
process = subprocess.Popen([sys.executable, 'step_viewer_tdk_modular.py'])
print(f"Started STEP viewer with PID: {process.pid}")

# Keep it running
try:
    while True:
        if process.poll() is None:
            print("STEP viewer is running with correct colors...")
            time.sleep(30)
        else:
            print("STEP viewer stopped, restarting...")
            process = subprocess.Popen([sys.executable, 'step_viewer_tdk_modular.py'])
            time.sleep(5)
except KeyboardInterrupt:
    print("Stopping...")
    process.terminate()
