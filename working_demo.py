#!/usr/bin/env python3
"""
Working Demo - Actually loads files and shows rotation save working
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QTimer

def main():
    app = QApplication(sys.argv)
    
    # Import viewer
    from step_viewer_tdk_modular_fixed import StepViewerTDK
    viewer = StepViewerTDK()
    viewer.show()
    viewer.setWindowTitle("Working Demo - Will Auto-Load Files")
    
    # Find test file
    test_file = None
    if os.path.exists('test.step'):
        test_file = 'test.step'
    elif os.path.exists('SOIC16P127_1270X940X610L89X51.STEP'):
        test_file = 'SOIC16P127_1270X940X610L89X51.STEP'
    
    if not test_file:
        msg = QMessageBox()
        msg.setText("No STEP file found to demo with!")
        msg.exec_()
        return
    
    def auto_load():
        print(f"Auto-loading {test_file}...")

        # Set active viewer to left
        viewer.active_viewer = 'top'

        # Load file using the main viewer's method (which handles the step_loader properly)
        try:
            success = viewer.load_step_file_direct(test_file)
            if success:
                print("✓ File loaded in LEFT viewer")
                viewer.setWindowTitle(f"Demo: {test_file} loaded in LEFT")

                # Wait then apply rotations
                QTimer.singleShot(3000, apply_rotations)
            else:
                print("✗ Failed to load file")
                viewer.setWindowTitle("Demo: FAILED to load file")
        except Exception as e:
            print(f"✗ Load error: {e}")
            viewer.setWindowTitle(f"Demo: Load error - {e}")
    
    def apply_rotations():
        print("Applying rotations...")
        try:
            viewer.rotate_shape('x', 15.0)
            viewer.rotate_shape('y', 30.0)
            viewer.rotate_shape('z', 45.0)
            print("✓ Rotations applied: X=15°, Y=30°, Z=45°")
            viewer.setWindowTitle("Demo: Rotations applied - X=15°, Y=30°, Z=45°")
            
            # Wait then save
            QTimer.singleShot(3000, save_rotated)
        except Exception as e:
            print(f"✗ Rotation error: {e}")
            viewer.setWindowTitle(f"Demo: Rotation error - {e}")
    
    def save_rotated():
        print("Saving rotated file...")
        output_file = "working_demo_output.step"
        
        try:
            delta_pos = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            delta_rot = {'x': 15.0, 'y': 30.0, 'z': 45.0}
            
            success = viewer._save_step_opencascade_transform(
                output_file,
                viewer.step_loader_left,
                delta_pos,
                delta_rot
            )
            
            if success and os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                print(f"✓ Saved: {output_file} ({file_size:,} bytes)")
                viewer.setWindowTitle(f"Demo: Saved {file_size:,} bytes")
                
                # Wait then load in right viewer
                QTimer.singleShot(3000, lambda: load_saved(output_file))
            else:
                print("✗ Save failed")
                viewer.setWindowTitle("Demo: SAVE FAILED")
                
        except Exception as e:
            print(f"✗ Save error: {e}")
            viewer.setWindowTitle(f"Demo: Save error - {e}")
    
    def load_saved(saved_file):
        print(f"Loading saved file in RIGHT viewer...")
        try:
            # Switch to bottom viewer and load
            viewer.active_viewer = 'bottom'
            success = viewer.load_step_file_direct(saved_file)

            if success:
                print("✓ Saved file loaded in RIGHT viewer")
                viewer.setWindowTitle("Demo: COMPLETE - Both viewers loaded")

                # Show completion message
                QTimer.singleShot(2000, show_completion)
            else:
                print("✗ Failed to load saved file")
                viewer.setWindowTitle("Demo: FAILED to load saved file")

        except Exception as e:
            print(f"✗ Load saved error: {e}")
            viewer.setWindowTitle(f"Demo: Load saved error - {e}")
    
    def show_completion():
        msg = QMessageBox()
        msg.setWindowTitle("Demo Complete")
        msg.setText(
            "ROTATION SAVE DEMO COMPLETE!\n\n"
            "LEFT viewer: Original file with rotations applied\n"
            "RIGHT viewer: Saved file with rotations preserved\n\n"
            "If both viewers show the same rotated geometry,\n"
            "then the OpenCASCADE rotation save fix is working!\n\n"
            "You can now see the proof with your own eyes."
        )
        msg.exec_()
    
    # Start the demo sequence after GUI is shown
    QTimer.singleShot(2000, auto_load)
    
    # Run the application
    app.exec_()

if __name__ == "__main__":
    main()
