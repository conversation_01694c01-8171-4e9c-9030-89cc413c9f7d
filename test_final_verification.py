#!/usr/bin/env python3
"""
FINAL VERIFICATION TEST - Test both fixes work end-to-end
"""

import sys
import os
import time
import math
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QLabel
from PyQt5.QtCore import QTimer
import vtk
from vtk.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor

class FinalVerificationTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("FINAL VERIFICATION TEST - Both Fixes")
        self.setGeometry(100, 100, 1200, 700)
        
        # State tracking
        self.current_rot = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_axis = {'x': 0.0, 'y': 0.0, 'z': 1.0}
        self.current_angle = 0.0
        self.last_orientation = [0.0, 0.0, 0.0]
        
        # VTK components
        self.vtk_widget = None
        self.renderer = None
        self.step_actor = None
        self.text_actor = None
        
        # Test results
        self.color_test_passed = False
        self.rotation_test_passed = False
        self.mouse_test_passed = False
        
        self.init_ui()
        self.setup_vtk()
        
        # Mouse tracking timer
        self.mouse_timer = QTimer()
        self.mouse_timer.timeout.connect(self.check_mouse_rotation)
        self.mouse_timer.start(200)
        
    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QHBoxLayout(central_widget)
        
        # Left panel
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_panel.setFixedWidth(400)
        
        # Test buttons
        self.test1_btn = QPushButton("1. TEST COLORS: Load STEP with Colors")
        self.test1_btn.clicked.connect(self.test_colors_fix)
        left_layout.addWidget(self.test1_btn)
        
        self.test2_btn = QPushButton("2. TEST ROTATION: X+15° Button")
        self.test2_btn.clicked.connect(self.test_rotation_fix)
        left_layout.addWidget(self.test2_btn)
        
        self.test3_btn = QPushButton("3. TEST MOUSE: Rotate with Mouse")
        self.test3_btn.clicked.connect(self.test_mouse_rotation)
        left_layout.addWidget(self.test3_btn)
        
        # Test results
        left_layout.addWidget(QLabel("TEST RESULTS:"))
        self.color_result = QLabel("❓ Colors: Not tested")
        self.rotation_result = QLabel("❓ Button rotation: Not tested")
        self.mouse_result = QLabel("❓ Mouse rotation: Not tested")
        
        left_layout.addWidget(self.color_result)
        left_layout.addWidget(self.rotation_result)
        left_layout.addWidget(self.mouse_result)
        
        # Current values
        left_layout.addWidget(QLabel("CURRENT VALUES:"))
        self.rot_label = QLabel("ROT: X=0.0° Y=0.0° Z=0.0°")
        self.axis_label = QLabel("AXIS: X=0.0 Y=0.0 Z=1.0")
        self.angle_label = QLabel("ANGLE: 0.0°")
        
        left_layout.addWidget(self.rot_label)
        left_layout.addWidget(self.axis_label)
        left_layout.addWidget(self.angle_label)
        
        # Overall result
        left_layout.addWidget(QLabel("OVERALL RESULT:"))
        self.overall_result = QLabel("❓ Not all tests completed")
        self.overall_result.setStyleSheet("font-weight: bold; font-size: 14pt;")
        left_layout.addWidget(self.overall_result)
        
        layout.addWidget(left_panel)
        
        # Right panel - VTK viewer
        self.vtk_widget = QVTKRenderWindowInteractor()
        layout.addWidget(self.vtk_widget)
        
    def setup_vtk(self):
        """Setup VTK"""
        self.renderer = vtk.vtkRenderer()
        self.renderer.SetBackground(0.1, 0.1, 0.2)
        
        self.vtk_widget.GetRenderWindow().AddRenderer(self.renderer)
        self.vtk_widget.Initialize()
        self.vtk_widget.Start()
        
        # Create text overlay
        self.text_actor = vtk.vtkTextActor()
        self.text_actor.SetInput("Load a STEP file to test")
        self.text_actor.SetPosition(10, 10)
        
        text_prop = self.text_actor.GetTextProperty()
        text_prop.SetFontSize(14)
        text_prop.SetColor(1.0, 1.0, 1.0)
        
        self.renderer.AddActor2D(self.text_actor)
        self.vtk_widget.GetRenderWindow().Render()
        
        print("✅ VTK setup complete")
        
    def test_colors_fix(self):
        """Test 1: Colors are displayed correctly"""
        print("🎨 TESTING COLOR FIX")
        print("=" * 40)
        
        # Use a STEP file that has colors
        filename = "debug_auto_saved.step"
        
        if not os.path.exists(filename):
            print(f"❌ File {filename} not found")
            self.color_result.setText("❌ Colors: Test file not found")
            return
            
        try:
            # Load with step_loader
            from step_loader import STEPLoader
            loader = STEPLoader()
            success, message = loader.load_step_file(filename)
            
            if not success or not loader.current_polydata:
                print("❌ STEP loading failed")
                self.color_result.setText("❌ Colors: STEP loading failed")
                return
                
            polydata = loader.current_polydata
            cell_colors = polydata.GetCellData().GetScalars()
            
            if not cell_colors:
                print("❌ No colors in STEP file")
                self.color_result.setText("❌ Colors: No colors in STEP file")
                return
                
            print(f"✅ Colors found: {cell_colors.GetNumberOfTuples()} colors")
            
            # Create VTK actor using vtk_renderer (same as main program)
            from vtk_renderer import VTKRenderer
            
            # Remove old actor
            if self.step_actor:
                self.renderer.RemoveActor(self.step_actor)
                
            # Create renderer instance and use its display method
            vtk_renderer = VTKRenderer(self.renderer, None)
            vtk_renderer.display_polydata(polydata)
            
            # Get the actor that was created
            self.step_actor = vtk_renderer.step_actor
            
            # Reset camera and render
            self.renderer.ResetCamera()
            self.vtk_widget.GetRenderWindow().Render()
            
            # Initialize tracking
            self.last_orientation = list(self.step_actor.GetOrientation())
            
            # Check if colors are actually applied
            mapper = self.step_actor.GetMapper()
            if mapper.GetScalarVisibility():
                print("✅ Colors are being used by VTK")
                self.color_result.setText("✅ Colors: STEP file colors displayed")
                self.color_test_passed = True
            else:
                print("❌ Colors not being used by VTK")
                self.color_result.setText("❌ Colors: Not applied to display")
                
            self.update_overall_result()
            
        except Exception as e:
            print(f"❌ Color test error: {e}")
            self.color_result.setText("❌ Colors: Test failed")
            
    def test_rotation_fix(self):
        """Test 2: Button rotation adds to current displayed value"""
        print("🔘 TESTING ROTATION FIX")
        print("=" * 40)
        
        if not self.step_actor:
            print("❌ No model loaded - load STEP file first")
            self.rotation_result.setText("❌ Rotation: No model loaded")
            return
            
        # Get current displayed X rotation value
        initial_x = self.current_rot['x']
        print(f"Initial X rotation: {initial_x:.1f}°")
        
        # Simulate button click: X+15°
        degrees = 15.0
        axis = 'x'
        
        # Apply rotation (same logic as main program)
        self.current_rot[axis] += degrees
        
        # Apply VTK rotation
        self.step_actor.RotateWXYZ(degrees, 1, 0, 0)
        
        # Calculate axis and angle
        self.calculate_axis_and_angle()
        
        # Update displays
        self.update_displays()
        
        # Render
        self.vtk_widget.GetRenderWindow().Render()
        
        # Check if value increased correctly
        new_x = self.current_rot['x']
        expected_x = initial_x + degrees
        
        print(f"Expected X rotation: {expected_x:.1f}°")
        print(f"Actual X rotation: {new_x:.1f}°")
        
        if abs(new_x - expected_x) < 0.1:
            print("✅ Button rotation adds to current value correctly")
            self.rotation_result.setText("✅ Rotation: Adds to current value")
            self.rotation_test_passed = True
        else:
            print("❌ Button rotation does not add correctly")
            self.rotation_result.setText("❌ Rotation: Does not add correctly")
            
        self.update_overall_result()
        
    def test_mouse_rotation(self):
        """Test 3: Mouse rotation updates displayed values"""
        print("🖱️ TESTING MOUSE ROTATION")
        print("=" * 40)
        
        if not self.step_actor:
            print("❌ No model loaded - load STEP file first")
            self.mouse_result.setText("❌ Mouse: No model loaded")
            return
            
        print("Rotate the model with your mouse now...")
        print("The test will automatically detect mouse rotation")
        self.mouse_result.setText("🔄 Mouse: Waiting for rotation...")
        
        # The mouse timer will detect rotation and update the result
        
    def check_mouse_rotation(self):
        """Check mouse rotation and update test result"""
        if not self.step_actor:
            return
            
        try:
            current_orientation = list(self.step_actor.GetOrientation())
            
            # Check for change
            changed = False
            for i in range(3):
                if abs(current_orientation[i] - self.last_orientation[i]) > 1.0:
                    changed = True
                    break
                    
            if changed:
                print(f"🖱️ MOUSE ROTATION DETECTED: {current_orientation}")
                
                # Update rotation values
                old_rot = self.current_rot.copy()
                self.current_rot['x'] = current_orientation[0]
                self.current_rot['y'] = current_orientation[1]
                self.current_rot['z'] = current_orientation[2]
                
                # Calculate axis
                self.calculate_axis_and_angle()
                
                # Update displays
                self.update_displays()
                
                self.last_orientation = current_orientation
                
                # Check if axis values changed
                if (abs(self.current_axis['x']) > 0.1 or 
                    abs(self.current_axis['y']) > 0.1 or
                    abs(self.current_rot['x'] - old_rot['x']) > 1.0 or
                    abs(self.current_rot['y'] - old_rot['y']) > 1.0 or
                    abs(self.current_rot['z'] - old_rot['z']) > 1.0):
                    
                    print("✅ Mouse rotation updates displayed values")
                    self.mouse_result.setText("✅ Mouse: Updates displayed values")
                    self.mouse_test_passed = True
                    self.update_overall_result()
                
        except Exception as e:
            pass
            
    def calculate_axis_and_angle(self):
        """Calculate axis and angle from rotation"""
        try:
            rot_mag = math.sqrt(self.current_rot['x']**2 + self.current_rot['y']**2 + self.current_rot['z']**2)
            
            if rot_mag > 0.001:
                self.current_axis = {
                    'x': self.current_rot['x'] / rot_mag,
                    'y': self.current_rot['y'] / rot_mag,
                    'z': self.current_rot['z'] / rot_mag
                }
                self.current_angle = rot_mag
            else:
                self.current_axis = {'x': 0.0, 'y': 0.0, 'z': 1.0}
                self.current_angle = 0.0
                
        except Exception as e:
            print(f"❌ Axis calculation error: {e}")
            
    def update_displays(self):
        """Update displays"""
        # Update GUI labels
        self.rot_label.setText(f"ROT: X={self.current_rot['x']:.1f}° Y={self.current_rot['y']:.1f}° Z={self.current_rot['z']:.1f}°")
        self.axis_label.setText(f"AXIS: X={self.current_axis['x']:.3f} Y={self.current_axis['y']:.3f} Z={self.current_axis['z']:.3f}")
        self.angle_label.setText(f"ANGLE: {self.current_angle:.1f}°")
        
        # Update VTK text
        text_content = (
            f"ROT: X={self.current_rot['x']:.1f}° Y={self.current_rot['y']:.1f}° Z={self.current_rot['z']:.1f}°\n"
            f"AXIS: X={self.current_axis['x']:.3f} Y={self.current_axis['y']:.3f} Z={self.current_axis['z']:.3f}\n"
            f"ANGLE: {self.current_angle:.1f}°"
        )
        self.text_actor.SetInput(text_content)
        
    def update_overall_result(self):
        """Update overall test result"""
        if self.color_test_passed and self.rotation_test_passed and self.mouse_test_passed:
            self.overall_result.setText("🎉 ALL TESTS PASSED - FIXES WORK!")
            self.overall_result.setStyleSheet("font-weight: bold; font-size: 14pt; color: green;")
            print("🎉 ALL TESTS PASSED - BOTH FIXES WORK CORRECTLY!")
        elif self.color_test_passed and self.rotation_test_passed:
            self.overall_result.setText("⚠️ Colors & Rotation OK - Test Mouse")
            self.overall_result.setStyleSheet("font-weight: bold; font-size: 14pt; color: orange;")
        else:
            tests_passed = sum([self.color_test_passed, self.rotation_test_passed, self.mouse_test_passed])
            self.overall_result.setText(f"⚠️ {tests_passed}/3 Tests Passed")
            self.overall_result.setStyleSheet("font-weight: bold; font-size: 14pt; color: orange;")

def main():
    app = QApplication(sys.argv)
    window = FinalVerificationTest()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
