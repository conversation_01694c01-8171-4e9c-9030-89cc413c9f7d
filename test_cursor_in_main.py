#!/usr/bin/env python3
"""
Test cursor functionality in a simplified version of the main program
This bypasses VTK rendering issues while testing cursor logic
"""

import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QLabel, QPushButton, QGroupBox)
from PyQt5.QtCore import Qt

class CursorTestMain(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Cursor Test - Main Program Style")
        self.setGeometry(100, 100, 1200, 800)
        
        # Initialize cursor positions
        self.cursor_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.cursor_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        
        # Initialize STEP file data (from your example)
        self.orig_pos_left = {'x': -4.190000000000000, 'y': -3.667300000000000, 'z': 0.491400000000000}
        self.current_rot_left = {'x': -24.443, 'y': 0.0, 'z': 0.0}  # Calculated angles
        
        self.orig_pos_right = {'x': -4.190000000000000, 'y': -3.667300000000000, 'z': 0.491400000000000}
        self.current_rot_right = {'x': -24.443, 'y': 0.0, 'z': 0.0}  # Calculated angles
        
        self.init_ui()
        
    def init_ui(self):
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QHBoxLayout(central_widget)
        
        # Left side - controls
        controls_widget = QWidget()
        controls_widget.setMaximumWidth(300)
        controls_layout = QVBoxLayout(controls_widget)
        
        # Title
        title = QLabel("CURSOR TEST")
        title.setStyleSheet("font-size: 16px; font-weight: bold; color: blue; padding: 10px;")
        controls_layout.addWidget(title)
        
        # Instructions
        instructions = QLabel("Move mouse over the viewer areas to test cursor tracking")
        instructions.setStyleSheet("font-size: 12px; padding: 10px; background-color: lightyellow;")
        instructions.setWordWrap(True)
        controls_layout.addWidget(instructions)
        
        # STEP file data display
        step_group = QGroupBox("STEP File Data")
        step_layout = QVBoxLayout(step_group)
        
        pos_label = QLabel(f"Position: X={self.orig_pos_left['x']:.3f}, Y={self.orig_pos_left['y']:.3f}, Z={self.orig_pos_left['z']:.3f}")
        pos_label.setStyleSheet("font-size: 11px; color: darkgreen;")
        step_layout.addWidget(pos_label)
        
        angle_label = QLabel(f"Angles: X={self.current_rot_left['x']:.3f}°, Y={self.current_rot_left['y']:.3f}°, Z={self.current_rot_left['z']:.3f}°")
        angle_label.setStyleSheet("font-size: 11px; color: darkgreen;")
        step_layout.addWidget(angle_label)
        
        controls_layout.addWidget(step_group)
        
        # Spacer
        controls_layout.addStretch()
        
        main_layout.addWidget(controls_widget)
        
        # Right side - viewers
        viewers_widget = QWidget()
        viewers_layout = QVBoxLayout(viewers_widget)
        
        # Top viewer
        top_group = QGroupBox("TOP VIEWER")
        top_layout = QVBoxLayout(top_group)
        
        # Top viewer text overlay (like the real program)
        self.top_text = QLabel("ANGLE ROT: X=-24.443° Y=0.000° Z=0.000° POS: X=-4.190mm Y=-3.667mm Z=0.491mm CURSOR: X=0.000 Y=0.000 Z=0.000")
        self.top_text.setStyleSheet("font-size: 14px; color: yellow; background-color: black; padding: 8px; font-family: monospace;")
        self.top_text.setWordWrap(True)
        top_layout.addWidget(self.top_text)
        
        # Top viewer area
        self.top_viewer = QLabel("TOP VIEWER AREA\n\nMove mouse here to test cursor tracking")
        self.top_viewer.setStyleSheet("font-size: 14px; background-color: darkblue; color: white; padding: 50px;")
        self.top_viewer.setAlignment(Qt.AlignCenter)
        self.top_viewer.setMouseTracking(True)
        self.top_viewer.mouseMoveEvent = self.on_top_mouse_move
        top_layout.addWidget(self.top_viewer)
        
        viewers_layout.addWidget(top_group)
        
        # Bottom viewer
        bottom_group = QGroupBox("BOTTOM VIEWER")
        bottom_layout = QVBoxLayout(bottom_group)
        
        # Bottom viewer text overlay
        self.bottom_text = QLabel("ANGLE ROT: X=-24.443° Y=0.000° Z=0.000° POS: X=-4.190mm Y=-3.667mm Z=0.491mm CURSOR: X=0.000 Y=0.000 Z=0.000")
        self.bottom_text.setStyleSheet("font-size: 14px; color: yellow; background-color: black; padding: 8px; font-family: monospace;")
        self.bottom_text.setWordWrap(True)
        bottom_layout.addWidget(self.bottom_text)
        
        # Bottom viewer area
        self.bottom_viewer = QLabel("BOTTOM VIEWER AREA\n\nMove mouse here to test cursor tracking")
        self.bottom_viewer.setStyleSheet("font-size: 14px; background-color: darkgreen; color: white; padding: 50px;")
        self.bottom_viewer.setAlignment(Qt.AlignCenter)
        self.bottom_viewer.setMouseTracking(True)
        self.bottom_viewer.mouseMoveEvent = self.on_bottom_mouse_move
        bottom_layout.addWidget(self.bottom_viewer)
        
        viewers_layout.addWidget(bottom_group)
        
        main_layout.addWidget(viewers_widget)
        
        # Enable mouse tracking for the main window
        self.setMouseTracking(True)
        central_widget.setMouseTracking(True)
        
    def on_top_mouse_move(self, event):
        """Handle mouse move in TOP viewer area"""
        # Convert mouse position to world coordinates (simplified)
        x = (event.x() - 200) / 50.0  # Center and scale
        y = (100 - event.y()) / 50.0  # Invert Y and scale
        z = 0.5  # Fixed Z for 2D mouse
        
        # Update cursor position
        self.cursor_pos_left = {'x': x, 'y': y, 'z': z}
        
        # Update text overlay
        self.top_text.setText(f"ANGLE ROT: X={self.current_rot_left['x']:.3f}° Y={self.current_rot_left['y']:.3f}° Z={self.current_rot_left['z']:.3f}° POS: X={self.orig_pos_left['x']:.3f}mm Y={self.orig_pos_left['y']:.3f}mm Z={self.orig_pos_left['z']:.3f}mm CURSOR: X={self.cursor_pos_left['x']:.3f} Y={self.cursor_pos_left['y']:.3f} Z={self.cursor_pos_left['z']:.3f}")
        
    def on_bottom_mouse_move(self, event):
        """Handle mouse move in BOTTOM viewer area"""
        # Convert mouse position to world coordinates (simplified)
        x = (event.x() - 200) / 50.0  # Center and scale
        y = (100 - event.y()) / 50.0  # Invert Y and scale
        z = 1.0  # Fixed Z for 2D mouse
        
        # Update cursor position
        self.cursor_pos_right = {'x': x, 'y': y, 'z': z}
        
        # Update text overlay
        self.bottom_text.setText(f"ANGLE ROT: X={self.current_rot_right['x']:.3f}° Y={self.current_rot_right['y']:.3f}° Z={self.current_rot_right['z']:.3f}° POS: X={self.orig_pos_right['x']:.3f}mm Y={self.orig_pos_right['y']:.3f}mm Z={self.orig_pos_right['z']:.3f}mm CURSOR: X={self.cursor_pos_right['x']:.3f} Y={self.cursor_pos_right['y']:.3f} Z={self.cursor_pos_right['z']:.3f}")

def main():
    app = QApplication(sys.argv)
    
    # Create and show the test window
    window = CursorTestMain()
    window.show()
    
    print("=== CURSOR TEST - MAIN PROGRAM STYLE ===")
    print("This shows how cursor tracking would work in the main program")
    print("✅ STEP file angles: X=-24.443° (calculated from DIRECTION vectors)")
    print("✅ STEP file position: (-4.190, -3.667, 0.491)")
    print("✅ Cursor tracking: Move mouse over viewer areas")
    print("The yellow text should update with cursor position in real-time")
    
    # Run the application
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
