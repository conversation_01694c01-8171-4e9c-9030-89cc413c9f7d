#!/usr/bin/env python3

print("COMPARING FREECAD vs OUR COLOR EXTRACTION")
print("=" * 60)

try:
    import FreeCAD
    import Import
    print("FreeCAD imported successfully")
    
    # Load the same STEP file in FreeCAD
    print("\nStep 1: Loading STEP file in FreeCAD...")
    doc = FreeCAD.newDocument("ColorTest")
    Import.insert('SOIC16P127_1270X940X610L89X51.STEP', doc.Name)
    
    print(f"FreeCAD document created with {len(doc.Objects)} objects")
    
    # Check colors of all objects
    print("\nStep 2: Checking FreeCAD object colors...")
    for i, obj in enumerate(doc.Objects):
        print(f"\nObject {i}: {obj.Name} (Type: {obj.TypeId})")
        
        if hasattr(obj, 'ViewObject'):
            view_obj = obj.ViewObject
            
            # Check various color properties
            if hasattr(view_obj, 'ShapeColor'):
                color = view_obj.ShapeColor
                r, g, b = int(color[0] * 255), int(color[1] * 255), int(color[2] * 255)
                print(f"  ShapeColor: RGB({r}, {g}, {b}) - Raw: {color}")
            
            if hasattr(view_obj, 'DiffuseColor'):
                diff_colors = view_obj.DiffuseColor
                if diff_colors:
                    print(f"  DiffuseColor: {len(diff_colors)} face colors")
                    # Show first few face colors
                    for j, face_color in enumerate(diff_colors[:5]):
                        r, g, b = int(face_color[0] * 255), int(face_color[1] * 255), int(face_color[2] * 255)
                        print(f"    Face {j}: RGB({r}, {g}, {b}) - Raw: {face_color}")
                    if len(diff_colors) > 5:
                        print(f"    ... and {len(diff_colors) - 5} more face colors")
            
            if hasattr(view_obj, 'Transparency'):
                print(f"  Transparency: {view_obj.Transparency}")
                
            # Check material properties
            if hasattr(view_obj, 'Material'):
                material = view_obj.Material
                if material:
                    print(f"  Material: {material}")
        
        # Check if object has Shape and get face count
        if hasattr(obj, 'Shape') and obj.Shape:
            print(f"  Shape: {obj.Shape.ShapeType}, Faces: {len(obj.Shape.Faces)}")
    
    print("\nStep 3: Checking document-level color settings...")
    
    # Check if there are any document-level color settings
    if hasattr(doc, 'Objects'):
        for obj in doc.Objects:
            if hasattr(obj, 'Material'):
                print(f"Object {obj.Name} Material: {obj.Material}")
    
    # Close the document
    FreeCAD.closeDocument(doc.Name)
    print("\nFreeCAD document closed")
    
except ImportError:
    print("FreeCAD not available - cannot compare colors")
    print("This would require FreeCAD Python API to be installed")
    
except Exception as e:
    print(f"Error accessing FreeCAD: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "=" * 60)

# Now compare with our extraction
print("Step 4: Our color extraction results:")
print("  Dark parts: RGB(13, 13, 13) - Almost black")
print("  Light parts: RGB(134, 134, 134) - Medium gray")

print("\nStep 5: Analysis:")
print("  If FreeCAD shows different colors than RGB(13, 13, 13),")
print("  then FreeCAD is likely adjusting/interpreting the colors")
print("  for better visibility or using different color spaces.")

print("\nCOMPARISON COMPLETE")
