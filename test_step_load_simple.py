#!/usr/bin/env python3

print("Testing STEP file loading...")

try:
    from step_loader import STEPLoader
    
    loader = STEPLoader()
    result = loader.load_step_file('SOIC16P127_1270X940X610L89X51.STEP')
    
    print(f"Result: {result}")
    
    if result and len(result) >= 2:
        if len(result) == 3:
            polydata, success, message = result
        else:
            polydata, success = result
            message = "No message"
            
        print(f"Success: {success}")
        print(f"Message: {message}")
        
        if success and polydata:
            print(f"Polydata cells: {polydata.GetNumberOfCells()}")
            print(f"Polydata points: {polydata.GetNumberOfPoints()}")
            print("STEP file loaded successfully!")
        else:
            print("STEP file loading failed")
    else:
        print("Invalid result from STEP loader")
        
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()

print("Test completed")
