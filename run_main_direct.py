#!/usr/bin/env python3
"""
Run main program directly and capture output
"""

import subprocess
import sys

try:
    result = subprocess.run(
        [sys.executable, 'step_viewer_tdk_modular.py'],
        capture_output=True,
        text=True,
        timeout=30
    )
    
    print("STDOUT:")
    print(result.stdout)
    print("\nSTDERR:")
    print(result.stderr)
    print(f"\nReturn code: {result.returncode}")
    
except subprocess.TimeoutExpired:
    print("Program is running (timeout after 30 seconds)")
except Exception as e:
    print(f"Error: {e}")
