#!/usr/bin/env python3
"""
Debug script to compare VTK rotation matrices with our STEP coordinate system calculations
"""

import vtk
import math
import numpy as np

def print_matrix(matrix, name):
    """Print a 4x4 matrix in readable format"""
    print(f"\n{name}:")
    for i in range(4):
        row = []
        for j in range(4):
            row.append(f"{matrix.GetElement(i, j):8.4f}")
        print("  [" + " ".join(row) + "]")

def vtk_rotation_matrix(rx, ry, rz):
    """Create VTK rotation matrix (XYZ order)"""
    transform = vtk.vtkTransform()
    transform.Identity()
    
    # VTK applies in this order: X -> Y -> Z
    transform.RotateX(rx)
    transform.RotateY(ry) 
    transform.RotateZ(rz)
    
    return transform.GetMatrix()

def step_rotation_matrix_xyz(rx, ry, rz):
    """Create rotation matrix using XYZ order (our current approach)"""
    rx_rad = math.radians(rx)
    ry_rad = math.radians(ry)
    rz_rad = math.radians(rz)
    
    cos_x, sin_x = math.cos(rx_rad), math.sin(rx_rad)
    cos_y, sin_y = math.cos(ry_rad), math.sin(ry_rad)
    cos_z, sin_z = math.cos(rz_rad), math.sin(rz_rad)
    
    # XYZ order rotation matrix
    r11 = cos_y * cos_z
    r12 = -cos_y * sin_z
    r13 = sin_y
    r21 = cos_x * sin_z + sin_x * sin_y * cos_z
    r22 = cos_x * cos_z - sin_x * sin_y * sin_z
    r23 = -sin_x * cos_y
    r31 = sin_x * sin_z - cos_x * sin_y * cos_z
    r32 = sin_x * cos_z + cos_x * sin_y * sin_z
    r33 = cos_x * cos_y
    
    # Create VTK matrix for comparison
    matrix = vtk.vtkMatrix4x4()
    matrix.Identity()
    
    # Set rotation part (top-left 3x3)
    matrix.SetElement(0, 0, r11)
    matrix.SetElement(0, 1, r12)
    matrix.SetElement(0, 2, r13)
    matrix.SetElement(1, 0, r21)
    matrix.SetElement(1, 1, r22)
    matrix.SetElement(1, 2, r23)
    matrix.SetElement(2, 0, r31)
    matrix.SetElement(2, 1, r32)
    matrix.SetElement(2, 2, r33)
    
    return matrix

def extract_axes_from_matrix(matrix):
    """Extract X and Z axes from rotation matrix"""
    # X-axis is first column
    x_axis = [matrix.GetElement(0, 0), matrix.GetElement(1, 0), matrix.GetElement(2, 0)]
    # Z-axis is third column  
    z_axis = [matrix.GetElement(0, 2), matrix.GetElement(1, 2), matrix.GetElement(2, 2)]
    
    return x_axis, z_axis

def test_rotation(rx, ry, rz):
    """Test a specific rotation and compare matrices"""
    print(f"\n{'='*60}")
    print(f"TESTING ROTATION: X={rx}°, Y={ry}°, Z={rz}°")
    print(f"{'='*60}")
    
    # Get VTK matrix
    vtk_matrix = vtk_rotation_matrix(rx, ry, rz)
    print_matrix(vtk_matrix, "VTK Matrix (RotateX->RotateY->RotateZ)")
    
    # Get our STEP matrix
    step_matrix = step_rotation_matrix_xyz(rx, ry, rz)
    print_matrix(step_matrix, "STEP Matrix (XYZ order)")
    
    # Compare matrices element by element
    print(f"\nMATRIX COMPARISON:")
    max_diff = 0
    for i in range(3):  # Only check rotation part (3x3)
        for j in range(3):
            vtk_val = vtk_matrix.GetElement(i, j)
            step_val = step_matrix.GetElement(i, j)
            diff = abs(vtk_val - step_val)
            max_diff = max(max_diff, diff)
            if diff > 0.0001:
                print(f"  DIFF at [{i},{j}]: VTK={vtk_val:.6f}, STEP={step_val:.6f}, diff={diff:.6f}")
    
    if max_diff < 0.0001:
        print("  ✅ MATRICES MATCH!")
    else:
        print(f"  ❌ MATRICES DIFFER (max diff: {max_diff:.6f})")
    
    # Extract and compare axes
    vtk_x_axis, vtk_z_axis = extract_axes_from_matrix(vtk_matrix)
    step_x_axis, step_z_axis = extract_axes_from_matrix(step_matrix)
    
    print(f"\nAXES COMPARISON:")
    print(f"  VTK  X-axis: ({vtk_x_axis[0]:8.4f}, {vtk_x_axis[1]:8.4f}, {vtk_x_axis[2]:8.4f})")
    print(f"  STEP X-axis: ({step_x_axis[0]:8.4f}, {step_x_axis[1]:8.4f}, {step_x_axis[2]:8.4f})")
    print(f"  VTK  Z-axis: ({vtk_z_axis[0]:8.4f}, {vtk_z_axis[1]:8.4f}, {vtk_z_axis[2]:8.4f})")
    print(f"  STEP Z-axis: ({step_z_axis[0]:8.4f}, {step_z_axis[1]:8.4f}, {step_z_axis[2]:8.4f})")

if __name__ == "__main__":
    print("🔍 DEBUGGING ROTATION MATRIX CALCULATIONS")
    print("Comparing VTK transformations with STEP coordinate system calculations")
    
    # Test simple rotations
    test_rotation(90, 0, 0)    # X rotation only
    test_rotation(0, 90, 0)    # Y rotation only  
    test_rotation(0, 0, 90)    # Z rotation only
    test_rotation(90, 45, 0)   # Combined X+Y
    test_rotation(45, 45, 45)  # All three axes
    
    print(f"\n{'='*60}")
    print("🎯 ANALYSIS COMPLETE")
    print("If matrices don't match, we need to fix the rotation order or calculation!")
    print(f"{'='*60}")
