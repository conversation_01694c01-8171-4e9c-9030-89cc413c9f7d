#!/usr/bin/env python3
"""
Manual debug version - YOU control everything, I just watch and debug
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication, QFileDialog
from PyQt5.QtCore import QTimer
from step_viewer_tdk_modular import StepViewerTDK

class ManualDebugWorkflow:
    def __init__(self):
        self.app = QApplication([])
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        print("🧪 MANUAL DEBUG WORKFLOW")
        print("=" * 60)
        print("YOU control everything - I just watch and debug!")
        print("")
        print("Instructions:")
        print("1. Load any STEP file into TOP window")
        print("2. Rotate it with MOUSE (not buttons)")
        print("3. Save with green button")
        print("4. Load saved file into BOTTOM window")
        print("5. Compare results")
        print("")
        print("I will show debug info for every action you take.")
        print("=" * 60)
        
        # Override methods to add debug output
        self.original_save_method = self.viewer.save_step_file_option1
        self.viewer.save_step_file_option1 = self.debug_save_method
        
        self.original_load_method = self.viewer.load_step_file_direct
        self.viewer.load_step_file_direct = self.debug_load_method
        
        # Monitor rotation changes
        self.last_rotation_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.last_rotation_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        
        # Start monitoring
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.monitor_rotations)
        self.monitor_timer.start(1000)  # Check every second
        
        print("✅ Debug monitoring started!")
        print("🎯 Waiting for your actions...")
        
    def debug_save_method(self):
        """Debug wrapper for save method"""
        print("\n" + "="*60)
        print("🔧 DEBUG: GREEN SAVE BUTTON CLICKED!")
        print("="*60)
        
        # Check current state before saving
        print(f"📊 Active viewer: {self.viewer.active_viewer}")
        
        if self.viewer.active_viewer == "top":
            if hasattr(self.viewer, 'model_rot_left'):
                print(f"📊 TOP model_rot_left: {self.viewer.model_rot_left}")
            if hasattr(self.viewer, 'current_rot_left'):
                print(f"📊 TOP current_rot_left: {self.viewer.current_rot_left}")
            if hasattr(self.viewer, 'orig_rot_left'):
                print(f"📊 TOP orig_rot_left: {self.viewer.orig_rot_left}")
        else:
            if hasattr(self.viewer, 'model_rot_right'):
                print(f"📊 BOTTOM model_rot_right: {self.viewer.model_rot_right}")
            if hasattr(self.viewer, 'current_rot_right'):
                print(f"📊 BOTTOM current_rot_right: {self.viewer.current_rot_right}")
            if hasattr(self.viewer, 'orig_rot_right'):
                print(f"📊 BOTTOM orig_rot_right: {self.viewer.orig_rot_right}")
        
        # Calculate what delta would be
        if self.viewer.active_viewer == "top":
            current_rot = getattr(self.viewer, 'model_rot_left', {'x': 0, 'y': 0, 'z': 0})
            orig_rot = getattr(self.viewer, 'orig_rot_left', {'x': 0, 'y': 0, 'z': 0})
        else:
            current_rot = getattr(self.viewer, 'model_rot_right', {'x': 0, 'y': 0, 'z': 0})
            orig_rot = getattr(self.viewer, 'orig_rot_right', {'x': 0, 'y': 0, 'z': 0})
            
        delta_rot = {
            'x': current_rot['x'] - orig_rot['x'],
            'y': current_rot['y'] - orig_rot['y'],
            'z': current_rot['z'] - orig_rot['z']
        }
        
        print(f"📊 SAVE CALCULATION:")
        print(f"   current_rot: {current_rot}")
        print(f"   orig_rot: {orig_rot}")
        print(f"   delta_rot: {delta_rot}")
        
        rot_changed = (abs(delta_rot['x']) > 0.001 or abs(delta_rot['y']) > 0.001 or abs(delta_rot['z']) > 0.001)
        print(f"   rotation_changed: {rot_changed}")
        
        if rot_changed:
            print("   ✅ SAVE will detect rotation and apply transformations")
        else:
            print("   ❌ SAVE will NOT detect rotation (will just copy original file)")
        
        # Call original save method
        result = self.original_save_method()
        
        print(f"📊 Save result: {result}")
        print("="*60)
        
        return result
        
    def debug_load_method(self, filename):
        """Debug wrapper for load method"""
        print("\n" + "="*60)
        print(f"🔧 DEBUG: LOAD FILE CALLED: {filename}")
        print("="*60)
        
        print(f"📊 Active viewer: {self.viewer.active_viewer}")
        print(f"📊 File exists: {os.path.exists(filename)}")
        
        if os.path.exists(filename):
            # Check if file has rotation marker
            with open(filename, 'r') as f:
                content = f.read()
                if "ROTATION_VALUES:" in content:
                    print("✅ File contains ROTATION_VALUES marker")
                    import re
                    match = re.search(r'ROTATION_VALUES: X=([\d.-]+) Y=([\d.-]+) Z=([\d.-]+)', content)
                    if match:
                        x, y, z = map(float, match.groups())
                        print(f"📊 Stored rotation in file: X={x}°, Y={y}°, Z={z}°")
                else:
                    print("❌ File does NOT contain ROTATION_VALUES marker")
        
        # Call original load method
        result = self.original_load_method(filename)
        
        # Check state after loading
        print(f"📊 AFTER LOAD:")
        if self.viewer.active_viewer == "top":
            if hasattr(self.viewer, 'orig_rot_left'):
                print(f"   TOP orig_rot_left: {self.viewer.orig_rot_left}")
            if hasattr(self.viewer, 'current_rot_left'):
                print(f"   TOP current_rot_left: {self.viewer.current_rot_left}")
            if hasattr(self.viewer, 'model_rot_left'):
                print(f"   TOP model_rot_left: {self.viewer.model_rot_left}")
        else:
            if hasattr(self.viewer, 'orig_rot_right'):
                print(f"   BOTTOM orig_rot_right: {self.viewer.orig_rot_right}")
            if hasattr(self.viewer, 'current_rot_right'):
                print(f"   BOTTOM current_rot_right: {self.viewer.current_rot_right}")
            if hasattr(self.viewer, 'model_rot_right'):
                print(f"   BOTTOM model_rot_right: {self.viewer.model_rot_right}")
                
        print(f"📊 Load result: {result}")
        print("="*60)
        
        return result
        
    def monitor_rotations(self):
        """Monitor rotation changes"""
        # Check TOP window rotations
        if hasattr(self.viewer, 'model_rot_left'):
            current_left = self.viewer.model_rot_left
            if current_left != self.last_rotation_left:
                print(f"\n🔄 TOP ROTATION CHANGED:")
                print(f"   From: {self.last_rotation_left}")
                print(f"   To:   {current_left}")
                self.last_rotation_left = current_left.copy()
                
        # Check BOTTOM window rotations
        if hasattr(self.viewer, 'model_rot_right'):
            current_right = self.viewer.model_rot_right
            if current_right != self.last_rotation_right:
                print(f"\n🔄 BOTTOM ROTATION CHANGED:")
                print(f"   From: {self.last_rotation_right}")
                print(f"   To:   {current_right}")
                self.last_rotation_right = current_right.copy()

def main():
    debug_workflow = ManualDebugWorkflow()
    debug_workflow.app.exec_()

if __name__ == "__main__":
    main()
