#!/usr/bin/env python3
"""
Debug Show Exact Numbers - Show precise positions before and after reset
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular import StepViewerTDK

def debug_show_exact_numbers():
    """Show exact numbers before and after reset"""
    
    print("🔧 DEBUG SHOW EXACT NUMBERS")
    print("=" * 60)
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    # Process events to ensure GUI is ready
    app.processEvents()
    time.sleep(1)
    
    # Find a STEP file to test with
    test_files = [
        "AMPHENOL_U77-A1118-200T.STEP",
        "test.step",
        "sample.stp"
    ]
    
    test_file = None
    for filename in test_files:
        if os.path.exists(filename):
            test_file = filename
            break
    
    if not test_file:
        print("❌ No STEP file found for testing")
        return False
    
    print(f"📁 Using test file: {test_file}")
    
    def show_all_actor_positions(label):
        """Show positions of all actors"""
        print(f"\n🔍 {label}")
        print("-" * 50)
        
        renderer = viewer.vtk_renderer_left
        if hasattr(renderer, 'renderer') and renderer.renderer:
            actor_collection = renderer.renderer.GetActors()
            actor_collection.InitTraversal()
            
            actor_count = 0
            actor = actor_collection.GetNextActor()
            while actor:
                actor_count += 1
                pos = actor.GetPosition()
                orient = actor.GetOrientation()
                bounds = actor.GetBounds()
                visible = actor.GetVisibility()
                
                # Identify actor type
                actor_type = "UNKNOWN"
                if hasattr(renderer, 'step_actors') and renderer.step_actors:
                    for j, multi_actor in enumerate(renderer.step_actors):
                        if multi_actor == actor:
                            actor_type = f"MULTI-ACTOR_{j}"
                            
                if hasattr(renderer, 'bbox_actor') and renderer.bbox_actor == actor:
                    actor_type = "BOUNDING_BOX"
                    
                if hasattr(renderer, 'step_actor') and renderer.step_actor == actor:
                    actor_type = "SINGLE_ACTOR"
                
                print(f"Actor {actor_count} ({actor_type}):")
                print(f"  Position: {pos}")
                print(f"  Orientation: {orient}")
                print(f"  Bounds: {bounds}")
                print(f"  Visible: {visible}")
                print()
                
                actor = actor_collection.GetNextActor()
            
            print(f"Total actors found: {actor_count}")
        else:
            print("❌ No renderer found")
    
    # Step 1: Load model and show initial positions
    print(f"\n📋 STEP 1: LOADING MODEL...")
    success = viewer.load_step_file_direct(test_file)
    app.processEvents()
    time.sleep(2)
    
    if not success:
        print("❌ Failed to load STEP file")
        return False
    
    show_all_actor_positions("INITIAL POSITIONS AFTER LOADING")
    
    # Step 2: Apply transformations and show positions
    print(f"\n📋 STEP 2: APPLYING TRANSFORMATIONS...")
    
    viewer.active_viewer = "top"
    
    print(f"🔧 Moving +100mm X")
    viewer.move_shape("x", 100)
    app.processEvents()
    time.sleep(1)
    
    print(f"🔧 Rotating +180° Z")
    viewer.rotate_shape("z", 180)
    app.processEvents()
    time.sleep(1)
    
    show_all_actor_positions("POSITIONS AFTER TRANSFORMATIONS")
    
    # Step 3: Reset and show final positions
    print(f"\n📋 STEP 3: RESETTING TO ORIGINAL...")
    
    print(f"🔧 Calling reset...")
    viewer.reset_to_original()
    app.processEvents()
    time.sleep(2)
    
    show_all_actor_positions("POSITIONS AFTER RESET")
    
    # Step 4: Analysis
    print(f"\n📋 STEP 4: ANALYSIS")
    print("=" * 60)
    
    print(f"🎯 VERIFICATION CHECKLIST:")
    print(f"1. Initial positions should be at or near (0,0,0)")
    print(f"2. After transformations, positions should be (100,0,0) with 180° Z rotation")
    print(f"3. After reset, positions should return to initial values")
    print(f"4. All VISIBLE actors should move together")
    
    print(f"\n🔍 WHAT TO CHECK:")
    print(f"- Do the numbers show the expected changes?")
    print(f"- Are visible actors being reset to (0,0,0)?")
    print(f"- Do the bounds change appropriately?")
    
    # Keep window open
    print(f"\n👁️ Window will stay open for visual verification...")
    QTimer.singleShot(15000, app.quit)
    app.exec_()
    
    return True

if __name__ == "__main__":
    debug_show_exact_numbers()
