#!/usr/bin/env python3
"""
Automated test for Option 1 save issue - NO USER INTERVENTION REQUIRED
This will programmatically test the rotation save problem and generate a detailed report.
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main program
sys.path.append('.')
from step_viewer_tdk_modular import StepViewerTDK

class AutomatedOption1Test:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        self.test_results = []
        self.test_step = 0
        
    def run_test(self):
        """Run the complete automated test"""
        print("AUTOMATED OPTION 1 SAVE TEST")
        print("=" * 50)
        print("This test will:")
        print("1. Create viewer instance")
        print("2. Load test.step automatically")
        print("3. Apply rotation programmatically")
        print("4. Save with Option 1")
        print("5. Load saved file and compare")
        print("6. Generate detailed report")
        print("=" * 50)
        
        # Create viewer
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        # Start automated test sequence
        QTimer.singleShot(1000, self.step1_load_file)
        
        # Run the application
        self.app.exec_()
        
    def step1_load_file(self):
        """Step 1: Load test file"""
        print("\n🔄 STEP 1: Loading test.step...")
        
        if not os.path.exists('test.step'):
            self.log_error("test.step not found!")
            return
            
        success = self.viewer.load_step_file_direct('test.step')
        if success:
            print("✅ test.step loaded successfully")
            self.log_result("File Load", "SUCCESS", "test.step loaded")
            QTimer.singleShot(2000, self.step2_apply_rotation)
        else:
            self.log_error("Failed to load test.step")
            
    def step2_apply_rotation(self):
        """Step 2: Apply rotation programmatically"""
        print("\n[STEP 2] Applying rotation...")

        try:
            # Set active viewer to top
            self.viewer.active_viewer = "top"

            # Apply rotation directly to the viewer's rotation values
            target_rotation = {'x': 0.0, 'y': 0.0, 'z': 174.0}

            # Update the viewer's current rotation values
            self.viewer.current_rot_left = target_rotation.copy()

            # Try to apply rotation to VTK actors
            success = False

            # Method 1: Try multi-actors (most likely)
            if hasattr(self.viewer, 'vtk_renderer_left') and self.viewer.vtk_renderer_left:
                if hasattr(self.viewer.vtk_renderer_left, 'multi_actors') and self.viewer.vtk_renderer_left.multi_actors:
                    for actor in self.viewer.vtk_renderer_left.multi_actors:
                        actor.SetOrientation(target_rotation['x'], target_rotation['y'], target_rotation['z'])
                    self.viewer.vtk_renderer_left.render_window.Render()
                    success = True
                    print(f"Applied rotation to {len(self.viewer.vtk_renderer_left.multi_actors)} multi-actors")

                # Method 2: Try single actor
                elif hasattr(self.viewer.vtk_renderer_left, 'step_actor') and self.viewer.vtk_renderer_left.step_actor:
                    self.viewer.vtk_renderer_left.step_actor.SetOrientation(target_rotation['x'], target_rotation['y'], target_rotation['z'])
                    self.viewer.vtk_renderer_left.render_window.Render()
                    success = True
                    print("Applied rotation to single step_actor")

            if success:
                print(f"SUCCESS: Rotation applied: {target_rotation}")
                self.log_result("Rotation Applied", "SUCCESS", f"Target: {target_rotation}")
            else:
                print("WARNING: Could not find VTK actors to rotate, but rotation values set")
                self.log_result("Rotation Applied", "PARTIAL", f"Values set but VTK not rotated: {target_rotation}")

            # Update text overlays to show new values
            self.viewer.update_text_overlays()

            QTimer.singleShot(2000, self.step3_capture_before_save)

        except Exception as e:
            self.log_error(f"Failed to apply rotation: {e}")
            
    def step3_capture_before_save(self):
        """Step 3: Capture values before save"""
        print("\n🔄 STEP 3: Capturing values before save...")
        
        try:
            # Get current values from viewer
            current_rot = getattr(self.viewer, 'current_rot_left', {'x': 0, 'y': 0, 'z': 0})
            current_pos = getattr(self.viewer, 'current_pos_left', {'x': 0, 'y': 0, 'z': 0})
            orig_rot = getattr(self.viewer, 'orig_rot_left', {'x': 0, 'y': 0, 'z': 0})
            orig_pos = getattr(self.viewer, 'orig_pos_left', {'x': 0, 'y': 0, 'z': 0})
            
            self.before_save_values = {
                'current_rot': current_rot,
                'current_pos': current_pos,
                'orig_rot': orig_rot,
                'orig_pos': orig_pos
            }
            
            print(f"📊 BEFORE SAVE VALUES:")
            print(f"   Current Rotation: {current_rot}")
            print(f"   Current Position: {current_pos}")
            print(f"   Original Rotation: {orig_rot}")
            print(f"   Original Position: {orig_pos}")
            
            self.log_result("Before Save Capture", "SUCCESS", f"Current rot: {current_rot}")
            
            QTimer.singleShot(1000, self.step4_save_option1)
            
        except Exception as e:
            self.log_error(f"Failed to capture before-save values: {e}")
            
    def step4_save_option1(self):
        """Step 4: Save with Option 1"""
        print("\n🔄 STEP 4: Saving with Option 1...")
        
        try:
            # Set filename for save
            save_filename = "automated_test_option1.step"
            
            # Remove old file if exists
            if os.path.exists(save_filename):
                os.remove(save_filename)
            
            # Get the save method values
            loader = self.viewer.step_loader_left
            current_rot = self.before_save_values['current_rot']
            current_pos = self.before_save_values['current_pos']
            orig_rot = self.before_save_values['orig_rot']
            orig_pos = self.before_save_values['orig_pos']
            
            print(f"🔧 Calling _save_step_with_transformations...")
            print(f"   Filename: {save_filename}")
            print(f"   Current rot: {current_rot}")
            print(f"   Original rot: {orig_rot}")
            
            # Call the save method directly
            success = self.viewer._save_step_with_transformations(
                save_filename, loader, current_pos, current_rot, orig_pos, orig_rot
            )
            
            if success and os.path.exists(save_filename):
                file_size = os.path.getsize(save_filename)
                print(f"✅ Option 1 save completed: {save_filename} ({file_size} bytes)")
                self.log_result("Option 1 Save", "SUCCESS", f"File: {save_filename}, Size: {file_size}")
                self.saved_filename = save_filename
                QTimer.singleShot(2000, self.step5_load_saved_file)
            else:
                self.log_error("Option 1 save failed - file not created")
                
        except Exception as e:
            self.log_error(f"Option 1 save failed: {e}")
            
    def step5_load_saved_file(self):
        """Step 5: Load saved file and compare"""
        print("\n🔄 STEP 5: Loading saved file for comparison...")
        
        try:
            # Switch to bottom viewer
            self.viewer.active_viewer = "bottom"
            
            # Load the saved file
            success = self.viewer.load_step_file_direct(self.saved_filename)
            
            if success:
                print(f"✅ Saved file loaded in bottom viewer")
                
                # Wait a moment for loading to complete
                QTimer.singleShot(3000, self.step6_compare_results)
            else:
                self.log_error("Failed to load saved file")
                
        except Exception as e:
            self.log_error(f"Failed to load saved file: {e}")
            
    def step6_compare_results(self):
        """Step 6: Compare results and generate report"""
        print("\n🔄 STEP 6: Comparing results...")
        
        try:
            # Get values from bottom viewer (loaded saved file)
            after_rot = getattr(self.viewer, 'current_rot_right', {'x': 0, 'y': 0, 'z': 0})
            after_pos = getattr(self.viewer, 'current_pos_right', {'x': 0, 'y': 0, 'z': 0})
            
            before_rot = self.before_save_values['current_rot']
            before_pos = self.before_save_values['current_pos']
            
            print(f"📊 COMPARISON RESULTS:")
            print(f"   BEFORE SAVE - Rotation: {before_rot}")
            print(f"   AFTER LOAD  - Rotation: {after_rot}")
            print(f"   BEFORE SAVE - Position: {before_pos}")
            print(f"   AFTER LOAD  - Position: {after_pos}")
            
            # Check if rotation was preserved
            rot_preserved = (
                abs(before_rot['x'] - after_rot['x']) < 1.0 and
                abs(before_rot['y'] - after_rot['y']) < 1.0 and
                abs(before_rot['z'] - after_rot['z']) < 1.0
            )
            
            if rot_preserved:
                print("✅ ROTATION PRESERVED - Option 1 save working correctly!")
                self.log_result("Rotation Preservation", "SUCCESS", "Rotation preserved correctly")
            else:
                print("❌ ROTATION LOST - Option 1 save not working!")
                self.log_result("Rotation Preservation", "FAILED", f"Before: {before_rot}, After: {after_rot}")
            
            QTimer.singleShot(1000, self.step7_generate_report)
            
        except Exception as e:
            self.log_error(f"Failed to compare results: {e}")
            
    def step7_generate_report(self):
        """Step 7: Generate final report"""
        print("\n🔄 STEP 7: Generating final report...")
        
        report = f"""
AUTOMATED OPTION 1 SAVE TEST REPORT
===================================
Test Date: {time.strftime('%Y-%m-%d %H:%M:%S')}

TEST RESULTS:
"""
        
        for result in self.test_results:
            status_icon = "[OK]" if result['status'] == 'SUCCESS' else "[FAIL]"
            report += f"{status_icon} {result['test']}: {result['status']} - {result['details']}\n"
        
        # Check for debug files
        debug_files = ['debug_option1_save.txt', 'debug_delta_calculation.txt']
        report += f"\nDEBUG FILES CREATED:\n"
        for debug_file in debug_files:
            if os.path.exists(debug_file):
                report += f"[OK] {debug_file} - Available for analysis\n"
            else:
                report += f"[MISSING] {debug_file} - Not created\n"

        report += f"\nCONCLUSION:\n"
        failed_tests = [r for r in self.test_results if r['status'] == 'FAILED']
        if failed_tests:
            report += f"[FAIL] OPTION 1 SAVE HAS ISSUES - {len(failed_tests)} test(s) failed\n"
            report += f"   Primary issue: Rotation not being preserved in saved file\n"
        else:
            report += f"[SUCCESS] OPTION 1 SAVE WORKING CORRECTLY - All tests passed\n"
        
        # Save report with UTF-8 encoding
        with open('automated_option1_test_report.txt', 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(report)
        print(f"\n📄 Full report saved to: automated_option1_test_report.txt")
        
        # Close the application
        QTimer.singleShot(2000, self.app.quit)
        
    def log_result(self, test_name, status, details):
        """Log a test result"""
        self.test_results.append({
            'test': test_name,
            'status': status,
            'details': details
        })
        
    def log_error(self, error_msg):
        """Log an error and continue"""
        print(f"❌ ERROR: {error_msg}")
        self.test_results.append({
            'test': f'Step {self.test_step}',
            'status': 'FAILED',
            'details': error_msg
        })
        # Continue with report generation
        QTimer.singleShot(1000, self.step7_generate_report)

if __name__ == "__main__":
    test = AutomatedOption1Test()
    test.run_test()
