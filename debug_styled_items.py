#!/usr/bin/env python3

print("DEBUG STYLED_ITEM PARSING")

# Read STEP file
with open('SOIC16P127_1270X940X610L89X51.STEP', 'r', encoding='utf-8', errors='ignore') as f:
    step_text = f.read()

lines = step_text.split('\n')

# Test the regex pattern
import re

print("Testing regex patterns...")

# Pattern 1: Current pattern
pattern1 = r'STYLED_ITEM.*?\(\s*#(\d+)\s*\).*?#(\d+)\s*\)'
matches1 = []

# Pattern 2: Simpler pattern
pattern2 = r'STYLED_ITEM.*?#(\d+).*?#(\d+)'
matches2 = []

for line in lines:
    if 'STYLED_ITEM' in line:
        match1 = re.search(pattern1, line)
        match2 = re.search(pattern2, line)
        
        if match1:
            matches1.append((match1.group(1), match1.group(2)))
        
        if match2:
            matches2.append((match2.group(1), match2.group(2)))
        
        # Show first few lines for debugging
        if len(matches1) < 5 or len(matches2) < 5:
            print(f"Line: {line}")
            print(f"  Pattern1 match: {match1.groups() if match1 else None}")
            print(f"  Pattern2 match: {match2.groups() if match2 else None}")

print(f"\nPattern1 matches: {len(matches1)}")
print(f"Pattern2 matches: {len(matches2)}")

# Show first few matches
print(f"\nFirst 5 Pattern1 matches: {matches1[:5]}")
print(f"First 5 Pattern2 matches: {matches2[:5]}")

print("\nDEBUG STYLED_ITEM PARSING COMPLETE")
