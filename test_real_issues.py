#!/usr/bin/env python3
"""
TEST REAL ISSUES - Test the actual problems with minimal code
"""

import sys
import os
import time
import math
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QLabel
from PyQt5.QtCore import QTimer
import vtk
from vtk.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor

class TestRealIssues(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("TEST REAL ISSUES")
        self.setGeometry(100, 100, 1000, 600)
        
        # State tracking
        self.current_rot = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_axis = {'x': 0.0, 'y': 0.0, 'z': 1.0}
        self.current_angle = 0.0
        self.last_orientation = [0.0, 0.0, 0.0]
        
        # VTK components
        self.vtk_widget = None
        self.renderer = None
        self.step_actor = None
        self.text_actor = None
        
        self.init_ui()
        self.setup_vtk()
        
        # Mouse tracking timer
        self.mouse_timer = QTimer()
        self.mouse_timer.timeout.connect(self.check_mouse_rotation)
        self.mouse_timer.start(200)
        
    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QHBoxLayout(central_widget)
        
        # Left panel
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_panel.setFixedWidth(300)
        
        # Test buttons
        self.load_btn = QPushButton("1. Load STEP File")
        self.load_btn.clicked.connect(self.test_step_loading)
        left_layout.addWidget(self.load_btn)
        
        self.rotate_btn = QPushButton("2. Test Button Rotation X+15°")
        self.rotate_btn.clicked.connect(lambda: self.test_button_rotation('x', 15))
        left_layout.addWidget(self.rotate_btn)
        
        # Status display
        left_layout.addWidget(QLabel("CURRENT VALUES:"))
        self.rot_label = QLabel("ROT: X=0.0° Y=0.0° Z=0.0°")
        self.axis_label = QLabel("AXIS: X=0.0 Y=0.0 Z=1.0")
        self.angle_label = QLabel("ANGLE: 0.0°")
        
        left_layout.addWidget(self.rot_label)
        left_layout.addWidget(self.axis_label)
        left_layout.addWidget(self.angle_label)
        
        # Issue status
        left_layout.addWidget(QLabel("ISSUES:"))
        self.color_status = QLabel("❓ Colors: Unknown")
        self.mouse_status = QLabel("❓ Mouse rotation: Unknown")
        self.button_status = QLabel("❓ Button rotation: Unknown")
        
        left_layout.addWidget(self.color_status)
        left_layout.addWidget(self.mouse_status)
        left_layout.addWidget(self.button_status)
        
        layout.addWidget(left_panel)
        
        # Right panel - VTK viewer
        self.vtk_widget = QVTKRenderWindowInteractor()
        layout.addWidget(self.vtk_widget)
        
    def setup_vtk(self):
        """Setup VTK"""
        self.renderer = vtk.vtkRenderer()
        self.renderer.SetBackground(0.1, 0.1, 0.2)
        
        self.vtk_widget.GetRenderWindow().AddRenderer(self.renderer)
        self.vtk_widget.Initialize()
        self.vtk_widget.Start()
        
        # Create text overlay
        self.text_actor = vtk.vtkTextActor()
        self.text_actor.SetInput("Load a STEP file to test")
        self.text_actor.SetPosition(10, 10)
        
        text_prop = self.text_actor.GetTextProperty()
        text_prop.SetFontSize(14)
        text_prop.SetColor(1.0, 1.0, 1.0)
        
        self.renderer.AddActor2D(self.text_actor)
        self.vtk_widget.GetRenderWindow().Render()
        
        print("✅ VTK setup complete")
        
    def test_step_loading(self):
        """Test STEP file loading and colors"""
        from PyQt5.QtWidgets import QFileDialog
        
        filename, _ = QFileDialog.getOpenFileName(self, "Load STEP File", "", "STEP Files (*.step *.stp)")
        
        if not filename:
            return
            
        print(f"🔍 TESTING STEP LOADING: {os.path.basename(filename)}")
        
        try:
            # Load with step_loader
            from step_loader import STEPLoader
            loader = STEPLoader()
            success, message = loader.load_step_file(filename)
            
            print(f"   Load result: {success}, {message}")
            
            if not success or not loader.current_polydata:
                print("❌ STEP loading failed")
                return
                
            polydata = loader.current_polydata
            print(f"   Polydata: {polydata.GetNumberOfCells()} cells, {polydata.GetNumberOfPoints()} points")
            
            # Check colors
            cell_colors = polydata.GetCellData().GetScalars()
            if cell_colors:
                print(f"   ✅ Colors found: {cell_colors.GetNumberOfTuples()} colors")
                self.color_status.setText("✅ Colors: Found in STEP file")
            else:
                print("   ⚠️ No colors in STEP file")
                self.color_status.setText("⚠️ Colors: None in STEP file")
            
            # Create VTK actor
            if self.step_actor:
                self.renderer.RemoveActor(self.step_actor)
                
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputData(polydata)
            
            self.step_actor = vtk.vtkActor()
            self.step_actor.SetMapper(mapper)
            
            # Apply colors or default
            if cell_colors:
                print("   Applying STEP file colors...")
                mapper.SetScalarVisibility(True)
                mapper.SetScalarModeToUseCellData()
                mapper.SetColorModeToDirectScalars()
                self.step_actor.GetProperty().SetAmbient(0.3)
                self.step_actor.GetProperty().SetDiffuse(0.7)
                self.color_status.setText("✅ Colors: Applied from STEP")
            else:
                print("   Applying default gray color...")
                self.step_actor.GetProperty().SetColor(0.7, 0.7, 0.7)
                mapper.SetScalarVisibility(False)
                self.color_status.setText("✅ Colors: Default gray applied")
            
            # Add to renderer
            self.renderer.AddActor(self.step_actor)
            self.renderer.ResetCamera()
            self.vtk_widget.GetRenderWindow().Render()
            
            # Initialize tracking
            self.last_orientation = list(self.step_actor.GetOrientation())
            print(f"   Initial orientation: {self.last_orientation}")
            
            self.update_displays()
            
            print("✅ STEP loading test complete")
            
        except Exception as e:
            print(f"❌ STEP loading error: {e}")
            import traceback
            traceback.print_exc()
            
    def test_button_rotation(self, axis, degrees):
        """Test button rotation"""
        if not self.step_actor:
            print("❌ No model loaded - cannot test rotation")
            self.button_status.setText("❌ Button: No model loaded")
            return
            
        print(f"🔍 TESTING BUTTON ROTATION: {axis.upper()}{'+' if degrees > 0 else ''}{degrees}°")
        
        # Update rotation values
        old_value = self.current_rot[axis]
        self.current_rot[axis] += degrees
        print(f"   ROT updated: {axis.upper()} {old_value:.1f}° → {self.current_rot[axis]:.1f}°")
        
        # Apply VTK rotation
        self.step_actor.RotateWXYZ(degrees,
            1 if axis == 'x' else 0,
            1 if axis == 'y' else 0,
            1 if axis == 'z' else 0)
        
        new_orientation = list(self.step_actor.GetOrientation())
        print(f"   New VTK orientation: {[f'{x:.1f}' for x in new_orientation]}")
        
        # Calculate axis and angle
        self.calculate_axis_and_angle()
        
        # Update displays
        self.update_displays()
        
        # Render
        self.vtk_widget.GetRenderWindow().Render()
        
        # Check if axis changed
        if abs(self.current_axis['x']) > 0.1 or abs(self.current_axis['y']) > 0.1:
            self.button_status.setText("✅ Button: Axis values update")
            print("✅ Button rotation working - axis values changed")
        else:
            self.button_status.setText("❌ Button: Axis values don't update")
            print("❌ Button rotation broken - axis values didn't change")
        
    def check_mouse_rotation(self):
        """Check mouse rotation"""
        if not self.step_actor:
            return
            
        try:
            current_orientation = list(self.step_actor.GetOrientation())
            
            # Check for change
            changed = False
            for i in range(3):
                if abs(current_orientation[i] - self.last_orientation[i]) > 1.0:
                    changed = True
                    break
                    
            if changed:
                print(f"🖱️ MOUSE ROTATION DETECTED:")
                print(f"   Old: {[f'{x:.1f}' for x in self.last_orientation]}")
                print(f"   New: {[f'{x:.1f}' for x in current_orientation]}")
                
                # Update rotation values
                self.current_rot['x'] = current_orientation[0]
                self.current_rot['y'] = current_orientation[1]
                self.current_rot['z'] = current_orientation[2]
                
                # Calculate axis
                self.calculate_axis_and_angle()
                
                # Update displays
                self.update_displays()
                
                self.last_orientation = current_orientation
                
                # Check if axis changed
                if abs(self.current_axis['x']) > 0.1 or abs(self.current_axis['y']) > 0.1:
                    self.mouse_status.setText("✅ Mouse: Axis values update")
                    print("✅ Mouse rotation working - axis values changed")
                else:
                    self.mouse_status.setText("❌ Mouse: Axis values don't update")
                    print("❌ Mouse rotation broken - axis values didn't change")
                
        except Exception as e:
            pass
            
    def calculate_axis_and_angle(self):
        """Calculate axis and angle from rotation"""
        try:
            rot_mag = math.sqrt(self.current_rot['x']**2 + self.current_rot['y']**2 + self.current_rot['z']**2)
            print(f"      Rotation magnitude: {rot_mag:.3f}")
            
            if rot_mag > 0.001:
                self.current_axis = {
                    'x': self.current_rot['x'] / rot_mag,
                    'y': self.current_rot['y'] / rot_mag,
                    'z': self.current_rot['z'] / rot_mag
                }
                self.current_angle = rot_mag
                print(f"      Calculated axis: {self.current_axis}")
                print(f"      Calculated angle: {self.current_angle:.1f}°")
            else:
                self.current_axis = {'x': 0.0, 'y': 0.0, 'z': 1.0}
                self.current_angle = 0.0
                print(f"      Zero rotation - using default axis")
                
        except Exception as e:
            print(f"❌ Axis calculation error: {e}")
            
    def update_displays(self):
        """Update displays"""
        # Update GUI labels
        self.rot_label.setText(f"ROT: X={self.current_rot['x']:.1f}° Y={self.current_rot['y']:.1f}° Z={self.current_rot['z']:.1f}°")
        self.axis_label.setText(f"AXIS: X={self.current_axis['x']:.3f} Y={self.current_axis['y']:.3f} Z={self.current_axis['z']:.3f}")
        self.angle_label.setText(f"ANGLE: {self.current_angle:.1f}°")
        
        # Update VTK text
        text_content = (
            f"ROT: X={self.current_rot['x']:.1f}° Y={self.current_rot['y']:.1f}° Z={self.current_rot['z']:.1f}°\n"
            f"AXIS: X={self.current_axis['x']:.3f} Y={self.current_axis['y']:.3f} Z={self.current_axis['z']:.3f}\n"
            f"ANGLE: {self.current_angle:.1f}°"
        )
        self.text_actor.SetInput(text_content)

def main():
    app = QApplication(sys.argv)
    window = TestRealIssues()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
