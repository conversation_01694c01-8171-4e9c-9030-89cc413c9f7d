#!/usr/bin/env python3
"""
Test the rotation detection fix
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from step_viewer_tdk_modular import StepViewerTDK
from PyQt5.QtWidgets import QApplication
import tempfile

def test_rotation_detection():
    """Test if rotation detection works on saved files"""
    
    print("🔍 Testing rotation detection...")
    
    # Create Qt application
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    
    # Test files
    original_file = "test.step"
    if not os.path.exists(original_file):
        print(f"❌ Original file {original_file} not found")
        return
    
    print(f"\n📁 Testing original file: {original_file}")
    
    # Test coordinate system detection on original file
    orig_pos, orig_rot = viewer._extract_step_coordinate_system(original_file)
    print(f"   Original file detection:")
    print(f"   Position: {orig_pos}")
    print(f"   Rotation: {orig_rot}")
    
    # Create a rotated file
    temp_save_file = tempfile.mktemp(suffix='.step')
    
    # Load original file
    viewer.set_active_viewer("bottom")
    success, message = viewer.step_loader_right.load_step_file(original_file)
    
    if success:
        print(f"\n🔧 Creating rotated file...")
        
        # Apply 45° rotation and save
        try:
            loader = viewer.step_loader_right
            current_rot = {'x': 45.0, 'y': 0.0, 'z': 0.0}
            current_pos = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            orig_rot_save = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            orig_pos_save = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            
            success = viewer._save_step_with_transformations(
                temp_save_file, loader, current_pos, current_rot, orig_pos_save, orig_rot_save
            )
            
            if success:
                print(f"   ✅ Rotated file created: {temp_save_file}")
                
                # Test coordinate system detection on saved file
                print(f"\n📁 Testing saved file: {temp_save_file}")
                saved_pos, saved_rot = viewer._extract_step_coordinate_system(temp_save_file)
                print(f"   Saved file detection:")
                print(f"   Position: {saved_pos}")
                print(f"   Rotation: {saved_rot}")
                
                # Compare results
                print(f"\n🎯 COMPARISON:")
                if saved_rot and (saved_rot['x'] != 0 or saved_rot['y'] != 0 or saved_rot['z'] != 0):
                    print(f"   ✅ Rotation detected in saved file!")
                    print(f"   Expected: X=45°, Y=0°, Z=0°")
                    print(f"   Detected: X={saved_rot['x']:.1f}°, Y={saved_rot['y']:.1f}°, Z={saved_rot['z']:.1f}°")
                    
                    if abs(saved_rot['x'] - 45.0) < 5.0:  # Allow 5° tolerance
                        print(f"   ✅ X rotation is approximately correct!")
                    else:
                        print(f"   ❌ X rotation is not correct")
                else:
                    print(f"   ❌ No rotation detected in saved file")
                    print(f"   This means the coordinate system detection is still not working")
                
                # Cleanup
                os.remove(temp_save_file)
                
            else:
                print(f"   ❌ Failed to create rotated file")
        
        except Exception as e:
            print(f"   ❌ Error: {e}")
            import traceback
            traceback.print_exc()
    
    else:
        print(f"❌ Failed to load original file: {message}")
    
    app.quit()

if __name__ == "__main__":
    test_rotation_detection()
