#!/usr/bin/env python3
"""
Test VTK integration to see if we can get a working embedded VTK window
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton
from vtk.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor
import vtk

class TestVTKWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("VTK Integration Test")
        self.setGeometry(100, 100, 800, 600)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Test button
        test_btn = QPushButton("Test - Should show cube in VTK window below")
        test_btn.clicked.connect(self.create_cube)
        layout.addWidget(test_btn)
        
        # VTK widget - this should be embedded
        self.vtk_widget = QVTKRenderWindowInteractor()
        layout.addWidget(self.vtk_widget)
        
        # VTK setup
        self.renderer = vtk.vtkRenderer()
        self.renderer.SetBackground(0.1, 0.1, 0.2)
        
        self.vtk_widget.GetRenderWindow().AddRenderer(self.renderer)
        self.vtk_widget.Initialize()
        self.vtk_widget.Start()
        
        print("✅ VTK widget created and should be embedded in main window")
        
    def create_cube(self):
        """Create a simple cube to test VTK rendering"""
        # Create cube
        cube = vtk.vtkCubeSource()
        
        # Create mapper
        mapper = vtk.vtkPolyDataMapper()
        mapper.SetInputConnection(cube.GetOutputPort())
        
        # Create actor
        actor = vtk.vtkActor()
        actor.SetMapper(mapper)
        actor.GetProperty().SetColor(1.0, 0.5, 0.0)  # Orange
        
        # Add to renderer
        self.renderer.AddActor(actor)
        self.renderer.ResetCamera()
        self.vtk_widget.GetRenderWindow().Render()
        
        print("✅ Cube created - should appear in embedded VTK window")

def main():
    app = QApplication(sys.argv)
    window = TestVTKWindow()
    window.show()
    
    print("🧪 VTK INTEGRATION TEST")
    print("=" * 40)
    print("If working correctly:")
    print("- ONE window should appear")
    print("- VTK area should be embedded in the main window")
    print("- Click button to show orange cube")
    print("- NO separate blue windows should appear")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
