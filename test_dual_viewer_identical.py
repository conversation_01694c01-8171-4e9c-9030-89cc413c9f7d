#!/usr/bin/env python3
"""
Test dual viewer identical display - verify both viewers show the same model with matching view numbers
"""

import sys
import os
import tempfile
import time
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    print("🧪 DUAL VIEWER IDENTICAL DISPLAY TEST")
    print("=" * 60)
    print("This test will:")
    print("1. Load test.step in TOP viewer")
    print("2. Apply rotations and save to a new file")
    print("3. Load the saved file in BOTTOM viewer")
    print("4. Compare both viewers to verify they show identical models")
    print("5. Check that view numbers and transformations match")
    print("=" * 60)
    
    # Use test.step as the test file
    test_file = "test.step"
    
    if not os.path.exists(test_file):
        print(f"❌ Test file {test_file} not found!")
        print("   Please ensure test.step exists in the current directory")
        return False
    
    print(f"📁 Using test file: {test_file}")
    
    # Import the viewer module
    try:
        from step_viewer_tdk_modular_fixed import StepViewerTDK
        from PyQt5.QtWidgets import QApplication
        import vtk
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    
    # Create a temporary save file
    with tempfile.NamedTemporaryFile(suffix='.step', delete=False) as tmp_file:
        save_file = tmp_file.name
    
    try:
        print(f"\n🔧 STARTING DUAL VIEWER TEST...")
        
        # Create Qt application
        app = QApplication(sys.argv)
        
        # Create viewer
        viewer = StepViewerTDK()
        
        print("\n📋 STEP 1: Loading original file in TOP viewer")
        print("-" * 50)
        
        # Load the test file in the top viewer
        viewer.active_viewer = "top"
        loader_top = viewer.step_loader_left
        
        if not loader_top.load_step_file(test_file):
            print(f"❌ Failed to load {test_file} in TOP viewer")
            return False
        
        print(f"✅ Loaded {test_file} in TOP viewer")
        
        # Get initial state of TOP viewer
        print("\n🔍 TOP VIEWER INITIAL STATE:")
        top_initial_rot = {
            'x': viewer.current_rot_left.get('x', 0),
            'y': viewer.current_rot_left.get('y', 0), 
            'z': viewer.current_rot_left.get('z', 0)
        }
        print(f"   Rotation: X={top_initial_rot['x']}°, Y={top_initial_rot['y']}°, Z={top_initial_rot['z']}°")
        
        print("\n📋 STEP 2: Applying rotations to TOP viewer")
        print("-" * 50)
        
        # Apply rotations to the TOP viewer
        viewer.rotate_shape('x', 20)  # 20 degrees around X
        viewer.rotate_shape('y', 35)  # 35 degrees around Y  
        viewer.rotate_shape('z', 50)  # 50 degrees around Z
        print("✅ Applied rotations to TOP: X=20°, Y=35°, Z=50°")
        
        # Get rotated state of TOP viewer
        print("\n🔍 TOP VIEWER AFTER ROTATION:")
        top_rotated_rot = {
            'x': viewer.current_rot_left.get('x', 0),
            'y': viewer.current_rot_left.get('y', 0), 
            'z': viewer.current_rot_left.get('z', 0)
        }
        print(f"   Rotation: X={top_rotated_rot['x']}°, Y={top_rotated_rot['y']}°, Z={top_rotated_rot['z']}°")
        
        # Calculate total rotation angle for TOP
        import math
        top_total_angle = math.sqrt(top_rotated_rot['x']**2 + top_rotated_rot['y']**2 + top_rotated_rot['z']**2)
        print(f"   Total Angle: {top_total_angle:.1f}°")
        
        print("\n📋 STEP 3: Saving rotated model")
        print("-" * 50)
        
        # Save the rotated model
        print(f"💾 Saving rotated model to: {save_file}")
        
        success = viewer.save_step_file_option1_direct(save_file)
        
        if not success:
            print("❌ Save failed!")
            return False
        
        print("✅ Save completed")
        
        # Check file size
        if os.path.exists(save_file):
            size = os.path.getsize(save_file)
            print(f"📊 Saved file size: {size:,} bytes")
            
            if size < 1000:
                print("❌ Saved file too small - likely empty or corrupted")
                return False
        else:
            print("❌ Saved file does not exist")
            return False
        
        print("\n📋 STEP 4: Loading saved file in BOTTOM viewer")
        print("-" * 50)
        
        # Load the saved file in the bottom viewer
        viewer.active_viewer = "bottom"
        loader_bottom = viewer.step_loader_right
        
        if not loader_bottom.load_step_file(save_file):
            print(f"❌ Failed to load saved file in BOTTOM viewer")
            return False
        
        print(f"✅ Loaded saved file in BOTTOM viewer")
        
        # Get state of BOTTOM viewer
        print("\n🔍 BOTTOM VIEWER STATE:")
        bottom_rot = {
            'x': viewer.current_rot_right.get('x', 0),
            'y': viewer.current_rot_right.get('y', 0), 
            'z': viewer.current_rot_right.get('z', 0)
        }
        print(f"   Rotation: X={bottom_rot['x']}°, Y={bottom_rot['y']}°, Z={bottom_rot['z']}°")
        
        # Calculate total rotation angle for BOTTOM
        bottom_total_angle = math.sqrt(bottom_rot['x']**2 + bottom_rot['y']**2 + bottom_rot['z']**2)
        print(f"   Total Angle: {bottom_total_angle:.1f}°")
        
        print("\n📋 STEP 5: Comparing both viewers")
        print("-" * 50)
        
        # Compare the viewers
        print("🔍 VIEWER COMPARISON:")
        
        # Compare rotation values
        rotation_match = True
        tolerance = 0.1  # Allow small differences due to floating point precision
        
        for axis in ['x', 'y', 'z']:
            top_val = top_rotated_rot[axis]
            bottom_val = bottom_rot[axis]
            diff = abs(top_val - bottom_val)
            
            if diff <= tolerance:
                print(f"   {axis.upper()} Rotation: ✅ MATCH (TOP: {top_val:.1f}°, BOTTOM: {bottom_val:.1f}°)")
            else:
                print(f"   {axis.upper()} Rotation: ❌ DIFFERENT (TOP: {top_val:.1f}°, BOTTOM: {bottom_val:.1f}°, Diff: {diff:.1f}°)")
                rotation_match = False
        
        # Compare total angles
        angle_diff = abs(top_total_angle - bottom_total_angle)
        if angle_diff <= tolerance:
            print(f"   Total Angle: ✅ MATCH (TOP: {top_total_angle:.1f}°, BOTTOM: {bottom_total_angle:.1f}°)")
        else:
            print(f"   Total Angle: ❌ DIFFERENT (TOP: {top_total_angle:.1f}°, BOTTOM: {bottom_total_angle:.1f}°, Diff: {angle_diff:.1f}°)")
            rotation_match = False
        
        print("\n📊 FINAL RESULTS:")
        print("=" * 60)
        
        if rotation_match:
            print("🎉 SUCCESS: Both viewers show identical models!")
            print("   ✅ Rotation values match between TOP and BOTTOM viewers")
            print("   ✅ The coordinate system fix is working correctly")
            print("   ✅ Saved files maintain proper transformations")
            print("\n💡 This means:")
            print("   • TOP viewer shows original file with applied rotations")
            print("   • BOTTOM viewer shows saved file with same rotations")
            print("   • Both viewers display the same visual result")
            print("   • The dual-viewer system is working correctly")
        else:
            print("❌ FAILURE: Viewers show different models")
            print("   • Rotation values don't match between viewers")
            print("   • There may still be coordinate system issues")
            print("   • The saved file doesn't preserve transformations correctly")
        
        return rotation_match
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Clean up
        if os.path.exists(save_file):
            os.unlink(save_file)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
