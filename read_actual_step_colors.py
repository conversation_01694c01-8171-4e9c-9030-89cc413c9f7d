#!/usr/bin/env python3

print("READING ACTUAL STEP FILE SHAPE COLORS")

# Open STEP file as text
with open('SOIC16P127_1270X940X610L89X51.STEP', 'r', encoding='utf-8', errors='ignore') as f:
    step_text = f.read()

lines = step_text.split('\n')

# Find each shape entry
shapes = []
for line in lines:
    if 'ADVANCED_FACE' in line and '#' in line:
        import re
        match = re.search(r'#(\d+)\s*=\s*ADVANCED_FACE', line)
        if match:
            shape_id = int(match.group(1))
            shapes.append(shape_id)

print(f"Found {len(shapes)} shapes")

# Find what color each specific shape has
shape_colors = {}
for line in lines:
    if 'STYLED_ITEM' in line and '#' in line:
        # Find which shape this line refers to
        import re
        match = re.search(r'STYLED_ITEM.*?#(\d+).*?#(\d+)', line)
        if match:
            color_ref = int(match.group(1))
            shape_id = int(match.group(2))
            
            # Find the actual RGB color for this color reference
            for color_line in lines:
                if f'#{color_ref}' in color_line and 'COLOUR_RGB' in color_line:
                    rgb_match = re.search(r'([\d\.]+)\s*,\s*([\d\.]+)\s*,\s*([\d\.]+)', color_line)
                    if rgb_match:
                        r = int(float(rgb_match.group(1)) * 255)
                        g = int(float(rgb_match.group(2)) * 255)
                        b = int(float(rgb_match.group(3)) * 255)
                        shape_colors[shape_id] = (r, g, b)
                        break

print(f"Found colors for {len(shape_colors)} shapes")

# Show first 10 shape colors
print("First 10 shape colors:")
count = 0
for shape_id, color in shape_colors.items():
    if count < 10:
        print(f"Shape #{shape_id}: {color}")
        count += 1

print("READING ACTUAL STEP FILE SHAPE COLORS COMPLETE")
