#!/usr/bin/env python3

# Test exactly what the step_loader is doing
import re

print("DEBUGGING COLOR PARSING STEP BY STEP")
print("=" * 50)

# Step 1: Read the STEP file
try:
    with open('SOIC16P127_1270X940X610L89X51.STEP', 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
    print(f"Step 1: STEP file read - {len(content)} characters")
except Exception as e:
    print(f"Step 1 FAILED: {e}")
    exit(1)

# Step 2: Test the regex pattern
colour_pattern = r'COLOUR_RGB\s*\(\s*[^,]*,\s*([\d\.]+)\s*,\s*([\d\.]+)\s*,\s*([\d\.]+)\s*\)'
print(f"Step 2: Using regex pattern: {colour_pattern}")

# Step 3: Find matches
matches = re.findall(colour_pattern, content)
print(f"Step 3: Found {len(matches)} regex matches")

# Step 3b: Also try a simpler pattern to see if we're missing some
simple_pattern = r'COLOUR_RGB.*?([\d\.]+)\s*,\s*([\d\.]+)\s*,\s*([\d\.]+)'
simple_matches = re.findall(simple_pattern, content)
print(f"Step 3b: Simple pattern found {len(simple_matches)} matches")

# Use the pattern that finds more matches
if len(simple_matches) > len(matches):
    print("Using simple pattern (found more matches)")
    matches = simple_matches
else:
    print("Using original pattern")

# Step 4: Process each match
colors_found = []
for i, match in enumerate(matches):
    try:
        r_val = float(match[0])
        g_val = float(match[1]) 
        b_val = float(match[2])
        
        r = int(r_val * 255)
        g = int(g_val * 255)
        b = int(b_val * 255)
        
        colors_found.append((r, g, b))
        print(f"Match {i}: ({r_val:.3f}, {g_val:.3f}, {b_val:.3f}) -> RGB({r}, {g}, {b})")
        
        if i >= 5:  # Show first 5 matches
            print(f"... and {len(matches) - 6} more matches")
            break
            
    except Exception as e:
        print(f"Error processing match {i}: {e}")

# Step 5: Remove duplicates
unique_colors = []
for color in colors_found:
    if color not in unique_colors:
        unique_colors.append(color)

print(f"Step 5: {len(unique_colors)} unique colors found:")
for i, color in enumerate(unique_colors):
    count = colors_found.count(color)
    print(f"  Color {i}: RGB{color} - appears {count} times")

# Step 6: Test what step_loader would do
print(f"Step 6: step_loader would use these colors: {unique_colors}")

if len(unique_colors) == 0:
    print("ERROR: No colors found - step_loader will use defaults")
else:
    print("SUCCESS: Colors found - step_loader will use STEP file colors")

print("=" * 50)
print("COLOR PARSING DEBUG COMPLETE")
