#!/usr/bin/env python3
"""
Simple test to see if we can run a basic GUI
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel
from PyQt5.QtCore import Qt

class SimpleWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Simple Test Window")
        self.setGeometry(300, 300, 400, 200)
        
        label = QLabel("Hello! This is a simple test window.", self)
        label.setAlignment(Qt.AlignCenter)
        self.setCentralWidget(label)

def main():
    print("🚀 Starting simple GUI test...")
    
    app = QApplication(sys.argv)
    window = SimpleWindow()
    window.show()
    
    print("✅ Simple window should be visible now!")
    print("Close the window to exit.")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
