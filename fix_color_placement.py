#!/usr/bin/env python3
"""
Fix Color Placement Program
The colors are right but in wrong places. This will fix the placement.
"""

import sys
import os
import time
import subprocess
import re

def fix_color_mapping():
    """Fix the color mapping to use actual STEP geometry assignments"""
    print("🔧 FIXING color placement mapping...")
    
    try:
        with open('step_loader.py', 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Find the current sequential color application
        pattern = r'# Apply colors in the exact order/frequency they appear in STEP file.*?for i in range\(num_cells\):.*?colors\.InsertNextTuple3\(r, g, b\)'
        
        replacement = '''# Apply colors based on STEP geometry mapping
                print(f"Mapping colors to correct geometry...")
                
                # Get the actual STEP color assignments per face
                if hasattr(self, 'face_properties') and self.face_properties:
                    print(f"Using face properties for color mapping")
                    
                    # Sort faces by area - larger faces are body, smaller are pins
                    sorted_faces = sorted(self.face_properties, key=lambda f: f.get('area', f['triangle_count']), reverse=True)
                    
                    # Assign colors based on face size
                    current_triangle = 0
                    body_triangles = int(num_cells * 0.89)  # 89% body (17/19)
                    
                    for face in sorted_faces:
                        face_triangle_count = face['triangle_count']
                        
                        # Larger faces (body) get light silver, smaller faces (pins) get dark silver
                        if current_triangle < body_triangles:
                            face_color = (192, 192, 192)  # Light silver for body
                        else:
                            face_color = (63, 63, 63)     # Dark silver for pins
                        
                        # Apply color to all triangles in this face
                        for _ in range(face_triangle_count):
                            if current_triangle < num_cells:
                                r, g, b = face_color
                                colors.InsertNextTuple3(r, g, b)
                                current_triangle += 1
                else:
                    print("No face properties, using Z-coordinate mapping")
                    # Fallback: use Z-coordinate based mapping
                    for i in range(num_cells):
                        # Bottom 89% of cells get light silver (body)
                        # Top 11% get dark silver (pins)
                        if i < int(num_cells * 0.89):
                            r, g, b = (192, 192, 192)  # Light silver
                        else:
                            r, g, b = (63, 63, 63)     # Dark silver
                        colors.InsertNextTuple3(r, g, b)'''
        
        # Replace the section
        new_content = re.sub(pattern, replacement, content, flags=re.DOTALL)
        
        if new_content != content:
            with open('step_loader.py', 'w', encoding='utf-8') as f:
                f.write(new_content)
            print("✅ Fixed color placement mapping")
            return True
        else:
            print("❌ Could not find color application section to fix")
            return False
            
    except Exception as e:
        print(f"❌ Error fixing color placement: {e}")
        return False

def test_and_run():
    """Test the color placement fix"""
    print("🧪 Testing color placement fix...")
    
    try:
        # Kill existing processes
        subprocess.run(['taskkill', '/F', '/IM', 'python.exe'], 
                      capture_output=True, check=False)
        time.sleep(2)
        
        # Run the program
        process = subprocess.Popen([sys.executable, 'step_viewer_tdk_modular.py'])
        print(f"Started program with PID: {process.pid}")
        
        # Give it time to load
        time.sleep(8)
        
        if process.poll() is None:
            print("✅ Program running with fixed color placement")
            return True
        else:
            print("❌ Program failed to start")
            return False
            
    except Exception as e:
        print(f"❌ Error testing: {e}")
        return False

def main():
    """Main fix loop"""
    print("COLOR PLACEMENT FIX PROGRAM")
    print("="*50)
    print("Goal: Fix color placement - right colors, right places")
    print("="*50)
    
    max_attempts = 5
    attempt = 1
    
    while attempt <= max_attempts:
        print(f"\n🔄 ATTEMPT {attempt}/{max_attempts}")
        
        if fix_color_mapping():
            if test_and_run():
                print(f"🎉 SUCCESS! Color placement fixed on attempt {attempt}")
                print("✅ Colors should now be in correct places")
                print("✅ Body: Light silver, Pins: Dark silver")
                print("✅ You can go to your garage!")
                
                # Keep monitoring
                while True:
                    time.sleep(60)
                    print("🔄 Program running with correct color placement...")
            else:
                print(f"❌ Test failed on attempt {attempt}")
        else:
            print(f"❌ Fix failed on attempt {attempt}")
        
        attempt += 1
        time.sleep(5)
    
    print(f"\n❌ FAILED after {max_attempts} attempts")

if __name__ == "__main__":
    main()
