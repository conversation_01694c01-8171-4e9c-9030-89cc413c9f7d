#!/usr/bin/env python3
import sys
sys.path.append('.')
from step_loader import STEP<PERSON>oader

print("VERIFYING COLORS WILL BE WRITTEN CORRECTLY TO DISPLAY...")

loader = STEPLoader()
result = loader.load_step_file('SOIC16P127_1270X940X610L89X51.STEP')
polydata, success, message = result

print(f"SUCCESS: {success}")

if success:
    colors = polydata.GetCellData().GetScalars('Colors')
    if colors:
        print(f"COLORS VERIFIED: {colors.GetNumberOfTuples()} cells have colors")
        
        # Check color distribution
        light_count = 0
        dark_count = 0
        
        for i in range(colors.GetNumberOfTuples()):
            r = int(colors.GetComponent(i, 0))
            g = int(colors.GetComponent(i, 1))
            b = int(colors.GetComponent(i, 2))
            
            if (r,g,b) == (192,192,192):
                light_count += 1
            elif (r,g,b) == (63,63,63):
                dark_count += 1
        
        total = light_count + dark_count
        light_percent = (light_count / total) * 100
        dark_percent = (dark_count / total) * 100
        
        print(f"COLOR DISTRIBUTION:")
        print(f"  Light silver (192,192,192): {light_count} cells ({light_percent:.1f}%)")
        print(f"  Dark silver (63,63,63): {dark_count} cells ({dark_percent:.1f}%)")
        
        # Expected: 89% light (body), 11% dark (pins)
        if abs(light_percent - 89) < 5 and abs(dark_percent - 11) < 5:
            print("✅ COLOR DISTRIBUTION IS CORRECT")
            print("✅ COLORS WILL BE WRITTEN TO DISPLAY CORRECTLY")
        else:
            print("❌ COLOR DISTRIBUTION IS WRONG")
            print("❌ COLORS WILL NOT BE DISPLAYED CORRECTLY")
    else:
        print("❌ NO COLORS FOUND - WILL NOT WORK")
else:
    print("❌ LOAD FAILED - WILL NOT WORK")
