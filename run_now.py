#!/usr/bin/env python3
import os
import sys

# Change to correct directory
os.chdir(r'e:\Python\3d-models')
sys.path.insert(0, r'e:\Python\3d-models')

print("RUNNING STEP VIEWER NOW...")

# Import and run the main program
try:
    import step_viewer_tdk_modular
    print("STEP viewer is now running!")
    print("Colors should be applied from STEP file!")
    print("You can go to your garage now!")
except Exception as e:
    print(f"Error running program: {e}")
    import traceback
    traceback.print_exc()
