import subprocess
import sys
import os

print("=== DEBUGGING STEP VIEWER ===")
print(f"Current directory: {os.getcwd()}")
print(f"Python version: {sys.version}")

# Check if the main file exists
if os.path.exists('step_viewer_tdk_modular.py'):
    print("✅ step_viewer_tdk_modular.py exists")
    
    # Check which debug message is in the file
    with open('step_viewer_tdk_modular.py', 'r') as f:
        content = f.read()
        
    if "DEBUG: Camera update timer working" in content:
        print("✅ CORRECT VERSION: Found 'Camera update timer working'")
    elif "DEBUG: Timer working" in content:
        print("❌ OLD VERSION: Found 'Timer working'")
    else:
        print("⚠️  No timer debug message found")
else:
    print("❌ step_viewer_tdk_modular.py NOT FOUND")

print("\n" + "="*50)
print("RUNNING THE PROGRAM...")
print("="*50)

# Run the program and capture output for a longer time to see debug messages
try:
    result = subprocess.run([sys.executable, 'step_viewer_tdk_modular.py'],
                          capture_output=True, text=True, timeout=10)
    
    print("STDOUT:")
    print(result.stdout)
    print("\nSTDERR:")
    print(result.stderr)
    print(f"\nReturn code: {result.returncode}")
    
except subprocess.TimeoutExpired:
    print("Program is running (GUI opened) - this is normal")
except Exception as e:
    print(f"Error running program: {e}")
