#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug Position Display Issue - Standalone test to find the X/Y coordinate problem
"""

import sys
import time
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                            QWidget, QPushButton, QLabel, QTextEdit, QGroupBox)
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular import StepViewerTDK

class PositionDisplayDebugger(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Position Display Issue Debugger")
        self.setGeometry(100, 100, 1400, 900)
        
        # Create main widget and layout
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QHBoxLayout(main_widget)
        
        # Left side - Controls and debug output
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_panel.setMaximumWidth(600)
        
        # Status
        self.status_label = QLabel("Ready - Click 'Load & Start Debug' to begin")
        left_layout.addWidget(self.status_label)
        
        # Control buttons
        controls_group = QGroupBox("Controls")
        controls_layout = QVBoxLayout(controls_group)
        
        btn_load = QPushButton("Load & Start Debug")
        btn_load.clicked.connect(self.load_and_debug)
        controls_layout.addWidget(btn_load)
        
        # Test buttons
        test_layout = QHBoxLayout()
        
        btn_x_plus = QPushButton("Test X+")
        btn_x_plus.clicked.connect(lambda: self.test_button('x', 1.0))
        test_layout.addWidget(btn_x_plus)
        
        btn_x_minus = QPushButton("Test X-")
        btn_x_minus.clicked.connect(lambda: self.test_button('x', -1.0))
        test_layout.addWidget(btn_x_minus)
        
        btn_y_plus = QPushButton("Test Y+")
        btn_y_plus.clicked.connect(lambda: self.test_button('y', 1.0))
        test_layout.addWidget(btn_y_plus)
        
        btn_y_minus = QPushButton("Test Y-")
        btn_y_minus.clicked.connect(lambda: self.test_button('y', -1.0))
        test_layout.addWidget(btn_y_minus)
        
        btn_z_plus = QPushButton("Test Z+")
        btn_z_plus.clicked.connect(lambda: self.test_button('z', 1.0))
        test_layout.addWidget(btn_z_plus)
        
        controls_layout.addLayout(test_layout)
        
        btn_reset = QPushButton("Reset Position")
        btn_reset.clicked.connect(self.reset_position)
        controls_layout.addWidget(btn_reset)
        
        left_layout.addWidget(controls_group)
        
        # Debug output
        debug_group = QGroupBox("Debug Output")
        debug_layout = QVBoxLayout(debug_group)
        
        self.debug_output = QTextEdit()
        self.debug_output.setMaximumHeight(400)
        debug_layout.addWidget(self.debug_output)
        
        left_layout.addWidget(debug_group)
        
        layout.addWidget(left_panel)
        
        # Right side - The actual viewer
        self.viewer = StepViewerTDK()
        layout.addWidget(self.viewer)
        
        # Test counter
        self.test_count = 0
        
    def log(self, message):
        """Add message to debug output"""
        self.debug_output.append(message)
        print(message)  # Also print to console
        
    def load_and_debug(self):
        """Load test file and start debugging"""
        self.log("="*60)
        self.log("🔧 POSITION DISPLAY DEBUGGER STARTED")
        self.log("="*60)
        
        # Set active viewer to TOP
        self.viewer.active_viewer = "top"
        self.log(f"Active viewer set to: {self.viewer.active_viewer}")
        
        # Load test file
        if os.path.exists("test.step"):
            self.log("Loading test.step...")
            success = self.viewer.load_step_file_direct("test.step")
            if success:
                self.log("✅ test.step loaded successfully")
                self.status_label.setText("✅ File loaded - Ready for testing")
                
                # Wait for loading to complete, then show initial state
                QTimer.singleShot(2000, self.show_initial_state)
            else:
                self.log("❌ Failed to load test.step")
                self.status_label.setText("❌ Failed to load file")
        else:
            self.log("❌ test.step not found")
            self.status_label.setText("❌ test.step not found")
            
    def show_initial_state(self):
        """Show the initial state of all position variables"""
        self.log("\n📋 INITIAL STATE ANALYSIS:")
        self.log("-" * 40)
        
        # Check all position-related variables
        if hasattr(self.viewer, 'orig_pos_left'):
            self.log(f"orig_pos_left: {self.viewer.orig_pos_left}")
        else:
            self.log("orig_pos_left: NOT FOUND")
            
        if hasattr(self.viewer, 'current_pos_left'):
            self.log(f"current_pos_left: {self.viewer.current_pos_left}")
        else:
            self.log("current_pos_left: NOT FOUND")
            
        if hasattr(self.viewer, 'movement_delta_left'):
            self.log(f"movement_delta_left: {self.viewer.movement_delta_left}")
        else:
            self.log("movement_delta_left: NOT FOUND")
            
        # Check text display content
        if hasattr(self.viewer, 'combined_text_actor_left'):
            try:
                text_content = self.viewer.combined_text_actor_left.GetInput()
                self.log(f"Current text display: '{text_content}'")
            except:
                self.log("Could not read text display content")
        else:
            self.log("combined_text_actor_left: NOT FOUND")
            
        self.log("\n✅ Ready for button testing!")
        
    def test_button(self, axis, amount):
        """Test a specific button press and analyze all data"""
        self.test_count += 1
        
        self.log(f"\n🔧 TEST #{self.test_count}: {axis.upper()}{'+' if amount > 0 else '-'} BUTTON")
        self.log("=" * 50)
        
        # Capture BEFORE state
        self.log("📊 BEFORE BUTTON PRESS:")
        before_data = self.capture_state()
        
        # Press the button (call move_shape directly)
        self.log(f"\n🎯 PRESSING BUTTON: move_shape('{axis}', {amount})")
        self.viewer.move_shape(axis, amount)
        
        # Allow processing
        QApplication.processEvents()
        time.sleep(0.2)
        
        # Capture AFTER state
        self.log("\n📊 AFTER BUTTON PRESS:")
        after_data = self.capture_state()
        
        # Analyze changes
        self.log("\n🔍 CHANGE ANALYSIS:")
        self.analyze_changes(before_data, after_data, axis, amount)
        
        # Check text display
        self.check_text_display()
        
        self.log("\n" + "="*50)
        
    def capture_state(self):
        """Capture current state of all position variables"""
        state = {}
        
        if hasattr(self.viewer, 'orig_pos_left'):
            state['orig_pos_left'] = self.viewer.orig_pos_left.copy()
            self.log(f"   orig_pos_left: {state['orig_pos_left']}")
        
        if hasattr(self.viewer, 'current_pos_left'):
            state['current_pos_left'] = self.viewer.current_pos_left.copy()
            self.log(f"   current_pos_left: {state['current_pos_left']}")
        
        if hasattr(self.viewer, 'movement_delta_left'):
            state['movement_delta_left'] = self.viewer.movement_delta_left.copy()
            self.log(f"   movement_delta_left: {state['movement_delta_left']}")
            
        return state
        
    def analyze_changes(self, before, after, axis, expected_amount):
        """Analyze what changed and if it's correct"""
        
        # Check movement_delta_left change
        if 'movement_delta_left' in before and 'movement_delta_left' in after:
            delta_change = after['movement_delta_left'][axis] - before['movement_delta_left'][axis]
            self.log(f"   movement_delta_left[{axis}]: {before['movement_delta_left'][axis]:.3f} → {after['movement_delta_left'][axis]:.3f}")
            self.log(f"   Change: {delta_change:.3f} (expected: {expected_amount:.3f})")
            delta_correct = abs(delta_change - expected_amount) < 0.001
            self.log(f"   Movement delta: {'✅ CORRECT' if delta_correct else '❌ WRONG'}")
        
        # Check current_pos_left change
        if 'current_pos_left' in before and 'current_pos_left' in after:
            pos_change = after['current_pos_left'][axis] - before['current_pos_left'][axis]
            self.log(f"   current_pos_left[{axis}]: {before['current_pos_left'][axis]:.3f} → {after['current_pos_left'][axis]:.3f}")
            self.log(f"   Change: {pos_change:.3f} (expected: {expected_amount:.3f})")
            pos_correct = abs(pos_change - expected_amount) < 0.001
            self.log(f"   Position change: {'✅ CORRECT' if pos_correct else '❌ WRONG'}")
            
    def check_text_display(self):
        """Check what's actually being displayed in the text"""
        self.log("\n📺 TEXT DISPLAY CHECK:")
        
        if hasattr(self.viewer, 'combined_text_actor_left'):
            try:
                text_content = self.viewer.combined_text_actor_left.GetInput()
                self.log(f"   Full text: '{text_content}'")
                
                # Extract position values from text
                import re
                pos_match = re.search(r'POS: X=([-\d.]+)mm Y=([-\d.]+)mm Z=([-\d.]+)mm', text_content)
                if pos_match:
                    display_x = float(pos_match.group(1))
                    display_y = float(pos_match.group(2))
                    display_z = float(pos_match.group(3))
                    self.log(f"   Displayed position: X={display_x:.3f}, Y={display_y:.3f}, Z={display_z:.3f}")
                    
                    # Compare with current_pos_left
                    if hasattr(self.viewer, 'current_pos_left'):
                        actual_x = self.viewer.current_pos_left['x']
                        actual_y = self.viewer.current_pos_left['y']
                        actual_z = self.viewer.current_pos_left['z']
                        self.log(f"   Actual position:    X={actual_x:.3f}, Y={actual_y:.3f}, Z={actual_z:.3f}")
                        
                        # Check for inversions
                        x_inverted = abs(display_x + actual_x) < 0.001
                        y_inverted = abs(display_y + actual_y) < 0.001
                        z_correct = abs(display_z - actual_z) < 0.001
                        
                        self.log(f"   X display: {'❌ INVERTED' if x_inverted else '✅ CORRECT'}")
                        self.log(f"   Y display: {'❌ INVERTED' if y_inverted else '✅ CORRECT'}")
                        self.log(f"   Z display: {'✅ CORRECT' if z_correct else '❌ WRONG'}")
                else:
                    self.log("   Could not parse position from text")
            except Exception as e:
                self.log(f"   Error reading text: {e}")
        else:
            self.log("   No text actor found")
            
    def reset_position(self):
        """Reset position to original"""
        self.log("\n🔄 RESETTING POSITION...")
        if hasattr(self.viewer, 'reset_to_original'):
            self.viewer.reset_to_original()
            QApplication.processEvents()
            time.sleep(0.5)
            self.show_initial_state()
        else:
            self.log("❌ Reset function not available")

def main():
    app = QApplication(sys.argv)
    debugger = PositionDisplayDebugger()
    debugger.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
