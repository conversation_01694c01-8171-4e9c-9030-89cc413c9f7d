#!/usr/bin/env python3
"""
Test Real Main Program - Show actual numbers from the main program
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular import Step<PERSON>iewerTDK

def test_real_main_program():
    """Test the real main program and show actual numbers"""
    
    print("🔧 TEST REAL MAIN PROGRAM - ACTUAL NUMBERS")
    print("=" * 60)
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    # Process events to ensure GUI is ready
    app.processEvents()
    time.sleep(2)
    
    # Find a STEP file to test with
    test_files = [
        "AMPHENOL_U77-A1118-200T.STEP",
        "test.step",
        "sample.stp"
    ]
    
    test_file = None
    for filename in test_files:
        if os.path.exists(filename):
            test_file = filename
            break
    
    if not test_file:
        print("❌ No STEP file found for testing")
        return False
    
    print(f"📁 Using test file: {test_file}")
    
    def show_actual_positions(label):
        """Show actual positions from the main program"""
        print(f"\n🔍 {label}")
        print("-" * 50)
        
        renderer = viewer.vtk_renderer_left
        if hasattr(renderer, 'renderer') and renderer.renderer:
            actor_collection = renderer.renderer.GetActors()
            actor_collection.InitTraversal()
            
            actor_count = 0
            actor = actor_collection.GetNextActor()
            while actor:
                actor_count += 1
                pos = actor.GetPosition()
                orient = actor.GetOrientation()
                visible = actor.GetVisibility()
                
                # Identify actor type
                actor_type = "UNKNOWN"
                if hasattr(renderer, 'step_actors') and renderer.step_actors:
                    for j, multi_actor in enumerate(renderer.step_actors):
                        if multi_actor == actor:
                            actor_type = f"MULTI-ACTOR_{j}"
                            
                if hasattr(renderer, 'bbox_actor') and renderer.bbox_actor == actor:
                    actor_type = "BOUNDING_BOX"
                    
                if hasattr(renderer, 'step_actor') and renderer.step_actor == actor:
                    actor_type = "SINGLE_ACTOR"
                
                print(f"Actor {actor_count} ({actor_type}): Pos={pos}, Orient={orient}, Visible={visible}")
                
                actor = actor_collection.GetNextActor()
            
            print(f"Total actors: {actor_count}")
        else:
            print("❌ No renderer found")
    
    # Step 1: Load model
    print(f"\n📋 STEP 1: LOADING MODEL IN MAIN PROGRAM...")
    success = viewer.load_step_file_direct(test_file)
    app.processEvents()
    time.sleep(3)
    
    if not success:
        print("❌ Failed to load STEP file")
        return False
    
    show_actual_positions("ACTUAL INITIAL POSITIONS")
    
    # Step 2: Apply transformations using main program GUI
    print(f"\n📋 STEP 2: APPLYING TRANSFORMATIONS USING MAIN PROGRAM...")
    
    viewer.active_viewer = "top"
    
    print(f"🔧 Moving +100mm X using main program")
    viewer.move_shape("x", 100)
    app.processEvents()
    time.sleep(2)
    
    show_actual_positions("ACTUAL POSITIONS AFTER +100mm X")
    
    print(f"🔧 Rotating +180° Z using main program")
    viewer.rotate_shape("z", 180)
    app.processEvents()
    time.sleep(2)
    
    show_actual_positions("ACTUAL POSITIONS AFTER +180° Z")
    
    # Step 3: Reset using main program
    print(f"\n📋 STEP 3: RESETTING USING MAIN PROGRAM...")
    
    print(f"🔧 Calling main program reset...")
    viewer.reset_to_original()
    app.processEvents()
    time.sleep(3)
    
    show_actual_positions("ACTUAL POSITIONS AFTER MAIN PROGRAM RESET")
    
    # Step 4: Analysis
    print(f"\n📋 STEP 4: ANALYSIS OF MAIN PROGRAM")
    print("=" * 60)
    
    print(f"🎯 THESE ARE THE ACTUAL NUMBERS FROM THE MAIN PROGRAM")
    print(f"- These numbers show what the main program is actually doing")
    print(f"- If reset doesn't work visually, but numbers show (0,0,0), it's a rendering issue")
    print(f"- If reset doesn't work and numbers don't show (0,0,0), it's a logic issue")
    
    # Keep window open for manual testing
    print(f"\n👁️ MAIN PROGRAM IS NOW OPEN FOR MANUAL TESTING")
    print(f"You can now:")
    print(f"1. Load a STEP file manually")
    print(f"2. Apply transformations manually")
    print(f"3. Click 'Reset to Original' manually")
    print(f"4. See if it works visually")
    
    print(f"\nWindow will stay open for 60 seconds for manual testing...")
    QTimer.singleShot(60000, app.quit)
    app.exec_()
    
    return True

if __name__ == "__main__":
    test_real_main_program()
