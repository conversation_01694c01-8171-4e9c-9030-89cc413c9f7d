#!/usr/bin/env python3
"""
Automated test program that loads a STEP file and tests rotation updates
Runs continuously until the AXIS numbers actually change
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
import time

# Clear debug file
with open("debug_auto_test.txt", "w", encoding="utf-8") as f:
    f.write("=== AUTO TEST WITH STEP FILE START ===\n")

print("🚀 Starting automated STEP file rotation test")

# Import the main program
try:
    from step_viewer_tdk_modular import StepViewerTDK
    print("✅ Successfully imported StepViewerTDK")
except Exception as e:
    print(f"❌ Failed to import StepViewerTDK: {e}")
    sys.exit(1)

class AutoStepTest:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        self.test_count = 0
        self.max_tests = 20
        self.step_file_loaded = False
        
        # Create the viewer
        try:
            print("🔧 Creating StepViewerTDK...")
            self.viewer = StepViewerTDK()
            print("✅ StepViewerTDK created")
        except Exception as e:
            print(f"❌ Failed to create StepViewerTDK: {e}")
            return
            
        # Set up timers
        self.setup_timers()
        
        # Start the test sequence
        self.start_test_sequence()
        
    def setup_timers(self):
        """Set up timers for automated testing"""
        # Timer to load STEP file after GUI is ready
        self.load_timer = QTimer()
        self.load_timer.timeout.connect(self.load_step_file)
        self.load_timer.setSingleShot(True)
        
        # Timer for rotation testing
        self.test_timer = QTimer()
        self.test_timer.timeout.connect(self.run_rotation_test)
        
    def start_test_sequence(self):
        """Start the automated test sequence"""
        print("🚀 Starting test sequence...")
        
        # Show the viewer
        self.viewer.show()
        
        # Load STEP file after 2 seconds
        self.load_timer.start(2000)
        
    def load_step_file(self):
        """Load a STEP file automatically"""
        print("📁 Loading STEP file...")
        
        # Look for STEP files in the current directory
        step_files = [f for f in os.listdir('.') if f.lower().endswith(('.step', '.stp'))]
        
        if not step_files:
            print("❌ No STEP files found in current directory")
            print("🔧 Creating test variables manually...")
            self.create_test_variables()
            self.step_file_loaded = True
            self.start_rotation_tests()
            return
            
        step_file = step_files[0]
        print(f"📂 Found STEP file: {step_file}")
        
        try:
            # Load the STEP file using the viewer's load method
            if hasattr(self.viewer, 'load_step_file'):
                self.viewer.load_step_file(step_file)
                print(f"✅ STEP file loaded: {step_file}")
                self.step_file_loaded = True
            else:
                print("❌ No load_step_file method found")
                self.create_test_variables()
                self.step_file_loaded = True
                
        except Exception as e:
            print(f"❌ Failed to load STEP file: {e}")
            print("🔧 Creating test variables manually...")
            self.create_test_variables()
            self.step_file_loaded = True
            
        # Start rotation tests after loading
        self.start_rotation_tests()
        
    def create_test_variables(self):
        """Create test variables manually if STEP file loading fails"""
        print("🔧 Creating test variables...")
        
        # Initialize all required variables
        self.viewer.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.viewer.current_angle_left = 45.0
        self.viewer.current_axis_left = {'x': 0.0, 'y': 0.0, 'z': 1.0}
        self.viewer.current_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        
        self.viewer.current_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.viewer.current_angle_right = 45.0
        self.viewer.current_axis_right = {'x': 0.0, 'y': 0.0, 'z': 1.0}
        self.viewer.current_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        
        # Original values
        self.viewer.orig_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.viewer.orig_angle_left = 45.0
        self.viewer.orig_axis_left = {'x': 0.0, 'y': 0.0, 'z': 1.0}
        
        # Set active viewer
        self.viewer.active_viewer = "top"
        
        print("✅ Test variables created")
        
        # Update display
        if hasattr(self.viewer, 'update_vtk_text_overlays'):
            self.viewer.update_vtk_text_overlays()
            
    def start_rotation_tests(self):
        """Start the rotation testing"""
        print("🧪 Starting rotation tests...")
        
        # Start the test timer - run every 3 seconds
        self.test_timer.start(3000)
        
    def run_rotation_test(self):
        """Run one rotation test cycle"""
        self.test_count += 1
        
        if self.test_count > self.max_tests:
            self.test_timer.stop()
            print("🏁 Testing complete - check debug_auto_test.txt")
            self.app.quit()
            return
            
        print(f"\n🧪 Running rotation test #{self.test_count}/{self.max_tests}")
        
        # Log current state before rotation
        self.log_current_state("BEFORE")
        
        # Test different rotations based on test number
        if self.test_count % 3 == 1:
            axis, degrees = 'x', 15
        elif self.test_count % 3 == 2:
            axis, degrees = 'y', 15
        else:
            axis, degrees = 'z', 15
            
        print(f"📞 Testing {axis.upper()}+ rotation ({degrees}°)...")
        
        try:
            # Call the rotate_shape function
            if hasattr(self.viewer, 'rotate_shape'):
                self.viewer.rotate_shape(axis, degrees)
                print(f"✅ rotate_shape('{axis}', {degrees}) completed")
            else:
                print("❌ rotate_shape method not found")
                
        except Exception as e:
            print(f"❌ Error in rotation test: {e}")
            import traceback
            traceback.print_exc()
            
        # Log current state after rotation
        self.log_current_state("AFTER")
        
        # Check if numbers actually changed
        self.check_if_numbers_changed()
        
    def log_current_state(self, when):
        """Log the current state of all variables"""
        debug_msg = f"\n=== {when} ROTATION TEST #{self.test_count} ===\n"
        
        if hasattr(self.viewer, 'current_axis_left'):
            debug_msg += f"current_axis_left = {self.viewer.current_axis_left}\n"
        else:
            debug_msg += "current_axis_left = NOT SET\n"
            
        if hasattr(self.viewer, 'current_axis_right'):
            debug_msg += f"current_axis_right = {self.viewer.current_axis_right}\n"
        else:
            debug_msg += "current_axis_right = NOT SET\n"
            
        if hasattr(self.viewer, 'current_rot_left'):
            debug_msg += f"current_rot_left = {self.viewer.current_rot_left}\n"
        else:
            debug_msg += "current_rot_left = NOT SET\n"
            
        debug_msg += f"Active viewer = {getattr(self.viewer, 'active_viewer', 'NOT SET')}\n"
        
        with open("debug_auto_test.txt", "a", encoding="utf-8") as f:
            f.write(debug_msg)
            
        print(debug_msg.strip())
        
    def check_if_numbers_changed(self):
        """Check if the AXIS numbers actually changed from [0,0,1]"""
        if hasattr(self.viewer, 'current_axis_left'):
            axis = self.viewer.current_axis_left
            if axis['x'] != 0.0 or axis['y'] != 0.0 or axis['z'] != 1.0:
                print(f"🎉 SUCCESS! AXIS numbers changed: {axis}")
                return True
        
        if hasattr(self.viewer, 'current_axis_right'):
            axis = self.viewer.current_axis_right
            if axis['x'] != 0.0 or axis['y'] != 0.0 or axis['z'] != 1.0:
                print(f"🎉 SUCCESS! AXIS numbers changed: {axis}")
                return True
                
        print("❌ AXIS numbers still [0,0,1] - not working yet")
        return False
        
    def run(self):
        """Run the test application"""
        print("🔄 Starting Qt application...")
        sys.exit(self.app.exec_())

def main():
    test = AutoStepTest()
    test.run()

if __name__ == "__main__":
    main()
