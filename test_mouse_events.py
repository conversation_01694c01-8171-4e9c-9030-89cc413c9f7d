#!/usr/bin/env python3
"""
Test if mouse interaction events are firing
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from step_viewer_tdk_modular import StepViewerTDK
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
import vtk

def test_mouse_events():
    """Test if mouse interaction events are being captured"""
    
    print("🚀 Testing mouse interaction events...")
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    # Load test file
    test_file = "test.step"
    if not os.path.exists(test_file):
        print(f"❌ Test file {test_file} not found")
        return
    
    print(f"📁 Loading {test_file}...")
    viewer.set_active_viewer("bottom")
    viewer.step_loader_right.load_step_file(test_file)
    
    def simulate_mouse_interaction():
        """Simulate mouse interaction by triggering VTK events"""
        print("\n🖱️ Simulating mouse interaction...")
        
        # Get the VTK interactor
        if hasattr(viewer, 'vtk_renderer_right') and viewer.vtk_renderer_right:
            if hasattr(viewer.vtk_renderer_right, 'interactor'):
                interactor = viewer.vtk_renderer_right.interactor
                
                # Simulate mouse events
                print("🔧 Triggering InteractionEvent...")
                interactor.InvokeEvent('InteractionEvent')
                
                print("🔧 Triggering MouseMoveEvent...")
                interactor.InvokeEvent('MouseMoveEvent')
                
                # Also try to get the camera and change its orientation
                if viewer.vtk_renderer_right.renderer:
                    camera = viewer.vtk_renderer_right.renderer.GetActiveCamera()
                    if camera:
                        print("🔧 Changing camera orientation...")
                        camera.SetPosition(0, -100, 50)
                        camera.SetFocalPoint(0, 0, 0)
                        
                        # Trigger interaction event after camera change
                        interactor.InvokeEvent('InteractionEvent')
                        
                        # Force render
                        viewer.vtk_renderer_right.render_window.Render()
                        
                        print("🔧 Camera changed, checking if events fired...")
        
        # Check rotation values after simulation
        print("\n📊 Checking rotation values after simulation:")
        extracted_rot = viewer._extract_rotation_from_vtk_actor("bottom")
        print(f"   Extracted rotation: {extracted_rot}")
        
        # Quit after test
        QTimer.singleShot(2000, app.quit)
    
    # Start simulation after a delay
    QTimer.singleShot(3000, simulate_mouse_interaction)
    
    # Run the application
    app.exec_()

if __name__ == "__main__":
    test_mouse_events()
