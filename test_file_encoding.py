#!/usr/bin/env python3
"""
Test file encoding issues
"""

files_to_test = ['step_loader.py', 'step_viewer_tdk_modular.py', 'vtk_renderer.py']

for filename in files_to_test:
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        print(f"OK {filename}: UTF-8 read successful")
        
        try:
            compile(content, filename, 'exec')
            print(f"OK {filename}: Compile successful")
        except SyntaxError as e:
            print(f"FAIL {filename}: Syntax error - {e}")
            
    except UnicodeDecodeError as e:
        print(f"FAIL {filename}: Unicode decode error - {e}")
        
        # Try with different encoding
        try:
            with open(filename, 'r', encoding='cp1252') as f:
                content = f.read()
            print(f"OK {filename}: CP1252 read successful")
        except Exception as e2:
            print(f"FAIL {filename}: CP1252 also failed - {e2}")
            
    except FileNotFoundError:
        print(f"WARN {filename}: File not found")
    except Exception as e:
        print(f"FAIL {filename}: Other error - {e}")
