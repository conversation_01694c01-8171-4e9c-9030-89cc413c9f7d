#!/usr/bin/env python3

print("CHECKING OPENCASCADE COLOR MODULES AVAILABILITY")

# Test different import patterns for OpenCASCADE color modules
import_tests = [
    # Pattern 1: Direct imports
    ("STEPCAFControl_Reader", "from OCC.Core.STEPCAFControl_Reader import STEPCAFControl_Reader"),
    ("XCAFDoc_ColorTool", "from OCC.Core.XCAFDoc_ColorTool import XCAFDoc_ColorTool"),
    ("XCAFApp_Application", "from OCC.Core.XCAFApp_Application import XCAFApp_Application"),
    ("TDocStd_Document", "from OCC.Core.TDocStd_Document import TDocStd_Document"),
    ("Quantity_Color", "from OCC.Core.Quantity_Color import Quantity_Color"),
    
    # Pattern 2: Module imports
    ("STEPCAFControl", "from OCC.Core import STEPCAFControl_Reader"),
    ("XCAFDoc", "from OCC.Core import XCAFDoc_ColorTool"),
    ("XCAFApp", "from OCC.Core import XCAFApp_Application"),
    ("TDocStd", "from OCC.Core import TDocStd_Document"),
    ("Quantity", "from OCC.Core import Quantity_Color"),
    
    # Pattern 3: Alternative names
    ("STEPCAF_Reader", "from OCC.Core.STEPControl_Reader import STEPControl_Reader"),
    ("ColorTool", "from OCC.Core.XCAFDoc import XCAFDoc_ColorTool"),
    ("Application", "from OCC.Core.XCAFApp import XCAFApp_Application"),
    ("Document", "from OCC.Core.TDocStd import TDocStd_Document"),
]

available_modules = []
failed_modules = []

for name, import_statement in import_tests:
    try:
        exec(import_statement)
        available_modules.append((name, import_statement))
        print(f"✓ {name}: {import_statement}")
    except Exception as e:
        failed_modules.append((name, str(e)))
        print(f"✗ {name}: {e}")

print(f"\nSUMMARY:")
print(f"Available modules: {len(available_modules)}")
print(f"Failed modules: {len(failed_modules)}")

if available_modules:
    print(f"\nAVAILABLE MODULES:")
    for name, statement in available_modules:
        print(f"  {name}: {statement}")

# Test what's actually in OCC.Core
try:
    import OCC.Core
    print(f"\nOCC.Core contents:")
    core_items = [item for item in dir(OCC.Core) if 'CAF' in item or 'Color' in item or 'STEP' in item]
    for item in core_items[:20]:  # Show first 20
        print(f"  {item}")
    if len(core_items) > 20:
        print(f"  ... and {len(core_items) - 20} more")
except Exception as e:
    print(f"Cannot access OCC.Core: {e}")

print("\nCHECKING OPENCASCADE COLOR MODULES COMPLETE")
