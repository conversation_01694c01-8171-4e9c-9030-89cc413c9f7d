import subprocess
import sys

def install_package(package):
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"Successfully installed {package}")
    except subprocess.CalledProcessError as e:
        print(f"Failed to install {package}: {e}")

packages = [
    "PyQt5",
    "vtk", 
    "trimesh",
    "numpy",
    "matplotlib"
]

print("Installing required packages...")
for package in packages:
    print(f"Installing {package}...")
    install_package(package)

print("Installation complete!")
