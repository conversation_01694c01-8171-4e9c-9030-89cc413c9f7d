#!/usr/bin/env python3
"""
Test real mouse rotation with the fixed viewer
This opens the viewer and lets you test mouse rotation manually
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from step_viewer_tdk_modular_fixed import StepViewerTDK

def main():
    print("🔧 REAL MOUSE ROTATION TEST")
    print("=" * 50)
    print("This will open the STEP viewer with the mouse rotation fix.")
    print("Instructions:")
    print("1. Load test.step using the 'Open STEP File' button")
    print("2. Use your mouse to rotate the model by dragging")
    print("3. Use the rotation buttons to apply additional rotations")
    print("4. Click 'Save STEP File (Improved Method)' to save")
    print("5. The saved file should contain BOTH mouse and button rotations!")
    print("")
    print("🎯 The fix ensures that mouse rotations are now captured and saved!")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    # Create the viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    # Auto-load test.step if it exists
    if os.path.exists("test.step"):
        print(f"📁 Auto-loading test.step...")
        viewer.active_viewer = "top"
        success, message = viewer.step_loader_left.load_step_file("test.step")
        
        if success:
            viewer.vtk_renderer_left.clear_view()
            viewer.vtk_renderer_left.display_polydata(viewer.step_loader_left.current_polydata)
            viewer.vtk_renderer_left.fit_view()
            viewer.top_file_label.setText(f"TOP: test.step")
            print(f"✅ Auto-loaded: test.step")
            print(f"🖱️  Now try rotating with your mouse and then save!")
        else:
            print(f"❌ Auto-load failed: {message}")
    else:
        print(f"📁 test.step not found - please load a file manually")
    
    # Run the application
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
