#!/usr/bin/env python3
"""
Debug program to analyze STEP file geometry structure to understand pin vs body distribution
"""

import sys
from step_loader import STEPLoader
import vtk

def analyze_geometry_structure():
    print("=== STEP GEOMETRY STRUCTURE ANALYSIS ===")
    
    # Load the STEP file
    loader = STEPLoader()
    filename = "E:/Python/3d-models/SOIC16P127_1270X940X610L89X51.STEP"
    
    print(f"Loading STEP file: {filename}")
    success, message = loader.load_step_file(filename)
    
    if not success or not loader.current_polydata:
        print(f"Failed to load: {message}")
        return
    
    polydata = loader.current_polydata
    num_cells = polydata.GetNumberOfCells()
    print(f"Total cells: {num_cells}")
    
    # Analyze cell positions to understand geometry structure
    cell_positions = []
    
    for i in range(num_cells):
        cell = polydata.GetCell(i)
        
        # Get cell center
        bounds = [0] * 6
        cell.GetBounds(bounds)
        center_x = (bounds[0] + bounds[1]) / 2
        center_y = (bounds[2] + bounds[3]) / 2  
        center_z = (bounds[4] + bounds[5]) / 2
        
        cell_positions.append((i, center_x, center_y, center_z))
    
    # Analyze Z-coordinate distribution (height)
    z_coords = [pos[3] for pos in cell_positions]
    min_z = min(z_coords)
    max_z = max(z_coords)
    z_range = max_z - min_z
    
    print(f"Z-coordinate range: {min_z:.3f} to {max_z:.3f} (range: {z_range:.3f})")
    
    # Classify cells based on Z-coordinate
    # Pins typically extend below the body, so lower Z = pins, higher Z = body
    z_threshold = min_z + z_range * 0.3  # Bottom 30% are likely pins
    
    pin_cells = []
    body_cells = []
    
    for i, x, y, z in cell_positions:
        if z < z_threshold:
            pin_cells.append(i)
        else:
            body_cells.append(i)
    
    print(f"Based on Z-coordinate analysis:")
    print(f"  Pin cells (Z < {z_threshold:.3f}): {len(pin_cells)} cells")
    print(f"  Body cells (Z >= {z_threshold:.3f}): {len(body_cells)} cells")
    
    # Also analyze by X-coordinate (pins are typically on the sides)
    x_coords = [pos[1] for pos in cell_positions]
    min_x = min(x_coords)
    max_x = max(x_coords)
    x_center = (min_x + max_x) / 2
    x_range = max_x - min_x
    
    print(f"X-coordinate range: {min_x:.3f} to {max_x:.3f} (center: {x_center:.3f})")
    
    # Pins are typically on the edges (far from center in X direction)
    x_edge_threshold = x_range * 0.3  # Within 30% of center = body, outside = pins
    
    pin_cells_x = []
    body_cells_x = []
    
    for i, x, y, z in cell_positions:
        distance_from_center = abs(x - x_center)
        if distance_from_center > x_edge_threshold:
            pin_cells_x.append(i)
        else:
            body_cells_x.append(i)
    
    print(f"Based on X-coordinate analysis:")
    print(f"  Pin cells (edge, |X-center| > {x_edge_threshold:.3f}): {len(pin_cells_x)} cells")
    print(f"  Body cells (center, |X-center| <= {x_edge_threshold:.3f}): {len(body_cells_x)} cells")
    
    # Combine both analyses - cells that are both low-Z AND edge-X are definitely pins
    combined_pin_cells = set(pin_cells) | set(pin_cells_x)  # Union of both
    combined_body_cells = set(range(num_cells)) - combined_pin_cells
    
    print(f"Combined analysis:")
    print(f"  Pin cells: {len(combined_pin_cells)} cells")
    print(f"  Body cells: {len(combined_body_cells)} cells")
    
    # Show some sample cell positions
    print(f"Sample pin cell positions (first 10):")
    for i, cell_id in enumerate(list(combined_pin_cells)[:10]):
        pos = cell_positions[cell_id]
        print(f"  Cell {pos[0]}: X={pos[1]:.3f}, Y={pos[2]:.3f}, Z={pos[3]:.3f}")
    
    print(f"Sample body cell positions (first 10):")
    for i, cell_id in enumerate(list(combined_body_cells)[:10]):
        pos = cell_positions[cell_id]
        print(f"  Cell {pos[0]}: X={pos[1]:.3f}, Y={pos[2]:.3f}, Z={pos[3]:.3f}")

if __name__ == "__main__":
    analyze_geometry_structure()
