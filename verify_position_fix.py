#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Verify Position Fix - Quick automated test to verify the fix actually works
"""

import sys
import time
import os
import re
from PyQt5.QtWidgets import QApplication
from step_viewer_tdk_modular import <PERSON><PERSON>iewerTDK

def test_position_fix():
    """Test if position fix actually works"""
    print("🔧 VERIFYING POSITION FIX...")
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.active_viewer = "top"
    
    # Initialize position data
    viewer.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
    viewer.orig_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
    viewer.movement_delta_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
    
    # Create text actor for testing
    import vtk
    viewer.combined_text_actor_left = vtk.vtkTextActor()
    viewer.update_text_overlays()
    
    print(f"Initial position: {viewer.current_pos_left}")
    
    # Test X+ button
    print("\n🔧 Testing X+ button:")
    print(f"Before: X = {viewer.current_pos_left['x']:.3f}")
    viewer.move_shape('x', 1.0)
    print(f"After:  X = {viewer.current_pos_left['x']:.3f}")
    
    # Check text display
    try:
        text = viewer.combined_text_actor_left.GetInput()
        x_match = re.search(r'POS: X=([-\d.]+)mm', text)
        if x_match:
            displayed_x = float(x_match.group(1))
            print(f"Text shows: X = {displayed_x:.3f}")
            
            # Check if correct
            if abs(displayed_x - viewer.current_pos_left['x']) < 0.001:
                print("✅ X+ CORRECT - Text matches data")
            else:
                print("❌ X+ WRONG - Text doesn't match data")
                print(f"   Expected: {viewer.current_pos_left['x']:.3f}")
                print(f"   Got:      {displayed_x:.3f}")
        else:
            print("❌ Could not parse X from text")
    except Exception as e:
        print(f"❌ Error reading text: {e}")
    
    # Reset and test Y+ button
    viewer.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
    viewer.movement_delta_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
    
    print("\n🔧 Testing Y+ button:")
    print(f"Before: Y = {viewer.current_pos_left['y']:.3f}")
    viewer.move_shape('y', 1.0)
    print(f"After:  Y = {viewer.current_pos_left['y']:.3f}")
    
    # Check text display
    try:
        text = viewer.combined_text_actor_left.GetInput()
        y_match = re.search(r'Y=([-\d.]+)mm', text)
        if y_match:
            displayed_y = float(y_match.group(1))
            print(f"Text shows: Y = {displayed_y:.3f}")
            
            # Check if correct
            if abs(displayed_y - viewer.current_pos_left['y']) < 0.001:
                print("✅ Y+ CORRECT - Text matches data")
            else:
                print("❌ Y+ WRONG - Text doesn't match data")
                print(f"   Expected: {viewer.current_pos_left['y']:.3f}")
                print(f"   Got:      {displayed_y:.3f}")
        else:
            print("❌ Could not parse Y from text")
    except Exception as e:
        print(f"❌ Error reading text: {e}")
    
    app.quit()

if __name__ == "__main__":
    test_position_fix()
