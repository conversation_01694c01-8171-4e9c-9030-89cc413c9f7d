#!/usr/bin/env python3
"""Minimal VTK test to check basic rendering"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout
import vtk
from vtk.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor

class MinimalVTKTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Minimal VTK Test")
        self.setGeometry(100, 100, 600, 400)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # VTK widget
        self.vtk_widget = QVTKRenderWindowInteractor()
        layout.addWidget(self.vtk_widget)
        
        # Create renderer
        self.renderer = vtk.vtkRenderer()
        self.renderer.SetBackground(1.0, 0.0, 0.0)  # Bright red background
        
        # Add renderer to render window
        render_window = self.vtk_widget.GetRenderWindow()
        render_window.AddRenderer(self.renderer)
        
        # Initialize and start
        self.vtk_widget.Initialize()
        self.vtk_widget.Start()
        
        print("VTK Test initialized")
        print("You should see a bright red background")

def main():
    app = QApplication(sys.argv)
    viewer = MinimalVTKTest()
    viewer.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
