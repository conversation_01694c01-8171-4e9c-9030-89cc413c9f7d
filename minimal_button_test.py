#!/usr/bin/env python3
import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel

class MinimalButtonTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Minimal Button Test")
        self.active_viewer = "top"
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        self.status_label = QLabel("Ready - Click buttons to test")
        layout.addWidget(self.status_label)
        
        # Test rotation buttons
        btn_x_plus = QPushButton("X+ Rotation")
        btn_x_plus.clicked.connect(lambda: self.rotate_shape('x', 15))
        layout.addWidget(btn_x_plus)
        
        btn_y_plus = QPushButton("Y+ Rotation") 
        btn_y_plus.clicked.connect(lambda: self.rotate_shape('y', 15))
        layout.addWidget(btn_y_plus)
        
        btn_z_plus = QPushButton("Z+ Rotation")
        btn_z_plus.clicked.connect(lambda: self.rotate_shape('z', 15))
        layout.addWidget(btn_z_plus)
        
        # Test movement buttons
        btn_move_x = QPushButton("Move X+")
        btn_move_x.clicked.connect(lambda: self.move_shape('x', 1.0))
        layout.addWidget(btn_move_x)
        
    def rotate_shape(self, axis, degrees):
        """Test rotate_shape method"""
        message = f"rotate_shape called: axis={axis}, degrees={degrees}, active_viewer={self.active_viewer}"
        print(message)
        self.status_label.setText(message)
        
    def move_shape(self, axis, distance):
        """Test move_shape method"""
        message = f"move_shape called: axis={axis}, distance={distance}, active_viewer={self.active_viewer}"
        print(message)
        self.status_label.setText(message)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MinimalButtonTest()
    window.show()
    sys.exit(app.exec_())
