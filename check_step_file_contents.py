#!/usr/bin/env python3
"""
Simple test to check what's actually in the generated STEP file
"""

import sys
import os
import tempfile
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def extract_coordinate_info(step_file):
    """Extract coordinate system and transformation info from STEP file"""
    try:
        with open(step_file, 'r') as f:
            content = f.read()
        
        print(f"\n🔍 ANALYZING STEP FILE: {step_file}")
        print("=" * 60)
        
        lines = content.split('\n')
        
        # Look for AXIS2_PLACEMENT_3D
        axis_placements = []
        directions = []
        cartesian_points = []
        
        for i, line in enumerate(lines):
            if 'AXIS2_PLACEMENT_3D' in line:
                axis_placements.append((i+1, line.strip()))
            elif 'DIRECTION' in line and '(' in line:
                directions.append((i+1, line.strip()))
            elif 'CARTESIAN_POINT' in line and '(' in line:
                cartesian_points.append((i+1, line.strip()))
        
        print(f"📊 STEP FILE STATISTICS:")
        print(f"   Total lines: {len(lines)}")
        print(f"   AXIS2_PLACEMENT_3D entries: {len(axis_placements)}")
        print(f"   DIRECTION entries: {len(directions)}")
        print(f"   CARTESIAN_POINT entries: {len(cartesian_points)}")
        
        print(f"\n🎯 AXIS2_PLACEMENT_3D ENTRIES:")
        for line_num, line in axis_placements[:5]:  # Show first 5
            print(f"   Line {line_num}: {line}")
        
        print(f"\n🎯 DIRECTION ENTRIES (first 10):")
        for line_num, line in directions[:10]:  # Show first 10
            print(f"   Line {line_num}: {line}")
            
        print(f"\n🎯 CARTESIAN_POINT ENTRIES (first 5):")
        for line_num, line in cartesian_points[:5]:  # Show first 5
            print(f"   Line {line_num}: {line}")
        
        # Look for standard coordinate directions
        standard_z = False
        standard_x = False
        
        for line_num, line in directions:
            if '(0.,0.,1.)' in line or '(0.0,0.0,1.0)' in line:
                standard_z = True
                print(f"\n✅ FOUND STANDARD Z DIRECTION: Line {line_num}")
                print(f"   {line}")
            elif '(1.,0.,0.)' in line or '(1.0,0.0,0.0)' in line or '(1.,0.,-0.)' in line:
                standard_x = True
                print(f"\n✅ FOUND STANDARD X DIRECTION: Line {line_num}")
                print(f"   {line}")
        
        print(f"\n📋 COORDINATE SYSTEM ANALYSIS:")
        print(f"   Standard Z direction (0,0,1): {'✅ FOUND' if standard_z else '❌ NOT FOUND'}")
        print(f"   Standard X direction (1,0,0): {'✅ FOUND' if standard_x else '❌ NOT FOUND'}")
        
        if standard_z and standard_x:
            print(f"   🎉 COORDINATE SYSTEM: STANDARD (Good!)")
        else:
            print(f"   ⚠️ COORDINATE SYSTEM: TRANSFORMED (May cause issues)")
            
        return {
            'standard_z': standard_z,
            'standard_x': standard_x,
            'axis_placements': len(axis_placements),
            'directions': len(directions),
            'points': len(cartesian_points)
        }
        
    except Exception as e:
        print(f"❌ Error analyzing {step_file}: {e}")
        return {}

def main():
    print("🧪 STEP FILE CONTENT CHECKER")
    print("=" * 60)
    print("This will:")
    print("1. Load test.step and apply rotations")
    print("2. Save with green button")
    print("3. Examine the actual STEP file contents")
    print("4. Check if coordinate system is correct")
    print("=" * 60)
    
    test_file = "test.step"
    
    if not os.path.exists(test_file):
        print(f"❌ Test file {test_file} not found!")
        return False
    
    # Import the viewer module
    try:
        from step_viewer_tdk_modular_fixed import StepViewerTDK
        from PyQt5.QtWidgets import QApplication
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    
    # Create save file
    save_file = "test_rotated_analysis.step"
    
    try:
        print(f"\n🔧 CREATING ROTATED STEP FILE...")
        
        # Create Qt application
        app = QApplication(sys.argv)
        
        # Create viewer
        viewer = StepViewerTDK()
        
        # Load test file
        viewer.active_viewer = "top"
        loader_top = viewer.step_loader_left
        
        if not loader_top.load_step_file(test_file):
            print(f"❌ Failed to load {test_file}")
            return False
        
        print(f"✅ Loaded {test_file}")
        
        # Apply rotations
        viewer.rotate_shape('x', 30)
        viewer.rotate_shape('y', 45) 
        viewer.rotate_shape('z', 60)
        print("✅ Applied rotations: X=30°, Y=45°, Z=60°")
        
        # Save with green button
        success = viewer.save_step_file_option1_direct(save_file)
        
        if not success:
            print("❌ Save failed!")
            return False
        
        print(f"✅ Saved to: {save_file}")
        
        # Now analyze both files
        print(f"\n" + "="*80)
        print("COMPARING ORIGINAL vs SAVED STEP FILES")
        print("="*80)
        
        # Analyze original file
        original_info = extract_coordinate_info(test_file)
        
        # Analyze saved file  
        saved_info = extract_coordinate_info(save_file)
        
        print(f"\n📊 COMPARISON SUMMARY:")
        print("=" * 60)
        
        if original_info and saved_info:
            print(f"Original file coordinate system: {'STANDARD' if (original_info.get('standard_z') and original_info.get('standard_x')) else 'TRANSFORMED'}")
            print(f"Saved file coordinate system:    {'STANDARD' if (saved_info.get('standard_z') and saved_info.get('standard_x')) else 'TRANSFORMED'}")
            
            if (original_info.get('standard_z') and original_info.get('standard_x') and 
                saved_info.get('standard_z') and saved_info.get('standard_x')):
                print(f"\n🎉 SUCCESS: Both files have STANDARD coordinate systems!")
                print(f"   This means the coordinate system fix is working correctly.")
                print(f"   The geometry is transformed but coordinate system stays standard.")
            else:
                print(f"\n❌ ISSUE: Coordinate systems don't match or aren't standard")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Keep the file for manual inspection
        if os.path.exists(save_file):
            print(f"\n📁 Saved file kept for inspection: {save_file}")

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
