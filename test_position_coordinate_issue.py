#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Position Coordinate Issue - Debug X/Y position display problem
"""

import sys
import time
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QLabel
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular import StepViewerTDK

class PositionCoordinateTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Position Coordinate Issue Test")
        self.setGeometry(100, 100, 1200, 800)
        
        # Create main widget and layout
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QVBoxLayout(main_widget)
        
        # Status label
        self.status_label = QLabel("Click 'Load & Test' to start coordinate testing")
        layout.addWidget(self.status_label)
        
        # Create the viewer
        self.viewer = StepViewerTDK()
        layout.addWidget(self.viewer)
        
        # Create test controls
        controls_layout = QHBoxLayout()
        
        # Load and test button
        btn_load_test = QPushButton("Load & Test Coordinates")
        btn_load_test.clicked.connect(self.load_and_test)
        controls_layout.addWidget(btn_load_test)
        
        # Manual test buttons
        btn_x_plus = QPushButton("X+ (should INCREASE X)")
        btn_x_plus.clicked.connect(lambda: self.manual_test('x', 1.0))
        controls_layout.addWidget(btn_x_plus)
        
        btn_x_minus = QPushButton("X- (should DECREASE X)")
        btn_x_minus.clicked.connect(lambda: self.manual_test('x', -1.0))
        controls_layout.addWidget(btn_x_minus)
        
        btn_y_plus = QPushButton("Y+ (should INCREASE Y)")
        btn_y_plus.clicked.connect(lambda: self.manual_test('y', 1.0))
        controls_layout.addWidget(btn_y_plus)
        
        btn_y_minus = QPushButton("Y- (should DECREASE Y)")
        btn_y_minus.clicked.connect(lambda: self.manual_test('y', -1.0))
        controls_layout.addWidget(btn_y_minus)
        
        layout.addLayout(controls_layout)
        
        # Results display
        self.results_label = QLabel("Results will appear here...")
        layout.addWidget(self.results_label)
        
    def load_and_test(self):
        """Load test file and run coordinate tests"""
        self.status_label.setText("Loading test.step...")
        
        # Set active viewer to TOP
        self.viewer.active_viewer = "top"
        
        # Load test file
        success = self.viewer.load_step_file_direct("test.step")
        if not success:
            self.status_label.setText("❌ Failed to load test.step")
            return
            
        self.status_label.setText("✅ test.step loaded, starting coordinate tests...")
        
        # Wait for loading to complete, then start tests
        QTimer.singleShot(2000, self.run_coordinate_tests)
        
    def run_coordinate_tests(self):
        """Run systematic coordinate tests"""
        print("\n" + "="*60)
        print("🔧 COORDINATE SYSTEM TEST - SYSTEMATIC ANALYSIS")
        print("="*60)
        
        # Get initial position
        if hasattr(self.viewer, 'current_pos_left'):
            initial_pos = self.viewer.current_pos_left.copy()
            print(f"Initial position: X={initial_pos['x']:.3f}, Y={initial_pos['y']:.3f}, Z={initial_pos['z']:.3f}")
        else:
            print("❌ No current_pos_left found")
            return
            
        # Test X+ movement
        print(f"\n1. Testing X+ movement (should INCREASE X value):")
        print(f"   Before: X={self.viewer.current_pos_left['x']:.3f}")
        self.viewer.move_shape('x', 1.0)
        QApplication.processEvents()
        time.sleep(0.2)
        print(f"   After:  X={self.viewer.current_pos_left['x']:.3f}")
        x_plus_change = self.viewer.current_pos_left['x'] - initial_pos['x']
        print(f"   Change: {x_plus_change:.3f} (expected: +1.000)")
        x_plus_correct = abs(x_plus_change - 1.0) < 0.001
        print(f"   Result: {'✅ CORRECT' if x_plus_correct else '❌ WRONG'}")
        
        # Reset to initial
        self.viewer.current_pos_left = initial_pos.copy()
        self.viewer.update_text_overlays()
        
        # Test X- movement  
        print(f"\n2. Testing X- movement (should DECREASE X value):")
        print(f"   Before: X={self.viewer.current_pos_left['x']:.3f}")
        self.viewer.move_shape('x', -1.0)
        QApplication.processEvents()
        time.sleep(0.2)
        print(f"   After:  X={self.viewer.current_pos_left['x']:.3f}")
        x_minus_change = self.viewer.current_pos_left['x'] - initial_pos['x']
        print(f"   Change: {x_minus_change:.3f} (expected: -1.000)")
        x_minus_correct = abs(x_minus_change + 1.0) < 0.001
        print(f"   Result: {'✅ CORRECT' if x_minus_correct else '❌ WRONG'}")
        
        # Reset to initial
        self.viewer.current_pos_left = initial_pos.copy()
        self.viewer.update_text_overlays()
        
        # Test Y+ movement
        print(f"\n3. Testing Y+ movement (should INCREASE Y value):")
        print(f"   Before: Y={self.viewer.current_pos_left['y']:.3f}")
        self.viewer.move_shape('y', 1.0)
        QApplication.processEvents()
        time.sleep(0.2)
        print(f"   After:  Y={self.viewer.current_pos_left['y']:.3f}")
        y_plus_change = self.viewer.current_pos_left['y'] - initial_pos['y']
        print(f"   Change: {y_plus_change:.3f} (expected: +1.000)")
        y_plus_correct = abs(y_plus_change - 1.0) < 0.001
        print(f"   Result: {'✅ CORRECT' if y_plus_correct else '❌ WRONG'}")
        
        # Reset to initial
        self.viewer.current_pos_left = initial_pos.copy()
        self.viewer.update_text_overlays()
        
        # Test Y- movement
        print(f"\n4. Testing Y- movement (should DECREASE Y value):")
        print(f"   Before: Y={self.viewer.current_pos_left['y']:.3f}")
        self.viewer.move_shape('y', -1.0)
        QApplication.processEvents()
        time.sleep(0.2)
        print(f"   After:  Y={self.viewer.current_pos_left['y']:.3f}")
        y_minus_change = self.viewer.current_pos_left['y'] - initial_pos['y']
        print(f"   Change: {y_minus_change:.3f} (expected: -1.000)")
        y_minus_correct = abs(y_minus_change + 1.0) < 0.001
        print(f"   Result: {'✅ CORRECT' if y_minus_correct else '❌ WRONG'}")
        
        # Summary
        print(f"\n" + "="*60)
        print("COORDINATE TEST SUMMARY:")
        print(f"  X+ movement: {'✅ CORRECT' if x_plus_correct else '❌ WRONG'}")
        print(f"  X- movement: {'✅ CORRECT' if x_minus_correct else '❌ WRONG'}")
        print(f"  Y+ movement: {'✅ CORRECT' if y_plus_correct else '❌ WRONG'}")
        print(f"  Y- movement: {'✅ CORRECT' if y_minus_correct else '❌ WRONG'}")
        
        if not x_plus_correct or not x_minus_correct:
            print("❌ X-AXIS COORDINATE ISSUE DETECTED")
        if not y_plus_correct or not y_minus_correct:
            print("❌ Y-AXIS COORDINATE ISSUE DETECTED")
            
        print("="*60)
        
        # Update status
        issues = []
        if not x_plus_correct or not x_minus_correct:
            issues.append("X-axis")
        if not y_plus_correct or not y_minus_correct:
            issues.append("Y-axis")
            
        if issues:
            self.status_label.setText(f"❌ Coordinate issues found in: {', '.join(issues)}")
        else:
            self.status_label.setText("✅ All coordinate tests passed!")
            
    def manual_test(self, axis, amount):
        """Manual test of a single movement"""
        if not hasattr(self.viewer, 'current_pos_left'):
            self.status_label.setText("❌ No model loaded")
            return
            
        # Get position before
        pos_before = self.viewer.current_pos_left[axis]
        
        # Make the move
        self.viewer.move_shape(axis, amount)
        QApplication.processEvents()
        time.sleep(0.1)
        
        # Get position after
        pos_after = self.viewer.current_pos_left[axis]
        change = pos_after - pos_before
        
        # Check if correct
        expected_change = amount
        is_correct = abs(change - expected_change) < 0.001
        
        result_text = f"{axis.upper()}{'+' if amount > 0 else '-'}: {pos_before:.3f} → {pos_after:.3f} (change: {change:.3f}, expected: {expected_change:.3f}) {'✅' if is_correct else '❌'}"
        self.results_label.setText(result_text)
        print(f"Manual test: {result_text}")

def main():
    app = QApplication(sys.argv)
    test = PositionCoordinateTest()
    test.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
