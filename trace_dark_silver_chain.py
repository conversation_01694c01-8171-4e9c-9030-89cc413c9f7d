#!/usr/bin/env python3

print("TRACING DARK SILVER COLOR CHAIN")

with open('SOIC16P127_1270X940X610L89X51.STEP', 'r', encoding='utf-8', errors='ignore') as f:
    content = f.read()

lines = content.split('\n')

# Start with dark silver fill area styles
dark_fill_styles = ['#7983', '#8035']

print(f"Starting with dark fill styles: {dark_fill_styles}")

# Find what surface styles reference these fill area styles
print("\n=== SURFACE STYLES THAT USE DARK FILL STYLES ===")
dark_surface_styles = []

for line in lines:
    if 'SURFACE_STYLE' in line and line.startswith('#'):
        for fill_style in dark_fill_styles:
            if fill_style in line:
                surface_id = line.split('=')[0].strip()
                dark_surface_styles.append(surface_id)
                print(f"{surface_id}: {line.strip()}")

print(f"\nDark surface styles: {dark_surface_styles}")

# Find what uses these surface styles
print("\n=== ITEMS THAT USE DARK SURFACE STYLES ===")
dark_items = []

for line in lines:
    for surface_style in dark_surface_styles:
        if surface_style in line and line.startswith('#'):
            item_id = line.split('=')[0].strip()
            if item_id not in dark_surface_styles:  # Don't include the surface style itself
                dark_items.append((item_id, line.strip()))
                print(f"{item_id}: {line.strip()}")

print(f"\nFound {len(dark_items)} items that use dark surface styles")

# Look for clues about what these items represent
print("\n=== ANALYZING DARK ITEMS FOR CLUES ===")
for item_id, line in dark_items:
    line_upper = line.upper()
    if any(word in line_upper for word in ['PIN', 'LEAD', 'TERMINAL', 'CONTACT']):
        print(f"FOUND PIN/LEAD: {line}")
    elif any(word in line_upper for word in ['BODY', 'PACKAGE', 'CASE']):
        print(f"FOUND BODY: {line}")
    elif any(word in line_upper for word in ['FACE', 'SURFACE', 'SHELL']):
        print(f"FOUND SURFACE: {line}")

print("\n=== DARK SILVER CHAIN TRACE COMPLETE ===")
print("This shows the complete chain from dark silver colors to the items that use them")
