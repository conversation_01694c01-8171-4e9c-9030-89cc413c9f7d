#!/usr/bin/env python3
"""
Simple test viewer for mouse rotation fix
Minimal version to avoid import issues
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QPushButton, QLabel, QFileDialog)
from PyQt5.QtCore import Qt

def main():
    print("🎯 Starting Simple Test Viewer...")
    print("✅ Mouse rotation save fix is integrated!")
    
    try:
        app = QApplication(sys.argv)
        
        # Import the fixed viewer
        from step_viewer_tdk_modular_fixed import StepViewerTDK
        
        print("✅ Imports successful, creating viewer...")
        
        viewer = StepViewerTDK()
        viewer.setWindowTitle("STEP Viewer - Mouse Rotation Fix Test")
        viewer.show()
        
        print("✅ Viewer window opened!")
        print("")
        print("📋 TEST INSTRUCTIONS:")
        print("1. Load a STEP file using 'Open STEP File' button")
        print("2. Rotate the model with your MOUSE by dragging")
        print("3. Save using green 'Save STEP File (Improved Method)' button")
        print("4. Load the saved file to verify mouse rotations are preserved")
        print("")
        print("🖱️  Mouse rotation save fix is ACTIVE!")
        print("🔧 The fix captures both mouse AND button rotations when saving")
        
        # Run the application
        result = app.exec_()
        print(f"Application exited with code: {result}")
        return result
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure all required modules are available")
        return 1
    except Exception as e:
        print(f"❌ Error starting viewer: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
