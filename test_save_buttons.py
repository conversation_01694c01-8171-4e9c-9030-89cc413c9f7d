#!/usr/bin/env python3
"""
Test script to verify the save button fixes are working correctly
"""

import sys
import vtk
from simple_step_modifier import SimpleSTEPModifier

def test_transformation_order():
    """Test that the transformation order fix is working correctly"""
    print("🧪 Testing VTK transformation order fix...")
    
    # Create test transformation with translation and rotation
    transform = vtk.vtkTransform()
    
    # OLD WAY (incorrect): Rotate first, then translate
    transform_old = vtk.vtkTransform()
    transform_old.RotateX(45)
    transform_old.RotateY(30) 
    transform_old.RotateZ(15)
    transform_old.Translate(10, 20, 30)
    
    # NEW WAY (correct): Translate first, then rotate
    transform_new = vtk.vtkTransform()
    transform_new.Translate(10, 20, 30)
    transform_new.RotateX(45)
    transform_new.RotateY(30)
    transform_new.RotateZ(15)
    
    # Test point transformation
    test_point = [1, 1, 1]
    
    # Apply old transformation
    old_result = transform_old.TransformPoint(test_point)
    print(f"❌ OLD (incorrect) transformation result: ({old_result[0]:.3f}, {old_result[1]:.3f}, {old_result[2]:.3f})")
    
    # Apply new transformation  
    new_result = transform_new.TransformPoint(test_point)
    print(f"✅ NEW (correct) transformation result: ({new_result[0]:.3f}, {new_result[1]:.3f}, {new_result[2]:.3f})")
    
    # They should be different
    if old_result != new_result:
        print("✅ Transformation order fix is working - results are different")
        return True
    else:
        print("❌ Transformation order fix may not be working - results are the same")
        return False

def test_simple_step_modifier():
    """Test that SimpleSTEPModifier error handling works"""
    print("\n🧪 Testing SimpleSTEPModifier error handling...")
    
    modifier = SimpleSTEPModifier()
    
    # Test loading non-existent file
    success = modifier.load_step_file("nonexistent_file.step")
    if not success:
        print("✅ Error handling works: load_step_file correctly returns False for missing file")
    else:
        print("❌ Error handling issue: load_step_file should return False for missing file")
        return False
    
    # Test modify_placement without loaded file
    try:
        result = modifier.modify_placement(0, 0, 0, 0, 0, 0)
        if not result:
            print("✅ Error handling works: modify_placement correctly returns False with no loaded file")
        else:
            print("❌ Error handling issue: modify_placement should return False with no loaded file")
            return False
    except Exception as e:
        print(f"✅ Error handling works: modify_placement raises exception with no loaded file: {e}")
    
    return True

def test_button_labels():
    """Test that button labels are improved"""
    print("\n🧪 Testing button label improvements...")
    
    # Check if gui_components.py has the new labels
    try:
        with open('gui_components.py', 'r') as f:
            content = f.read()
        
        if "Save with New Position" in content:
            print("✅ Button label fix: Option 1 has new clear label")
        else:
            print("❌ Button label issue: Option 1 still has old label")
            return False
            
        if "Save with Transformed Geometry" in content:
            print("✅ Button label fix: Option 2 has new clear label")
        else:
            print("❌ Button label issue: Option 2 still has old label")
            return False
            
        if "setToolTip" in content:
            print("✅ Tooltip improvement: Tooltips added to buttons")
        else:
            print("❌ Tooltip issue: No tooltips found")
            return False
            
        return True
        
    except FileNotFoundError:
        print("❌ Could not find gui_components.py file")
        return False

def test_error_handling_code():
    """Test that error handling code is present in save functions"""
    print("\n🧪 Testing error handling code improvements...")
    
    try:
        with open('step_viewer_tdk_modular.py', 'r') as f:
            content = f.read()
        
        # Check for improved error handling in Option 1
        if "if not modifier.load_step_file(loader.current_filename):" in content:
            print("✅ Error handling: Option 1 checks load_step_file return value")
        else:
            print("❌ Error handling issue: Option 1 missing load_step_file check")
            return False
            
        if "if not placement_success:" in content:
            print("✅ Error handling: Placement success is checked")
        else:
            print("❌ Error handling issue: Placement success not checked")
            return False
            
        # Check for transformation order fix
        if "transform.Translate(current_pos['x'], current_pos['y'], current_pos['z'])" in content:
            print("✅ Transformation fix: Translate called before rotate")
        else:
            print("❌ Transformation issue: Translate not called before rotate")
            return False
            
        return True
        
    except FileNotFoundError:
        print("❌ Could not find step_viewer_tdk_modular.py file")
        return False

def main():
    """Run all tests"""
    print("🧪 TESTING SAVE BUTTON FIXES")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 4
    
    # Test 1: Transformation order
    if test_transformation_order():
        tests_passed += 1
    
    # Test 2: SimpleSTEPModifier error handling
    if test_simple_step_modifier():
        tests_passed += 1
    
    # Test 3: Button labels
    if test_button_labels():
        tests_passed += 1
        
    # Test 4: Error handling code
    if test_error_handling_code():
        tests_passed += 1
    
    print("\n" + "=" * 50)
    print(f"🧪 TEST RESULTS: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("✅ ALL TESTS PASSED - Save button fixes are working correctly!")
        return True
    else:
        print(f"❌ {total_tests - tests_passed} tests failed - Some issues remain")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
