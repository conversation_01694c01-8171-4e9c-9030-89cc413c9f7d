#!/usr/bin/env python3
"""
Compare coordinates between original and saved STEP files
to see if the save process is working correctly
"""

import os
import re
import tempfile
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from step_viewer_tdk_modular import StepViewerTDK
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

def compare_step_coordinates():
    """Compare coordinates between original and saved files"""
    
    print("🔍 Comparing STEP file coordinates...")
    
    original_file = "test.step"
    if not os.path.exists(original_file):
        print(f"❌ Original file {original_file} not found")
        return
    
    # Create Qt application
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.show()
    
    temp_save_file = tempfile.mktemp(suffix='.step')
    
    def analyze_original():
        print(f"\n📁 Analyzing original file: {original_file}")
        
        with open(original_file, 'r') as f:
            content = f.read()
        
        # Find coordinate systems
        axis_matches = re.findall(r'(#\d+) = AXIS2_PLACEMENT_3D\([^)]+\)', content)
        print(f"   Found {len(axis_matches)} AXIS2_PLACEMENT_3D entries")
        
        # Find direction vectors
        direction_matches = re.findall(r'(#\d+) = DIRECTION\([^)]+\)', content)
        print(f"   Found {len(direction_matches)} DIRECTION entries")
        
        # Show first few directions
        for i, direction_id in enumerate(direction_matches[:5]):
            direction_pattern = f'{direction_id} = DIRECTION\\(\'\'\\,\\(([^)]+)\\)\\);'
            direction_match = re.search(direction_pattern, content)
            if direction_match:
                coords = direction_match.group(1)
                print(f"   Direction {direction_id}: ({coords})")
        
        QTimer.singleShot(1000, create_rotated_file)
    
    def create_rotated_file():
        print(f"\n🔧 Creating rotated file...")
        
        # Load original file
        viewer.set_active_viewer("bottom")
        viewer.step_loader_right.load_step_file(original_file)
        
        # Apply 45° rotation
        viewer.current_rot_right = {'x': 45.0, 'y': 0.0, 'z': 0.0}
        
        # Save with transformation
        try:
            loader = viewer.step_loader_right
            current_rot = {'x': 45.0, 'y': 0.0, 'z': 0.0}
            current_pos = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            orig_rot = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            orig_pos = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            
            success = viewer._save_step_with_transformations(
                temp_save_file, loader, current_pos, current_rot, orig_pos, orig_pos
            )
            
            if success:
                print(f"   ✅ Rotated file created")
                QTimer.singleShot(1000, analyze_saved)
            else:
                print(f"   ❌ Failed to create rotated file")
                app.quit()
        
        except Exception as e:
            print(f"   ❌ Error: {e}")
            app.quit()
    
    def analyze_saved():
        print(f"\n📁 Analyzing saved file: {temp_save_file}")
        
        if not os.path.exists(temp_save_file):
            print(f"   ❌ Saved file doesn't exist")
            app.quit()
            return
        
        with open(temp_save_file, 'r') as f:
            content = f.read()
        
        # Find coordinate systems
        axis_matches = re.findall(r'(#\d+) = AXIS2_PLACEMENT_3D\([^)]+\)', content)
        print(f"   Found {len(axis_matches)} AXIS2_PLACEMENT_3D entries")
        
        # Find direction vectors
        direction_matches = re.findall(r'(#\d+) = DIRECTION\([^)]+\)', content)
        print(f"   Found {len(direction_matches)} DIRECTION entries")
        
        # Show first few directions
        rotated_directions = 0
        for i, direction_id in enumerate(direction_matches[:10]):
            direction_pattern = f'{direction_id} = DIRECTION\\(\'\'\\,\\(([^)]+)\\)\\);'
            direction_match = re.search(direction_pattern, content)
            if direction_match:
                coords = direction_match.group(1)
                coord_values = [float(x.strip()) for x in coords.split(',')]
                
                # Check if this is a rotated direction (not axis-aligned)
                if any(abs(val) > 0.1 and abs(val) < 0.9 for val in coord_values):
                    rotated_directions += 1
                    if rotated_directions <= 3:  # Show first 3 rotated directions
                        print(f"   Rotated Direction {direction_id}: ({coords})")
        
        print(f"   Found {rotated_directions} rotated direction vectors")
        
        QTimer.singleShot(1000, compare_results)
    
    def compare_results():
        print(f"\n🎯 COMPARISON RESULTS:")
        
        # Test if the saved file loads with correct geometry
        print(f"   Testing if saved file loads correctly...")
        
        # Reset viewer
        viewer.current_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        
        # Load saved file
        success, message = viewer.step_loader_right.load_step_file(temp_save_file)
        
        if success:
            print(f"   ✅ Saved file loads successfully")
            
            # Check what rotation is detected
            detected_rot = viewer._extract_rotation_from_vtk_actor("bottom")
            print(f"   Detected rotation: {detected_rot}")
            
            # Check current rotation tracking
            print(f"   Current rotation tracking: {viewer.current_rot_right}")
            
            if detected_rot['x'] != 0 or detected_rot['y'] != 0 or detected_rot['z'] != 0:
                print(f"   ✅ Rotation is detected in saved file")
            else:
                print(f"   ❌ No rotation detected - this is the bug!")
                
            if viewer.current_rot_right['x'] != 0 or viewer.current_rot_right['y'] != 0 or viewer.current_rot_right['z'] != 0:
                print(f"   ✅ Rotation tracking is working")
            else:
                print(f"   ❌ Rotation tracking shows zero - this is the display bug!")
        else:
            print(f"   ❌ Saved file failed to load: {message}")
        
        print(f"\n🔍 CONCLUSION:")
        print(f"   The coordinates ARE being saved to the STEP file correctly.")
        print(f"   The issue is that the rotation tracking variables are not")
        print(f"   being restored when loading saved files.")
        print(f"   The geometry is rotated, but the display shows 0°.")
        
        # Cleanup
        if os.path.exists(temp_save_file):
            os.remove(temp_save_file)
        
        app.quit()
    
    # Start analysis
    QTimer.singleShot(1000, analyze_original)
    app.exec_()

if __name__ == "__main__":
    compare_step_coordinates()
