#!/usr/bin/env python3
"""
SYSTEMATIC OVERLAY DEBUG - Find exactly why overlay shows only one model
This will run the main program and systematically test each step of overlay creation
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from step_viewer_tdk_modular import Step<PERSON>iewerTDK

def debug_overlay_step_by_step():
    """Debug overlay creation step by step with detailed output"""
    
    print("🔍 SYSTEMATIC OVERLAY DEBUG")
    print("=" * 50)
    
    # Create app and viewer
    app = QApplication([])
    viewer = StepViewerTDK()
    viewer.show()
    
    # Step 1: Load files into both viewers
    print("\n📁 STEP 1: Loading files into both viewers...")
    
    # Load into TOP viewer
    viewer.active_viewer = "top"
    viewer.update_viewer_highlights()
    success1 = viewer.load_step_file_direct('SOIC16P127_1270X940X610L89X51.STEP')
    print(f"🔴 TOP file loaded: {success1}")
    
    if success1:
        print(f"   TOP renderer has step_actors: {hasattr(viewer.vtk_renderer_left, 'step_actors') and bool(viewer.vtk_renderer_left.step_actors)}")
        print(f"   TOP renderer has step_actor: {hasattr(viewer.vtk_renderer_left, 'step_actor') and bool(viewer.vtk_renderer_left.step_actor)}")
        if hasattr(viewer.vtk_renderer_left, 'step_actors') and viewer.vtk_renderer_left.step_actors:
            print(f"   TOP step_actors count: {len(viewer.vtk_renderer_left.step_actors)}")
        if hasattr(viewer.vtk_renderer_left, 'step_actor') and viewer.vtk_renderer_left.step_actor:
            print(f"   TOP step_actor exists: {viewer.vtk_renderer_left.step_actor is not None}")
    
    # Load into BOTTOM viewer
    viewer.active_viewer = "bottom"
    viewer.update_viewer_highlights()
    success2 = viewer.load_step_file_direct('AMPHENOL_U77-A1118-200T.STEP')
    print(f"🔵 BOTTOM file loaded: {success2}")
    
    if success2:
        print(f"   BOTTOM renderer has step_actors: {hasattr(viewer.vtk_renderer_right, 'step_actors') and bool(viewer.vtk_renderer_right.step_actors)}")
        print(f"   BOTTOM renderer has step_actor: {hasattr(viewer.vtk_renderer_right, 'step_actor') and bool(viewer.vtk_renderer_right.step_actor)}")
        if hasattr(viewer.vtk_renderer_right, 'step_actors') and viewer.vtk_renderer_right.step_actors:
            print(f"   BOTTOM step_actors count: {len(viewer.vtk_renderer_right.step_actors)}")
        if hasattr(viewer.vtk_renderer_right, 'step_actor') and viewer.vtk_renderer_right.step_actor:
            print(f"   BOTTOM step_actor exists: {viewer.vtk_renderer_right.step_actor is not None}")
    
    if not (success1 and success2):
        print("❌ FAILED: Cannot test overlay without both files loaded")
        return False
    
    # Step 2: Test overlay creation
    print("\n🎯 STEP 2: Testing overlay creation...")
    
    try:
        # Call the overlay method directly
        viewer.toggle_viewer_overlay()
        
        # Check if overlay was created
        overlay_created = hasattr(viewer, 'overlay_widget') and viewer.overlay_widget is not None
        print(f"   Overlay widget created: {overlay_created}")
        
        if overlay_created:
            print(f"   Overlay widget visible: {viewer.overlay_widget.isVisible()}")
            print(f"   Overlay mode active: {viewer.overlay_mode}")
            
            # Check if overlay VTK widget exists
            overlay_vtk_exists = hasattr(viewer, 'overlay_vtk_widget') and viewer.overlay_vtk_widget is not None
            print(f"   Overlay VTK widget exists: {overlay_vtk_exists}")
            
            if overlay_vtk_exists:
                # Get the overlay renderer and check actors
                render_window = viewer.overlay_vtk_widget.GetRenderWindow()
                print(f"   Render window exists: {render_window is not None}")
                if render_window:
                    renderers = render_window.GetRenderers()
                    renderer_count = renderers.GetNumberOfItems()
                    print(f"   Number of renderers: {renderer_count}")
                    renderers.InitTraversal()
                    overlay_renderer = renderers.GetNextItem()
                    print(f"   First renderer: {overlay_renderer is not None}")

                    if overlay_renderer:
                        actor_count = overlay_renderer.GetActors().GetNumberOfItems()
                        print(f"   Overlay renderer actor count: {actor_count}")
                        
                        # List all actors and their colors
                        actors = overlay_renderer.GetActors()
                        actors.InitTraversal()
                        for i in range(actor_count):
                            actor = actors.GetNextItem()
                            if actor:
                                color = actor.GetProperty().GetColor()
                                visible = actor.GetVisibility()
                                print(f"   Actor {i}: Color={color}, Visible={visible}")
                    else:
                        print("   ❌ No overlay renderer found")
                else:
                    print("   ❌ No overlay render window found")
        
        print("\n✅ OVERLAY DEBUG COMPLETE")
        print("Check the output above to see exactly what's happening")
        
        return True
        
    except Exception as e:
        print(f"❌ OVERLAY CREATION FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_overlay_step_by_step()
