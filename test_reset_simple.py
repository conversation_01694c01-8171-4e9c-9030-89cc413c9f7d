#!/usr/bin/env python3
"""
Simple test to verify reset functionality
"""

import sys
import time
from PyQt5.QtWidgets import QApplication
from step_viewer_tdk_modular import StepViewerTDK

def test_reset():
    """Test the reset functionality"""
    print("🔧 TESTING RESET FUNCTIONALITY")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    
    # Show initial state
    print(f"Initial active viewer: {viewer.active_viewer}")
    print(f"Initial position (top): {viewer.current_pos_left}")
    print(f"Initial rotation (top): {viewer.current_rot_left}")
    
    # Apply some rotation
    print("\n🔄 Applying 45° Z rotation...")
    viewer.rotate_shape('z', 45)
    print(f"After rotation position (top): {viewer.current_pos_left}")
    print(f"After rotation rotation (top): {viewer.current_rot_left}")
    
    # Test reset
    print("\n🔧 Testing reset...")
    viewer.reset_to_original()
    print(f"After reset position (top): {viewer.current_pos_left}")
    print(f"After reset rotation (top): {viewer.current_rot_left}")
    
    # Check if values are actually 0
    pos_reset = all(abs(v) < 0.01 for v in viewer.current_pos_left.values())
    rot_reset = all(abs(v) < 0.01 for v in viewer.current_rot_left.values())
    
    print(f"\n📊 RESULTS:")
    print(f"Position reset to 0,0,0: {'✅ YES' if pos_reset else '❌ NO'}")
    print(f"Rotation reset to 0,0,0: {'✅ YES' if rot_reset else '❌ NO'}")
    
    success = pos_reset and rot_reset
    print(f"Overall: {'✅ SUCCESS' if success else '❌ FAILED'}")
    
    app.quit()
    return success

if __name__ == "__main__":
    success = test_reset()
    print(f"\nTest {'PASSED' if success else 'FAILED'}")
