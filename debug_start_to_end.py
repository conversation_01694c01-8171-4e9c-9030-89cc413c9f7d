#!/usr/bin/env python3

print("DEBUG FROM START TO END - STEP FILE TO DISPLAY")

# Step 1: Check STEP file colors
print("=== STEP 1: STEP FILE COLORS ===")
with open('SOIC16P127_1270X940X610L89X51.STEP', 'r', encoding='utf-8', errors='ignore') as f:
    content = f.read()

import re
colour_pattern = r'COLOUR_RGB.*?([\d\.]+)\s*,\s*([\d\.]+)\s*,\s*([\d\.]+)'
matches = re.findall(colour_pattern, content)

step_file_colors = []
for match in matches:
    r = int(float(match[0]) * 255)
    g = int(float(match[1]) * 255)
    b = int(float(match[2]) * 255)
    step_file_colors.append((r, g, b))

unique_step_colors = list(set(step_file_colors))
print(f"STEP file has {len(unique_step_colors)} unique colors:")
for color in unique_step_colors:
    count = step_file_colors.count(color)
    print(f"  RGB{color}: {count} entries")

# Step 2: Test step_loader
print("\n=== STEP 2: STEP_LOADER TEST ===")
try:
    from step_loader import STEPLoader
    loader = STEPLoader()
    success, msg = loader.load_step_file('SOIC16P127_1270X940X610L89X51.STEP')
    
    if success and loader.current_polydata:
        polydata = loader.current_polydata
        colors_array = polydata.GetCellData().GetScalars("Colors")
        
        if colors_array:
            # Count display colors
            display_colors = []
            for i in range(colors_array.GetNumberOfTuples()):
                r = int(colors_array.GetComponent(i, 0))
                g = int(colors_array.GetComponent(i, 1))
                b = int(colors_array.GetComponent(i, 2))
                display_colors.append((r, g, b))
            
            unique_display_colors = list(set(display_colors))
            print(f"Display has {len(unique_display_colors)} unique colors:")
            for color in unique_display_colors:
                count = display_colors.count(color)
                print(f"  RGB{color}: {count} cells")
            
            # Step 3: Compare
            print("\n=== STEP 3: COMPARISON ===")
            colors_match = set(unique_step_colors) == set(unique_display_colors)
            
            if colors_match:
                print("SUCCESS: STEP file colors match display colors exactly")
            else:
                print("FAILURE: Colors do not match")
                print("STEP file colors:", unique_step_colors)
                print("Display colors:", unique_display_colors)
                
                missing_in_display = set(unique_step_colors) - set(unique_display_colors)
                extra_in_display = set(unique_display_colors) - set(unique_step_colors)
                
                if missing_in_display:
                    print(f"Missing in display: {missing_in_display}")
                if extra_in_display:
                    print(f"Extra in display: {extra_in_display}")
            
            # Step 4: Test main program compatibility
            print("\n=== STEP 4: MAIN PROGRAM COMPATIBILITY ===")
            print(f"Polydata ready: {polydata.GetNumberOfCells()} cells, {polydata.GetNumberOfPoints()} points")
            print(f"Colors ready: {colors_array.GetNumberOfTuples()} color tuples")
            print("Ready for main program display")
            
        else:
            print("FAILURE: No colors in polydata")
    else:
        print(f"FAILURE: step_loader failed - {msg}")
        
except Exception as e:
    print(f"FAILURE: Error in step_loader - {e}")
    import traceback
    traceback.print_exc()

print("\n=== DEBUG COMPLETE ===")
print("If all steps show SUCCESS, the main program should display correct colors")
