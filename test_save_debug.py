#!/usr/bin/env python3
"""
Test program to debug and fix STEP file save issues
- Test file overwriting
- Test transformation application
- Verify STEP file content
"""

import os
import sys
import shutil
import vtk
import math

# Add current directory to path for imports
sys.path.append('.')

from step_loader import STEP<PERSON>oader

def test_file_overwrite():
    """Test if we can overwrite existing files"""
    print("🧪 TESTING FILE OVERWRITE...")
    
    test_file = "test_overwrite.step"
    
    # Create a test file
    with open(test_file, 'w') as f:
        f.write("TEST CONTENT 1")
    
    original_size = os.path.getsize(test_file)
    print(f"   Original file size: {original_size} bytes")
    
    # Try to overwrite it
    with open(test_file, 'w') as f:
        f.write("TEST CONTENT 2 - MUCH LONGER CONTENT TO TEST OVERWRITE")
    
    new_size = os.path.getsize(test_file)
    print(f"   New file size: {new_size} bytes")
    
    if new_size != original_size:
        print("✅ File overwrite works correctly")
        os.remove(test_file)
        return True
    else:
        print("❌ File overwrite failed")
        return False

def test_step_loader_save():
    """Test the STEPLoader save functionality"""
    print("\n🧪 TESTING STEP LOADER SAVE...")
    
    # Load a test file
    loader = STEPLoader()
    if not loader.load_step_file("test.step"):
        print("❌ Failed to load test.step")
        return False
    
    print("✅ Loaded test.step successfully")
    
    # Test 1: Save without transformations
    print("\n📝 Test 1: Save without transformations")
    output_file1 = "test_save_no_transform.step"
    
    # Remove existing file if it exists
    if os.path.exists(output_file1):
        os.remove(output_file1)
        print(f"   Removed existing file: {output_file1}")
    
    success1 = loader.save_step_file(output_file1)
    print(f"   Save result: {success1}")
    
    if os.path.exists(output_file1):
        size1 = os.path.getsize(output_file1)
        print(f"   File created, size: {size1} bytes")
    else:
        print("   ❌ File not created")
    
    # Test 2: Save with transformations
    print("\n📝 Test 2: Save with transformations")
    output_file2 = "test_save_with_transform.step"
    
    # Remove existing file if it exists
    if os.path.exists(output_file2):
        os.remove(output_file2)
        print(f"   Removed existing file: {output_file2}")
    
    # Create a transformation matrix (45 degree rotation around Z axis)
    transform = vtk.vtkTransform()
    transform.RotateZ(45.0)  # 45 degrees
    transform.Translate(10.0, 5.0, 2.0)  # Move it
    
    print(f"   Applying transformation: 45° Z rotation + translation (10, 5, 2)")
    
    success2 = loader.save_step_file(output_file2, transform.GetMatrix())
    print(f"   Save result: {success2}")
    
    if os.path.exists(output_file2):
        size2 = os.path.getsize(output_file2)
        print(f"   File created, size: {size2} bytes")
    else:
        print("   ❌ File not created")
    
    return success1 and success2

def analyze_step_file_content(filename):
    """Analyze STEP file content to check for transformations"""
    print(f"\n🔍 ANALYZING STEP FILE: {filename}")
    
    if not os.path.exists(filename):
        print(f"❌ File does not exist: {filename}")
        return
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"   File size: {len(content)} characters")
        
        # Look for coordinate system placements
        import re
        
        # Find AXIS2_PLACEMENT_3D entries
        axis_pattern = r'#(\d+)\s*=\s*AXIS2_PLACEMENT_3D\s*\([^)]+\)'
        axis_matches = re.findall(axis_pattern, content)
        print(f"   Found {len(axis_matches)} AXIS2_PLACEMENT_3D entries")
        
        # Find CARTESIAN_POINT entries
        point_pattern = r'#(\d+)\s*=\s*CARTESIAN_POINT\s*\([^)]+\)'
        point_matches = re.findall(point_pattern, content)
        print(f"   Found {len(point_matches)} CARTESIAN_POINT entries")
        
        # Look for specific coordinate values to detect transformations
        coord_pattern = r'CARTESIAN_POINT\s*\(\s*\'[^\']*\'\s*,\s*\(\s*([-\d.E+-]+)\s*,\s*([-\d.E+-]+)\s*,\s*([-\d.E+-]+)\s*\)'
        coord_matches = re.findall(coord_pattern, content)
        
        if coord_matches:
            print(f"   Sample coordinates found:")
            for i, (x, y, z) in enumerate(coord_matches[:5]):  # Show first 5
                print(f"     Point {i+1}: ({x}, {y}, {z})")
        
        # Look for the main placement (usually the first AXIS2_PLACEMENT_3D)
        main_placement_pattern = r'#8044\s*=\s*AXIS2_PLACEMENT_3D[^;]+;'
        main_placement = re.search(main_placement_pattern, content)
        if main_placement:
            print(f"   Main placement found: {main_placement.group(0)}")
        
    except Exception as e:
        print(f"❌ Error analyzing file: {e}")

def compare_step_files(file1, file2):
    """Compare two STEP files to see if they're different"""
    print(f"\n🔍 COMPARING FILES: {file1} vs {file2}")
    
    if not os.path.exists(file1) or not os.path.exists(file2):
        print("❌ One or both files don't exist")
        return
    
    size1 = os.path.getsize(file1)
    size2 = os.path.getsize(file2)
    
    print(f"   {file1}: {size1} bytes")
    print(f"   {file2}: {size2} bytes")
    
    if size1 == size2:
        # Check if content is identical
        with open(file1, 'rb') as f1, open(file2, 'rb') as f2:
            content1 = f1.read()
            content2 = f2.read()
            
        if content1 == content2:
            print("❌ Files are identical - no transformation applied!")
        else:
            print("✅ Files are different - transformation may have been applied")
    else:
        print("✅ Files have different sizes - transformation likely applied")

def test_opencascade_availability():
    """Test if OpenCASCADE modules are available for STEP writing"""
    print("\n🧪 TESTING OPENCASCADE AVAILABILITY...")
    
    try:
        from OCC.Core.STEPControl_Writer import STEPControl_Writer
        from OCC.Core.Interface_Static import Interface_Static
        from OCC.Core.IFSelect_ReturnStatus import IFSelect_RetDone
        print("✅ OpenCASCADE STEP writing modules available")
        return True
    except ImportError as e:
        print(f"❌ OpenCASCADE STEP writing modules not available: {e}")
        return False

def test_step_transformer_verification():
    """Test and verify the STEP transformer creates correct transformations"""
    print("\n🧪 TESTING STEP TRANSFORMER VERIFICATION...")

    # Test the new STEP transformer
    from step_transformer import STEPTransformer

    transformer = STEPTransformer()
    if not transformer.load_step_file("test.step"):
        print("❌ Failed to load test.step")
        return False

    # Apply transformation
    success = transformer.apply_transformation(
        rotation_z=90.0,  # 90 degree rotation should be very visible
        translation_x=20.0,
        translation_y=10.0,
        translation_z=5.0
    )

    if not success:
        print("❌ Transformation failed")
        return False

    # Save transformed file
    output_file = "test_step_transformer_90deg.step"
    if not transformer.save_step_file(output_file):
        print("❌ Save failed")
        return False

    print("✅ STEP transformer test successful")
    return True

def main():
    """Main test function"""
    print("🔧 STEP FILE SAVE DEBUG PROGRAM")
    print("=" * 50)

    # Test 1: Basic file overwrite
    overwrite_ok = test_file_overwrite()

    # Test 2: OpenCASCADE availability
    occ_available = test_opencascade_availability()

    # Test 3: STEP loader save functionality
    save_ok = test_step_loader_save()

    # Test 4: Test new STEP transformer
    transformer_ok = test_step_transformer_verification()

    # Test 5: Analyze created files
    analyze_step_file_content("test_save_no_transform.step")
    analyze_step_file_content("test_save_with_transform.step")
    analyze_step_file_content("test_step_transformer_90deg.step")

    # Test 6: Compare files
    compare_step_files("test.step", "test_save_no_transform.step")
    compare_step_files("test.step", "test_step_transformer_90deg.step")

    print("\n" + "=" * 50)
    print("🎯 SUMMARY:")
    print(f"   File overwrite: {'✅ OK' if overwrite_ok else '❌ FAILED'}")
    print(f"   OpenCASCADE: {'✅ Available' if occ_available else '❌ Not available'}")
    print(f"   STEP save: {'✅ OK' if save_ok else '❌ FAILED'}")
    print(f"   STEP transformer: {'✅ OK' if transformer_ok else '❌ FAILED'}")

    if transformer_ok:
        print("\n💡 SOLUTION FOUND:")
        print("   - Use STEPTransformer for STEP file transformations")
        print("   - It creates proper STEP files with transformations applied")
        print("   - Works without OpenCASCADE writing modules")

if __name__ == "__main__":
    main()
