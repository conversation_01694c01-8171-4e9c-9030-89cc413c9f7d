#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug <PERSON><PERSON> - Find out what's overriding our button sizes
"""

import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QPushButton, QLabel, QGroupBox, QDockWidget)
from PyQt5.QtCore import Qt

class ButtonSizeDebug(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Button Size Debug - Find the Problem")
        self.setGeometry(100, 100, 1000, 700)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QHBoxLayout(central_widget)
        
        # Create dock widget exactly like main program
        dock = QDockWidget("Debug Dock", self)
        dock.setAllowedAreas(Qt.LeftDockWidgetArea | Qt.RightDockWidgetArea)
        dock.setMinimumWidth(350)  # Same as our fix
        dock.setMaximumWidth(400)  # Same as our fix
        
        panel = QWidget()
        panel.setMinimumWidth(340)  # Same as our fix
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # Test different approaches
        self.create_debug_tests(layout)
        
        dock.setWidget(panel)
        self.addDockWidget(Qt.LeftDockWidgetArea, dock)
        
        # Add info display
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)
        
        self.info_label = QLabel("Button Size Information:")
        info_layout.addWidget(self.info_label)
        
        main_layout.addWidget(info_widget)
        
    def create_debug_tests(self, layout):
        """Create various button tests to find what works"""
        
        # Test 1: Exact copy of our current approach
        test1_group = QGroupBox("Test 1: Our Current Approach")
        test1_layout = QVBoxLayout(test1_group)
        
        x_layout1 = QHBoxLayout()
        x_layout1.setSpacing(5)
        btn1_minus = QPushButton("X-")
        btn1_minus.setStyleSheet("QPushButton { font-size: 14px; padding: 15px; min-width: 60px; }")
        btn1_plus = QPushButton("X+")
        btn1_plus.setStyleSheet("QPushButton { font-size: 14px; padding: 15px; min-width: 60px; }")
        x_layout1.addWidget(btn1_minus)
        x_layout1.addWidget(btn1_plus)
        test1_layout.addLayout(x_layout1)
        layout.addWidget(test1_group)
        
        # Test 2: Force with setFixedSize
        test2_group = QGroupBox("Test 2: Force with setFixedSize")
        test2_layout = QVBoxLayout(test2_group)
        
        x_layout2 = QHBoxLayout()
        x_layout2.setSpacing(5)
        btn2_minus = QPushButton("X-")
        btn2_minus.setFixedSize(80, 40)  # Force exact size
        btn2_minus.setStyleSheet("QPushButton { font-size: 14px; }")
        btn2_plus = QPushButton("X+")
        btn2_plus.setFixedSize(80, 40)  # Force exact size
        btn2_plus.setStyleSheet("QPushButton { font-size: 14px; }")
        x_layout2.addWidget(btn2_minus)
        x_layout2.addWidget(btn2_plus)
        test2_layout.addLayout(x_layout2)
        layout.addWidget(test2_group)
        
        # Test 3: Check if there's a parent stylesheet override
        test3_group = QGroupBox("Test 3: Override Parent Styles")
        test3_layout = QVBoxLayout(test3_group)
        
        x_layout3 = QHBoxLayout()
        x_layout3.setSpacing(5)
        btn3_minus = QPushButton("X-")
        btn3_minus.setStyleSheet("QPushButton { font-size: 14px; padding: 15px; min-width: 60px; min-height: 40px; } QPushButton:hover { background-color: lightblue; }")
        btn3_plus = QPushButton("X+")
        btn3_plus.setStyleSheet("QPushButton { font-size: 14px; padding: 15px; min-width: 60px; min-height: 40px; } QPushButton:hover { background-color: lightblue; }")
        x_layout3.addWidget(btn3_minus)
        x_layout3.addWidget(btn3_plus)
        test3_layout.addLayout(x_layout3)
        layout.addWidget(test3_group)
        
        # Test 4: Check actual sizes after creation
        test4_group = QGroupBox("Test 4: Size Reporting")
        test4_layout = QVBoxLayout(test4_group)
        
        x_layout4 = QHBoxLayout()
        x_layout4.setSpacing(5)
        self.btn4_minus = QPushButton("X-")
        self.btn4_minus.setStyleSheet("QPushButton { font-size: 14px; padding: 15px; min-width: 60px; }")
        self.btn4_plus = QPushButton("X+")
        self.btn4_plus.setStyleSheet("QPushButton { font-size: 14px; padding: 15px; min-width: 60px; }")
        x_layout4.addWidget(self.btn4_minus)
        x_layout4.addWidget(self.btn4_plus)
        test4_layout.addLayout(x_layout4)
        layout.addWidget(test4_group)
        
        # Add a button to check sizes
        check_btn = QPushButton("Check Button Sizes")
        check_btn.clicked.connect(self.check_button_sizes)
        layout.addWidget(check_btn)
        
    def check_button_sizes(self):
        """Check and report actual button sizes"""
        size_info = []
        size_info.append(f"Test 4 X- button size: {self.btn4_minus.size().width()} x {self.btn4_minus.size().height()}")
        size_info.append(f"Test 4 X+ button size: {self.btn4_plus.size().width()} x {self.btn4_plus.size().height()}")
        size_info.append(f"Test 4 X- sizeHint: {self.btn4_minus.sizeHint().width()} x {self.btn4_minus.sizeHint().height()}")
        size_info.append(f"Test 4 X+ sizeHint: {self.btn4_plus.sizeHint().width()} x {self.btn4_plus.sizeHint().height()}")
        
        info_text = "Button Size Information:\n" + "\n".join(size_info)
        self.info_label.setText(info_text)
        print("\n" + info_text)

def main():
    app = QApplication(sys.argv)
    window = ButtonSizeDebug()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
