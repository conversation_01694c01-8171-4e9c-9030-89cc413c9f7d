#!/usr/bin/env python3
"""
Test ALL save options and show actual file results
"""

import os
import sys
import shutil
import time
from datetime import datetime

def get_file_info(filename):
    """Get detailed file information"""
    if os.path.exists(filename):
        stat = os.stat(filename)
        return {
            'exists': True,
            'size': stat.st_size,
            'mtime': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
            'readable': os.access(filename, os.R_OK)
        }
    else:
        return {'exists': False, 'size': 0, 'mtime': 'N/A', 'readable': False}

def show_file_contents(filename, max_lines=10):
    """Show first few lines of file content"""
    if not os.path.exists(filename):
        return "FILE DOES NOT EXIST"
    
    try:
        with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
            if len(lines) == 0:
                return "FILE IS EMPTY (0 bytes)"
            elif len(lines) <= max_lines:
                return ''.join(lines)
            else:
                return ''.join(lines[:max_lines]) + f"\n... ({len(lines)} total lines)"
    except Exception as e:
        return f"ERROR READING FILE: {e}"

def test_all_save_options():
    """Test all save options and show results"""
    print("=" * 80)
    print("🧪 TESTING ALL SAVE OPTIONS - OPTION 1, OPTION 2, ORIGINAL")
    print("=" * 80)
    
    # Check if test.step exists
    test_file = "test.step"
    if not os.path.exists(test_file):
        print(f"❌ ERROR: {test_file} not found!")
        return False
    
    original_info = get_file_info(test_file)
    print(f"📁 Original file: {test_file}")
    print(f"   Size: {original_info['size']} bytes")
    print(f"   Modified: {original_info['mtime']}")
    
    try:
        # Import the main program
        from step_viewer_tdk_modular import StepViewerTDK
        from PyQt5.QtWidgets import QApplication
        
        # Create QApplication if needed
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("✅ Successfully imported StepViewerTDK")
        
        # Create viewer instance
        viewer = StepViewerTDK()
        
        # Load the test file
        print(f"\n📝 Loading test file: {test_file}")
        success, msg = viewer.step_loader_left.load_step_file(test_file)
        
        if not success:
            print(f"❌ Failed to load {test_file}: {msg}")
            return False
        
        print(f"✅ Loaded {test_file} successfully")
        
        # Set up some transformations for testing
        viewer.current_pos_left = {'x': 10.0, 'y': 5.0, 'z': 2.0}
        viewer.current_rot_left = {'x': 45.0, 'y': 30.0, 'z': 15.0}
        viewer.active_viewer = "top"
        
        print(f"\n🔧 Set up test transformations:")
        print(f"   Position: {viewer.current_pos_left}")
        print(f"   Rotation: {viewer.current_rot_left}")
        
        # Test files
        test_files = {
            'original': 'test_all_original.step',
            'option1': 'test_all_option1.step',
            'option2': 'test_all_option2.step'
        }
        
        # Remove any existing test files
        for filename in test_files.values():
            if os.path.exists(filename):
                os.remove(filename)
        
        results = {}
        
        print("\n" + "=" * 60)
        print("🧪 TESTING ORIGINAL SAVE")
        print("=" * 60)
        
        # Test Original Save
        def mock_get_save_filename_original(title):
            return test_files['original']
        
        viewer._get_save_filename = mock_get_save_filename_original
        
        try:
            print(f"📝 Testing Original Save to: {test_files['original']}")
            viewer.save_original_step()
            
            info = get_file_info(test_files['original'])
            print(f"   Result: Exists={info['exists']}, Size={info['size']} bytes")
            
            if info['exists'] and info['size'] > 0:
                print(f"   ✅ SUCCESS: Original save worked")
                results['original'] = True
            else:
                print(f"   ❌ FAILED: Original save failed")
                results['original'] = False
                
        except Exception as e:
            print(f"   ❌ EXCEPTION: {e}")
            results['original'] = False
        
        print("\n" + "=" * 60)
        print("🧪 TESTING OPTION 1 SAVE")
        print("=" * 60)
        
        # Test Option 1 Save
        def mock_get_save_filename_option1(title):
            return test_files['option1']
        
        viewer._get_save_filename = mock_get_save_filename_option1
        
        try:
            print(f"📝 Testing Option 1 Save to: {test_files['option1']}")
            viewer.save_step_file_option1()
            
            info = get_file_info(test_files['option1'])
            print(f"   Result: Exists={info['exists']}, Size={info['size']} bytes")
            
            if info['exists'] and info['size'] > 0:
                print(f"   ✅ SUCCESS: Option 1 save worked")
                results['option1'] = True
            else:
                print(f"   ❌ FAILED: Option 1 save failed")
                results['option1'] = False
                
        except Exception as e:
            print(f"   ❌ EXCEPTION: {e}")
            results['option1'] = False
        
        print("\n" + "=" * 60)
        print("🧪 TESTING OPTION 2 SAVE")
        print("=" * 60)
        
        # Test Option 2 Save
        def mock_get_save_filename_option2(title):
            return test_files['option2']
        
        viewer._get_save_filename = mock_get_save_filename_option2
        
        try:
            print(f"📝 Testing Option 2 Save to: {test_files['option2']}")
            viewer.save_step_file_option2()
            
            info = get_file_info(test_files['option2'])
            print(f"   Result: Exists={info['exists']}, Size={info['size']} bytes")
            
            if info['exists'] and info['size'] > 0:
                print(f"   ✅ SUCCESS: Option 2 save worked")
                results['option2'] = True
            else:
                print(f"   ❌ FAILED: Option 2 save failed")
                results['option2'] = False
                
        except Exception as e:
            print(f"   ❌ EXCEPTION: {e}")
            results['option2'] = False
        
        print("\n" + "=" * 60)
        print("📊 DETAILED FILE ANALYSIS")
        print("=" * 60)
        
        for save_type, filename in test_files.items():
            info = get_file_info(filename)
            print(f"\n📁 {save_type.upper()} SAVE: {filename}")
            print(f"   Exists: {info['exists']}")
            print(f"   Size: {info['size']} bytes")
            print(f"   Modified: {info['mtime']}")
            
            if info['exists']:
                print(f"   Content preview:")
                content = show_file_contents(filename, 5)
                for line in content.split('\n')[:5]:
                    if line.strip():
                        print(f"     {line}")
            else:
                print(f"   ❌ FILE DOES NOT EXIST")
        
        print("\n" + "=" * 60)
        print("📊 FINAL RESULTS SUMMARY")
        print("=" * 60)
        
        all_passed = True
        for save_type, passed in results.items():
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f"{save_type.upper():15} {status}")
            if not passed:
                all_passed = False
        
        if all_passed:
            print(f"\n🎉 ALL SAVE OPTIONS WORK!")
        else:
            print(f"\n❌ SOME SAVE OPTIONS FAILED!")
            print(f"\nKeeping test files for inspection:")
            for save_type, filename in test_files.items():
                if os.path.exists(filename):
                    info = get_file_info(filename)
                    print(f"   {filename}: {info['size']} bytes")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ CRITICAL ERROR: {e}")
        return False

if __name__ == "__main__":
    success = test_all_save_options()
    if success:
        print(f"\n✅ ALL SAVE OPTIONS WORKING!")
    else:
        print(f"\n❌ SAVE OPTIONS NEED FIXING!")
