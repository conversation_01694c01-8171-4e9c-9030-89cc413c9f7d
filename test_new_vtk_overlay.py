#!/usr/bin/env python3
"""
Test the NEW VTK multiple renderer overlay approach
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer_tdk_modular import VTKSTEPViewer

class NewVTKOverlayTest:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = VTKSTEPViewer()
        self.viewer.show()
        
        # Timer for automatic testing
        self.timer = QTimer()
        self.timer.timeout.connect(self.run_test_sequence)
        self.step = 0
        
        print("🎯 NEW VTK MULTIPLE RENDERER OVERLAY TEST")
        print("   Testing the corrected VTK approach")
        
    def run_test_sequence(self):
        """Run the test sequence step by step"""
        try:
            if self.step == 0:
                print("🎯 Starting NEW VTK test sequence...")
                self.step += 1
                self.timer.start(3000)  # Wait 3 seconds between steps
                
            elif self.step == 1:
                print("\n📁 STEP 1: Loading TOP file...")
                # Load TOP file
                top_file = "SOIC16P127_1270X940X610L89X51.STEP"
                if os.path.exists(top_file):
                    self.viewer.load_step_file(top_file, viewer='top')
                    print("✅ TOP file loaded successfully")
                else:
                    print(f"❌ TOP file not found: {top_file}")
                self.step += 1
                
            elif self.step == 2:
                print("\n📁 STEP 2: Loading BOTTOM file...")
                # Load BOTTOM file  
                bottom_file = "AMPHENOL_U77-A1118-200T.STEP"
                if os.path.exists(bottom_file):
                    self.viewer.load_step_file(bottom_file, viewer='bottom')
                    print("✅ BOTTOM file loaded successfully")
                else:
                    print(f"❌ BOTTOM file not found: {bottom_file}")
                self.step += 1
                
            elif self.step == 3:
                print("\n🎯 STEP 3: Triggering NEW VTK overlay...")
                print("🎯 Calling toggle_viewer_overlay()...")
                self.viewer.toggle_viewer_overlay()
                print("✅ toggle_viewer_overlay() completed")
                self.step += 1
                
            elif self.step == 4:
                print("\n🔍 STEP 4: Diagnosing NEW VTK overlay...")
                self.diagnose_new_overlay()
                self.step += 1
                
            else:
                print("\n✅ NEW VTK TEST COMPLETE")
                self.timer.stop()
                
        except Exception as e:
            print(f"❌ Error in test step {self.step}: {e}")
            import traceback
            traceback.print_exc()
            self.timer.stop()
    
    def diagnose_new_overlay(self):
        """Diagnose the NEW VTK overlay result"""
        print("🔍 Diagnosing NEW VTK overlay...")
        print(f"   overlay_mode: {self.viewer.overlay_mode}")
        
        if hasattr(self.viewer, 'overlay_renderer') and self.viewer.overlay_renderer:
            print("   ✅ NEW overlay_renderer found!")
            
            # Check TOP render window (where overlay should be)
            if hasattr(self.viewer, 'top_vtk_widget') and self.viewer.top_vtk_widget:
                top_render_window = self.viewer.top_vtk_widget.GetRenderWindow()
                if top_render_window:
                    print(f"   ✅ TOP render_window exists")
                    
                    # Check renderers in TOP window
                    renderers = top_render_window.GetRenderers()
                    renderer_count = renderers.GetNumberOfItems()
                    print(f"   📊 TOP renderer count: {renderer_count}")
                    
                    # Check overlay renderer specifically
                    actors = self.viewer.overlay_renderer.GetActors()
                    actor_count = actors.GetNumberOfItems()
                    print(f"   🎭 OVERLAY renderer actor count: {actor_count}")
                    
                    # Check each overlay actor
                    actors.InitTraversal()
                    for i in range(actor_count):
                        actor = actors.GetNextItem()
                        if actor:
                            color = actor.GetProperty().GetColor()
                            visible = actor.GetVisibility()
                            print(f"   🎨 Overlay Actor {i}: Color={color}, Visible={visible}")
                else:
                    print("   ❌ TOP render_window not found")
            else:
                print("   ❌ TOP vtk_widget not found")
        else:
            print("   ❌ NEW overlay_renderer not found")
    
    def run(self):
        """Start the test"""
        # Start the test sequence
        self.timer.start(1000)  # Start after 1 second
        return self.app.exec_()

if __name__ == "__main__":
    test = NewVTKOverlayTest()
    sys.exit(test.run())
