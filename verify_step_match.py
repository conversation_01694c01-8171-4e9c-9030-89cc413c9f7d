#!/usr/bin/env python3

print("VERIFYING COLORS AND SHAPES MATCH ORIGINAL STEP FILE")

# Step 1: Load STEP file with OpenCASCADE to get original colors and geometry
try:
    from OCC.Core.STEPControl import STEP<PERSON><PERSON>rol_Reader
    from OCC.Core.IFSelect import IFSelect_RetDone
    from OCC.Core.STEPCAFControl import STEPCAFControl_Reader
    from OCC.Core.XCAFDoc import XCAFDoc_ColorTool, XCAFDoc_ColorType
    from OCC.Core.XCAFApp import XCAFApp_Application
    from OCC.Core.TDocStd import TDocStd_Document
    from OCC.Core.TopExp import TopExp_Explorer
    from OCC.Core.TopAbs import TopAbs_FACE
    from OCC.Core.Quantity import Quantity_Color
    
    print("Reading original STEP file...")
    
    # Read with color support
    app = XCAFApp_Application.GetApplication()
    doc = TDocStd_Document("MDTV-XCAF")
    
    reader = STEPCAFControl_Reader()
    reader.SetColorMode(True)
    reader.SetNameMode(True)
    
    status = reader.ReadFile('SOIC16P127_1270X940X610L89X51.STEP')
    if status == IFSelect_RetDone:
        reader.Transfer(doc)
        color_tool = XCAFDoc_ColorTool.Set(doc.Main())
        
        # Also get shape
        basic_reader = STEPControl_Reader()
        basic_reader.ReadFile('SOIC16P127_1270X940X610L89X51.STEP')
        basic_reader.TransferRoots()
        original_shape = basic_reader.OneShape()
        
        print("Original STEP file loaded successfully")
        
        # Count faces and colors in original
        explorer = TopExp_Explorer(original_shape, TopAbs_FACE)
        original_face_colors = []
        face_count = 0
        
        while explorer.More():
            face = explorer.Current()
            color = Quantity_Color()
            
            try:
                if color_tool.GetColor(face, XCAFDoc_ColorType.XCAFDoc_ColorSurf, color):
                    r = int(color.Red() * 255)
                    g = int(color.Green() * 255)
                    b = int(color.Blue() * 255)
                    original_face_colors.append((r, g, b))
                else:
                    original_face_colors.append((192, 192, 192))  # Default
            except:
                original_face_colors.append((192, 192, 192))  # Default
            
            face_count += 1
            explorer.Next()
        
        print(f"Original STEP: {face_count} faces")
        
        # Count colors in original
        original_color_counts = {}
        for color in original_face_colors:
            original_color_counts[color] = original_color_counts.get(color, 0) + 1
        
        print("Original STEP colors:")
        for color, count in original_color_counts.items():
            percentage = (count / len(original_face_colors)) * 100
            print(f"  RGB{color}: {count} faces ({percentage:.1f}%)")
        
    else:
        print("Failed to read original STEP file")
        exit(1)
        
except Exception as e:
    print(f"Error reading original STEP: {e}")
    exit(1)

# Step 2: Load with my step_loader
print("\nLoading with my step_loader...")
from step_loader import STEPLoader

loader = STEPLoader()
success, msg = loader.load_step_file('SOIC16P127_1270X940X610L89X51.STEP')

if success and loader.current_polydata:
    polydata = loader.current_polydata
    colors_array = polydata.GetCellData().GetScalars("Colors")
    
    if colors_array:
        num_cells = colors_array.GetNumberOfTuples()
        
        # Count my colors
        my_color_counts = {}
        for i in range(num_cells):
            r = int(colors_array.GetComponent(i, 0))
            g = int(colors_array.GetComponent(i, 1))
            b = int(colors_array.GetComponent(i, 2))
            color = (r, g, b)
            my_color_counts[color] = my_color_counts.get(color, 0) + 1
        
        print(f"My step_loader: {num_cells} cells")
        print("My step_loader colors:")
        for color, count in my_color_counts.items():
            percentage = (count / num_cells) * 100
            print(f"  RGB{color}: {count} cells ({percentage:.1f}%)")
        
        # Step 3: Compare
        print("\n=== COMPARISON ===")
        print(f"Original faces: {face_count}")
        print(f"My cells: {num_cells}")
        
        if original_color_counts == my_color_counts:
            print("SUCCESS: Colors match exactly!")
        else:
            print("FAILURE: Colors do not match")
            print("Differences:")
            all_colors = set(original_color_counts.keys()) | set(my_color_counts.keys())
            for color in all_colors:
                orig_count = original_color_counts.get(color, 0)
                my_count = my_color_counts.get(color, 0)
                if orig_count != my_count:
                    print(f"  RGB{color}: Original={orig_count}, Mine={my_count}")
        
    else:
        print("FAILURE: No colors in my polydata")
else:
    print("FAILURE: My step_loader failed")

print("\nVERIFICATION COMPLETE")
