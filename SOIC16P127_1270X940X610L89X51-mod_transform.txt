# STEP File Transform Data
# Generated for: SOIC16P127_1270X940X610L89X51-mod.STEP
# Date: 2025-08-27 08:39:16
# NOTE: FreeCAD not available - transformations not applied to STEP file
# This file contains the transformation data that should be applied

[ROTATION]
X = -8.183500  # degrees
Y = -135.772941  # degrees
Z = 15.000000  # degrees

[POSITION]
X = 0.300000  # mm
Y = 0.300000  # mm
Z = 3.350000  # mm

[VTK_TRANSFORM_MATRIX]
# 4x4 transformation matrix from VTK
Row0 = -0.692164 0.185465 -0.697504 -2.488647
Row1 = 0.352086 0.930393 -0.102001 0.043040
Row2 = 0.630035 -0.316183 -0.709285 -2.281947
Row3 = 0.000000 0.000000 0.000000 1.000000

# To apply these transformations:
# 1. Install FreeCAD Python module
# 2. Re-save the STEP file to apply transformations
# 3. Or use this data in your CAD software
