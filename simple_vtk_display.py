#!/usr/bin/env python3
"""
Simple VTK display that just shows a colored cube - no STEP loading
"""

import vtk

def main():
    print("Creating simple VTK display...")
    
    # Create cube
    cube = vtk.vtkCubeSource()
    cube.SetXLength(2.0)
    cube.SetYLength(1.0)
    cube.SetZLength(0.5)
    
    # Create mapper
    mapper = vtk.vtkPolyDataMapper()
    mapper.SetInputConnection(cube.GetOutputPort())
    
    # Create actor with light silver color (like STEP file should have)
    actor = vtk.vtkActor()
    actor.SetMapper(mapper)
    actor.GetProperty().SetColor(0.75, 0.75, 0.75)  # Light silver RGB(192,192,192)
    
    # Create renderer
    renderer = vtk.vtkRenderer()
    renderer.SetBackground(0.1, 0.1, 0.1)  # Dark background
    renderer.AddActor(actor)
    renderer.ResetCamera()
    
    # Create render window
    render_window = vtk.vtkRenderWindow()
    render_window.AddRenderer(renderer)
    render_window.SetSize(800, 600)
    render_window.SetWindowName("VTK Display - RUNNING")
    
    # Create interactor
    interactor = vtk.vtkRenderWindowInteractor()
    interactor.SetRenderWindow(render_window)
    
    print("VTK display created - window should be visible")
    print("Showing light silver cube (like STEP file colors)")
    
    # Start the display
    render_window.Render()
    interactor.Start()
    
    print("VTK display finished")

if __name__ == "__main__":
    main()
