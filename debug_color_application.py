#!/usr/bin/env python3

print("DEBUGGING COLOR APPLICATION")

from step_loader import STEP<PERSON>oader

loader = STEPLoader()
success, msg = loader.load_step_file('SOIC16P127_1270X940X610L89X51.STEP')

if success and loader.current_polydata:
    polydata = loader.current_polydata
    colors_array = polydata.GetCellData().GetScalars("Colors")
    
    if colors_array:
        num_cells = polydata.GetNumberOfCells()
        num_colors = colors_array.GetNumberOfTuples()
        
        print(f"Total cells: {num_cells}")
        print(f"Color tuples: {num_colors}")
        
        # Count each color
        color_counts = {}
        for i in range(num_colors):
            r = int(colors_array.GetComponent(i, 0))
            g = int(colors_array.GetComponent(i, 1))
            b = int(colors_array.GetComponent(i, 2))
            color = (r, g, b)
            color_counts[color] = color_counts.get(color, 0) + 1
        
        print("Color distribution:")
        for color, count in color_counts.items():
            percentage = (count / num_colors) * 100
            print(f"  RGB{color}: {count} cells ({percentage:.1f}%)")
        
        # Show first 20 cell colors to see the pattern
        print("\nFirst 20 cell colors:")
        for i in range(min(20, num_colors)):
            r = int(colors_array.GetComponent(i, 0))
            g = int(colors_array.GetComponent(i, 1))
            b = int(colors_array.GetComponent(i, 2))
            print(f"  Cell {i}: RGB({r}, {g}, {b})")
        
        # Show last 20 cell colors
        print("\nLast 20 cell colors:")
        start = max(0, num_colors - 20)
        for i in range(start, num_colors):
            r = int(colors_array.GetComponent(i, 0))
            g = int(colors_array.GetComponent(i, 1))
            b = int(colors_array.GetComponent(i, 2))
            print(f"  Cell {i}: RGB({r}, {g}, {b})")
        
        print("\nCOLOR APPLICATION DEBUG COMPLETE")
    else:
        print("NO COLORS FOUND IN POLYDATA")
else:
    print("STEP LOADING FAILED")
