#!/usr/bin/env python3
"""
Debug Which Actor is Actually Visible - Find the exact actor causing the display
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular import Step<PERSON>iewerTD<PERSON>

def debug_which_actor_visible():
    """Debug to find exactly which actor is visible and being displayed"""
    
    print("🔧 DEBUG WHICH ACTOR IS ACTUALLY VISIBLE")
    print("=" * 60)
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    # Process events to ensure GUI is ready
    app.processEvents()
    time.sleep(1)
    
    # Find a STEP file to test with
    test_files = [
        "AMPHENOL_U77-A1118-200T.STEP",
        "test.step",
        "sample.stp"
    ]
    
    test_file = None
    for filename in test_files:
        if os.path.exists(filename):
            test_file = filename
            break
    
    if not test_file:
        print("❌ No STEP file found for testing")
        return False
    
    print(f"📁 Using test file: {test_file}")
    
    # Step 1: Load STEP file
    print(f"\n📋 STEP 1: LOADING {test_file}...")
    success = viewer.load_step_file_direct(test_file)
    app.processEvents()
    time.sleep(2)
    
    if not success:
        print("❌ Failed to load STEP file")
        return False
    
    renderer = viewer.vtk_renderer_left
    
    # Get all actors in the renderer
    all_actors = []
    if hasattr(renderer, 'renderer') and renderer.renderer:
        actor_collection = renderer.renderer.GetActors()
        actor_collection.InitTraversal()
        
        actor = actor_collection.GetNextActor()
        while actor:
            all_actors.append(actor)
            actor = actor_collection.GetNextActor()
    
    print(f"\n🔍 ALL ACTORS IN RENDERER:")
    print(f"Total actors: {len(all_actors)}")
    
    for i, actor in enumerate(all_actors):
        pos = actor.GetPosition()
        orient = actor.GetOrientation()
        visible = actor.GetVisibility()
        bounds = actor.GetBounds()
        memory_addr = hex(id(actor))
        
        print(f"  Actor {i}: {memory_addr}")
        print(f"    Position: {pos}")
        print(f"    Orientation: {orient}")
        print(f"    Visible: {visible}")
        print(f"    Bounds: {bounds}")
        
        # Check if this matches our known actors
        is_multi = False
        is_single = False
        
        if hasattr(renderer, 'step_actors') and renderer.step_actors:
            for j, multi_actor in enumerate(renderer.step_actors):
                if multi_actor == actor:
                    is_multi = True
                    print(f"    *** THIS IS MULTI-ACTOR {j} ***")
                    
        if hasattr(renderer, 'step_actor') and renderer.step_actor == actor:
            is_single = True
            print(f"    *** THIS IS THE SINGLE-ACTOR ***")
            
        if not is_multi and not is_single:
            print(f"    *** UNKNOWN ACTOR TYPE ***")
    
    print(f"\n👁️ INITIAL MODEL LOADED - Note the appearance (3 seconds)")
    time.sleep(3)
    
    # Step 2: Test visibility by hiding each actor one by one
    print(f"\n📋 STEP 2: TESTING VISIBILITY BY HIDING ACTORS...")
    
    for i, actor in enumerate(all_actors):
        if not actor.GetVisibility():
            print(f"🔧 Skipping Actor {i} - already hidden")
            continue
            
        print(f"\n🔧 HIDING Actor {i} ({hex(id(actor))})...")
        actor.SetVisibility(False)
        renderer.render_window.Render()
        app.processEvents()
        time.sleep(1)
        
        print(f"👁️ Actor {i} hidden - Can you still see the model? (3 seconds)")
        time.sleep(3)
        
        # Show it again
        print(f"🔧 SHOWING Actor {i} again...")
        actor.SetVisibility(True)
        renderer.render_window.Render()
        app.processEvents()
        time.sleep(1)
    
    # Step 3: Test transformations on each visible actor
    print(f"\n📋 STEP 3: TESTING TRANSFORMATIONS ON EACH VISIBLE ACTOR...")
    
    for i, actor in enumerate(all_actors):
        if not actor.GetVisibility():
            print(f"🔧 Skipping Actor {i} - not visible")
            continue
            
        print(f"\n🔧 TRANSFORMING Actor {i} ({hex(id(actor))})...")
        
        # Get original state
        orig_pos = actor.GetPosition()
        orig_orient = actor.GetOrientation()
        print(f"  Original: Pos={orig_pos}, Orient={orig_orient}")
        
        # Apply HUGE transformation
        print(f"  Applying HUGE transformation: Move +200mm X, Rotate +180° Z")
        actor.AddPosition(200, 0, 0)
        actor.RotateWXYZ(180, 0, 0, 1)
        actor.Modified()
        
        # Force render
        renderer.render_window.Render()
        app.processEvents()
        time.sleep(1)
        
        # Check new state
        new_pos = actor.GetPosition()
        new_orient = actor.GetOrientation()
        print(f"  After transform: Pos={new_pos}, Orient={new_orient}")
        
        print(f"👁️ Actor {i} transformed - Can you see the model move? (5 seconds)")
        time.sleep(5)
        
        # Reset this actor
        print(f"  Resetting Actor {i}...")
        actor.SetPosition(orig_pos)
        actor.SetOrientation(orig_orient)
        actor.Modified()
        
        # Force render
        renderer.render_window.Render()
        app.processEvents()
        time.sleep(1)
        
        print(f"👁️ Actor {i} reset - Did the model move back? (3 seconds)")
        time.sleep(3)
    
    # Step 4: Final analysis
    print(f"\n📋 STEP 4: FINAL ANALYSIS")
    print("=" * 60)
    
    print(f"🔍 SUMMARY:")
    print(f"Total actors in renderer: {len(all_actors)}")
    
    visible_actors = [actor for actor in all_actors if actor.GetVisibility()]
    print(f"Visible actors: {len(visible_actors)}")
    
    if hasattr(renderer, 'step_actors') and renderer.step_actors:
        print(f"Multi-actors: {len(renderer.step_actors)}")
        for i, actor in enumerate(renderer.step_actors):
            print(f"  Multi-actor {i}: Visible={actor.GetVisibility()}")
    
    if hasattr(renderer, 'step_actor') and renderer.step_actor:
        print(f"Single-actor: Visible={renderer.step_actor.GetVisibility()}")
    
    print(f"\n🎯 KEY FINDINGS:")
    print(f"1. Which actor disappeared when hidden? = That's the displayed actor")
    print(f"2. Which actor caused visible movement when transformed? = That's the active actor")
    print(f"3. If no actor caused movement, there's a deeper rendering issue")
    
    # Keep window open
    print(f"\n👁️ Window will stay open for final inspection...")
    QTimer.singleShot(10000, app.quit)
    app.exec_()
    
    return True

if __name__ == "__main__":
    debug_which_actor_visible()
