#!/usr/bin/env python3
"""
Extract rotation angle and axis values from STEP files
Shows how to read the coordinate system from STEP file format
"""

import math
import re

def extract_step_coordinate_system(step_file_path):
    """
    Extract the main coordinate system from a STEP file
    Returns: (origin, z_direction, x_direction)
    """
    try:
        with open(step_file_path, 'r') as f:
            content = f.read()
        
        # Find the main AXIS2_PLACEMENT_3D (usually #11)
        axis_match = re.search(r'#11 = AXIS2_PLACEMENT_3D\(\'\',(#\d+),(#\d+),(#\d+)\);', content)
        if not axis_match:
            print(f"❌ Could not find main AXIS2_PLACEMENT_3D in {step_file_path}")
            return None, None, None
            
        origin_ref = axis_match.group(1)
        z_dir_ref = axis_match.group(2)
        x_dir_ref = axis_match.group(3)
        
        print(f"🔍 Found AXIS2_PLACEMENT_3D: origin={origin_ref}, z_dir={z_dir_ref}, x_dir={x_dir_ref}")
        
        # Extract origin point
        origin_pattern = f'{origin_ref} = CARTESIAN_POINT\\(\'\'\\,\\(([^)]+)\\)\\);'
        origin_match = re.search(origin_pattern, content)
        if origin_match:
            origin_coords = [float(x.strip()) for x in origin_match.group(1).split(',')]
            print(f"📍 Origin: ({origin_coords[0]:.6f}, {origin_coords[1]:.6f}, {origin_coords[2]:.6f})")
        else:
            origin_coords = [0, 0, 0]
            
        # Extract Z direction
        z_dir_pattern = f'{z_dir_ref} = DIRECTION\\(\'\'\\,\\(([^)]+)\\)\\);'
        z_dir_match = re.search(z_dir_pattern, content)
        if z_dir_match:
            z_direction = [float(x.strip()) for x in z_dir_match.group(1).split(',')]
            print(f"🔺 Z Direction: ({z_direction[0]:.6f}, {z_direction[1]:.6f}, {z_direction[2]:.6f})")
        else:
            z_direction = [0, 0, 1]
            
        # Extract X direction  
        x_dir_pattern = f'{x_dir_ref} = DIRECTION\\(\'\'\\,\\(([^)]+)\\)\\);'
        x_dir_match = re.search(x_dir_pattern, content)
        if x_dir_match:
            x_direction = [float(x.strip()) for x in x_dir_match.group(1).split(',')]
            print(f"➡️  X Direction: ({x_direction[0]:.6f}, {x_direction[1]:.6f}, {x_direction[2]:.6f})")
        else:
            x_direction = [1, 0, 0]
            
        return origin_coords, z_direction, x_direction
        
    except Exception as e:
        print(f"❌ Error reading {step_file_path}: {e}")
        return None, None, None

def calculate_rotation_from_directions(z_dir, x_dir):
    """
    Calculate rotation angles from direction vectors
    This is a simplified calculation - real STEP rotation can be more complex
    """
    # Standard coordinate system
    std_z = [0, 0, 1]
    std_x = [1, 0, 0]
    
    # Calculate angles from standard directions
    # Z-axis rotation (around Z)
    z_angle = math.degrees(math.atan2(z_dir[1], z_dir[0])) if z_dir[0] != 0 or z_dir[1] != 0 else 0
    
    # X-axis rotation (around X) 
    x_angle = math.degrees(math.atan2(x_dir[2], x_dir[1])) if x_dir[1] != 0 or x_dir[2] != 0 else 0
    
    # Y-axis rotation (around Y)
    y_angle = math.degrees(math.atan2(z_dir[0], z_dir[2])) if z_dir[0] != 0 or z_dir[2] != 0 else 0
    
    # Total rotation magnitude
    total_angle = math.sqrt(x_angle**2 + y_angle**2 + z_angle**2)
    
    return total_angle, x_angle, y_angle, z_angle

def analyze_step_file(file_path):
    """Analyze a STEP file and show its coordinate system and rotation"""
    print(f"\n🔍 Analyzing STEP file: {file_path}")
    print("=" * 60)
    
    origin, z_dir, x_dir = extract_step_coordinate_system(file_path)
    
    if origin is None:
        print("❌ Could not extract coordinate system")
        return
        
    # Calculate rotation angles
    total_angle, x_angle, y_angle, z_angle = calculate_rotation_from_directions(z_dir, x_dir)
    
    print(f"\n📊 CALCULATED ROTATION VALUES:")
    print(f"   ANGLE: {total_angle:.1f}°")
    print(f"   AXIS: (x - {x_angle:.2f}° y - {y_angle:.2f}° z - {z_angle:.2f}°)")
    
    # Check if it's close to standard orientation
    is_standard = (abs(z_dir[0]) < 0.001 and abs(z_dir[1]) < 0.001 and abs(z_dir[2] - 1) < 0.001 and
                   abs(x_dir[0] - 1) < 0.001 and abs(x_dir[1]) < 0.001 and abs(x_dir[2]) < 0.001)
    
    if is_standard:
        print("✅ This is a standard orientation (no rotation)")
    else:
        print("🔄 This has a custom orientation (rotated)")
        
    return total_angle, x_angle, y_angle, z_angle

if __name__ == "__main__":
    # Analyze both files
    print("🚀 STEP File Rotation Analyzer")
    
    # Analyze original test.step
    if True:  # Change to False to skip
        try:
            analyze_step_file("test.step")
        except FileNotFoundError:
            print("❌ test.step not found")
    
    # Analyze saved test1.step  
    if True:  # Change to False to skip
        try:
            analyze_step_file("test1.step")
        except FileNotFoundError:
            print("❌ test1.step not found")
            
    print(f"\n📝 NOTE: These calculations are simplified.")
    print(f"   Real STEP rotation matrices can be more complex.")
    print(f"   The viewer uses VTK's rotation system which may differ.")
