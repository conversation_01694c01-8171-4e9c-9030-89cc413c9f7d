#!/usr/bin/env python3
import ast

try:
    with open('step_loader.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    ast.parse(content)
    print("SYNTAX OK: step_loader.py")
    
    # Also test the specific import
    try:
        from OCC.Core.BRepMesh import BRepMesh_IncrementalMesh
        print("IMPORT OK: BRepMesh_IncrementalMesh")
    except Exception as e:
        print(f"IMPORT FAIL: BRepMesh_IncrementalMesh - {e}")
        
except SyntaxError as e:
    print(f"SYNTAX ERROR: step_loader.py - {e}")
except Exception as e:
    print(f"ERROR: {e}")
