#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
STEP Viewer TDK Modular - Dual Viewer Version
Enhanced with top/bottom viewer selection and compact transform display
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QSplitter, QPushButton, QLabel,
                            QFileDialog, QComboBox, QSpinBox, QGroupBox)
from PyQt5.QtCore import Qt, QTimer

# Import custom modules
from step_loader import STEPLoader
from vtk_renderer import VTKRenderer
from gui_components import create_tool_dock
from gui_components import create_tool_dock

class StepViewerTDK(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("VTK TDK STEP Viewer (Dual View) - Enhanced")
        self.setGeometry(200, 200, 1200, 800)  # More conservative size and position

        # Initialize dual components
        self.step_loader_left = STEPLoader()
        self.step_loader_right = STEPLoader()
        self.vtk_renderer_left = VTKRenderer(self)
        self.vtk_renderer_right = VTKRenderer(self)

        # Data tracking for both viewers - bounding box on by default
        self.bbox_visible_left = True
        self.bbox_visible_right = True

        # Transform data for left (top) viewer
        self.orig_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.orig_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.model_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}  # Track button rotations

        # Transform data for right (bottom) viewer
        self.orig_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.orig_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.model_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}  # Track button rotations

        # Active viewer tracking
        self.active_viewer = "top"

        # Setup UI
        self.init_ui()

        # Mouse tracking timer and previous camera positions
        self.mouse_timer = QTimer()
        self.mouse_timer.timeout.connect(self.update_camera_display)
        self.mouse_timer.start(200)

        # Initialize cursor position tracking
        self.cursor_pos = {'x': 0.0, 'y': 0.0, 'z': 0.0}

        # Create custom mouse tracking
        self.mouse_tracker_timer = QTimer()
        self.mouse_tracker_timer.timeout.connect(self.update_cursor_position)
        self.mouse_tracker_timer.start(100)  # Update every 100ms

        # Track previous camera positions to detect mouse rotation
        self.prev_camera_pos_left = None
        self.prev_camera_pos_right = None

        self.statusBar().showMessage("Ready - Select TOP or BOTTOM viewer, then load STEP files")

    def init_ui(self):
        # Central widget with splitter
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout
        main_layout = QHBoxLayout(central_widget)

        # Create splitter for dual view
        splitter = QSplitter(Qt.Vertical)

        # Top viewer container
        top_container = QWidget()
        top_layout = QVBoxLayout(top_container)

        # Top file label
        self.top_file_label = QLabel("TOP VIEWER - No file loaded")
        self.top_file_label.setStyleSheet("color: blue; font-size: 12px; padding: 5px; background-color: lightgray;")
        top_layout.addWidget(self.top_file_label)

        # Top VTK widget
        self.vtk_widget_left = self.vtk_renderer_left.vtk_widget
        if self.vtk_widget_left:
            print(f"Adding TOP VTK widget: {type(self.vtk_widget_left)}")
            # Set minimum size and size policy for proper resizing
            self.vtk_widget_left.setMinimumSize(400, 300)
            from PyQt5.QtWidgets import QSizePolicy
            self.vtk_widget_left.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
            # Enable mouse tracking for cursor position
            self.vtk_widget_left.setMouseTracking(True)
            top_layout.addWidget(self.vtk_widget_left)
        else:
            print("ERROR: TOP VTK widget is None")

        # Bottom viewer container
        bottom_container = QWidget()
        bottom_layout = QVBoxLayout(bottom_container)

        # Bottom file label
        self.bottom_file_label = QLabel("BOTTOM VIEWER - No file loaded")
        self.bottom_file_label.setStyleSheet("color: blue; font-size: 12px; padding: 5px; background-color: lightgray;")
        bottom_layout.addWidget(self.bottom_file_label)

        # Bottom VTK widget
        self.vtk_widget_right = self.vtk_renderer_right.vtk_widget
        if self.vtk_widget_right:
            print(f"Adding BOTTOM VTK widget: {type(self.vtk_widget_right)}")
            # Set minimum size and size policy for proper resizing
            self.vtk_widget_right.setMinimumSize(400, 300)
            self.vtk_widget_right.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
            # Enable mouse tracking for cursor position
            self.vtk_widget_right.setMouseTracking(True)
            bottom_layout.addWidget(self.vtk_widget_right)
        else:
            print("ERROR: BOTTOM VTK widget is None")

        # Add containers to splitter
        splitter.addWidget(top_container)
        splitter.addWidget(bottom_container)
        splitter.setSizes([400, 400])

        # Set splitter to expand properly
        from PyQt5.QtWidgets import QSizePolicy
        splitter.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        main_layout.addWidget(splitter)

        # Create tool dock
        dock = create_tool_dock(self)
        self.addDockWidget(Qt.LeftDockWidgetArea, dock)

        # Set initial active viewer
        self.update_viewer_highlights()

    def set_active_viewer(self, viewer):
        """Set the active viewer (top or bottom)"""
        self.active_viewer = viewer
        self.update_viewer_highlights()
        self.update_transform_display()
        self.statusBar().showMessage(f"Active viewer: {viewer.upper()}")

    def update_viewer_highlights(self):
        """Update button highlights to show active viewer"""
        if hasattr(self, 'top_btn') and hasattr(self, 'bottom_btn'):
            if self.active_viewer == "top":
                self.top_btn.setStyleSheet("QPushButton { background-color: lightgreen; font-weight: bold; padding: 8px; }")
                self.bottom_btn.setStyleSheet("QPushButton { padding: 8px; }")
            else:
                self.top_btn.setStyleSheet("QPushButton { padding: 8px; }")
                self.bottom_btn.setStyleSheet("QPushButton { background-color: lightgreen; font-weight: bold; padding: 8px; }")

    def load_step_file(self):
        """Load STEP file into active viewer"""
        filename, _ = QFileDialog.getOpenFileName(
            self, "Open STEP File", "", "STEP Files (*.step *.stp);;All Files (*)"
        )

        if filename:
            print(f"Loading STEP file: {filename}")
            if self.active_viewer == "top":
                success, message = self.step_loader_left.load_step_file(filename)
                print(f"TOP load result: success={success}, message={message}")
                if success:
                    print(f"Polydata available: {self.step_loader_left.current_polydata is not None}")
                    if self.step_loader_left.current_polydata:
                        print(f"Polydata points: {self.step_loader_left.current_polydata.GetNumberOfPoints()}")
                        print(f"Polydata cells: {self.step_loader_left.current_polydata.GetNumberOfCells()}")

                    # Reset any previous transformations before loading new model
                    self.vtk_renderer_left.clear_view()

                    # Display the polydata
                    display_success = self.vtk_renderer_left.display_polydata(self.step_loader_left.current_polydata)
                    print(f"Display success: {display_success}")

                    self.vtk_renderer_left.fit_view()  # Auto-fit the view
                    # Show bounding box by default
                    self.vtk_renderer_left.toggle_bounding_box(True)
                    self.top_file_label.setText(f"TOP: {os.path.basename(filename)}")
                    self.extract_step_transformation_data("top")
                else:
                    print(f"Load failed: {message}")
                    self.top_file_label.setText("TOP: Load failed")
            else:
                success, message = self.step_loader_right.load_step_file(filename)
                if success:
                    # Reset any previous transformations before loading new model
                    self.vtk_renderer_right.clear_view()
                    self.vtk_renderer_right.display_polydata(self.step_loader_right.current_polydata)
                    self.vtk_renderer_right.fit_view()  # Auto-fit the view
                    # Show bounding box by default
                    self.vtk_renderer_right.toggle_bounding_box(True)
                    self.bottom_file_label.setText(f"BOTTOM: {os.path.basename(filename)}")
                    self.extract_step_transformation_data("bottom")
                else:
                    self.bottom_file_label.setText("BOTTOM: Load failed")

            self.statusBar().showMessage(f"{self.active_viewer.title()}: {message}")
            self.update_transform_display()

    def extract_step_transformation_data(self, viewer):
        """Extract transformation data from loaded STEP file"""
        if viewer == "top":
            loader = self.step_loader_left
            polydata = loader.current_polydata
        else:
            loader = self.step_loader_right
            polydata = loader.current_polydata

        if polydata:
            # Get the bounds of the geometry
            bounds = polydata.GetBounds()  # [xmin, xmax, ymin, ymax, zmin, zmax]

            # Calculate center position
            center_x = (bounds[0] + bounds[1]) / 2.0
            center_y = (bounds[2] + bounds[3]) / 2.0
            center_z = (bounds[4] + bounds[5]) / 2.0

            # Get original position and orientation from the geometry
            orig_pos = {'x': center_x, 'y': center_y, 'z': center_z}
            orig_rot = {'x': 0.0, 'y': 0.0, 'z': 0.0}  # Default rotation

            if viewer == "top":
                self.orig_pos_left = orig_pos
                self.orig_rot_left = orig_rot
                # Copy original values to current values as starting point
                self.current_pos_left = orig_pos.copy()
                self.current_rot_left = orig_rot.copy()
                # Initialize model rotation tracking
                self.model_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                print(f"TOP: Original position: {orig_pos}, Current copied from original")
            else:
                self.orig_pos_right = orig_pos
                self.orig_rot_right = orig_rot
                # Copy original values to current values as starting point
                self.current_pos_right = orig_pos.copy()
                self.current_rot_right = orig_rot.copy()
                # Initialize model rotation tracking
                self.model_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                print(f"BOTTOM: Original position: {orig_pos}, Current copied from original")
        else:
            print(f"No polydata available for {viewer} viewer")

    def clear_view(self):
        """Clear the active viewer and reset numbers to zero"""
        if self.active_viewer == "top":
            self.vtk_renderer_left.clear_view()
            self.top_file_label.setText("TOP VIEWER - No file loaded")
            # Reset all transform values to zero
            self.orig_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.orig_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.current_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.model_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        else:
            self.vtk_renderer_right.clear_view()
            self.bottom_file_label.setText("BOTTOM VIEWER - No file loaded")
            # Reset all transform values to zero
            self.orig_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.orig_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.current_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.current_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.model_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}

        # Update the display to show zeros
        self.update_transform_display()
        self.statusBar().showMessage(f"{self.active_viewer.title()} view cleared")

    def fit_view(self):
        """Fit view in active viewer"""
        if self.active_viewer == "top":
            if self.vtk_renderer_left:
                self.vtk_renderer_left.fit_view()
                print("Fitted TOP view")
        else:
            if self.vtk_renderer_right:
                self.vtk_renderer_right.fit_view()
                print("Fitted BOTTOM view")

    def save_transformed_step(self):
        """Save transformed STEP file from active viewer"""
        filename, _ = QFileDialog.getSaveFileName(
            self, "Save STEP File", "", "STEP Files (*.step);;STL Files (*.stl);;All Files (*)"
        )

        if filename:
            print(f"Saving file: {filename}")
            import vtk  # Add missing import
            if self.active_viewer == "top":
                # Get the current polydata from the VTK renderer (includes transformations)
                if self.vtk_renderer_left.step_actor:
                    # Get transformed polydata
                    transform_filter = vtk.vtkTransformPolyDataFilter()
                    transform_filter.SetInputData(self.step_loader_left.current_polydata)
                    transform_filter.SetTransform(self.vtk_renderer_left.step_actor.GetUserTransform() or vtk.vtkTransform())
                    transform_filter.Update()
                    self.step_loader_left.current_polydata = transform_filter.GetOutput()
                    print("Applied TOP viewer transformations to save data")
                success = self.step_loader_left.save_step_file(filename)
            else:
                # Get the current polydata from the VTK renderer (includes transformations)
                if self.vtk_renderer_right.step_actor:
                    # Get transformed polydata
                    transform_filter = vtk.vtkTransformPolyDataFilter()
                    transform_filter.SetInputData(self.step_loader_right.current_polydata)
                    transform_filter.SetTransform(self.vtk_renderer_right.step_actor.GetUserTransform() or vtk.vtkTransform())
                    transform_filter.Update()
                    self.step_loader_right.current_polydata = transform_filter.GetOutput()
                    print("Applied BOTTOM viewer transformations to save data")
                success = self.step_loader_right.save_step_file(filename)

            if success:
                self.statusBar().showMessage(f"Saved: {filename}")
            else:
                self.statusBar().showMessage("Save failed")

    def reset_to_original(self):
        """Reset active viewer to original transform"""
        if self.active_viewer == "top":
            # Reset all rotation tracking to 0,0,0
            self.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.current_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.model_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            # Reset the actual VTK actor rotation and camera
            if self.vtk_renderer_left.step_actor:
                # Reset transform completely
                import vtk
                transform = vtk.vtkTransform()
                transform.Identity()
                self.vtk_renderer_left.step_actor.SetUserTransform(transform)
                self.vtk_renderer_left.step_actor.SetOrientation(0, 0, 0)
                self.vtk_renderer_left.step_actor.SetPosition(0, 0, 0)
                # Update bounding box after reset
                if self.bbox_visible_left:
                    self.vtk_renderer_left.update_bounding_box()
                self.vtk_renderer_left.fit_view()  # Also reset camera view
                self.vtk_renderer_left.render_window.Render()
                print("Reset TOP viewer to original")
        else:
            # Reset all rotation tracking to 0,0,0
            self.current_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.current_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.model_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            # Reset the actual VTK actor rotation and camera
            if self.vtk_renderer_right.step_actor:
                # Reset transform completely
                import vtk
                transform = vtk.vtkTransform()
                transform.Identity()
                self.vtk_renderer_right.step_actor.SetUserTransform(transform)
                self.vtk_renderer_right.step_actor.SetOrientation(0, 0, 0)
                self.vtk_renderer_right.step_actor.SetPosition(0, 0, 0)
                # Update bounding box after reset
                if self.bbox_visible_right:
                    self.vtk_renderer_right.update_bounding_box()
                self.vtk_renderer_right.fit_view()  # Also reset camera view
                self.vtk_renderer_right.render_window.Render()
                print("Reset BOTTOM viewer to original")

        self.update_transform_display()
        self.statusBar().showMessage(f"{self.active_viewer.title()} reset to original")

    def align_bottom_center(self):
        """Align model to bottom-center"""
        self.statusBar().showMessage(f"{self.active_viewer.title()}: Align bottom-center not implemented")

    def rotate_shape(self, axis, degrees):
        """Rotate shape in active viewer"""
        print(f"DEBUG: rotate_shape called with axis={axis}, degrees={degrees}")
        try:
            if self.active_viewer == "top":
                # Initialize if not exists
                if not hasattr(self, 'model_rot_left'):
                    self.model_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                if not hasattr(self, 'current_rot_left'):
                    self.current_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                if not hasattr(self, 'orig_rot_left'):
                    self.orig_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}

                self.model_rot_left[axis] += degrees
                # Update current rotation values immediately
                self.current_rot_left[axis] = self.orig_rot_left[axis] + self.model_rot_left[axis]
                print(f"DEBUG: Rotated {axis} by {degrees}°, new current_{axis} = {self.current_rot_left[axis]}°")
            # Apply actual rotation to the VTK actor
            if self.vtk_renderer_left.step_actor:
                self.vtk_renderer_left.step_actor.RotateWXYZ(degrees,
                    1 if axis == 'x' else 0,
                    1 if axis == 'y' else 0,
                    1 if axis == 'z' else 0)
                # Update bounding box to follow rotation
                if self.bbox_visible_left:
                    self.vtk_renderer_left.update_bounding_box()
                # Don't call fit_view during rotation to prevent jumping
                self.vtk_renderer_left.render_window.Render()
                # Update the display immediately
                print(f"DEBUG: Calling update_transform_display() for TOP")
                self.update_transform_display()
                print(f"DEBUG: After update - current_rot_left = {self.current_rot_left}")

            else:
                # Initialize if not exists
                if not hasattr(self, 'model_rot_right'):
                    self.model_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                if not hasattr(self, 'current_rot_right'):
                    self.current_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                if not hasattr(self, 'orig_rot_right'):
                    self.orig_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}

                self.model_rot_right[axis] += degrees
                # Update current rotation values immediately
                self.current_rot_right[axis] = self.orig_rot_right[axis] + self.model_rot_right[axis]
                # Apply actual rotation to the VTK actor
                if self.vtk_renderer_right.step_actor:
                    self.vtk_renderer_right.step_actor.RotateWXYZ(degrees,
                        1 if axis == 'x' else 0,
                        1 if axis == 'y' else 0,
                        1 if axis == 'z' else 0)
                    # Update bounding box to follow rotation
                    if self.bbox_visible_right:
                        self.vtk_renderer_right.update_bounding_box()
                    # Don't call fit_view during rotation to prevent jumping
                    self.vtk_renderer_right.render_window.Render()
                # Update the display immediately
                self.update_transform_display()

        except Exception as e:
            print(f"Error in rotate_shape: {e}")
            # Don't crash, just show error
            self.statusBar().showMessage(f"Rotation error: {e}")

        # Update transform display if it exists
        try:
            self.update_transform_display()
        except:
            pass

    def move_shape(self, axis, distance):
        """Move shape in active viewer"""
        try:
            if self.active_viewer == "top":
                # Initialize if not exists
                if not hasattr(self, 'model_pos_left'):
                    self.model_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                if not hasattr(self, 'current_pos_left'):
                    self.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                if not hasattr(self, 'orig_pos_left'):
                    self.orig_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}

                self.model_pos_left[axis] += distance
                # Update current position values immediately
                self.current_pos_left[axis] = self.orig_pos_left[axis] + self.model_pos_left[axis]

                # Apply actual translation to the VTK actor
                if hasattr(self, 'top_renderer') and self.top_renderer.step_actor:
                    current_pos = self.top_renderer.step_actor.GetPosition()
                    new_pos = list(current_pos)
                    if axis == 'x':
                        new_pos[0] += distance
                    elif axis == 'y':
                        new_pos[1] += distance
                    elif axis == 'z':
                        new_pos[2] += distance
                    self.top_renderer.step_actor.SetPosition(new_pos)
                    self.top_renderer.render_window.Render()

                # Update the display immediately
                self.update_transform_display()

            else:
                # Initialize if not exists
                if not hasattr(self, 'model_pos_right'):
                    self.model_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                if not hasattr(self, 'current_pos_right'):
                    self.current_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                if not hasattr(self, 'orig_pos_right'):
                    self.orig_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}

                self.model_pos_right[axis] += distance
                # Update current position values immediately
                self.current_pos_right[axis] = self.orig_pos_right[axis] + self.model_pos_right[axis]

                # Apply actual translation to the VTK actor
                if hasattr(self, 'bottom_renderer') and self.bottom_renderer.step_actor:
                    current_pos = self.bottom_renderer.step_actor.GetPosition()
                    new_pos = list(current_pos)
                    if axis == 'x':
                        new_pos[0] += distance
                    elif axis == 'y':
                        new_pos[1] += distance
                    elif axis == 'z':
                        new_pos[2] += distance
                    self.bottom_renderer.step_actor.SetPosition(new_pos)
                    self.bottom_renderer.render_window.Render()

                # Update the display immediately
                self.update_transform_display()

        except Exception as e:
            print(f"Error in move_shape: {e}")
            self.statusBar().showMessage(f"Move error: {e}")

        # Update transform display if it exists
        try:
            self.update_transform_display()
        except:
            pass



    def update_cursor_position(self):
        """Update cursor position based on mouse location over VTK widgets"""
        try:
            # Get the currently active VTK widget
            if self.active_viewer == "top" and hasattr(self, 'vtk_widget_left'):
                widget = self.vtk_widget_left
            elif self.active_viewer == "bottom" and hasattr(self, 'vtk_widget_right'):
                widget = self.vtk_widget_right
            else:
                return

            # Check if mouse is over the widget
            if widget.underMouse():
                # Get mouse position relative to widget
                mouse_pos = widget.mapFromGlobal(widget.cursor().pos())
                x = mouse_pos.x()
                y = mouse_pos.y()

                # Get widget size
                widget_size = widget.size()
                if widget_size.width() > 0 and widget_size.height() > 0:
                    # Normalize mouse position (0-1)
                    norm_x = x / widget_size.width()
                    norm_y = y / widget_size.height()

                    # Convert to approximate 3D coordinates
                    # Scale based on model bounds (approximately 12.7mm x 9.4mm)
                    self.cursor_pos = {
                        'x': (norm_x - 0.5) * 15.0,  # Scale to model width
                        'y': (0.5 - norm_y) * 12.0,  # Flip Y and scale to model height
                        'z': 3.0  # Approximate Z based on model center
                    }

                    # Update cursor position display
                    if hasattr(self, 'lbl_cursor_x'):
                        self.lbl_cursor_x.setText(f"X: {self.cursor_pos['x']:.3f}mm")
                        self.lbl_cursor_y.setText(f"Y: {self.cursor_pos['y']:.3f}mm")
                        self.lbl_cursor_z.setText(f"Z: {self.cursor_pos['z']:.3f}mm")

        except Exception as e:
            # Silently handle errors
            pass

    def update_combined_rotation(self, viewer):
        """Update current rotation to show only model rotation (not camera)"""
        if viewer == "top":
            # Only show the actual model rotation from buttons, not camera orientation
            self.current_rot_left = self.model_rot_left.copy()
        else:
            # Only show the actual model rotation from buttons, not camera orientation
            self.current_rot_right = self.model_rot_right.copy()

    def change_model_color(self, color_name):
        """Change model color in active viewer"""
        self.statusBar().showMessage(f"{self.active_viewer.title()}: Color changed to {color_name}")

    def toggle_bbox_overlay(self):
        """Toggle bounding box in active viewer"""
        if self.active_viewer == "top":
            self.bbox_visible_left = not self.bbox_visible_left
            if self.vtk_renderer_left:
                self.vtk_renderer_left.toggle_bounding_box(self.bbox_visible_left)
            status = "shown" if self.bbox_visible_left else "hidden"
            print(f"TOP bounding box {status}")
        else:
            self.bbox_visible_right = not self.bbox_visible_right
            if self.vtk_renderer_right:
                self.vtk_renderer_right.toggle_bounding_box(self.bbox_visible_right)
            status = "shown" if self.bbox_visible_right else "hidden"
            print(f"BOTTOM bounding box {status}")

        self.statusBar().showMessage(f"{self.active_viewer.title()}: Bounding box {status}")

    def force_view_update(self):
        """Force update of transform display - refreshes the position/rotation numbers"""
        self.update_transform_display()
        self.statusBar().showMessage("Transform display refreshed - numbers updated from current view")

    def update_transform_display(self):
        """Update the transform display labels for both viewers"""
        print(f"DEBUG: update_transform_display called")
        # Update TOP viewer labels with 3 decimal precision
        if hasattr(self, 'lbl_orig_pos_x'):
            self.lbl_orig_pos_x.setText(f"X: {self.orig_pos_left['x']:.3f}mm")
            self.lbl_orig_pos_y.setText(f"Y: {self.orig_pos_left['y']:.3f}mm")
            self.lbl_orig_pos_z.setText(f"Z: {self.orig_pos_left['z']:.3f}mm")
            self.lbl_orig_rot_x.setText(f"X: {self.orig_rot_left['x']:.3f}°")
            self.lbl_orig_rot_y.setText(f"Y: {self.orig_rot_left['y']:.3f}°")
            self.lbl_orig_rot_z.setText(f"Z: {self.orig_rot_left['z']:.3f}°")

        if hasattr(self, 'lbl_curr_pos_x'):
            self.lbl_curr_pos_x.setText(f"X: {self.current_pos_left['x']:.3f}mm")
            self.lbl_curr_pos_y.setText(f"Y: {self.current_pos_left['y']:.3f}mm")
            self.lbl_curr_pos_z.setText(f"Z: {self.current_pos_left['z']:.3f}mm")
            self.lbl_curr_rot_x.setText(f"X: {self.current_rot_left['x']:.3f}°")
            self.lbl_curr_rot_y.setText(f"Y: {self.current_rot_left['y']:.3f}°")
            self.lbl_curr_rot_z.setText(f"Z: {self.current_rot_left['z']:.3f}°")
            print(f"DEBUG: Updated GUI labels - Current ROT: X={self.current_rot_left['x']:.3f}°, Y={self.current_rot_left['y']:.3f}°, Z={self.current_rot_left['z']:.3f}°")

        # Update BOTTOM viewer labels with 3 decimal precision
        if hasattr(self, 'lbl_orig_pos_x_bottom'):
            self.lbl_orig_pos_x_bottom.setText(f"X: {self.orig_pos_right['x']:.3f}")
            self.lbl_orig_pos_y_bottom.setText(f"Y: {self.orig_pos_right['y']:.3f}")
            self.lbl_orig_pos_z_bottom.setText(f"Z: {self.orig_pos_right['z']:.3f}")
            self.lbl_orig_rot_x_bottom.setText(f"X: {self.orig_rot_right['x']:.3f}°")
            self.lbl_orig_rot_y_bottom.setText(f"Y: {self.orig_rot_right['y']:.3f}°")
            self.lbl_orig_rot_z_bottom.setText(f"Z: {self.orig_rot_right['z']:.3f}°")

        if hasattr(self, 'lbl_curr_pos_x_bottom'):
            self.lbl_curr_pos_x_bottom.setText(f"X: {self.current_pos_right['x']:.3f}")
            self.lbl_curr_pos_y_bottom.setText(f"Y: {self.current_pos_right['y']:.3f}")
            self.lbl_curr_pos_z_bottom.setText(f"Z: {self.current_pos_right['z']:.3f}")
            self.lbl_curr_rot_x_bottom.setText(f"X: {self.current_rot_right['x']:.3f}°")
            self.lbl_curr_rot_y_bottom.setText(f"Y: {self.current_rot_right['y']:.3f}°")
            self.lbl_curr_rot_z_bottom.setText(f"Z: {self.current_rot_right['z']:.3f}°")

    def update_camera_display(self):
        """Update position/rotation display when camera moves (mouse rotation)"""
        # Debug: Print every 10th call to see if timer is working
        if not hasattr(self, 'timer_count'):
            self.timer_count = 0
        self.timer_count += 1
        if self.timer_count % 50 == 0:  # Every 10 seconds (50 * 200ms)
            print(f"DEBUG: Timer working, call #{self.timer_count}")

        try:
            # Get current camera position for active viewer
            if self.active_viewer == "top" and hasattr(self, 'vtk_renderer_left'):
                camera = self.vtk_renderer_left.renderer.GetActiveCamera()
                current_pos = camera.GetPosition()
                current_focal = camera.GetFocalPoint()

                # Get the actual model bounds from the displayed geometry
                if hasattr(self.top_renderer, 'step_actor') and self.top_renderer.step_actor:
                    bounds = self.top_renderer.step_actor.GetBounds()
                    # Calculate actual displayed model center
                    model_center = [
                        (bounds[0] + bounds[1]) / 2,  # X center
                        (bounds[2] + bounds[3]) / 2,  # Y center
                        (bounds[4] + bounds[5]) / 2   # Z center
                    ]
                else:
                    # Fallback to camera focal point
                    model_center = current_focal

                # Update position display (actual displayed model position)
                self.current_pos_left = {
                    'x': model_center[0],
                    'y': model_center[1],
                    'z': model_center[2]
                }

                # Calculate current rotation from camera view direction
                import math
                view_direction = camera.GetDirectionOfProjection()

                # Convert view direction to rotation angles (approximate)
                # This gives the viewing angle, not the model rotation
                pitch = math.degrees(math.asin(max(-1, min(1, -view_direction[2]))))  # X rotation
                yaw = math.degrees(math.atan2(view_direction[1], view_direction[0]))  # Z rotation

                # Add any button-applied rotations to the base view rotation
                if hasattr(self, 'model_rot_left'):
                    self.current_rot_left = {
                        'x': pitch + self.model_rot_left.get('x', 0.0),
                        'y': self.model_rot_left.get('y', 0.0),  # Y from buttons only
                        'z': yaw + self.model_rot_left.get('z', 0.0)
                    }
                else:
                    # Initialize tracking variables
                    self.model_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                    self.current_rot_left = {
                        'x': pitch,
                        'y': 0.0,
                        'z': yaw
                    }

                # Update display
                self.update_position_display()

            elif self.active_viewer == "bottom" and hasattr(self, 'vtk_renderer_right'):
                camera = self.vtk_renderer_right.renderer.GetActiveCamera()
                current_pos = camera.GetPosition()
                current_focal = camera.GetFocalPoint()

                # Get the actual model bounds from the displayed geometry
                if hasattr(self.bottom_renderer, 'step_actor') and self.bottom_renderer.step_actor:
                    bounds = self.bottom_renderer.step_actor.GetBounds()
                    # Calculate actual displayed model center
                    model_center = [
                        (bounds[0] + bounds[1]) / 2,  # X center
                        (bounds[2] + bounds[3]) / 2,  # Y center
                        (bounds[4] + bounds[5]) / 2   # Z center
                    ]
                else:
                    # Fallback to camera focal point
                    model_center = current_focal

                # Update position display (actual displayed model position)
                self.current_pos_right = {
                    'x': model_center[0],
                    'y': model_center[1],
                    'z': model_center[2]
                }

                # Get current model rotation from our tracking variables
                # These are updated by the rotate_shape() function
                if hasattr(self, 'model_rot_right'):
                    self.current_rot_right = {
                        'x': self.model_rot_right.get('x', 0.0),
                        'y': self.model_rot_right.get('y', 0.0),
                        'z': self.model_rot_right.get('z', 0.0)
                    }
                else:
                    # Initialize if not exists
                    self.model_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                    self.current_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}

                # Update display
                self.update_position_display()

        except Exception as e:
            # Silently handle errors to avoid spam
            pass

def main():
    app = QApplication(sys.argv)
    app.setApplicationName("TDK STEP Viewer Dual")
    app.setApplicationVersion("3.0")

    viewer = StepViewerTDK()
    viewer.show()

    sys.exit(app.exec_())

if __name__ == "__main__":
    main()