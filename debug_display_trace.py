#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug Display Trace - Test rotation and movement button values all the way to screen display
This will help identify where the text display update is failing
"""

import sys
import os
import time
import math
from PyQt5.QtWidgets import (QApp<PERSON>, QMainWindow, QW<PERSON>t, QVBoxLayout,
                            QHBoxLayout, QPushButton, QLabel, QTextEdit)
from PyQt5.QtCore import Qt, QTimer

class DebugDisplayTracer(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Debug Display Trace - Rotation & Movement Testing")
        self.setGeometry(100, 100, 1200, 800)

        # Debug log - initialize first
        self.debug_log = []

        # Initialize all tracking variables
        self.active_viewer = "top"
        self.init_tracking_variables()

        # Setup UI
        self.setup_ui()

        self.log_debug("=== DEBUG DISPLAY TRACER STARTED ===")
        
    def init_tracking_variables(self):
        """Initialize all the tracking variables that the main program uses"""
        # TOP viewer variables
        self.current_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_axis_left = {'x': 0.0, 'y': 0.0, 'z': 1.0}
        self.current_angle_left = 0.0
        self.model_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.model_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.orig_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.orig_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.orig_axis_left = {'x': 0.0, 'y': 0.0, 'z': 1.0}
        self.orig_angle_left = 0.0
        
        # BOTTOM viewer variables
        self.current_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_axis_right = {'x': 0.0, 'y': 0.0, 'z': 1.0}
        self.current_angle_right = 0.0
        self.model_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.model_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.orig_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.orig_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.orig_axis_right = {'x': 0.0, 'y': 0.0, 'z': 1.0}
        self.orig_angle_right = 0.0
        
        self.log_debug("All tracking variables initialized")
        
    def setup_ui(self):
        """Setup the debug UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Viewer selection
        viewer_layout = QHBoxLayout()
        self.btn_top_viewer = QPushButton("Top Viewer")
        self.btn_bottom_viewer = QPushButton("Bottom Viewer")
        self.btn_top_viewer.clicked.connect(lambda: self.set_active_viewer("top"))
        self.btn_bottom_viewer.clicked.connect(lambda: self.set_active_viewer("bottom"))
        viewer_layout.addWidget(self.btn_top_viewer)
        viewer_layout.addWidget(self.btn_bottom_viewer)
        layout.addLayout(viewer_layout)
        
        # Rotation buttons
        rot_layout = QHBoxLayout()
        self.btn_x_plus = QPushButton("X+")
        self.btn_x_minus = QPushButton("X-")
        self.btn_y_plus = QPushButton("Y+")
        self.btn_y_minus = QPushButton("Y-")
        self.btn_z_plus = QPushButton("Z+")
        self.btn_z_minus = QPushButton("Z-")
        
        self.btn_x_plus.clicked.connect(lambda: self.debug_rotate_shape('x', 15))
        self.btn_x_minus.clicked.connect(lambda: self.debug_rotate_shape('x', -15))
        self.btn_y_plus.clicked.connect(lambda: self.debug_rotate_shape('y', 15))
        self.btn_y_minus.clicked.connect(lambda: self.debug_rotate_shape('y', -15))
        self.btn_z_plus.clicked.connect(lambda: self.debug_rotate_shape('z', 15))
        self.btn_z_minus.clicked.connect(lambda: self.debug_rotate_shape('z', -15))
        
        rot_layout.addWidget(self.btn_x_plus)
        rot_layout.addWidget(self.btn_x_minus)
        rot_layout.addWidget(self.btn_y_plus)
        rot_layout.addWidget(self.btn_y_minus)
        rot_layout.addWidget(self.btn_z_plus)
        rot_layout.addWidget(self.btn_z_minus)
        layout.addLayout(rot_layout)
        
        # Movement buttons
        move_layout = QHBoxLayout()
        self.btn_move_x_plus = QPushButton("Move X+")
        self.btn_move_x_minus = QPushButton("Move X-")
        self.btn_move_y_plus = QPushButton("Move Y+")
        self.btn_move_y_minus = QPushButton("Move Y-")
        self.btn_move_z_plus = QPushButton("Move Z+")
        self.btn_move_z_minus = QPushButton("Move Z-")
        
        self.btn_move_x_plus.clicked.connect(lambda: self.debug_move_shape('x', 1.0))
        self.btn_move_x_minus.clicked.connect(lambda: self.debug_move_shape('x', -1.0))
        self.btn_move_y_plus.clicked.connect(lambda: self.debug_move_shape('y', 1.0))
        self.btn_move_y_minus.clicked.connect(lambda: self.debug_move_shape('y', -1.0))
        self.btn_move_z_plus.clicked.connect(lambda: self.debug_move_shape('z', 1.0))
        self.btn_move_z_minus.clicked.connect(lambda: self.debug_move_shape('z', -1.0))
        
        move_layout.addWidget(self.btn_move_x_plus)
        move_layout.addWidget(self.btn_move_x_minus)
        move_layout.addWidget(self.btn_move_y_plus)
        move_layout.addWidget(self.btn_move_y_minus)
        move_layout.addWidget(self.btn_move_z_plus)
        move_layout.addWidget(self.btn_move_z_minus)
        layout.addLayout(move_layout)
        
        # Display areas
        display_layout = QHBoxLayout()
        
        # TOP viewer display
        top_group = QWidget()
        top_layout = QVBoxLayout(top_group)
        top_layout.addWidget(QLabel("TOP VIEWER DISPLAY:"))
        self.top_display = QLabel("ANGLE: 0.0° AXIS: [0.00 0.00 1.00] POS: X=0.000mm Y=0.000mm Z=0.000mm")
        self.top_display.setStyleSheet("border: 1px solid black; padding: 5px; background-color: lightblue;")
        top_layout.addWidget(self.top_display)
        display_layout.addWidget(top_group)
        
        # BOTTOM viewer display
        bottom_group = QWidget()
        bottom_layout = QVBoxLayout(bottom_group)
        bottom_layout.addWidget(QLabel("BOTTOM VIEWER DISPLAY:"))
        self.bottom_display = QLabel("ANGLE: 0.0° AXIS: [0.00 0.00 1.00] POS: X=0.000mm Y=0.000mm Z=0.000mm")
        self.bottom_display.setStyleSheet("border: 1px solid black; padding: 5px; background-color: lightgreen;")
        bottom_layout.addWidget(self.bottom_display)
        display_layout.addWidget(bottom_group)
        
        layout.addLayout(display_layout)
        
        # Debug log
        layout.addWidget(QLabel("DEBUG LOG:"))
        self.debug_text = QTextEdit()
        self.debug_text.setMaximumHeight(300)
        layout.addWidget(self.debug_text)
        
        # Update active viewer display
        self.update_viewer_buttons()
        
    def log_debug(self, message):
        """Add debug message to log"""
        timestamp = time.strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}"
        self.debug_log.append(full_message)
        if hasattr(self, 'debug_text'):
            self.debug_text.append(full_message)
            self.debug_text.ensureCursorVisible()
        print(full_message)
        
    def set_active_viewer(self, viewer):
        """Set active viewer and update display"""
        self.active_viewer = viewer
        self.log_debug(f"Active viewer set to: {viewer}")
        self.update_viewer_buttons()
        
    def update_viewer_buttons(self):
        """Update viewer button colors"""
        if self.active_viewer == "top":
            self.btn_top_viewer.setStyleSheet("background-color: lightgreen;")
            self.btn_bottom_viewer.setStyleSheet("")
        else:
            self.btn_top_viewer.setStyleSheet("")
            self.btn_bottom_viewer.setStyleSheet("background-color: lightgreen;")

    def debug_rotate_shape(self, axis, degrees):
        """Debug version of rotate_shape - trace all values"""
        self.log_debug(f"=== ROTATE DEBUG START ===")
        self.log_debug(f"Button clicked: {axis}{'+' if degrees > 0 else '-'} ({degrees}°)")
        self.log_debug(f"Active viewer: {self.active_viewer}")

        # Step 1: Update rotation values based on active viewer
        if self.active_viewer == "top":
            self.log_debug(f"BEFORE: current_rot_left[{axis}] = {self.current_rot_left[axis]}")
            self.current_rot_left[axis] += degrees
            self.model_rot_left[axis] = self.current_rot_left[axis] - self.orig_rot_left[axis]
            self.log_debug(f"AFTER: current_rot_left[{axis}] = {self.current_rot_left[axis]}")
            self.log_debug(f"AFTER: model_rot_left[{axis}] = {self.model_rot_left[axis]}")
        else:
            self.log_debug(f"BEFORE: current_rot_right[{axis}] = {self.current_rot_right[axis]}")
            self.current_rot_right[axis] += degrees
            self.model_rot_right[axis] = self.current_rot_right[axis] - self.orig_rot_right[axis]
            self.log_debug(f"AFTER: current_rot_right[{axis}] = {self.current_rot_right[axis]}")
            self.log_debug(f"AFTER: model_rot_right[{axis}] = {self.model_rot_right[axis]}")

        # Step 2: Calculate axis and angle for display (like the main program)
        self.calculate_display_values()

        # Step 3: Update display
        self.update_display_text()

        self.log_debug(f"=== ROTATE DEBUG END ===")

    def debug_move_shape(self, axis, distance):
        """Debug version of move_shape - trace all values"""
        self.log_debug(f"=== MOVE DEBUG START ===")
        self.log_debug(f"Button clicked: Move {axis}{'+' if distance > 0 else '-'} ({distance}mm)")
        self.log_debug(f"Active viewer: {self.active_viewer}")

        # Step 1: Update position values based on active viewer
        if self.active_viewer == "top":
            self.log_debug(f"BEFORE: current_pos_left[{axis}] = {self.current_pos_left[axis]}")
            self.model_pos_left[axis] += distance
            self.current_pos_left[axis] = self.orig_pos_left[axis] + self.model_pos_left[axis]
            self.log_debug(f"AFTER: current_pos_left[{axis}] = {self.current_pos_left[axis]}")
            self.log_debug(f"AFTER: model_pos_left[{axis}] = {self.model_pos_left[axis]}")
        else:
            self.log_debug(f"BEFORE: current_pos_right[{axis}] = {self.current_pos_right[axis]}")
            self.model_pos_right[axis] += distance
            self.current_pos_right[axis] = self.orig_pos_right[axis] + self.model_pos_right[axis]
            self.log_debug(f"AFTER: current_pos_right[{axis}] = {self.current_pos_right[axis]}")
            self.log_debug(f"AFTER: model_pos_right[{axis}] = {self.model_pos_right[axis]}")

        # Step 2: Calculate display values
        self.calculate_display_values()

        # Step 3: Update display
        self.update_display_text()

        self.log_debug(f"=== MOVE DEBUG END ===")

    def calculate_display_values(self):
        """Calculate axis and angle values for display (mimics main program logic)"""
        self.log_debug("--- CALCULATING DISPLAY VALUES ---")

        # Calculate for LEFT viewer (TOP display)
        rot = self.current_rot_left
        rot_mag = math.sqrt(rot['x']**2 + rot['y']**2 + rot['z']**2)
        if rot_mag > 0.001:
            self.current_axis_left = {
                'x': rot['x'] / rot_mag,
                'y': rot['y'] / rot_mag,
                'z': rot['z'] / rot_mag
            }
        else:
            self.current_axis_left = {'x': 0.0, 'y': 0.0, 'z': 1.0}
        self.current_angle_left = rot_mag

        self.log_debug(f"LEFT: rot_mag = {rot_mag}, axis = {self.current_axis_left}, angle = {self.current_angle_left}")

        # Calculate for RIGHT viewer (BOTTOM display) - should use LEFT values per main program
        rot = self.current_rot_left  # Main program uses LEFT rotation for RIGHT display
        rot_mag = math.sqrt(rot['x']**2 + rot['y']**2 + rot['z']**2)
        if rot_mag > 0.001:
            self.current_axis_right = {
                'x': rot['x'] / rot_mag,
                'y': rot['y'] / rot_mag,
                'z': rot['z'] / rot_mag
            }
        else:
            self.current_axis_right = {'x': 0.0, 'y': 0.0, 'z': 1.0}
        self.current_angle_right = rot_mag

        # Copy position from left to right (they should show same values)
        self.current_pos_right = self.current_pos_left.copy()

        self.log_debug(f"RIGHT: rot_mag = {rot_mag}, axis = {self.current_axis_right}, angle = {self.current_angle_right}")
        self.log_debug(f"RIGHT: pos = {self.current_pos_right}")

    def update_display_text(self):
        """Update the display text (mimics update_vtk_text_overlays)"""
        self.log_debug("--- UPDATING DISPLAY TEXT ---")

        # TOP viewer display (shows ORIGINAL values in main program)
        top_text = f"ANGLE: {self.orig_angle_left:.1f}° AXIS: [{self.orig_axis_left['x']:.2f} {self.orig_axis_left['y']:.2f} {self.orig_axis_left['z']:.2f}] POS: X={self.orig_pos_left['x']:.3f}mm Y={self.orig_pos_left['y']:.3f}mm Z={self.orig_pos_left['z']:.3f}mm"
        self.top_display.setText(top_text)
        self.log_debug(f"TOP DISPLAY: {top_text}")

        # BOTTOM viewer display (shows CURRENT values in main program)
        bottom_text = f"ANGLE: {self.current_angle_right:.1f}° AXIS: [{self.current_axis_right['x']:.2f} {self.current_axis_right['y']:.2f} {self.current_axis_right['z']:.2f}] POS: X={self.current_pos_right['x']:.3f}mm Y={self.current_pos_right['y']:.3f}mm Z={self.current_pos_right['z']:.3f}mm"
        self.bottom_display.setText(bottom_text)
        self.log_debug(f"BOTTOM DISPLAY: {bottom_text}")

        # Check if variables exist (like main program does)
        self.log_debug(f"Variable check - current_pos_right exists: {hasattr(self, 'current_pos_right')}")
        self.log_debug(f"Variable check - current_angle_right exists: {hasattr(self, 'current_angle_right')}")
        self.log_debug(f"Variable check - current_axis_right exists: {hasattr(self, 'current_axis_right')}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = DebugDisplayTracer()
    window.show()
    sys.exit(app.exec_())
