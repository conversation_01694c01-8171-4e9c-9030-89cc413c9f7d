#!/usr/bin/env python3

print("DEBUGGING STEP FILE COLOR TO ITEM MAPPING")

with open('SOIC16P127_1270X940X610L89X51.STEP', 'r', encoding='utf-8', errors='ignore') as f:
    content = f.read()

lines = content.split('\n')

# Find color definitions
print("=== COLOR DEFINITIONS ===")
light_silver_ids = []
dark_silver_ids = []

for line in lines:
    if 'COLOUR_RGB' in line and line.startswith('#'):
        color_id = line.split('=')[0].strip()
        if '0.75294117647059' in line:  # Light silver
            light_silver_ids.append(color_id)
            print(f"Light silver: {color_id}")
        elif '0.25098039215686' in line:  # Dark silver
            dark_silver_ids.append(color_id)
            print(f"Dark silver: {color_id}")

print(f"\nLight silver IDs: {light_silver_ids}")
print(f"Dark silver IDs: {dark_silver_ids}")

# Find what references these colors
print("\n=== ITEMS THAT USE DARK SILVER ===")
dark_references = []

for line in lines:
    for dark_id in dark_silver_ids:
        if dark_id in line and line.startswith('#') and '=' in line:
            item_id = line.split('=')[0].strip()
            if item_id != dark_id:  # Don't include the color definition itself
                dark_references.append((item_id, line.strip()))
                print(f"{item_id}: {line.strip()}")

print(f"\nFound {len(dark_references)} items that reference dark silver")

# Look for shape/part names that use dark silver
print("\n=== SEARCHING FOR PART NAMES WITH DARK SILVER ===")
for item_id, line in dark_references:
    # Look for lines that might contain part names
    if 'NAME' in line.upper() or 'LABEL' in line.upper():
        print(f"Named item with dark silver: {line}")
    
    # Look for geometric entities
    if any(word in line.upper() for word in ['FACE', 'SOLID', 'SHELL', 'BODY', 'PART']):
        print(f"Geometric item with dark silver: {line}")

print("\n=== STEP COLOR TO ITEM DEBUG COMPLETE ===")
