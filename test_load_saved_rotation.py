#!/usr/bin/env python3
"""
Test loading a saved file with embedded rotation to see if VTK transformation is applied
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer_tdk_modular import StepViewerTDK

class LoadSavedRotationTest:
    def __init__(self):
        self.app = QApplication([])
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        print("🧪 LOAD SAVED ROTATION TEST")
        print("=" * 50)
        
        # Start the test
        QTimer.singleShot(1000, self.test_load_saved_file)
        
    def test_load_saved_file(self):
        """Test loading a saved file with embedded rotation"""
        saved_file = "test_workflow_rotated.step"
        
        if not os.path.exists(saved_file):
            print(f"❌ {saved_file} not found!")
            self.app.quit()
            return
            
        print(f"\n🔄 Loading saved file: {saved_file}")
        
        # Set active viewer to bottom (to test loading saved file)
        self.viewer.active_viewer = "bottom"
        self.viewer.update_viewer_highlights()
        
        # Load the saved file
        success = self.viewer.load_step_file_direct(saved_file)
        
        if success:
            print("✅ Saved file loaded successfully")
            
            # Check extracted values
            if hasattr(self.viewer, 'orig_rot_right'):
                print(f"📊 Extracted orig_rot_right: {self.viewer.orig_rot_right}")
            else:
                print("❌ orig_rot_right not found")
                
            if hasattr(self.viewer, 'current_rot_right'):
                print(f"📊 Current current_rot_right: {self.viewer.current_rot_right}")
            else:
                print("❌ current_rot_right not found")
                
            # Check if VTK transformation was applied
            print(f"\n🔍 Checking if VTK transformation was applied...")
            
            # Check the file content for rotation marker
            with open(saved_file, 'r') as f:
                content = f.read()
                if "ROTATION_VALUES:" in content:
                    print("✅ File contains ROTATION_VALUES marker")
                    # Extract the rotation values from the comment
                    import re
                    match = re.search(r'ROTATION_VALUES: X=([\d.-]+) Y=([\d.-]+) Z=([\d.-]+)', content)
                    if match:
                        stored_x, stored_y, stored_z = map(float, match.groups())
                        print(f"📊 Stored rotation values: X={stored_x}°, Y={stored_y}°, Z={stored_z}°")
                        
                        # Compare with extracted values
                        if hasattr(self.viewer, 'orig_rot_right'):
                            extracted = self.viewer.orig_rot_right
                            print(f"📊 Extracted rotation values: X={extracted['x']}°, Y={extracted['y']}°, Z={extracted['z']}°")
                            
                            # Check if they match
                            if (abs(extracted['x'] - stored_x) < 0.1 and 
                                abs(extracted['y'] - stored_y) < 0.1 and 
                                abs(extracted['z'] - stored_z) < 0.1):
                                print("✅ Extracted values match stored values")
                            else:
                                print("❌ Extracted values don't match stored values")
                        
                else:
                    print("❌ File does not contain ROTATION_VALUES marker")
                    
        else:
            print("❌ Failed to load saved file")
            
        # Keep window open for a few seconds
        QTimer.singleShot(5000, self.app.quit)

def main():
    test = LoadSavedRotationTest()
    test.app.exec_()

if __name__ == "__main__":
    main()
