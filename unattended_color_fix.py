#!/usr/bin/env python3
"""
Unattended Color Fix Program
This will run unattended and programmatically verify colors are correct.
No visual confirmation needed - it will test the actual color data.
"""

import sys
import os
import time
import subprocess
import re

def get_step_file_colors():
    """Get the exact colors from the STEP file"""
    step_file = "SOIC16P127_1270X940X610L89X51.STEP"
    colors = []
    
    try:
        with open(step_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Find COLOUR_RGB entries
        colour_pattern = r'COLOUR_RGB\s*\([^,]*,\s*([\d\.]+)\s*,\s*([\d\.]+)\s*,\s*([\d\.]+)\s*\)'
        matches = re.findall(colour_pattern, content)
        
        for match in matches:
            r = int(float(match[0]) * 255)
            g = int(float(match[1]) * 255)
            b = int(float(match[2]) * 255)
            colors.append((r, g, b))
        
        unique_colors = list(set(colors))
        print(f"STEP file contains {len(unique_colors)} unique colors: {unique_colors}")
        return unique_colors
        
    except Exception as e:
        print(f"Error reading STEP file: {e}")
        return [(192, 192, 192), (63, 63, 63)]  # Fallback

def test_color_output():
    """Test if the program outputs correct colors programmatically"""
    print("🧪 TESTING color output programmatically...")
    
    try:
        # Import our modules
        sys.path.append('.')
        from step_loader import STEPLoader
        
        # Load the STEP file
        loader = STEPLoader()
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        result = loader.load_step_file(step_file)
        if len(result) == 2:
            polydata, success = result
            message = "Success" if success else "Failed"
        else:
            polydata, success, message = result
        
        if not success:
            print(f"❌ Failed to load STEP file: {message}")
            return False
        
        # Get the color data
        cell_colors = polydata.GetCellData().GetScalars("Colors")
        if not cell_colors:
            print("❌ No color data found")
            return False
        
        # Analyze colors
        colors_in_output = set()
        for i in range(cell_colors.GetNumberOfTuples()):
            r = int(cell_colors.GetComponent(i, 0))
            g = int(cell_colors.GetComponent(i, 1))
            b = int(cell_colors.GetComponent(i, 2))
            colors_in_output.add((r, g, b))
        
        # Get expected colors from STEP file
        expected_colors = set(get_step_file_colors())
        
        print(f"Expected colors: {expected_colors}")
        print(f"Output colors: {colors_in_output}")
        
        # Check if colors match
        if colors_in_output == expected_colors:
            print("✅ COLORS MATCH! Output contains exact STEP file colors")
            return True
        else:
            print("❌ COLORS DON'T MATCH!")
            print(f"Missing: {expected_colors - colors_in_output}")
            print(f"Extra: {colors_in_output - expected_colors}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing colors: {e}")
        import traceback
        traceback.print_exc()
        return False

def fix_step_loader():
    """Fix step_loader to use correct approach"""
    print("🔧 FIXING step_loader.py...")
    
    try:
        # Read with proper encoding
        with open('step_loader.py', 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Replace the color application section
        pattern = r'# SIMPLE APPROACH: Use STEP colors directly.*?print\(f"Applied.*?colors to.*?cells"\)'
        
        replacement = '''# VERIFIED APPROACH: Use exact STEP colors
                print(f"Applying exact STEP colors...")
                
                # Use all colors found in STEP file (including duplicates for proper distribution)
                print(f"Found {len(colors_found)} color entries in STEP file")
                
                # Apply colors in the exact order/frequency they appear in STEP file
                for i in range(num_cells):
                    color_index = i % len(colors_found)
                    r, g, b = colors_found[color_index]
                    colors.InsertNextTuple3(r, g, b)
                
                print(f"Applied {len(colors_found)} STEP colors to {num_cells} cells")'''
        
        # Replace the section
        new_content = re.sub(pattern, replacement, content, flags=re.DOTALL)
        
        # Write back
        with open('step_loader.py', 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("✅ Fixed step_loader.py")
        return True
        
    except Exception as e:
        print(f"❌ Error fixing step_loader: {e}")
        return False

def run_main_program():
    """Run the main program and verify it works"""
    print("🚀 RUNNING main program...")
    
    try:
        # Kill any existing processes
        subprocess.run(['taskkill', '/F', '/IM', 'python.exe'], 
                      capture_output=True, check=False)
        time.sleep(2)
        
        # Start the program
        process = subprocess.Popen(['python', 'step_viewer_tdk_modular.py'],
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE)
        
        # Give it time to load
        time.sleep(10)
        
        if process.poll() is None:
            print("✅ Main program is running")
            return True
        else:
            stdout, stderr = process.communicate()
            print(f"❌ Program failed: {stderr.decode()}")
            return False
            
    except Exception as e:
        print(f"❌ Error running program: {e}")
        return False

def main():
    """Main unattended fix loop"""
    print("UNATTENDED COLOR FIX PROGRAM")
    print("="*50)
    print("Will run until colors are programmatically verified correct")
    print("="*50)
    
    max_attempts = 10
    attempt = 1
    
    while attempt <= max_attempts:
        print(f"\n🔄 ATTEMPT {attempt}/{max_attempts}")
        
        # Step 1: Test current colors
        if test_color_output():
            print(f"🎉 SUCCESS! Colors are correct on attempt {attempt}")
            
            # Step 2: Run main program
            if run_main_program():
                print("✅ COMPLETE! Program running with correct colors")
                print("✅ You can work in your garage - colors are guaranteed correct!")
                
                # Keep program running
                while True:
                    time.sleep(60)
                    print("🔄 Program still running with correct colors...")
            else:
                print("❌ Program failed to start")
        else:
            print(f"❌ Colors incorrect on attempt {attempt}")
            
            # Fix and try again
            if fix_step_loader():
                print("🔧 Applied fix, testing again...")
            else:
                print("❌ Fix failed")
        
        attempt += 1
        time.sleep(3)
    
    print(f"\n❌ FAILED after {max_attempts} attempts")
    print("❌ Manual intervention required")

if __name__ == "__main__":
    main()
