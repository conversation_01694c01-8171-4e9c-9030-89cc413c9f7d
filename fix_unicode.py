#!/usr/bin/env python3
"""
Fix Unicode characters in debug program
"""

# Read the file
with open('complete_debug_no_intervention.py', 'r', encoding='utf-8') as f:
    content = f.read()

# Replace Unicode characters
replacements = {
    '✅': 'OK',
    '❌': 'FAIL',
    '⚠️': 'WARN', 
    '🎉': 'SUCCESS',
    '📁': 'FILE'
}

for unicode_char, replacement in replacements.items():
    content = content.replace(unicode_char, replacement)

# Write back
with open('complete_debug_no_intervention.py', 'w', encoding='utf-8') as f:
    f.write(content)

print("Fixed Unicode characters in complete_debug_no_intervention.py")
