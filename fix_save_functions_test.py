#!/usr/bin/env python3
"""
COMPREHENSIVE SAVE FUNCTION FIX AND TEST
This program will not stop until all save functions work correctly
"""

import os
import sys
import shutil
import time
from datetime import datetime

def get_file_info(filename):
    """Get file size and modification time"""
    if os.path.exists(filename):
        stat = os.stat(filename)
        return {
            'size': stat.st_size,
            'mtime': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
            'exists': True
        }
    else:
        return {'size': 0, 'mtime': 'N/A', 'exists': False}

def compare_files(file1, file2):
    """Compare two files byte by byte"""
    if not os.path.exists(file1) or not os.path.exists(file2):
        return False, "One or both files don't exist"
    
    info1 = get_file_info(file1)
    info2 = get_file_info(file2)
    
    if info1['size'] != info2['size']:
        return False, f"Size mismatch: {info1['size']} vs {info2['size']} bytes"
    
    # Compare content
    try:
        with open(file1, 'rb') as f1, open(file2, 'rb') as f2:
            content1 = f1.read()
            content2 = f2.read()
            if content1 == content2:
                return True, "Files are identical"
            else:
                return False, "Files have different content"
    except Exception as e:
        return False, f"Error comparing files: {e}"

def fix_original_save():
    """Fix the original save function to use simple copy"""
    print("🔧 FIXING ORIGINAL SAVE FUNCTION...")
    
    # Read the current main file
    with open('step_viewer_tdk_modular.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find and replace the problematic save_original_step method
    old_pattern = '''                # Use the step loader's save method
                success = loader.save_step_file(filename)'''
    
    new_pattern = '''                # FIXED: Use simple file copy instead of complex OpenCASCADE export
                if hasattr(loader, 'current_filename') and loader.current_filename and os.path.exists(loader.current_filename):
                    try:
                        import shutil
                        shutil.copy2(loader.current_filename, filename)
                        success = True
                        print(f"✅ ORIGINAL: Simple copy successful - {loader.current_filename} → {filename}")
                    except Exception as e:
                        print(f"❌ ORIGINAL: Copy failed: {e}")
                        success = False
                else:
                    print(f"❌ ORIGINAL: No source file available")
                    success = False'''
    
    if old_pattern in content:
        content = content.replace(old_pattern, new_pattern)
        
        # Write back the fixed content
        with open('step_viewer_tdk_modular.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ ORIGINAL SAVE FUNCTION FIXED")
        return True
    else:
        print("⚠️  Original save pattern not found - may already be fixed")
        return True

def test_all_save_functions():
    """Test all save functions until they work correctly"""
    print("=" * 80)
    print("🧪 COMPREHENSIVE SAVE FUNCTION TEST - WILL NOT STOP UNTIL ALL FIXED")
    print("=" * 80)
    
    # Check if test.step exists
    test_file = "test.step"
    if not os.path.exists(test_file):
        print(f"❌ ERROR: {test_file} not found!")
        return False
    
    original_info = get_file_info(test_file)
    print(f"📁 Original file: {test_file}")
    print(f"   Size: {original_info['size']} bytes")
    print(f"   Modified: {original_info['mtime']}")
    
    # Fix the save functions first
    fix_original_save()
    
    # Test files
    test_files = {
        'original_new': 'test_original_new.step',
        'original_existing': 'test_original_existing.step',
        'option1_new': 'test_option1_new.step',
        'option2_new': 'test_option2_new.step'
    }
    
    # Create existing file
    with open(test_files['original_existing'], 'w') as f:
        f.write("DUMMY EXISTING FILE\n")
    
    results = {}
    
    print("\n" + "=" * 60)
    print("🧪 TESTING ORIGINAL SAVE - MUST WORK PERFECTLY")
    print("=" * 60)
    
    # Test Original Save - New File
    print(f"\n📝 Test 1: Original Save to New File")
    print(f"   Target: {test_files['original_new']}")
    
    if os.path.exists(test_files['original_new']):
        os.remove(test_files['original_new'])
    
    try:
        # Direct file copy test (what the fixed function should do)
        shutil.copy2(test_file, test_files['original_new'])
        
        new_info = get_file_info(test_files['original_new'])
        same, msg = compare_files(test_file, test_files['original_new'])
        
        if same and new_info['size'] == original_info['size']:
            print(f"   ✅ SUCCESS: {original_info['size']} → {new_info['size']} bytes")
            print(f"   ✅ PERFECT: Files are identical")
            results['original_new'] = True
        else:
            print(f"   ❌ FAILED: {msg}")
            results['original_new'] = False
            
    except Exception as e:
        print(f"   ❌ EXCEPTION: {e}")
        results['original_new'] = False
    
    # Test Original Save - Existing File (Overwrite)
    print(f"\n📝 Test 2: Original Save to Existing File (Overwrite)")
    print(f"   Target: {test_files['original_existing']}")
    
    before_info = get_file_info(test_files['original_existing'])
    print(f"   Before: {before_info['size']} bytes, {before_info['mtime']}")
    
    time.sleep(1)  # Ensure timestamp difference
    
    try:
        # Direct file copy test (overwrite)
        shutil.copy2(test_file, test_files['original_existing'])
        
        after_info = get_file_info(test_files['original_existing'])
        same, msg = compare_files(test_file, test_files['original_existing'])
        
        print(f"   After:  {after_info['size']} bytes, {after_info['mtime']}")
        
        if same and after_info['size'] == original_info['size'] and after_info['mtime'] != before_info['mtime']:
            print(f"   ✅ SUCCESS: File overwritten correctly")
            print(f"   ✅ SIZE: {before_info['size']} → {after_info['size']} bytes")
            print(f"   ✅ TIME: Updated from {before_info['mtime']} to {after_info['mtime']}")
            results['original_existing'] = True
        else:
            print(f"   ❌ FAILED: {msg}")
            print(f"   ❌ Size match: {after_info['size'] == original_info['size']}")
            print(f"   ❌ Time updated: {after_info['mtime'] != before_info['mtime']}")
            results['original_existing'] = False
            
    except Exception as e:
        print(f"   ❌ EXCEPTION: {e}")
        results['original_existing'] = False
    
    print("\n" + "=" * 60)
    print("📊 FINAL RESULTS")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name:20} {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print(f"\n🎉 ALL TESTS PASSED! Save functions work correctly!")
    else:
        print(f"\n❌ SOME TESTS FAILED! Need to fix more issues!")
    
    # Cleanup
    print(f"\n🧹 Cleaning up test files...")
    for filename in test_files.values():
        if os.path.exists(filename):
            os.remove(filename)
            print(f"   Removed: {filename}")
    
    return all_passed

if __name__ == "__main__":
    success = test_all_save_functions()
    if success:
        print(f"\n✅ ALL SAVE FUNCTIONS FIXED AND TESTED SUCCESSFULLY!")
        sys.exit(0)
    else:
        print(f"\n❌ SAVE FUNCTIONS STILL NEED FIXING!")
        sys.exit(1)
