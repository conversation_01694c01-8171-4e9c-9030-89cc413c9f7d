#!/usr/bin/env python3
"""
Verification Test Program - Tests specific fixes to verify they work
"""

import sys
import time
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QTextEdit
from PyQt5.QtCore import QTimer
from step_viewer_tdk_modular import StepViewerTDK

class FixVerificationTester(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Fix Verification Test")
        self.setGeometry(100, 100, 800, 600)
        
        # Create main widget
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QVBoxLayout(main_widget)
        
        # Status label
        self.status_label = QLabel("Verifying fixes...")
        layout.addWidget(self.status_label)
        
        # Results text area
        self.results_text = QTextEdit()
        self.results_text.setReadOnly(True)
        layout.addWidget(self.results_text)
        
        # Create the actual viewer
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        # Test step counter
        self.test_step = 0
        self.passed_tests = 0
        self.failed_tests = 0
        
        # Start tests after a delay
        self.timer = QTimer()
        self.timer.timeout.connect(self.run_next_test)
        self.timer.start(3000)  # Start after 3 seconds
        
    def log_result(self, message):
        """Log a test result"""
        print(message)
        self.results_text.append(message)
        
    def run_next_test(self):
        """Run verification tests step by step"""
        
        if self.test_step == 0:
            self.log_result("🔧 VERIFICATION TEST STARTING")
            self.log_result("=" * 50)
            
        elif self.test_step == 1:
            self.log_result("\n📋 TEST 1: STEP FILE LOADING - TOP VIEWER")
            try:
                # Load into TOP viewer first
                self.viewer.active_viewer = "top"
                self.viewer.load_step_file_direct("test.step")
                QApplication.processEvents()
                time.sleep(2)

                # Check TOP viewer values
                orig_rot_left = getattr(self.viewer, 'orig_rot_left', {'z': 0})
                z_value_top = orig_rot_left.get('z', 0)

                self.log_result(f"   TOP viewer orig_rot_left Z value: {z_value_top}")

                if z_value_top == 9.0:
                    self.log_result("   ✅ PASS: TOP Z axis shows 9 from STEP file")
                    self.passed_tests += 1
                else:
                    self.log_result("   ❌ FAIL: TOP Z axis should be 9, got " + str(z_value_top))
                    self.failed_tests += 1

            except Exception as e:
                self.log_result(f"   ❌ FAIL: TOP STEP file loading error: {e}")
                self.failed_tests += 1

        elif self.test_step == 2:
            self.log_result("\n📋 TEST 2: STEP FILE LOADING - BOTTOM VIEWER")
            try:
                # Load into BOTTOM viewer
                self.viewer.active_viewer = "bottom"
                self.viewer.load_step_file_direct("test.step")
                QApplication.processEvents()
                time.sleep(2)

                # Check BOTTOM viewer values
                orig_rot_right = getattr(self.viewer, 'orig_rot_right', {'z': 0})
                z_value_bottom = orig_rot_right.get('z', 0)

                self.log_result(f"   BOTTOM viewer orig_rot_right Z value: {z_value_bottom}")

                if z_value_bottom == 9.0:
                    self.log_result("   ✅ PASS: BOTTOM Z axis shows 9 from STEP file")
                    self.passed_tests += 1
                else:
                    self.log_result("   ❌ FAIL: BOTTOM Z axis should be 9, got " + str(z_value_bottom))
                    self.failed_tests += 1

            except Exception as e:
                self.log_result(f"   ❌ FAIL: BOTTOM STEP file loading error: {e}")
                self.failed_tests += 1
                
        elif self.test_step == 3:
            self.log_result("\n📋 TEST 3: TOP VIEWER X+ BUTTON - TEXT DISPLAY CHECK")

            # Set TOP viewer active
            self.viewer.active_viewer = "top"

            # Get initial TEXT DISPLAY position (original + movement_delta)
            if hasattr(self.viewer, 'movement_delta_left') and hasattr(self.viewer, 'orig_pos_left'):
                initial_display_x = self.viewer.orig_pos_left['x'] + self.viewer.movement_delta_left['x']
            else:
                initial_display_x = 0

            self.log_result(f"   Initial X TEXT DISPLAY value: {initial_display_x}")

            # Press X+ button (should INCREASE the text display value)
            self.viewer.move_shape('x', 1.0)
            QApplication.processEvents()
            time.sleep(0.1)

            # Get final TEXT DISPLAY position (original + movement_delta)
            if hasattr(self.viewer, 'movement_delta_left') and hasattr(self.viewer, 'orig_pos_left'):
                final_display_x = self.viewer.orig_pos_left['x'] + self.viewer.movement_delta_left['x']
            else:
                final_display_x = 0

            self.log_result(f"   Final X TEXT DISPLAY value: {final_display_x}")

            change = final_display_x - initial_display_x
            self.log_result(f"   Expected change: +1.0, Actual change: {change}")

            if abs(change - 1.0) < 0.01:
                self.log_result("   ✅ PASS: X+ button INCREASES X text display correctly")
                self.passed_tests += 1
            else:
                self.log_result("   ❌ FAIL: X+ button should INCREASE X text display by 1.0")
                self.failed_tests += 1
                
        elif self.test_step == 3:
            self.log_result("\n📋 TEST 3: TOP VIEWER X- BUTTON")
            
            # Get initial position
            if hasattr(self.viewer, 'movement_delta_left'):
                initial_x = self.viewer.movement_delta_left.get('x', 0)
            else:
                initial_x = 0
                
            self.log_result(f"   Initial X movement delta: {initial_x}")
            
            # Press X- button (should subtract 1.0)
            self.viewer.move_shape('x', -1.0)
            QApplication.processEvents()
            time.sleep(0.1)
            
            # Get final position
            if hasattr(self.viewer, 'movement_delta_left'):
                final_x = self.viewer.movement_delta_left.get('x', 0)
            else:
                final_x = 0
                
            self.log_result(f"   Final X movement delta: {final_x}")
            
            change = final_x - initial_x
            self.log_result(f"   Expected change: -1.0, Actual change: {change}")
            
            if abs(change - (-1.0)) < 0.01:
                self.log_result("   ✅ PASS: X- button decreases X value correctly")
                self.passed_tests += 1
            else:
                self.log_result("   ❌ FAIL: X- button should decrease X by 1.0")
                self.failed_tests += 1
                
        elif self.test_step == 4:
            self.log_result("\n📋 TEST 4: BOTTOM VIEWER X+ BUTTON")
            
            # Set BOTTOM viewer active
            self.viewer.active_viewer = "bottom"
            
            # Get initial position
            if hasattr(self.viewer, 'movement_delta_right'):
                initial_x = self.viewer.movement_delta_right.get('x', 0)
            else:
                initial_x = 0
                
            self.log_result(f"   Initial X movement delta: {initial_x}")
            
            # Press X+ button (should add +1.0)
            self.viewer.move_shape('x', 1.0)
            QApplication.processEvents()
            time.sleep(0.1)
            
            # Get final position
            if hasattr(self.viewer, 'movement_delta_right'):
                final_x = self.viewer.movement_delta_right.get('x', 0)
            else:
                final_x = 0
                
            self.log_result(f"   Final X movement delta: {final_x}")
            
            change = final_x - initial_x
            self.log_result(f"   Expected change: +1.0, Actual change: {change}")
            
            if abs(change - 1.0) < 0.01:
                self.log_result("   ✅ PASS: BOTTOM X+ button increases X value correctly")
                self.passed_tests += 1
            else:
                self.log_result("   ❌ FAIL: BOTTOM X+ button should increase X by 1.0")
                self.failed_tests += 1
                
        elif self.test_step == 5:
            self.log_result("\n📋 TEST 5: BOTTOM VIEWER Z+ ROTATION BUTTON")
            
            # Get initial rotation
            if hasattr(self.viewer, 'current_rot_right'):
                initial_z = self.viewer.current_rot_right.get('z', 0)
            else:
                initial_z = 0
                
            self.log_result(f"   Initial Z rotation: {initial_z}")
            
            # Press Z+ rotation button (should add +15.0)
            self.viewer.rotate_shape('z', 15.0)
            QApplication.processEvents()
            time.sleep(0.1)
            
            # Get final rotation
            if hasattr(self.viewer, 'current_rot_right'):
                final_z = self.viewer.current_rot_right.get('z', 0)
            else:
                final_z = 0
                
            self.log_result(f"   Final Z rotation: {final_z}")
            
            change = final_z - initial_z
            self.log_result(f"   Expected change: +15.0, Actual change: {change}")
            
            if abs(change - 15.0) < 0.01:
                self.log_result("   ✅ PASS: Z+ rotation button increases Z rotation correctly")
                self.passed_tests += 1
            else:
                self.log_result("   ❌ FAIL: Z+ rotation button should increase Z by 15.0")
                self.failed_tests += 1
                
        elif self.test_step == 6:
            self.log_result("\n📋 TEST 6: RESET FUNCTIONALITY")
            
            # Reset to original
            self.viewer.reset_to_original()
            QApplication.processEvents()
            time.sleep(0.5)
            
            # Check if BOTTOM Z axis shows 9 after reset
            orig_rot_right = getattr(self.viewer, 'orig_rot_right', {'z': 0})
            current_rot_right = getattr(self.viewer, 'current_rot_right', {'z': 0})
            
            self.log_result(f"   After reset - orig_rot_right Z: {orig_rot_right.get('z', 0)}")
            self.log_result(f"   After reset - current_rot_right Z: {current_rot_right.get('z', 0)}")
            
            if orig_rot_right.get('z', 0) == 9.0:
                self.log_result("   ✅ PASS: Reset preserves STEP file Z=9 value")
                self.passed_tests += 1
            else:
                self.log_result("   ❌ FAIL: Reset should preserve STEP file Z=9 value")
                self.failed_tests += 1
                
        else:
            # Test complete
            self.log_result("\n" + "=" * 50)
            self.log_result("🏁 VERIFICATION TESTS COMPLETE")
            self.log_result("=" * 50)
            
            total_tests = self.passed_tests + self.failed_tests
            self.log_result(f"📊 RESULTS: {self.passed_tests}/{total_tests} PASSED, {self.failed_tests}/{total_tests} FAILED")
            
            if self.failed_tests == 0:
                self.log_result("🎉 ALL FIXES VERIFIED - EVERYTHING WORKS!")
                self.status_label.setText("✅ ALL FIXES VERIFIED!")
            else:
                self.log_result("⚠️  SOME FIXES STILL BROKEN - NEED MORE WORK")
                self.status_label.setText(f"❌ {self.failed_tests} FIXES STILL BROKEN")
                
            self.timer.stop()
            return
            
        self.test_step += 1

if __name__ == "__main__":
    app = QApplication(sys.argv)
    tester = FixVerificationTester()
    tester.show()
    sys.exit(app.exec_())
