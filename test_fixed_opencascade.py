#!/usr/bin/env python3
"""
Test the fixed OpenCASCADE imports
"""

print("🔧 TESTING FIXED OPENCASCADE IMPORTS")
print("=" * 60)

# Test 1: Basic STEP control imports (FIXED)
print("\n1. Testing STEP control imports (FIXED)...")
try:
    from OCC.Core.STEPControl import STEPControl_Reader, STEPControl_Writer
    print("✅ STEP control imports successful")
    print(f"   - STEPControl_Reader: {STEPControl_Reader}")
    print(f"   - STEPControl_Writer: {STEPControl_Writer}")
except ImportError as e:
    print(f"❌ STEP control imports failed: {e}")

# Test 2: Transformation imports (FIXED)
print("\n2. Testing transformation imports (FIXED)...")
try:
    from OCC.Core.gp import gp_Trsf, gp_Vec, gp_Ax1, gp_Pnt, gp_Dir
    print("✅ Transformation imports successful")
    print(f"   - gp_Trsf: {gp_Trsf}")
    print(f"   - gp_Vec: {gp_Vec}")
    print(f"   - gp_Ax1: {gp_Ax1}")
except ImportError as e:
    print(f"❌ Transformation imports failed: {e}")

# Test 3: BRep transformation imports (FIXED)
print("\n3. Testing BRep transformation imports (FIXED)...")
try:
    from OCC.Core.BRepBuilderAPI import BRepBuilderAPI_Transform
    print("✅ BRep transformation imports successful")
    print(f"   - BRepBuilderAPI_Transform: {BRepBuilderAPI_Transform}")
except ImportError as e:
    print(f"❌ BRep transformation imports failed: {e}")

# Test 4: Interface imports (FIXED)
print("\n4. Testing Interface imports (FIXED)...")
try:
    from OCC.Core.Interface import Interface_Static
    from OCC.Core.IFSelect import IFSelect_RetDone
    print("✅ Interface imports successful")
    print(f"   - Interface_Static: {Interface_Static}")
    print(f"   - IFSelect_RetDone: {IFSelect_RetDone}")
except ImportError as e:
    print(f"❌ Interface imports failed: {e}")

# Test 5: Create a simple transformation to verify functionality
print("\n5. Testing transformation creation...")
try:
    from OCC.Core.gp import gp_Trsf, gp_Vec
    import math
    
    # Create a transformation
    trsf = gp_Trsf()
    
    # Set rotation around X axis (15 degrees)
    angle_rad = math.radians(15.0)
    from OCC.Core.gp import gp_Ax1, gp_Pnt, gp_Dir
    axis = gp_Ax1(gp_Pnt(0, 0, 0), gp_Dir(1, 0, 0))
    trsf.SetRotation(axis, angle_rad)
    
    print("✅ Transformation creation successful")
    print(f"   - Created rotation transformation: {angle_rad} radians around X-axis")
    
    # Test transformation matrix access
    matrix = trsf.VectorialPart()
    print(f"   - Transformation matrix accessible: {matrix}")
    
except Exception as e:
    print(f"❌ Transformation creation failed: {e}")

print("\n" + "=" * 60)
print("FIXED OPENCASCADE TEST COMPLETE")
print("If all tests pass, the rotation save feature should now work!")
