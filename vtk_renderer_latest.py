import numpy as np
import math
from PyQt5.QtWidgets import QWidget

# Try different VTK import approaches
vtk = None
try:
    # Try importing specific VTK modules to avoid Qt dependency
    import vtkmodules.vtkCommonCore as vtk_core
    import vtkmodules.vtkRenderingCore as vtk_render
    import vtkmodules.vtkInteractionStyle as vtk_interact

    # Create a minimal vtk namespace
    class VTKNamespace:
        def __init__(self):
            # Import the essential VTK classes we need
            from vtkmodules.vtkCommonCore import vtkPoints, vtkCellArray
            from vtkmodules.vtkCommonDataModel import vtkPolyData, vtkTriangle
            from vtkmodules.vtkRenderingCore import vtkActor, vtkPolyDataMapper, vtkRenderer, vtkRenderWindow
            from vtkmodules.vtkInteractionStyle import vtkInteractorStyleTrackballCamera

            # Add them to this namespace
            self.vtkPoints = vtkPoints
            self.vtkCellArray = vtkCellArray
            self.vtkPolyData = vtkPolyData
            self.vtkTriangle = vtkTriangle
            self.vtkActor = vtkActor
            self.vtkPolyDataMapper = vtkPolyDataMapper
            self.vtkRenderer = vtkRenderer
            self.vtkRenderWindow = vtkRenderWindow
            self.vtkInteractorStyleTrackballCamera = vtkInteractorStyleTrackballCamera

    vtk = VTKNamespace()
    print("VTK imported successfully (modular)")
except ImportError as e1:
    try:
        # Fallback to regular VTK import
        import vtk
        print("VTK imported successfully (regular)")
    except ImportError as e2:
        print(f"VTK modular import failed: {e1}")
        print(f"VTK regular import failed: {e2}")
        raise ImportError("Cannot import VTK")

# Try to import VTK Qt components with multiple fallbacks
QVTKRenderWindowInteractor = None
vtk_qt_error = None

try:
    from vtkmodules.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor
    print("VTK Qt imported from vtkmodules.qt")
except ImportError as e1:
    vtk_qt_error = str(e1)
    try:
        from vtk.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor
        print("VTK Qt imported from vtk.qt")
    except ImportError as e2:
        try:
            # Try alternative import
            import vtk.qt4.QVTKRenderWindowInteractor as vtk_qt
            QVTKRenderWindowInteractor = vtk_qt.QVTKRenderWindowInteractor
            print("VTK Qt imported from vtk.qt4")
        except ImportError as e3:
            try:
                # Last resort - try direct vtk import
                from vtk import vtkRenderWindow, vtkRenderer
                print("Using basic VTK without Qt integration")
                # Create a minimal replacement
                class BasicVTKWidget(QWidget):
                    def __init__(self, parent=None):
                        super().__init__(parent)
                        self.render_window = vtkRenderWindow()
                        self.renderer = vtkRenderer()
                        self.render_window.AddRenderer(self.renderer)

                    def GetRenderWindow(self):
                        return self.render_window

                QVTKRenderWindowInteractor = BasicVTKWidget
            except ImportError as e4:
                print(f"All VTK Qt import attempts failed:")
                print(f"  vtkmodules.qt: {e1}")
                print(f"  vtk.qt: {e2}")
                print(f"  vtk.qt4: {e3}")
                print(f"  basic vtk: {e4}")
                raise ImportError("Cannot import VTK Qt components")
    except ImportError:
        QVTKRenderWindowInteractor = None

class VTKRenderer:
    def __init__(self, parent=None):
        self.parent = parent

        # Check if VTK is available
        if vtk is None:
            print("VTK not available - creating fallback renderer")
            self.renderer = None
            self.render_window = None
            self.vtk_widget = None
            self.interactor = None
            self.step_actor = None
            self.bbox_actor = None
            self.origin_actor = None
            return

        self.renderer = vtk.vtkRenderer()
        self.render_window = None  # Will be set by the widget
        self.vtk_widget = None
        self.interactor = None
        self.step_actor = None
        self.bbox_actor = None
        self.origin_actor = None
        self.initialize()
    
    def initialize(self):
        try:
            if QVTKRenderWindowInteractor:
                # Create the Qt VTK widget with minimal settings
                self.vtk_widget = QVTKRenderWindowInteractor()

                # Completely disable all VTK widget decorations and tools
                from PyQt5.QtCore import Qt
                self.vtk_widget.setWindowFlags(Qt.Widget)
                self.vtk_widget.setContextMenuPolicy(Qt.NoContextMenu)
                self.vtk_widget.setFocusPolicy(Qt.StrongFocus)

                # Get the render window
                self.render_window = self.vtk_widget.GetRenderWindow()

                # Disable VTK render window decorations
                self.render_window.SetBorders(0)
                self.render_window.SetOffScreenRendering(0)

                # Set up our renderer with dark blue background
                self.renderer.SetBackground(0.1, 0.1, 0.2)  # Dark blue background

                # Add renderer to the render window
                self.render_window.AddRenderer(self.renderer)

                # Force dark blue background immediately
                self.renderer.SetBackground(0.1, 0.1, 0.2)
                self.render_window.Render()

                # Set the widget background too
                self.vtk_widget.setStyleSheet("background-color: rgb(25, 25, 51);")

                # Get interactor and set up SAFE interaction
                self.interactor = self.vtk_widget.GetRenderWindow().GetInteractor()

                # Create SAFE custom interactor style that prevents crashes
                class SafeRotationStyle(vtk.vtkInteractorStyleTrackballCamera):
                    def __init__(self):
                        super().__init__()
                        self.SetMotionFactor(5.0)  # Reduced sensitivity for stability

                    def OnMouseMove(self):
                        # Override to add error handling
                        try:
                            super().OnMouseMove()
                        except Exception as e:
                            print(f"VTK mouse move error (handled): {e}")
                            pass

                    def OnLeftButtonDown(self):
                        try:
                            super().OnLeftButtonDown()
                        except Exception as e:
                            print(f"VTK left button error (handled): {e}")
                            pass

                    def OnLeftButtonUp(self):
                        try:
                            super().OnLeftButtonUp()
                        except Exception as e:
                            print(f"VTK left button up error (handled): {e}")
                            pass

                style = SafeRotationStyle()
                self.interactor.SetInteractorStyle(style)
                print("DEBUG: VTK interactor ENABLED with SAFE rotation (crash protection)")

                # Aggressively disable ALL VTK decorations and toolbars
                self.render_window.SetBorders(0)
                self.render_window.SetOffScreenRendering(0)

                # Try to hide VTK toolbar without breaking display
                try:
                    from PyQt5.QtCore import Qt
                    # Only set basic widget flags to avoid display issues
                    self.vtk_widget.setWindowFlags(Qt.Widget)

                    # Hide toolbar children but don't hide the main widget
                    for child in self.vtk_widget.children():
                        if hasattr(child, 'hide') and hasattr(child, 'objectName'):
                            obj_name = child.objectName()
                            if 'toolbar' in obj_name.lower() or 'button' in obj_name.lower():
                                child.hide()
                                print(f"Hidden VTK child: {obj_name}")

                except Exception as e:
                    print(f"Could not disable VTK decorations: {e}")
                    # Continue anyway - display is more important than hiding toolbar

                # SAFE initialization with error handling
                try:
                    self.interactor.Initialize()
                    print("DEBUG: VTK interactor initialized successfully")
                except Exception as e:
                    print(f"VTK interactor init warning (continuing): {e}")
                    # Continue anyway - some systems have issues but still work

                # DON'T call Start() - it blocks the Qt event loop
                # self.interactor.Start()  # NEVER call this in Qt applications

                return True
            else:
                print("QVTKRenderWindowInteractor not available")
                self.vtk_widget = QWidget()
                return False
        except Exception as e:
            print(f"VTK init error: {e}")
            self.vtk_widget = QWidget()
            return False
    
    def display_polydata(self, polydata):
        print(f"🔍 VTK DEBUG: display_polydata called with polydata: {polydata is not None}")
        if not polydata:
            print("🔍 VTK DEBUG: No polydata provided to display")
            return False

        print(f"🔍 VTK DEBUG: Polydata has {polydata.GetNumberOfPoints()} points, {polydata.GetNumberOfCells()} cells")

        try:
            print(f"🔍 VTK DEBUG: step_actor BEFORE creation: {self.step_actor}")

            if self.step_actor:
                print("🔍 VTK DEBUG: Removing existing step actor")
                self.renderer.RemoveActor(self.step_actor)

            print(f"🔍 VTK DEBUG: Creating VTK mapper...")
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputData(polydata)

            print(f"🔍 VTK DEBUG: Creating VTK actor...")
            self.step_actor = vtk.vtkActor()
            self.step_actor.SetMapper(mapper)

            print(f"🔍 VTK DEBUG: step_actor AFTER creation: {self.step_actor}")
            print(f"🔍 VTK DEBUG: step_actor type: {type(self.step_actor)}")

            # Check if polydata has color information
            cell_colors = polydata.GetCellData().GetScalars()
            point_colors = polydata.GetPointData().GetScalars()

            print(f"Cell colors: {cell_colors is not None}")
            print(f"Point colors: {point_colors is not None}")

            if cell_colors or point_colors:
                print("Using exact colors from STEP file")

                # CRITICAL FIX: Ensure colors are properly applied to VTK
                if cell_colors:
                    print("Using exact colors from STEP file")

                    # Ensure colors are set as active scalars BEFORE mapper setup
                    polydata.GetCellData().SetScalars(cell_colors)
                    polydata.GetCellData().SetActiveScalars("Colors")

                    print(f"✅ Colors applied: {cell_colors.GetNumberOfTuples()} colors")
                    print(f"✅ Color range: {cell_colors.GetRange()}")
                else:
                    print("❌ No colors found in STEP file")

                # CRITICAL FIX: Ensure VTK mapper uses colors correctly
                mapper.SetScalarVisibility(True)
                mapper.SetScalarModeToUseCellData()  # Force use of cell colors
                mapper.SetColorModeToDirectScalars()  # Use RGB values directly

                # Force mapper to use the color data
                mapper.SetInputData(polydata)
                mapper.Update()

                # Set lighting for good color appearance
                self.step_actor.GetProperty().SetAmbient(0.3)
                self.step_actor.GetProperty().SetDiffuse(0.7)

                print("✅ VTK mapper configured for colors")
                if cell_colors:
                    print(f"Using cell colors: {cell_colors.GetNumberOfTuples()} colors")
                if point_colors:
                    print(f"Using point colors: {point_colors.GetNumberOfTuples()} colors")
                print("✅ Applied exact STEP file colors")
            else:
                print("No colors found, using default gray color")
                # Use default gray color
                self.step_actor.GetProperty().SetColor(0.7, 0.7, 0.7)  # Light gray
                mapper.SetScalarVisibility(False)

            # Check if model is very small and scale it up
            bounds = polydata.GetBounds()
            max_dimension = max(bounds[1]-bounds[0], bounds[3]-bounds[2], bounds[5]-bounds[4])
            print(f"Model max dimension: {max_dimension}")

            if max_dimension < 0.01:  # Very small model
                scale_factor = 100.0  # Scale up 100x
                print(f"Model is very small ({max_dimension}), scaling up by {scale_factor}x")
                self.step_actor.SetScale(scale_factor, scale_factor, scale_factor)

            # Check if model is very small and scale it up
            bounds = polydata.GetBounds()
            max_dimension = max(bounds[1]-bounds[0], bounds[3]-bounds[2], bounds[5]-bounds[4])
            print(f"Model max dimension: {max_dimension}")

            if max_dimension < 0.01:  # Very small model
                scale_factor = 100.0  # Scale up 100x
                print(f"Model is very small ({max_dimension}), scaling up by {scale_factor}x")
                self.step_actor.SetScale(scale_factor, scale_factor, scale_factor)
                # Use scaled dimension for marker
                scaled_dimension = max_dimension * scale_factor
            else:
                scaled_dimension = max_dimension

            # DO NOT center the model - keep original STEP file coordinates
            bounds = polydata.GetBounds()
            print(f"Model bounds (original STEP coordinates): {bounds}")

            # Keep model at its original position from STEP file
            # The model should be displayed exactly as it was designed
            print(f"🔍 VTK DEBUG: Adding step_actor to renderer: {self.step_actor}")
            self.renderer.AddActor(self.step_actor)
            print(f"🔍 VTK DEBUG: step_actor added successfully")

            # Add origin marker scaled to SCALED part size
            self.add_origin_marker(scaled_dimension)

            # Force camera to show the full model
            self.fit_view_aggressive()

            # Force multiple renders to ensure everything is displayed
            self.render_window.Render()
            self.renderer.Render()
            self.render_window.Render()

            print(f"🔍 VTK DEBUG: display_polydata() COMPLETED SUCCESSFULLY")
            print(f"🔍 VTK DEBUG: Final step_actor state: {self.step_actor}")
            return True
        except Exception as e:
            print(f"Display error: {e}")
            return False
    
    def fit_view(self):
        if self.renderer:
            # Get all actors bounds for better camera positioning
            bounds = [0, 0, 0, 0, 0, 0]
            if self.step_actor:
                bounds = self.step_actor.GetBounds()

            # Reset camera with proper bounds
            self.renderer.ResetCamera(bounds)

            # Add some padding around the model
            camera = self.renderer.GetActiveCamera()
            camera.Zoom(0.8)  # Zoom out a bit to show full model

            self.render_window.Render()

    def fit_view_aggressive(self):
        """Fit the view to show the complete model at its original STEP coordinates"""
        if self.renderer and self.step_actor:
            # Get the bounds of the step actor (original STEP coordinates)
            bounds = self.step_actor.GetBounds()
            print(f"Fitting view to model bounds: {bounds}")

            # Calculate center and size of the model
            center = [(bounds[1] + bounds[0]) / 2.0,
                     (bounds[3] + bounds[2]) / 2.0,
                     (bounds[5] + bounds[4]) / 2.0]

            max_size = max(bounds[1] - bounds[0],
                          bounds[3] - bounds[2],
                          bounds[5] - bounds[4])

            # Use VTK's automatic camera positioning first
            self.renderer.ResetCamera(bounds)

            # Then adjust for better viewing
            camera = self.renderer.GetActiveCamera()

            # Move camera back to see both model and origin
            distance = max_size * 3  # Distance from model

            # Position camera at an isometric-like view
            camera.SetPosition(center[0] + distance,
                             center[1] - distance,
                             center[2] + distance * 0.7)
            camera.SetFocalPoint(center[0], center[1], center[2])
            camera.SetViewUp(0, 0, 1)

            # Use VTK's automatic camera reset and then zoom out to ensure full model
            self.renderer.ResetCamera(bounds)
            camera.Zoom(0.9)  # Slight zoom out to ensure full model is visible

            # Set up coordinate system to match user's expectation:
            # X: West to East (left to right)
            # Y: North to South (top to bottom)
            # Z: Out of screen toward viewer

            # VTK default is usually: X=right, Y=up, Z=toward viewer
            # We may need to rotate the view to match your coordinate system
            print(f"VTK Camera up vector: {camera.GetViewUp()}")
            print(f"VTK Camera view direction: {camera.GetDirectionOfProjection()}")

            # Try setting camera to look down from above (top-down view)
            camera.SetPosition(center[0], center[1], center[2] + max_size * 2)
            camera.SetFocalPoint(center[0], center[1], center[2])
            camera.SetViewUp(0, 1, 0)  # Y-axis points "up" on screen (north to south)

            print(f"Camera positioned at: {camera.GetPosition()}")
            print(f"Camera looking at: {camera.GetFocalPoint()}")
            print(f"Model center: {center}")
            origin_distance = (center[0]**2 + center[1]**2 + center[2]**2)**0.5
            print(f"Distance from origin to model: {origin_distance:.3f}")

    def get_camera_orientation(self):
        try:
            camera = self.renderer.GetActiveCamera()
            position = camera.GetPosition()
            focal_point = camera.GetFocalPoint()
            
            dx = focal_point[0] - position[0]
            dy = focal_point[1] - position[1]
            dz = focal_point[2] - position[2]
            
            x_rot = math.degrees(math.atan2(dy, dz))
            y_rot = math.degrees(math.atan2(dx, dz))
            z_rot = math.degrees(math.atan2(dy, dx))
            
            return {'x': x_rot, 'y': y_rot, 'z': z_rot}
        except:
            return {'x': 0.0, 'y': 0.0, 'z': 0.0}
    
    def clear_view(self):
        if self.renderer:
            self.renderer.RemoveAllViewProps()
            # Reset actors
            self.step_actor = None
            self.bbox_actor = None
            self.origin_actor = None  # Reset origin actor so it can be re-added
            # Reset camera to default position
            camera = self.renderer.GetActiveCamera()
            camera.SetPosition(0, 0, 1)
            camera.SetFocalPoint(0, 0, 0)
            camera.SetViewUp(0, 1, 0)
            self.renderer.ResetCamera()
            self.render_window.Render()

    def toggle_bounding_box(self, show):
        """Toggle bounding box display"""
        if not self.step_actor or not self.renderer:
            return

        if show:
            # Create bounding box if it doesn't exist
            if not self.bbox_actor:
                # Get bounds from the step actor
                bounds = self.step_actor.GetBounds()

                # Create outline filter
                outline = vtk.vtkOutlineFilter()

                # Create a simple box source with the bounds
                box = vtk.vtkCubeSource()
                box.SetBounds(bounds)
                outline.SetInputConnection(box.GetOutputPort())

                # Create mapper and actor for bounding box
                bbox_mapper = vtk.vtkPolyDataMapper()
                bbox_mapper.SetInputConnection(outline.GetOutputPort())

                self.bbox_actor = vtk.vtkActor()
                self.bbox_actor.SetMapper(bbox_mapper)
                self.bbox_actor.GetProperty().SetColor(1.0, 0.0, 0.0)  # Red color
                self.bbox_actor.GetProperty().SetLineWidth(2)
                self.bbox_actor.GetProperty().SetRepresentationToWireframe()

            # Add bounding box to renderer
            self.renderer.AddActor(self.bbox_actor)
        else:
            # Remove bounding box from renderer
            if self.bbox_actor:
                self.renderer.RemoveActor(self.bbox_actor)

        self.render_window.Render()

    def add_origin_marker(self, part_size=None):
        """Add small red hemisphere marker at (0,0,0) scaled to part size"""
        if self.origin_actor:
            return  # Already added

        # Make marker small but visible
        if part_size:
            marker_radius = part_size * 0.05  # 5% of part size (small but visible)
        else:
            marker_radius = 0.2  # Small default size

        # Force reasonable size limits
        if marker_radius < 0.1:
            marker_radius = 0.1
        elif marker_radius > 1.0:
            marker_radius = 1.0

        # Override - force small marker size
        marker_radius = 0.3  # Fixed small size
        print(f"Creating red hemisphere origin marker with radius: {marker_radius}")

        # Create a proper red hemisphere sitting on the XY plane at origin (0,0,0)
        sphere = vtk.vtkSphereSource()
        sphere.SetCenter(0, 0, 0)  # Center at origin
        sphere.SetRadius(marker_radius)
        sphere.SetPhiResolution(20)
        sphere.SetThetaResolution(20)
        sphere.SetStartPhi(0)    # Start at north pole
        sphere.SetEndPhi(90)     # End at equator (creates top hemisphere)

        # Apply transform to position hemisphere properly
        transform = vtk.vtkTransform()
        transform.Translate(0, 0, marker_radius)  # Move up so bottom touches origin

        transform_filter = vtk.vtkTransformPolyDataFilter()
        transform_filter.SetInputConnection(sphere.GetOutputPort())
        transform_filter.SetTransform(transform)

        # Apply transform to flip it to be a top hemisphere
        transform = vtk.vtkTransform()
        transform.RotateX(180)  # Flip upside down to make it a top hemisphere

        transform_filter = vtk.vtkTransformPolyDataFilter()
        transform_filter.SetInputConnection(sphere.GetOutputPort())
        transform_filter.SetTransform(transform)

        # Create mapper and actor
        mapper = vtk.vtkPolyDataMapper()
        mapper.SetInputConnection(transform_filter.GetOutputPort())

        self.origin_actor = vtk.vtkActor()
        self.origin_actor.SetMapper(mapper)
        self.origin_actor.GetProperty().SetColor(1.0, 0.0, 0.0)  # Bright red color
        self.origin_actor.GetProperty().SetOpacity(1.0)  # Fully opaque
        self.origin_actor.GetProperty().SetAmbient(0.3)   # Some ambient lighting
        self.origin_actor.GetProperty().SetDiffuse(0.7)   # Good diffuse reflection

        # Position origin marker at the actual coordinate origin (0,0,0)
        # Make sure it's visible by positioning it correctly
        self.origin_actor.SetPosition(0, 0, 0)

        # Make sure the origin marker is always visible by adjusting its size if needed
        bounds = self.step_actor.GetBounds() if self.step_actor else None
        if bounds:
            model_center = [(bounds[0] + bounds[1])/2, (bounds[2] + bounds[3])/2, (bounds[4] + bounds[5])/2]
            distance_from_origin = (model_center[0]**2 + model_center[1]**2 + model_center[2]**2)**0.5
            print(f"Origin marker at (0,0,0), model center at {model_center}, distance: {distance_from_origin:.3f}")
        else:
            print(f"Origin marker positioned at coordinate origin: (0, 0, 0)")

        self.renderer.AddActor(self.origin_actor)
        print(f"Added red hemisphere origin marker with radius {marker_radius}")

        # Add coordinate axes to show X, Y, Z directions
        self.add_coordinate_axes()

    def add_coordinate_axes(self):
        """Add X, Y, Z coordinate axes to show orientation"""
        import vtk

        # Create axes actor
        axes = vtk.vtkAxesActor()
        axes.SetPosition(0, 0, 0)  # Position at origin
        axes.SetTotalLength(2.0, 2.0, 2.0)  # Length of each axis
        axes.SetShaftType(0)  # Cylinder shaft
        axes.SetTipType(0)    # Cone tip

        # Set colors: X=Red, Y=Green, Z=Blue (no text labels)
        axes.GetXAxisCaptionActor2D().GetCaptionTextProperty().SetColor(1, 0, 0)
        axes.GetYAxisCaptionActor2D().GetCaptionTextProperty().SetColor(0, 1, 0)
        axes.GetZAxisCaptionActor2D().GetCaptionTextProperty().SetColor(0, 0, 1)

        # Hide the text labels
        axes.SetXAxisLabelText("")
        axes.SetYAxisLabelText("")
        axes.SetZAxisLabelText("")

        self.renderer.AddActor(axes)
        print("Added coordinate axes: X=Red (West-East), Y=Green (North-South), Z=Blue (Out of screen)")

    def update_bounding_box(self):
        """Update bounding box to match current model rotation"""
        if self.bbox_actor and self.step_actor:
            # Remove old bounding box
            self.renderer.RemoveActor(self.bbox_actor)

            # Get the transformed bounds from the step actor
            bounds = self.step_actor.GetBounds()

            # Create outline directly from the step actor's polydata
            outline = vtk.vtkOutlineFilter()
            outline.SetInputData(self.step_actor.GetMapper().GetInput())

            # Create mapper and actor for bounding box
            bbox_mapper = vtk.vtkPolyDataMapper()
            bbox_mapper.SetInputConnection(outline.GetOutputPort())

            # Create new bounding box actor
            self.bbox_actor = vtk.vtkActor()
            self.bbox_actor.SetMapper(bbox_mapper)

            # Apply the same transform as the step actor
            if self.step_actor.GetUserTransform():
                self.bbox_actor.SetUserTransform(self.step_actor.GetUserTransform())
            self.bbox_actor.SetOrientation(self.step_actor.GetOrientation())
            self.bbox_actor.SetPosition(self.step_actor.GetPosition())

            # Set bounding box properties
            self.bbox_actor.GetProperty().SetColor(1.0, 0.0, 0.0)  # Red color
            self.bbox_actor.GetProperty().SetLineWidth(3)
            self.bbox_actor.GetProperty().SetRepresentationToWireframe()

            # Add updated bounding box
            self.renderer.AddActor(self.bbox_actor)
            print("Updated bounding box to follow model rotation")

