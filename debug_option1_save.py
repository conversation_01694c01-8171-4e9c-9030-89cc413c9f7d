#!/usr/bin/env python3
"""
Debug script to trace what happens during Option 1 save
"""

import sys
import os

# Add debug prints to the save process
def debug_option1_save():
    """Debug the Option 1 save process"""
    
    print("🔍 DEBUG: Analyzing Option 1 save process...")
    
    # Check if the main program is running
    try:
        # Import the main program module
        sys.path.append('.')
        
        # Look for the running program's variables
        print("🔍 DEBUG: Checking for program state...")
        
        # Check if there are any saved debug files
        debug_files = [f for f in os.listdir('.') if 'debug' in f.lower() and f.endswith('.txt')]
        if debug_files:
            print(f"🔍 DEBUG: Found debug files: {debug_files}")
        
        # Check for recently saved STEP files
        step_files = [f for f in os.listdir('.') if f.endswith('.step') or f.endswith('.STEP')]
        recent_files = []
        
        import time
        current_time = time.time()
        
        for f in step_files:
            try:
                file_time = os.path.getmtime(f)
                age_minutes = (current_time - file_time) / 60
                if age_minutes < 10:  # Files modified in last 10 minutes
                    recent_files.append((f, age_minutes))
            except:
                pass
        
        if recent_files:
            print(f"🔍 DEBUG: Recently modified STEP files:")
            for filename, age in sorted(recent_files, key=lambda x: x[1]):
                print(f"   {filename} (modified {age:.1f} minutes ago)")
        
        # Check the main program file for the save logic
        print(f"🔍 DEBUG: Analyzing save logic in step_viewer_tdk_modular.py...")
        
        with open('step_viewer_tdk_modular.py', 'r') as f:
            content = f.read()
        
        # Look for the delta calculation
        if 'delta_rot = {' in content:
            print(f"✅ Found delta rotation calculation in save logic")
        else:
            print(f"❌ Delta rotation calculation not found!")
        
        # Look for the transformation methods
        methods = [
            '_save_step_opencascade_transform',
            '_save_step_text_transform', 
            '_save_as_stl_fallback'
        ]
        
        for method in methods:
            if f'def {method}' in content:
                print(f"✅ Found method: {method}")
            else:
                print(f"❌ Missing method: {method}")
        
        print(f"\n🔍 DEBUG: The issue is likely:")
        print(f"1. Delta calculation not capturing current rotation correctly")
        print(f"2. Transformation methods failing silently") 
        print(f"3. Current rotation values not being updated properly")
        
        print(f"\n💡 SOLUTION: We need to add debug prints to the save process")
        print(f"   to see exactly what values are being calculated.")
        
    except Exception as e:
        print(f"❌ DEBUG: Error analyzing save process: {e}")

if __name__ == "__main__":
    debug_option1_save()
