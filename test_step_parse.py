#!/usr/bin/env python3
"""
Test STEP file parsing to see what's actually in the file
"""

filename = "E:/Python/3d-models/SOIC16P127_1270X940X610L89X51.STEP"

print("=== STEP FILE PARSING TEST ===")
print(f"Reading: {filename}")

try:
    with open(filename, 'r') as f:
        lines = f.readlines()
    
    print(f"Total lines: {len(lines)}")
    
    # Look for lines containing CARTESIAN_POINT
    cartesian_lines = []
    for i, line in enumerate(lines):
        if 'CARTESIAN_POINT' in line:
            cartesian_lines.append((i+1, line.strip()))
    
    print(f"\nFound {len(cartesian_lines)} CARTESIAN_POINT lines:")
    for line_num, line in cartesian_lines[:10]:  # Show first 10
        print(f"Line {line_num}: {line}")
    
    # Look for lines containing DIRECTION
    direction_lines = []
    for i, line in enumerate(lines):
        if 'DIRECTION' in line:
            direction_lines.append((i+1, line.strip()))
    
    print(f"\nFound {len(direction_lines)} DIRECTION lines:")
    for line_num, line in direction_lines[:10]:  # Show first 10
        print(f"Line {line_num}: {line}")
    
    # Look for AXIS2_PLACEMENT_3D
    axis_lines = []
    for i, line in enumerate(lines):
        if 'AXIS2_PLACEMENT_3D' in line:
            axis_lines.append((i+1, line.strip()))
    
    print(f"\nFound {len(axis_lines)} AXIS2_PLACEMENT_3D lines:")
    for line_num, line in axis_lines[:10]:  # Show first 10
        print(f"Line {line_num}: {line}")
        
    # Show first 20 lines of file to see format
    print(f"\nFirst 20 lines of STEP file:")
    for i, line in enumerate(lines[:20]):
        print(f"{i+1:3d}: {line.rstrip()}")

except Exception as e:
    print(f"Error: {e}")
