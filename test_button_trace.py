#!/usr/bin/env python3
"""
Test Button Trace - Verify button calls reset function
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular import StepViewerTDK

def test_button_trace():
    """Test that button actually calls reset function"""
    
    print("🔧 TEST BUTTON TRACE")
    print("=" * 40)
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    # Process events to ensure GUI is ready
    app.processEvents()
    time.sleep(1)
    
    # Find a STEP file to test with
    test_files = [
        "AMPHENOL_U77-A1118-200T.STEP",
        "test.step",
        "sample.stp"
    ]
    
    test_file = None
    for filename in test_files:
        if os.path.exists(filename):
            test_file = filename
            break
    
    if not test_file:
        print("❌ No STEP file found for testing")
        return False
    
    print(f"📁 Using test file: {test_file}")
    
    # Step 1: Load model
    print(f"\n📋 STEP 1: LOADING MODEL...")
    success = viewer.load_step_file_direct(test_file)
    app.processEvents()
    time.sleep(2)
    
    if not success:
        print("❌ Failed to load STEP file")
        return False
    
    # Step 2: Apply transformation
    print(f"\n📋 STEP 2: APPLYING TRANSFORMATION...")
    
    viewer.active_viewer = "top"
    
    print(f"🔧 Moving +100mm X")
    viewer.move_shape("x", 100)
    app.processEvents()
    time.sleep(1)
    
    print(f"🔧 Rotating +90° Z")
    viewer.rotate_shape("z", 90)
    app.processEvents()
    time.sleep(1)
    
    # Step 3: Patch reset function to add detailed tracing
    original_reset = viewer.reset_to_original
    
    def traced_reset():
        print(f"\n🔧 TRACED RESET: Button clicked - reset_to_original() called!")
        print(f"🔧 TRACED RESET: About to call original reset function...")
        
        # Call original reset
        result = original_reset()
        
        print(f"🔧 TRACED RESET: Original reset function completed")
        return result
    
    # Replace with traced version
    viewer.reset_to_original = traced_reset
    
    # Step 4: Test direct function call
    print(f"\n📋 STEP 4: TESTING DIRECT FUNCTION CALL...")
    
    print(f"🔧 Calling reset function directly...")
    viewer.reset_to_original()
    app.processEvents()
    time.sleep(2)
    
    # Step 5: Test button click simulation
    print(f"\n📋 STEP 5: TESTING BUTTON CLICK SIMULATION...")
    
    # Re-apply transformation first
    print(f"Re-applying transformation...")
    viewer.move_shape("x", 100)
    viewer.rotate_shape("z", 90)
    app.processEvents()
    time.sleep(1)
    
    # Find the reset button and click it
    print(f"🔧 Finding and clicking reset button...")
    
    # Look for the reset button in the dock widget
    from PyQt5.QtWidgets import QDockWidget, QPushButton
    dock_widget = None
    for child in viewer.findChildren(QDockWidget):
        if "Tool" in child.windowTitle():
            dock_widget = child
            break
    
    if dock_widget:
        print(f"Found dock widget: {dock_widget.windowTitle()}")
        
        # Find the reset button
        reset_button = None
        for button in dock_widget.findChildren(QPushButton):
            if "Reset to Original" in button.text():
                reset_button = button
                break
        
        if reset_button:
            print(f"Found reset button: {reset_button.text()}")
            print(f"🔧 Clicking reset button...")
            reset_button.click()
            app.processEvents()
            time.sleep(2)
            print(f"🔧 Button click completed")
        else:
            print(f"❌ Reset button not found")
            # List all buttons
            buttons = dock_widget.findChildren(QPushButton)
            print(f"Available buttons: {[btn.text() for btn in buttons]}")
    else:
        print(f"❌ Dock widget not found")
        # List all dock widgets
        docks = viewer.findChildren(QDockWidget)
        print(f"Available dock widgets: {[dock.windowTitle() for dock in docks]}")
    
    print(f"\n🎯 RESULTS:")
    print(f"1. Did direct function call work?")
    print(f"2. Did button click work?")
    print(f"3. Check console output for reset traces")
    
    # Keep window open
    print(f"\n👁️ Window will stay open for inspection...")
    QTimer.singleShot(15000, app.quit)
    app.exec_()
    
    return True

if __name__ == "__main__":
    test_button_trace()
