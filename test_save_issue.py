#!/usr/bin/env python3
"""
Test script to reproduce the save issue and geometry difference
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular import <PERSON><PERSON>iewerTDK

def test_save_issue():
    """Test the save functionality and geometry differences"""
    print("🔧 TESTING: Starting save issue test...")
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.show()
    
    # Load test file
    test_file = 'test.step'
    if os.path.exists(test_file):
        print(f"🔧 TESTING: Loading test file: {test_file}")
        
        # Load file in both viewers
        viewer.active_viewer = "top"
        viewer.load_step_file_direct(test_file)

        viewer.active_viewer = "bottom"
        viewer.load_step_file_direct(test_file)
        
        print("🔧 TESTING: Both viewers loaded")
        
        # Test save after a short delay
        def test_save():
            print("🔧 TESTING: Testing save functionality with rotation...")

            # Set active viewer to top
            viewer.active_viewer = "top"

            # First, apply some rotation to the top viewer
            print("🔧 TESTING: Applying rotation to top viewer...")
            try:
                # Simulate rotating the model by 45 degrees in Z
                if hasattr(viewer, 'current_rot_left'):
                    original_rot = viewer.current_rot_left.copy()
                    print(f"🔧 TESTING: Original rotation: {original_rot}")

                    # Apply additional 45° rotation in Z
                    viewer.current_rot_left['z'] += 45.0
                    print(f"🔧 TESTING: New rotation: {viewer.current_rot_left}")

                    # Update the display to show the new rotation
                    if hasattr(viewer, 'update_transform_display'):
                        viewer.update_transform_display()
                        print("🔧 TESTING: Updated transform display")

            except Exception as e:
                print(f"❌ TESTING: Failed to apply rotation: {e}")

            # Test the actual save method directly (bypass dialogs)
            try:
                print("🔧 TESTING: Testing step_loader.save_step_file() with rotation...")

                # Get the loader
                loader = viewer.step_loader_left

                # Create a test filename for rotated version
                test_save_file = "test_output_rotated.step"

                # Create a transformation matrix for the rotation
                import vtk
                transform = vtk.vtkTransform()
                current_rot = viewer.current_rot_left if hasattr(viewer, 'current_rot_left') else {'x': 0, 'y': 0, 'z': 0}
                current_pos = viewer.current_pos_left if hasattr(viewer, 'current_pos_left') else {'x': 0, 'y': 0, 'z': 0}

                print(f"🔧 TESTING: Using position: {current_pos}")
                print(f"🔧 TESTING: Using rotation: {current_rot}")

                transform.Translate(current_pos['x'], current_pos['y'], current_pos['z'])
                transform.RotateX(current_rot['x'])
                transform.RotateY(current_rot['y'])
                transform.RotateZ(current_rot['z'])

                # Test save with transformation matrix
                print("🔧 TESTING: Calling loader.save_step_file() with transform matrix...")
                success = loader.save_step_file(test_save_file, transform.GetMatrix())

                if success:
                    print("✅ TESTING: Rotated save_step_file() completed successfully")

                    # Check if file was created
                    import os
                    if os.path.exists(test_save_file):
                        size = os.path.getsize(test_save_file)
                        print(f"✅ TESTING: Rotated file created successfully, size: {size} bytes")
                    else:
                        print("❌ TESTING: Rotated file was not created")
                else:
                    print("❌ TESTING: Rotated save_step_file() returned False")

            except Exception as e:
                print(f"❌ TESTING: Rotated save_step_file() failed with error: {e}")
                import traceback
                traceback.print_exc()

            # Close after test
            QTimer.singleShot(2000, app.quit)
        
        # Run save test after 3 seconds
        QTimer.singleShot(3000, test_save)
        
    else:
        print(f"❌ TESTING: Test file not found: {test_file}")
        app.quit()
        return
    
    # Run the application
    app.exec_()
    print("🔧 TESTING: Test completed")

if __name__ == "__main__":
    test_save_issue()
