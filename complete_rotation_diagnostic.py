#!/usr/bin/env python3
"""
Complete Rotation Diagnostic - Find and fix the rotation save/load issue once and for all
"""

import sys
import os
import time
import math
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main program
sys.path.append('.')
from step_viewer_tdk_modular import StepViewerTDK

class CompleteRotationDiagnostic:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        self.test_results = []
        self.step = 0
        
    def run_diagnostic(self):
        """Run complete diagnostic"""
        print("🔧 COMPLETE ROTATION DIAGNOSTIC")
        print("=" * 80)
        print("This will systematically test and fix the rotation save/load issue")
        print("=" * 80)
        
        # Create viewer
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        # Start diagnostic sequence
        QTimer.singleShot(1000, self.step1_load_original)
        
        # Run the application
        self.app.exec_()
        
    def step1_load_original(self):
        """Step 1: Load original file and capture baseline"""
        print("\n🔄 STEP 1: Loading original test.step...")
        
        if not os.path.exists('test.step'):
            print("❌ test.step not found!")
            return
            
        success = self.viewer.load_step_file_direct('test.step')
        if success:
            print("✅ Original file loaded")
            
            # Capture original values
            self.original_values = self.capture_current_state("TOP")
            print(f"📊 ORIGINAL VALUES: {self.original_values}")
            
            QTimer.singleShot(2000, self.step2_apply_test_rotation)
        else:
            print("❌ Failed to load original file")
            
    def step2_apply_test_rotation(self):
        """Step 2: Apply a specific test rotation"""
        print("\n🔄 STEP 2: Applying test rotation...")
        
        # Apply a specific rotation for testing
        test_rotation = {'x': 30.0, 'y': 45.0, 'z': 60.0}
        
        try:
            # Set the viewer to top
            self.viewer.active_viewer = "top"
            
            # Apply rotations step by step
            self.viewer.rotate_shape("x", test_rotation['x'])
            self.viewer.rotate_shape("y", test_rotation['y']) 
            self.viewer.rotate_shape("z", test_rotation['z'])
            
            # Update display
            self.viewer.update_text_overlays()
            
            # Capture rotated values
            self.rotated_values = self.capture_current_state("TOP")
            print(f"📊 AFTER ROTATION: {self.rotated_values}")
            
            QTimer.singleShot(2000, self.step3_save_file)
            
        except Exception as e:
            print(f"❌ Rotation failed: {e}")
            
    def step3_save_file(self):
        """Step 3: Save the rotated file"""
        print("\n🔄 STEP 3: Saving rotated file...")
        
        try:
            # Get current values before save
            current_pos = getattr(self.viewer, 'current_pos_left', {'x': 0, 'y': 0, 'z': 0})
            current_rot = getattr(self.viewer, 'current_rot_left', {'x': 0, 'y': 0, 'z': 0})
            orig_pos = getattr(self.viewer, 'orig_pos_left', {'x': 0, 'y': 0, 'z': 0})
            orig_rot = getattr(self.viewer, 'orig_rot_left', {'x': 0, 'y': 0, 'z': 0})
            
            print(f"🔍 BEFORE SAVE:")
            print(f"   Current Pos: {current_pos}")
            print(f"   Current Rot: {current_rot}")
            print(f"   Original Pos: {orig_pos}")
            print(f"   Original Rot: {orig_rot}")
            
            # Save using the improved method
            save_filename = "diagnostic_test.step"
            loader = self.viewer.step_loader_left
            
            success = self.viewer._save_step_with_transformations(
                save_filename, loader, current_pos, current_rot, orig_pos, orig_rot
            )
            
            if success and os.path.exists(save_filename):
                file_size = os.path.getsize(save_filename)
                print(f"✅ File saved: {save_filename} ({file_size} bytes)")
                self.saved_filename = save_filename
                QTimer.singleShot(2000, self.step4_load_saved_file)
            else:
                print("❌ Save failed")
                QTimer.singleShot(1000, self.step6_analyze_results)
                
        except Exception as e:
            print(f"❌ Save error: {e}")
            import traceback
            traceback.print_exc()
            QTimer.singleShot(1000, self.step6_analyze_results)
            
    def step4_load_saved_file(self):
        """Step 4: Load the saved file in bottom viewer"""
        print("\n🔄 STEP 4: Loading saved file in bottom viewer...")
        
        try:
            # Switch to bottom viewer
            self.viewer.active_viewer = "bottom"
            
            # Load the saved file
            success = self.viewer.load_step_file_direct(self.saved_filename)
            
            if success:
                print("✅ Saved file loaded in bottom viewer")
                QTimer.singleShot(3000, self.step5_compare_values)
            else:
                print("❌ Failed to load saved file")
                QTimer.singleShot(1000, self.step6_analyze_results)
                
        except Exception as e:
            print(f"❌ Load error: {e}")
            QTimer.singleShot(1000, self.step6_analyze_results)
            
    def step5_compare_values(self):
        """Step 5: Compare the values"""
        print("\n🔄 STEP 5: Comparing values...")
        
        # Capture loaded values
        self.loaded_values = self.capture_current_state("BOTTOM")
        print(f"📊 LOADED VALUES: {self.loaded_values}")
        
        QTimer.singleShot(1000, self.step6_analyze_results)
        
    def step6_analyze_results(self):
        """Step 6: Analyze results and identify issues"""
        print("\n🔄 STEP 6: ANALYZING RESULTS...")
        print("=" * 80)
        
        if hasattr(self, 'original_values'):
            print(f"📊 ORIGINAL:  {self.original_values}")
        if hasattr(self, 'rotated_values'):
            print(f"📊 ROTATED:   {self.rotated_values}")
        if hasattr(self, 'loaded_values'):
            print(f"📊 LOADED:    {self.loaded_values}")
            
        # Check if rotation was preserved
        if hasattr(self, 'rotated_values') and hasattr(self, 'loaded_values'):
            rot_diff_x = abs(self.rotated_values['rotation']['x'] - self.loaded_values['rotation']['x'])
            rot_diff_y = abs(self.rotated_values['rotation']['y'] - self.loaded_values['rotation']['y'])
            rot_diff_z = abs(self.rotated_values['rotation']['z'] - self.loaded_values['rotation']['z'])
            
            print(f"\n🔍 ROTATION DIFFERENCES:")
            print(f"   X: {rot_diff_x:.2f}°")
            print(f"   Y: {rot_diff_y:.2f}°")
            print(f"   Z: {rot_diff_z:.2f}°")
            
            if rot_diff_x < 1.0 and rot_diff_y < 1.0 and rot_diff_z < 1.0:
                print("✅ ROTATION PRESERVED CORRECTLY!")
            else:
                print("❌ ROTATION NOT PRESERVED - ISSUE IDENTIFIED")
                self.diagnose_rotation_issue()
        
        print("=" * 80)
        print("🎯 DIAGNOSTIC COMPLETE")
        
        # Close after a delay
        QTimer.singleShot(5000, self.app.quit)
        
    def capture_current_state(self, viewer_name):
        """Capture current state of a viewer"""
        try:
            if viewer_name == "TOP":
                current_pos = getattr(self.viewer, 'current_pos_left', {'x': 0, 'y': 0, 'z': 0})
                current_rot = getattr(self.viewer, 'current_rot_left', {'x': 0, 'y': 0, 'z': 0})
                orig_pos = getattr(self.viewer, 'orig_pos_left', {'x': 0, 'y': 0, 'z': 0})
                orig_rot = getattr(self.viewer, 'orig_rot_left', {'x': 0, 'y': 0, 'z': 0})
            else:
                current_pos = getattr(self.viewer, 'current_pos_right', {'x': 0, 'y': 0, 'z': 0})
                current_rot = getattr(self.viewer, 'current_rot_right', {'x': 0, 'y': 0, 'z': 0})
                orig_pos = getattr(self.viewer, 'orig_pos_right', {'x': 0, 'y': 0, 'z': 0})
                orig_rot = getattr(self.viewer, 'orig_rot_right', {'x': 0, 'y': 0, 'z': 0})
                
            return {
                'position': current_pos,
                'rotation': current_rot,
                'orig_position': orig_pos,
                'orig_rotation': orig_rot
            }
        except Exception as e:
            print(f"❌ Error capturing state: {e}")
            return None
            
    def diagnose_rotation_issue(self):
        """Diagnose the specific rotation issue"""
        print("\n🔍 DIAGNOSING ROTATION ISSUE...")
        
        # Check if the saved file exists and analyze it
        if hasattr(self, 'saved_filename') and os.path.exists(self.saved_filename):
            print(f"🔍 Analyzing saved file: {self.saved_filename}")
            
            # Check file format
            with open(self.saved_filename, 'r') as f:
                first_line = f.readline().strip()
                if first_line.startswith('ISO-10303-21'):
                    print("✅ File is in STEP format")
                else:
                    print(f"❌ File is not in STEP format: {first_line}")
                    
            # Check for rotation comments
            with open(self.saved_filename, 'r') as f:
                content = f.read()
                if 'ROTATION_VALUES:' in content:
                    print("✅ Rotation values stored in file")
                else:
                    print("❌ No rotation values stored in file")
                    
        else:
            print("❌ Saved file not found for analysis")

if __name__ == "__main__":
    diagnostic = CompleteRotationDiagnostic()
    diagnostic.run_diagnostic()
