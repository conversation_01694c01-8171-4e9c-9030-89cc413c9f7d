#!/usr/bin/env python3

print("ANALYZING WHICH STEP IDS HAVE DARK COLORS")

import re

# Read STEP file as text
with open('SOIC16P127_1270X940X610L89X51.STEP', 'r', encoding='utf-8', errors='ignore') as f:
    step_text = f.read()

lines = step_text.split('\n')

# Parse color definitions and reference chains (copied from step_loader.py)
color_definitions = {}
reference_chain = {}

# Find all COLOUR_RGB definitions
for line in lines:
    if 'COLOUR_RGB' in line:
        match = re.search(r'#(\d+)\s*=.*?COLOUR_RGB.*?\'\'?,\s*(\d+\.?\d*),\s*(\d+\.?\d*),\s*(\d+\.?\d*)', line)
        if match:
            color_id = match.group(1)
            r = int(float(match.group(2)) * 255)
            g = int(float(match.group(3)) * 255)
            b = int(float(match.group(4)) * 255)
            color_definitions[color_id] = (r, g, b)

# Build reference chain
for line in lines:
    if '=' in line and '#' in line:
        # Look for patterns like #123 = ... #456 ...
        matches = re.findall(r'#(\d+)', line)
        if len(matches) >= 2:
            from_id = matches[0]
            to_id = matches[1]
            reference_chain[from_id] = to_id

# Function to trace color reference chain
def trace_to_color(ref_id):
    visited = set()
    current = ref_id
    while current and current not in visited:
        visited.add(current)
        if current in color_definitions:
            return color_definitions[current]
        if current in reference_chain:
            current = reference_chain[current]
        else:
            break
    return None

# Debug: Check if we find STYLED_ITEM lines
styled_item_count = 0
for line in lines:
    if 'STYLED_ITEM' in line:
        styled_item_count += 1

print(f"Found {styled_item_count} STYLED_ITEM lines")
print(f"Found {len(color_definitions)} color definitions")
print(f"Found {len(reference_chain)} reference chain entries")

# Create STEP geometry ID to color mapping
step_id_to_color = {}
for line in lines:
    if 'STYLED_ITEM' in line:
        match = re.search(r'STYLED_ITEM.*?#(\d+).*?#(\d+)', line)
        if match:
            color_ref = match.group(1)
            geometry_id = int(match.group(2))
            actual_color = trace_to_color(color_ref)
            if actual_color:
                step_id_to_color[geometry_id] = actual_color

print(f"Found {len(step_id_to_color)} STEP geometry-to-color mappings")

# Find which STEP IDs have dark colors
dark_step_ids = []
light_step_ids = []

for step_id, color in step_id_to_color.items():
    if color == (63, 63, 63):  # Dark color
        dark_step_ids.append(step_id)
    elif color == (192, 192, 192):  # Light color
        light_step_ids.append(step_id)

print(f"\nDARK COLORS (63, 63, 63):")
print(f"Count: {len(dark_step_ids)}")
print(f"STEP IDs: {sorted(dark_step_ids)}")

print(f"\nLIGHT COLORS (192, 192, 192):")
print(f"Count: {len(light_step_ids)}")
print(f"First 10 STEP IDs: {sorted(light_step_ids)[:10]}")
print(f"Last 10 STEP IDs: {sorted(light_step_ids)[-10:]}")

# Show the sorted order
all_step_ids = sorted(step_id_to_color.keys())
print(f"\nFIRST 20 STEP IDs IN SORTED ORDER:")
for i, step_id in enumerate(all_step_ids[:20]):
    color = step_id_to_color[step_id]
    color_type = "DARK" if color == (63, 63, 63) else "LIGHT"
    print(f"  {i}: STEP ID #{step_id} = {color_type} {color}")

print(f"\nLAST 20 STEP IDs IN SORTED ORDER:")
for i, step_id in enumerate(all_step_ids[-20:]):
    color = step_id_to_color[step_id]
    color_type = "DARK" if color == (63, 63, 63) else "LIGHT"
    actual_index = len(all_step_ids) - 20 + i
    print(f"  {actual_index}: STEP ID #{step_id} = {color_type} {color}")

print("\nANALYSIS COMPLETE")
