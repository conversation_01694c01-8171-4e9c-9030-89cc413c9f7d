#!/usr/bin/env python3

print("READING STEP TEXT FILE DIRECTLY FOR GEOMETRY AND COLORS")

# Read STEP file as text
with open('SOIC16P127_1270X940X610L89X51.STEP', 'r', encoding='utf-8', errors='ignore') as f:
    step_text = f.read()

lines = step_text.split('\n')

print(f"Total lines in STEP file: {len(lines)}")

# Find geometry entries
geometry_entries = []
for line in lines:
    if 'ADVANCED_FACE' in line:
        geometry_entries.append(line)

print(f"Found {len(geometry_entries)} geometry entries")

# Find color entries  
color_entries = []
for line in lines:
    if 'COLOUR_RGB' in line:
        color_entries.append(line)

print(f"Found {len(color_entries)} color entries")

# Find style entries that connect geometry to colors
style_entries = []
for line in lines:
    if 'STYLED_ITEM' in line:
        style_entries.append(line)

print(f"Found {len(style_entries)} style entries")

# Show first few of each
print("\nFirst 3 geometry entries:")
for i, entry in enumerate(geometry_entries[:3]):
    print(f"{i+1}: {entry}")

print("\nFirst 3 color entries:")
for i, entry in enumerate(color_entries[:3]):
    print(f"{i+1}: {entry}")

print("\nFirst 3 style entries:")
for i, entry in enumerate(style_entries[:3]):
    print(f"{i+1}: {entry}")

print("\nREADING STEP TEXT FILE DIRECTLY COMPLETE")
