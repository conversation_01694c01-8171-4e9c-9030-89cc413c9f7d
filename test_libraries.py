#!/usr/bin/env python3
"""Test if OpenCASCADE and FreeCAD are working"""

print("Testing OpenCASCADE...")
try:
    from OCC.Core.STEPControl_Reader import STEP<PERSON><PERSON>rol_Reader
    from OCC.Core.IFSelect_ReturnStatus import IFSelect_RetDone
    print("✅ OpenCASCADE is working!")
    occ_working = True
except Exception as e:
    print(f"❌ OpenCASCADE error: {e}")
    occ_working = False

print("\nTesting FreeCAD...")
try:
    import FreeCAD
    import Part
    print("✅ FreeCAD is working!")
    print(f"FreeCAD version: {FreeCAD.Version()}")
    freecad_working = True
except Exception as e:
    print(f"❌ FreeCAD error: {e}")
    freecad_working = False

print("\nTesting other required packages...")
try:
    import vtk
    print(f"✅ VTK version: {vtk.vtkVersion.GetVTKVersion()}")
except Exception as e:
    print(f"❌ VTK error: {e}")

try:
    import trimesh
    print(f"✅ Trimesh version: {trimesh.__version__}")
except Exception as e:
    print(f"❌ Trimesh error: {e}")

try:
    from PyQt5.QtWidgets import QApplication
    print("✅ PyQt5 is working!")
except Exception as e:
    print(f"❌ PyQt5 error: {e}")

print(f"\n🎯 Summary:")
print(f"OpenCASCADE: {'✅ Working' if occ_working else '❌ Not working'}")
print(f"FreeCAD: {'✅ Working' if freecad_working else '❌ Not working'}")

if occ_working or freecad_working:
    print("🎉 At least one STEP library is working!")
else:
    print("😞 No STEP libraries are working")
