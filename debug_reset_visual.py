#!/usr/bin/env python3
"""
Debug Reset Visual Test - Load STEP file, rotate, and test reset button
This test will help identify why the visual reset isn't working properly.
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular import StepViewerTDK

def debug_actor_state(viewer, label=""):
    """Debug function to print detailed actor state"""
    print(f"\n🔍 DEBUG ACTOR STATE - {label}")
    print("=" * 50)
    
    renderer = viewer.vtk_renderer_left
    
    # Check multi-actor models
    if hasattr(renderer, 'step_actors') and renderer.step_actors:
        print(f"📊 Multi-actor model: {len(renderer.step_actors)} actors")
        for i, actor in enumerate(renderer.step_actors):
            pos = actor.GetPosition()
            orient = actor.GetOrientation()
            transform = actor.GetUserTransform()
            print(f"   Actor {i}:")
            print(f"     Position: {pos}")
            print(f"     Orientation: {orient}")
            print(f"     UserTransform: {transform}")
            if transform:
                matrix = transform.GetMatrix()
                print(f"     Transform Matrix: {matrix}")
    
    # Check single-actor model
    if hasattr(renderer, 'step_actor') and renderer.step_actor:
        print(f"📊 Single-actor model")
        actor = renderer.step_actor
        pos = actor.GetPosition()
        orient = actor.GetOrientation()
        transform = actor.GetUserTransform()
        print(f"   Position: {pos}")
        print(f"   Orientation: {orient}")
        print(f"   UserTransform: {transform}")
        if transform:
            matrix = transform.GetMatrix()
            print(f"   Transform Matrix: {matrix}")
    
    # Check display values
    print(f"📊 Display Values:")
    print(f"   Position: {viewer.current_pos_left}")
    print(f"   Rotation: {viewer.current_rot_left}")
    
    # Check original transforms storage
    if hasattr(viewer, 'original_actor_transforms_left'):
        print(f"📊 Original Transforms Stored: {len(viewer.original_actor_transforms_left)}")
        for i, orig_state in enumerate(viewer.original_actor_transforms_left):
            print(f"   Original {i}:")
            print(f"     Position: {orig_state['position']}")
            print(f"     Orientation: {orig_state['orientation']}")
            print(f"     Transform: {orig_state['transform']}")

def test_reset_visual():
    """Test the reset functionality with visual debugging"""
    
    print("🔧 VISUAL RESET DEBUG TEST")
    print("=" * 60)
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    # Process events to ensure GUI is ready
    app.processEvents()
    time.sleep(1)
    
    # Find a STEP file to test with
    test_files = [
        "AMPHENOL_U77-A1118-200T.STEP",
        "test.step",
        "sample.stp"
    ]
    
    test_file = None
    for filename in test_files:
        if os.path.exists(filename):
            test_file = filename
            break
    
    if not test_file:
        print("❌ No STEP file found for testing")
        return False
    
    print(f"📁 Using test file: {test_file}")
    
    # Step 1: Load STEP file
    print(f"\n📋 STEP 1: LOADING {test_file}...")
    success = viewer.load_step_file_direct(test_file)
    app.processEvents()
    time.sleep(2)
    
    if not success:
        print("❌ Failed to load STEP file")
        return False
    
    debug_actor_state(viewer, "AFTER LOADING")
    
    # Step 2: Apply rotation
    print(f"\n📋 STEP 2: APPLYING 45° Z ROTATION...")
    
    # Simulate clicking Z+ rotation button
    viewer.active_viewer = "top"
    viewer.rotate_shape("z", 45)
    app.processEvents()
    time.sleep(1)
    
    debug_actor_state(viewer, "AFTER ROTATION")
    
    # Step 3: Apply position movement
    print(f"\n📋 STEP 3: APPLYING 10mm X MOVEMENT...")
    
    # Simulate clicking X+ movement button
    viewer.move_shape("x", 10)
    app.processEvents()
    time.sleep(1)
    
    debug_actor_state(viewer, "AFTER MOVEMENT")
    
    # Step 4: Test reset button
    print(f"\n📋 STEP 4: TESTING RESET BUTTON...")
    
    # Call reset function directly
    print("🔧 Calling reset_to_original()...")
    viewer.reset_to_original()
    app.processEvents()
    time.sleep(1)
    
    debug_actor_state(viewer, "AFTER RESET")
    
    # Step 5: Analysis
    print(f"\n📋 STEP 5: ANALYSIS...")
    
    # Check if reset worked
    renderer = viewer.vtk_renderer_left
    reset_worked = True
    
    # Check actors
    if hasattr(renderer, 'step_actors') and renderer.step_actors:
        for i, actor in enumerate(renderer.step_actors):
            pos = actor.GetPosition()
            orient = actor.GetOrientation()
            if abs(pos[0]) > 0.1 or abs(pos[1]) > 0.1 or abs(pos[2]) > 0.1:
                print(f"❌ Actor {i} position not reset: {pos}")
                reset_worked = False
            if abs(orient[0]) > 0.1 or abs(orient[1]) > 0.1 or abs(orient[2]) > 0.1:
                print(f"❌ Actor {i} orientation not reset: {orient}")
                reset_worked = False
    
    if hasattr(renderer, 'step_actor') and renderer.step_actor:
        actor = renderer.step_actor
        pos = actor.GetPosition()
        orient = actor.GetOrientation()
        if abs(pos[0]) > 0.1 or abs(pos[1]) > 0.1 or abs(pos[2]) > 0.1:
            print(f"❌ Single actor position not reset: {pos}")
            reset_worked = False
        if abs(orient[0]) > 0.1 or abs(orient[1]) > 0.1 or abs(orient[2]) > 0.1:
            print(f"❌ Single actor orientation not reset: {orient}")
            reset_worked = False
    
    # Check display values
    pos = viewer.current_pos_left
    rot = viewer.current_rot_left
    if abs(pos['x']) > 0.1 or abs(pos['y']) > 0.1 or abs(pos['z']) > 0.1:
        print(f"❌ Display position not reset: {pos}")
        reset_worked = False
    if abs(rot['x']) > 0.1 or abs(rot['y']) > 0.1 or abs(rot['z']) > 0.1:
        print(f"❌ Display rotation not reset: {rot}")
        reset_worked = False
    
    print(f"\n🎯 FINAL RESULT: {'✅ RESET WORKED' if reset_worked else '❌ RESET FAILED'}")
    
    # Force rendering updates
    print(f"\n🔄 FORCING RENDER UPDATES...")
    renderer = viewer.vtk_renderer_left
    if hasattr(renderer, 'render_window') and renderer.render_window:
        renderer.render_window.Render()
        print("✅ Render window updated")

    if hasattr(renderer, 'vtk_widget') and renderer.vtk_widget:
        renderer.vtk_widget.GetRenderWindow().Render()
        print("✅ VTK widget updated")

    # Force camera reset
    if hasattr(renderer, 'renderer') and renderer.renderer:
        renderer.renderer.ResetCamera()
        renderer.renderer.GetRenderWindow().Render()
        print("✅ Camera reset and rendered")

    # Keep window open for manual inspection
    print(f"\n👁️ Window will stay open for 15 seconds for visual inspection...")
    print("🔍 Check if the model and bounding box have visually reset!")
    QTimer.singleShot(15000, app.quit)  # Close after 15 seconds

    app.exec_()
    return reset_worked

if __name__ == "__main__":
    test_reset_visual()
