#!/usr/bin/env python3
"""
Test the file extension fix
"""

import os
import sys

def test_extension_logic():
    """Test the extension logic directly"""
    print("🧪 Testing File Extension Logic")
    print("=" * 50)
    
    # Test cases
    test_cases = [
        ("myfile", "myfile.step"),
        ("myfile.step", "myfile.step"),
        ("myfile.stp", "myfile.stp"),
        ("myfile.STEP", "myfile.STEP"),
        ("myfile.STP", "myfile.STP"),
        ("myfile.txt", "myfile.txt.step"),
        ("myfile.stl", "myfile.stl.step"),
        ("", ".step"),
    ]
    
    print("Testing extension logic:")
    for input_name, expected in test_cases:
        # Simulate the logic from the fixed function
        filename = input_name
        if not filename.lower().endswith(('.step', '.stp')):
            filename += '.step'
        
        status = "✅ PASS" if filename == expected else "❌ FAIL"
        print(f"  '{input_name}' → '{filename}' (expected '{expected}') {status}")
    
    print(f"\n🎯 The fix ensures:")
    print(f"✅ Files without extension get '.step' added")
    print(f"✅ Files with .step or .stp extension are preserved")
    print(f"✅ Files with other extensions get '.step' appended")
    print(f"✅ Case insensitive extension checking")
    
    return True

if __name__ == "__main__":
    test_extension_logic()
    print(f"\n✅ Extension fix logic verified!")
