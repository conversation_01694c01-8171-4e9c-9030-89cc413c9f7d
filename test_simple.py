#!/usr/bin/env python3
"""Simple test to check what's working"""

print("=== SIMPLE TEST START ===")

try:
    print("1. Testing PyQt5 import...")
    from PyQt5.QtWidgets import QApplication
    print("   ✅ PyQt5 OK")
except Exception as e:
    print(f"   ❌ PyQt5 Error: {e}")

try:
    print("2. Testing VTK import...")
    import vtk
    print("   ✅ VTK OK")
except Exception as e:
    print(f"   ❌ VTK Error: {e}")

try:
    print("3. Testing FreeCAD import...")
    import freecad
    import FreeCAD
    print("   ✅ FreeCAD OK")
except Exception as e:
    print(f"   ❌ FreeCAD Error: {e}")

try:
    print("4. Testing step_loader import...")
    from step_loader import STEPLoader
    print("   ✅ step_loader OK")
except Exception as e:
    print(f"   ❌ step_loader Error: {e}")

try:
    print("5. Testing vtk_renderer import...")
    from vtk_renderer import VTKRenderer
    print("   ✅ vtk_renderer OK")
except Exception as e:
    print(f"   ❌ vtk_renderer Error: {e}")

print("=== SIMPLE TEST END ===")
