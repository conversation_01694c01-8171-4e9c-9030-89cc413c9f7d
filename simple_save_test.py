#!/usr/bin/env python3
"""
Simple test for save functions - tests the actual fixed save methods
"""

import os
import shutil
import time

def test_simple_copy():
    """Test simple file copy functionality"""
    print("=" * 60)
    print("🧪 SIMPLE SAVE TEST")
    print("=" * 60)
    
    # Check if test.step exists
    source_file = "test.step"
    if not os.path.exists(source_file):
        print(f"❌ ERROR: {source_file} not found!")
        return False
    
    # Get original file info
    original_size = os.path.getsize(source_file)
    print(f"📁 Source file: {source_file}")
    print(f"   Size: {original_size} bytes")
    
    # Test 1: Copy to new file
    target_file = "test_copy_new.step"
    if os.path.exists(target_file):
        os.remove(target_file)
    
    print(f"\n📝 Test 1: Copy to new file")
    print(f"   Target: {target_file}")
    
    try:
        # Simple copy
        shutil.copy2(source_file, target_file)
        
        if os.path.exists(target_file):
            copied_size = os.path.getsize(target_file)
            print(f"   ✅ SUCCESS: {original_size} bytes → {copied_size} bytes")
            
            # Verify content is identical
            with open(source_file, 'rb') as f1, open(target_file, 'rb') as f2:
                content1 = f1.read()
                content2 = f2.read()
                if content1 == content2:
                    print(f"   ✅ Content identical")
                else:
                    print(f"   ❌ Content different")
        else:
            print(f"   ❌ FAILED: Target file not created")
            
    except Exception as e:
        print(f"   ❌ EXCEPTION: {e}")
    
    # Test 2: Copy to existing file (overwrite)
    existing_file = "test_copy_existing.step"
    
    # Create existing file
    with open(existing_file, 'w') as f:
        f.write("DUMMY CONTENT\n")
    
    before_size = os.path.getsize(existing_file)
    before_time = os.path.getmtime(existing_file)
    
    print(f"\n📝 Test 2: Copy to existing file (overwrite)")
    print(f"   Target: {existing_file}")
    print(f"   Before: {before_size} bytes")
    
    time.sleep(1)  # Ensure timestamp difference
    
    try:
        # Overwrite existing file
        shutil.copy2(source_file, existing_file)
        
        if os.path.exists(existing_file):
            after_size = os.path.getsize(existing_file)
            after_time = os.path.getmtime(existing_file)
            
            print(f"   After:  {after_size} bytes")
            print(f"   ✅ SUCCESS: File overwritten")
            print(f"   ✅ Size changed: {before_size} → {after_size}")
            print(f"   ✅ Time updated: {after_time > before_time}")
            
        else:
            print(f"   ❌ FAILED: Target file doesn't exist after copy")
            
    except Exception as e:
        print(f"   ❌ EXCEPTION: {e}")
    
    # Test 3: Test the actual save function from main program
    print(f"\n📝 Test 3: Test actual save function")
    
    try:
        # Import the main program
        import sys
        sys.path.append('.')
        
        # Test the simple copy logic directly
        test_target = "test_actual_save.step"
        if os.path.exists(test_target):
            os.remove(test_target)
        
        # Simulate the fixed save logic
        if os.path.exists(source_file):
            print(f"   🔧 SIMPLE COPY: Copying {source_file} to {test_target}")
            
            # Ensure target directory exists
            target_dir = os.path.dirname(test_target)
            if target_dir and not os.path.exists(target_dir):
                os.makedirs(target_dir)
            
            # Copy the file
            shutil.copy2(source_file, test_target)
            
            # Verify the copy worked
            if os.path.exists(test_target):
                original_size = os.path.getsize(source_file)
                copied_size = os.path.getsize(test_target)
                print(f"   ✅ ACTUAL SAVE: File copied successfully - {original_size} bytes → {copied_size} bytes")
                
                if original_size == copied_size:
                    print(f"   ✅ PERFECT: Sizes match exactly")
                else:
                    print(f"   ❌ ERROR: Size mismatch")
            else:
                print(f"   ❌ ACTUAL SAVE: Copy failed - target file doesn't exist")
        else:
            print(f"   ❌ ACTUAL SAVE: No source file to copy from")
            
    except Exception as e:
        print(f"   ❌ ACTUAL SAVE EXCEPTION: {e}")
    
    # Cleanup
    print(f"\n🧹 Cleaning up...")
    for cleanup_file in [target_file, existing_file, test_target]:
        if os.path.exists(cleanup_file):
            os.remove(cleanup_file)
            print(f"   Removed: {cleanup_file}")
    
    print(f"\n✅ Simple save test completed!")
    return True

if __name__ == "__main__":
    test_simple_copy()
