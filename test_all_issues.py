#!/usr/bin/env python3
"""
Test all three issues:
1. Colors are wrong
2. Numbers don't update 
3. Bounding box is off
"""

import sys
import os
os.environ['QT_QPA_PLATFORM'] = 'offscreen'

from PyQt5.QtWidgets import QApplication
app = QApplication([])

import step_viewer_tdk_modular

print("🔍 TESTING ALL THREE ISSUES")
print("=" * 50)

# Create main window
main_window = step_viewer_tdk_modular.StepViewerTDK()

# Test 1: Load STEP file and check colors
print("\n1. TESTING COLORS:")
main_window.active_viewer = 'top'

# Simulate loading STEP file
try:
    main_window.load_step_file('debug_auto_saved.step')
    
    # Check if colors are applied
    if hasattr(main_window, 'vtk_renderer_left') and main_window.vtk_renderer_left.step_actor:
        mapper = main_window.vtk_renderer_left.step_actor.GetMapper()
        if mapper.GetScalarVisibility():
            print("   ✅ Colors are being applied")
            color_range = mapper.GetScalarRange()
            print(f"   Color range: {color_range}")
        else:
            print("   ❌ Colors are NOT being applied")
    else:
        print("   ❌ No VTK actor found")
        
except Exception as e:
    print(f"   ❌ Error loading STEP file: {e}")

# Test 2: Test rotation number updates
print("\n2. TESTING ROTATION NUMBERS:")
try:
    # Initialize rotation tracking
    if not hasattr(main_window, 'model_rot_left'):
        main_window.model_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
    
    print(f"   Initial: {main_window.model_rot_left}")
    
    # Test 3 button clicks
    for i in range(3):
        print(f"   Button click #{i+1}:")
        main_window.rotate_shape('x', 15.0)
        current_x = main_window.model_rot_left.get('x', 0.0)
        expected_x = (i+1) * 15.0
        print(f"     Expected: {expected_x:.1f}°, Actual: {current_x:.1f}°")
        
        if abs(current_x - expected_x) < 0.1:
            print(f"     ✅ Correct")
        else:
            print(f"     ❌ Wrong")
            
    # Test if text overlay updates
    print("   Testing text overlay update...")
    main_window.update_vtk_text_overlays()
    print("   ✅ Text overlay update called")
    
except Exception as e:
    print(f"   ❌ Error testing rotation: {e}")
    import traceback
    traceback.print_exc()

# Test 3: Check if model_rot_left is used in display
print("\n3. TESTING NUMBER DISPLAY:")
try:
    # Check if model_rot_left is used in VTK text
    if hasattr(main_window, 'model_rot_left'):
        print(f"   model_rot_left exists: {main_window.model_rot_left}")
        
        # Check if text overlay uses model_rot_left
        if hasattr(main_window, 'combined_text_actor_left'):
            print("   ✅ VTK text actor exists")
            # Try to get current text
            try:
                current_text = main_window.combined_text_actor_left.GetInput()
                if 'ROT:' in current_text:
                    print(f"   ✅ Text contains rotation values")
                    print(f"   Current text: {current_text[:100]}...")
                else:
                    print(f"   ❌ Text does not contain rotation values")
                    print(f"   Current text: {current_text}")
            except:
                print("   ❌ Could not get text content")
        else:
            print("   ❌ VTK text actor missing")
    else:
        print("   ❌ model_rot_left missing")
        
except Exception as e:
    print(f"   ❌ Error testing display: {e}")

print("\n🎯 SUMMARY:")
print("Issue 1 (Colors): Check color range and scalar visibility above")
print("Issue 2 (Numbers): Check if rotation increments correctly above") 
print("Issue 3 (Display): Check if text contains rotation values above")

app.quit()
