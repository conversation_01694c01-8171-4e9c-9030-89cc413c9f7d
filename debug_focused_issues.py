#!/usr/bin/env python3
"""
FOCUSED DEBUG - Identify exactly what's broken and needs minimal fixes
Only focus on the 3 specific issues:
1. Mouse rotation doesn't change axis values in display
2. Rotation buttons don't change axis values in display  
3. Colors need to be from STEP file (not overridden)
"""

import sys
import os
import time
import math
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QLabel, QTextEdit
from PyQt5.QtCore import QTimer
import vtk
from vtk.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor

class FocusedDebug(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("FOCUSED DEBUG - Test Core Issues Only")
        self.setGeometry(100, 100, 1200, 700)
        
        # Track the exact issues
        self.current_rot = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_axis = {'x': 0.0, 'y': 0.0, 'z': 1.0}
        self.current_angle = 0.0
        self.last_orientation = [0.0, 0.0, 0.0]
        
        # VTK components
        self.vtk_widget = None
        self.renderer = None
        self.step_actor = None
        self.text_actor = None
        
        self.init_ui()
        self.setup_vtk()
        
        # Timer to check mouse rotation
        self.mouse_timer = QTimer()
        self.mouse_timer.timeout.connect(self.check_mouse_rotation)
        self.mouse_timer.start(200)
        
    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QHBoxLayout(central_widget)
        
        # Left panel
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_panel.setFixedWidth(350)
        
        # Load button
        self.load_btn = QPushButton("Load STEP File")
        self.load_btn.clicked.connect(self.load_step_file)
        left_layout.addWidget(self.load_btn)
        
        # Test buttons
        left_layout.addWidget(QLabel("TEST ROTATION ISSUES:"))
        
        self.x_btn = QPushButton("X+15° (Test Button Rotation)")
        self.x_btn.clicked.connect(lambda: self.test_button_rotation('x', 15))
        left_layout.addWidget(self.x_btn)
        
        self.mouse_test_btn = QPushButton("Test Mouse Rotation Detection")
        self.mouse_test_btn.clicked.connect(self.test_mouse_detection)
        left_layout.addWidget(self.mouse_test_btn)
        
        self.color_test_btn = QPushButton("Test STEP File Colors")
        self.color_test_btn.clicked.connect(self.test_step_colors)
        left_layout.addWidget(self.color_test_btn)
        
        # Current values
        left_layout.addWidget(QLabel("CURRENT VALUES:"))
        self.rot_label = QLabel("ROT: X=0.0° Y=0.0° Z=0.0°")
        self.axis_label = QLabel("AXIS: X=0.0 Y=0.0 Z=1.0")
        self.angle_label = QLabel("ANGLE: 0.0°")
        
        left_layout.addWidget(self.rot_label)
        left_layout.addWidget(self.axis_label)
        left_layout.addWidget(self.angle_label)
        
        # Issue tracking
        left_layout.addWidget(QLabel("ISSUE STATUS:"))
        self.issue1_label = QLabel("❌ Mouse rotation → axis update")
        self.issue2_label = QLabel("❌ Button rotation → axis update")
        self.issue3_label = QLabel("❌ STEP file colors")
        
        left_layout.addWidget(self.issue1_label)
        left_layout.addWidget(self.issue2_label)
        left_layout.addWidget(self.issue3_label)
        
        # Debug log
        left_layout.addWidget(QLabel("DEBUG LOG:"))
        self.debug_log = QTextEdit()
        self.debug_log.setMaximumHeight(200)
        left_layout.addWidget(self.debug_log)
        
        layout.addWidget(left_panel)
        
        # Right panel - VTK viewer
        self.vtk_widget = QVTKRenderWindowInteractor()
        layout.addWidget(self.vtk_widget)
        
    def setup_vtk(self):
        """Setup VTK renderer"""
        self.renderer = vtk.vtkRenderer()
        self.renderer.SetBackground(0.1, 0.1, 0.2)
        
        self.vtk_widget.GetRenderWindow().AddRenderer(self.renderer)
        self.vtk_widget.Initialize()
        self.vtk_widget.Start()
        
        # Create text overlay
        self.text_actor = vtk.vtkTextActor()
        self.text_actor.SetInput("ROT: X=0.0° Y=0.0° Z=0.0°\nAXIS: X=0.0 Y=0.0 Z=1.0\nANGLE: 0.0°")
        self.text_actor.SetPosition(10, 10)
        
        text_prop = self.text_actor.GetTextProperty()
        text_prop.SetFontSize(12)
        text_prop.SetColor(1.0, 1.0, 1.0)
        
        self.renderer.AddActor2D(self.text_actor)
        
        self.log("✅ VTK setup complete")
        
    def load_step_file(self):
        """Load STEP file and test colors"""
        from PyQt5.QtWidgets import QFileDialog
        filename, _ = QFileDialog.getOpenFileName(self, "Load STEP File", "", "STEP Files (*.step *.stp)")
        
        if filename:
            self.log(f"📁 Loading: {os.path.basename(filename)}")
            
            try:
                from step_loader import STEPLoader
                loader = STEPLoader()
                success, message = loader.load_step_file(filename)
                
                if success and loader.current_polydata:
                    self.display_polydata(loader.current_polydata)
                    self.test_step_colors()
                    self.log(f"✅ Loaded: {message}")
                else:
                    self.log(f"❌ Failed: {message}")
                    
            except Exception as e:
                self.log(f"❌ Error: {e}")
                
    def display_polydata(self, polydata):
        """Display polydata and check colors"""
        try:
            if self.step_actor:
                self.renderer.RemoveActor(self.step_actor)
                
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputData(polydata)
            
            self.step_actor = vtk.vtkActor()
            self.step_actor.SetMapper(mapper)
            
            # Check for colors in polydata
            cell_colors = polydata.GetCellData().GetScalars()
            if cell_colors:
                self.log(f"✅ Found {cell_colors.GetNumberOfTuples()} cell colors")
                mapper.SetScalarVisibility(True)
                mapper.SetScalarModeToUseCellData()
                self.issue3_label.setText("✅ STEP file colors found")
            else:
                self.log("⚠️ No colors found - using default")
                self.step_actor.GetProperty().SetColor(0.7, 0.7, 0.7)
                self.issue3_label.setText("❌ No STEP file colors")
            
            self.renderer.AddActor(self.step_actor)
            self.renderer.ResetCamera()
            self.vtk_widget.GetRenderWindow().Render()
            
            # Initialize tracking
            self.last_orientation = list(self.step_actor.GetOrientation())
            self.log(f"🎭 Model displayed, initial orientation: {self.last_orientation}")
            
        except Exception as e:
            self.log(f"❌ Display error: {e}")
            
    def test_button_rotation(self, axis, degrees):
        """Test button rotation and axis calculation"""
        self.log(f"🔘 TESTING BUTTON ROTATION: {axis.upper()}{'+' if degrees > 0 else ''}{degrees}°")
        
        # Update rotation values
        old_rot = self.current_rot[axis]
        self.current_rot[axis] += degrees
        self.log(f"   ROT updated: {axis.upper()} {old_rot:.1f}° → {self.current_rot[axis]:.1f}°")
        
        # Apply VTK rotation
        if self.step_actor:
            self.step_actor.RotateWXYZ(degrees,
                1 if axis == 'x' else 0,
                1 if axis == 'y' else 0,
                1 if axis == 'z' else 0)
            self.vtk_widget.GetRenderWindow().Render()
            self.log(f"   VTK rotation applied")
        
        # Calculate axis from rotation
        self.calculate_axis_from_rotation()
        
        # Update displays
        self.update_displays()
        
        # Check if axis changed
        if abs(self.current_axis['x']) > 0.1 or abs(self.current_axis['y']) > 0.1:
            self.issue2_label.setText("✅ Button rotation → axis update")
            self.log("✅ ISSUE 2 FIXED: Button rotation updates axis")
        else:
            self.issue2_label.setText("❌ Button rotation → axis update")
            self.log("❌ ISSUE 2 STILL BROKEN: Axis not updating")
        
    def check_mouse_rotation(self):
        """Check if mouse rotation changed the model"""
        if not self.step_actor:
            return
            
        try:
            current_orientation = list(self.step_actor.GetOrientation())
            
            # Check for significant change
            changed = False
            for i in range(3):
                if abs(current_orientation[i] - self.last_orientation[i]) > 1.0:
                    changed = True
                    break
                    
            if changed:
                self.log(f"🖱️ MOUSE ROTATION DETECTED: {current_orientation}")
                
                # Update rotation values
                self.current_rot['x'] = current_orientation[0]
                self.current_rot['y'] = current_orientation[1]
                self.current_rot['z'] = current_orientation[2]
                
                # Calculate axis
                self.calculate_axis_from_rotation()
                
                # Update displays
                self.update_displays()
                
                self.last_orientation = current_orientation
                
                # Check if axis changed
                if abs(self.current_axis['x']) > 0.1 or abs(self.current_axis['y']) > 0.1:
                    self.issue1_label.setText("✅ Mouse rotation → axis update")
                    self.log("✅ ISSUE 1 FIXED: Mouse rotation updates axis")
                else:
                    self.issue1_label.setText("❌ Mouse rotation → axis update")
                    
        except Exception as e:
            pass
            
    def calculate_axis_from_rotation(self):
        """Calculate axis and angle from rotation values"""
        try:
            rot_mag = math.sqrt(self.current_rot['x']**2 + self.current_rot['y']**2 + self.current_rot['z']**2)
            
            if rot_mag > 0.001:
                self.current_axis = {
                    'x': self.current_rot['x'] / rot_mag,
                    'y': self.current_rot['y'] / rot_mag,
                    'z': self.current_rot['z'] / rot_mag
                }
                self.current_angle = rot_mag
                self.log(f"   AXIS calculated: {self.current_axis}")
                self.log(f"   ANGLE calculated: {self.current_angle:.1f}°")
            else:
                self.current_axis = {'x': 0.0, 'y': 0.0, 'z': 1.0}
                self.current_angle = 0.0
                self.log("   Zero rotation - default axis")
                
        except Exception as e:
            self.log(f"❌ Axis calculation error: {e}")
            
    def update_displays(self):
        """Update all displays"""
        # Update GUI labels
        self.rot_label.setText(f"ROT: X={self.current_rot['x']:.1f}° Y={self.current_rot['y']:.1f}° Z={self.current_rot['z']:.1f}°")
        self.axis_label.setText(f"AXIS: X={self.current_axis['x']:.3f} Y={self.current_axis['y']:.3f} Z={self.current_axis['z']:.3f}")
        self.angle_label.setText(f"ANGLE: {self.current_angle:.1f}°")
        
        # Update VTK text
        text_content = (
            f"ROT: X={self.current_rot['x']:.1f}° Y={self.current_rot['y']:.1f}° Z={self.current_rot['z']:.1f}°\n"
            f"AXIS: X={self.current_axis['x']:.3f} Y={self.current_axis['y']:.3f} Z={self.current_axis['z']:.3f}\n"
            f"ANGLE: {self.current_angle:.1f}°"
        )
        self.text_actor.SetInput(text_content)
        self.vtk_widget.GetRenderWindow().Render()
        
    def test_mouse_detection(self):
        """Test if mouse rotation detection is working"""
        self.log("🧪 TESTING MOUSE DETECTION")
        self.log("   Rotate the model with your mouse now...")
        self.log("   Watch for 'MOUSE ROTATION DETECTED' messages")
        
    def test_step_colors(self):
        """Test STEP file color handling"""
        if not self.step_actor:
            self.log("❌ No model loaded for color test")
            return
            
        mapper = self.step_actor.GetMapper()
        polydata = mapper.GetInput()
        
        cell_colors = polydata.GetCellData().GetScalars()
        if cell_colors:
            self.log(f"✅ STEP colors: {cell_colors.GetNumberOfTuples()} colors found")
            self.issue3_label.setText("✅ STEP file colors")
        else:
            self.log("❌ No STEP colors found")
            self.issue3_label.setText("❌ No STEP file colors")
        
    def log(self, message):
        """Add message to debug log"""
        timestamp = time.strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}"
        print(full_message)
        self.debug_log.append(full_message)

def main():
    app = QApplication(sys.argv)
    window = FocusedDebug()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
