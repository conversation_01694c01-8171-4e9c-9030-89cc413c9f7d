#!/usr/bin/env python3
"""
Quick verification script to check OpenCASCADE transformation capabilities
This will help identify exactly what's broken in the transformation system
"""

print("🔧 OPENCASCADE TRANSFORMATION VERIFICATION")
print("=" * 60)

# Test 1: Basic OpenCASCADE imports
print("\n1. Testing basic OpenCASCADE imports...")
try:
    from OCC.Core import STEPControl_Reader, STEPControl_Writer
    print("✅ Basic STEP control imports successful")
except ImportError as e:
    print(f"❌ Basic STEP control imports failed: {e}")

# Test 2: Transformation imports
print("\n2. Testing transformation imports...")
try:
    from OCC.Core import gp_Trsf, gp_Vec, gp_Ax1, gp_Pnt, gp_Dir
    print("✅ Transformation imports successful")
    print(f"   - gp_Trsf: {gp_Trsf}")
    print(f"   - gp_Vec: {gp_Vec}")
    print(f"   - gp_Ax1: {gp_Ax1}")
except ImportError as e:
    print(f"❌ Transformation imports failed: {e}")

# Test 3: BRep transformation imports
print("\n3. Testing BRep transformation imports...")
try:
    from OCC.Core import BRepBuilderAPI_Transform
    print("✅ BRep transformation imports successful")
    print(f"   - BRepBuilderAPI_Transform: {BRepBuilderAPI_Transform}")
except ImportError as e:
    print(f"❌ BRep transformation imports failed: {e}")

# Test 4: Check what's actually available in OCC.Core
print("\n4. Checking available OCC.Core modules...")
try:
    import OCC.Core
    available_modules = [attr for attr in dir(OCC.Core) if not attr.startswith('_')]
    print(f"✅ Found {len(available_modules)} available modules in OCC.Core")
    
    # Look for transformation-related modules
    transform_modules = [mod for mod in available_modules if 'trsf' in mod.lower() or 'transform' in mod.lower()]
    if transform_modules:
        print(f"   Transformation-related modules: {transform_modules}")
    else:
        print("   ❌ No transformation-related modules found")
        
    # Look for geometry modules
    geom_modules = [mod for mod in available_modules if 'gp_' in mod.lower()]
    if geom_modules:
        print(f"   Geometry modules (first 10): {geom_modules[:10]}")
    else:
        print("   ❌ No geometry modules found")
        
except Exception as e:
    print(f"❌ Error checking OCC.Core modules: {e}")

# Test 5: Alternative import methods
print("\n5. Testing alternative import methods...")
try:
    # Try different import styles
    import OCC
    print(f"✅ Base OCC import successful: {OCC}")
    
    # Try direct module imports
    try:
        from OCC import gp
        print(f"✅ Direct gp module import successful: {gp}")
        if hasattr(gp, 'gp_Trsf'):
            print(f"✅ gp_Trsf found in gp module: {gp.gp_Trsf}")
        else:
            print("❌ gp_Trsf not found in gp module")
    except ImportError as e:
        print(f"❌ Direct gp module import failed: {e}")
        
except ImportError as e:
    print(f"❌ Base OCC import failed: {e}")

# Test 6: Check OpenCASCADE version and installation
print("\n6. Checking OpenCASCADE installation...")
try:
    import OCC
    if hasattr(OCC, '__version__'):
        print(f"✅ OpenCASCADE version: {OCC.__version__}")
    else:
        print("⚠️  OpenCASCADE version not available")
        
    # Check installation path
    import os
    occ_path = os.path.dirname(OCC.__file__)
    print(f"✅ OpenCASCADE installed at: {occ_path}")
    
    # Check if Core directory exists
    core_path = os.path.join(occ_path, 'Core')
    if os.path.exists(core_path):
        print(f"✅ Core directory exists: {core_path}")
        core_files = os.listdir(core_path)
        print(f"   Core directory contains {len(core_files)} files")
    else:
        print(f"❌ Core directory not found: {core_path}")
        
except Exception as e:
    print(f"❌ Error checking OpenCASCADE installation: {e}")

print("\n" + "=" * 60)
print("VERIFICATION COMPLETE")
print("If transformation imports failed, the rotation save feature will not work.")
print("This explains why the automated test fell back to simple file copying.")
