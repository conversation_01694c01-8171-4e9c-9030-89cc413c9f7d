#!/usr/bin/env python3
"""
CLEAN VERSION - STEP Viewer with Mouse Rotation Fix
No automatic testing, just the main program with mouse rotation save fix
"""

import sys
from PyQt5.QtWidgets import QApplication
from step_viewer_tdk_modular_fixed import StepViewerTDK

def main():
    print("🎯 STEP VIEWER - Mouse Rotation Fix Version")
    print("=" * 50)
    print("✅ Mouse rotation save fix is ACTIVE!")
    print("")
    print("📋 Instructions:")
    print("1. Load a STEP file using 'Open STEP File' button")
    print("2. Rotate with mouse by dragging the model")
    print("3. Use rotation buttons (X+15°, Y+15°, Z+15°) if desired")
    print("4. Save using green 'Save STEP File (Improved Method)' button")
    print("5. Both mouse and button rotations will be saved!")
    print("=" * 50)

    app = QApplication(sys.argv)
    app.setApplicationName("STEP Viewer - Mouse Rotation Fixed")
    app.setApplicationVersion("3.1")

    viewer = StepViewerTDK()
    viewer.show()

    print("🎯 Viewer is running! Mouse rotation fix is ACTIVE!")
    print("🖱️  You can now rotate with mouse and save correctly!")

    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
