#!/usr/bin/env python3
"""
COMPREHENSIVE DEBUG TEST for all issues:
1. Text overlays showing at startup
2. Cursor tracking not working
3. Overlay colors not different
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from step_viewer_tdk_modular import <PERSON><PERSON>iewerTDK

def debug_all_issues():
    """Debug all the major issues systematically"""
    print("🔧 COMPREHENSIVE DEBUG TEST STARTING...")
    print("=" * 60)
    
    # Create application and viewer
    app = QApplication([])
    viewer = StepViewerTDK()
    viewer.show()
    
    print("\n🔧 DEBUG 1: CHECKING TEXT OVERLAYS AT STARTUP")
    print("-" * 50)
    
    # Check if text overlays exist and their visibility
    if hasattr(viewer, 'combined_text_actor_left'):
        visibility = viewer.combined_text_actor_left.GetVisibility()
        text_content = viewer.combined_text_actor_left.GetInput()
        print(f"✅ TOP text overlay exists: visibility={visibility}, content='{text_content}'")
    else:
        print("❌ TOP text overlay does NOT exist")
    
    if hasattr(viewer, 'combined_text_actor_right'):
        visibility = viewer.combined_text_actor_right.GetVisibility()
        text_content = viewer.combined_text_actor_right.GetInput()
        print(f"✅ BOTTOM text overlay exists: visibility={visibility}, content='{text_content}'")
    else:
        print("❌ BOTTOM text overlay does NOT exist")
    
    if hasattr(viewer, 'cursor_text_actor_left'):
        visibility = viewer.cursor_text_actor_left.GetVisibility()
        text_content = viewer.cursor_text_actor_left.GetInput()
        print(f"✅ CURSOR text overlay exists: visibility={visibility}, content='{text_content}'")
    else:
        print("❌ CURSOR text overlay does NOT exist")
    
    print("\n🔧 DEBUG 2: CHECKING VTK RENDERERS")
    print("-" * 50)
    
    # Check VTK renderers
    if hasattr(viewer, 'vtk_renderer_left') and viewer.vtk_renderer_left:
        print("✅ TOP VTK renderer exists")
        if hasattr(viewer.vtk_renderer_left, 'renderer'):
            renderer = viewer.vtk_renderer_left.renderer
            actor_count = renderer.GetActors2D().GetNumberOfItems()
            print(f"✅ TOP renderer has {actor_count} 2D actors")
        else:
            print("❌ TOP renderer.renderer does NOT exist")
    else:
        print("❌ TOP VTK renderer does NOT exist")
    
    if hasattr(viewer, 'vtk_renderer_right') and viewer.vtk_renderer_right:
        print("✅ BOTTOM VTK renderer exists")
        if hasattr(viewer.vtk_renderer_right, 'renderer'):
            renderer = viewer.vtk_renderer_right.renderer
            actor_count = renderer.GetActors2D().GetNumberOfItems()
            print(f"✅ BOTTOM renderer has {actor_count} 2D actors")
        else:
            print("❌ BOTTOM renderer.renderer does NOT exist")
    else:
        print("❌ BOTTOM VTK renderer does NOT exist")
    
    print("\n🔧 DEBUG 3: TESTING CURSOR CALLBACK CONNECTION")
    print("-" * 50)
    
    # Test cursor callback connection
    if hasattr(viewer, 'vtk_widget_left'):
        interactor = viewer.vtk_widget_left.GetInteractor()
        if interactor:
            print("✅ TOP interactor exists")
            if hasattr(interactor, 'cursor_callback'):
                print("✅ TOP cursor_callback is connected")
                # Test the callback
                try:
                    test_pos = [1.0, 2.0, 3.0]
                    interactor.cursor_callback(test_pos)
                    print("✅ TOP cursor callback test SUCCESSFUL")
                except Exception as e:
                    print(f"❌ TOP cursor callback test FAILED: {e}")
            else:
                print("❌ TOP cursor_callback is NOT connected")
        else:
            print("❌ TOP interactor does NOT exist")
    else:
        print("❌ TOP vtk_widget does NOT exist")
    
    print("\n🔧 DEBUG 4: TESTING FILE LOADING")
    print("-" * 50)
    
    # Test loading a STEP file if one exists
    test_files = [
        "SOIC16P127_1270X940X160L89X51.STEP",
        "test.step",
        "sample.step"
    ]
    
    test_file = None
    for filename in test_files:
        if os.path.exists(filename):
            test_file = filename
            break
    
    if test_file:
        print(f"✅ Found test file: {test_file}")
        print("🔧 Loading file in TOP viewer...")
        
        # Load file and check what happens
        try:
            viewer.load_step_file_to_viewer(test_file, "top")
            print("✅ File loading completed")
            
            # Check text overlays after loading
            if hasattr(viewer, 'combined_text_actor_left'):
                visibility = viewer.combined_text_actor_left.GetVisibility()
                text_content = viewer.combined_text_actor_left.GetInput()
                print(f"✅ TOP text after load: visibility={visibility}, content='{text_content[:50]}...'")
            
            if hasattr(viewer, 'cursor_text_actor_left'):
                visibility = viewer.cursor_text_actor_left.GetVisibility()
                text_content = viewer.cursor_text_actor_left.GetInput()
                print(f"✅ CURSOR text after load: visibility={visibility}, content='{text_content}'")
                
        except Exception as e:
            print(f"❌ File loading FAILED: {e}")
    else:
        print("❌ No test STEP files found")
    
    print("\n🔧 DEBUG 5: TESTING OVERLAY CREATION")
    print("-" * 50)
    
    # Test overlay creation
    try:
        if hasattr(viewer, 'create_overlay_widget'):
            print("✅ create_overlay_widget method exists")
            viewer.create_overlay_widget()
            print("✅ Overlay widget creation completed")
        else:
            print("❌ create_overlay_widget method does NOT exist")
    except Exception as e:
        print(f"❌ Overlay creation FAILED: {e}")
    
    print("\n" + "=" * 60)
    print("🔧 COMPREHENSIVE DEBUG TEST COMPLETED")
    print("=" * 60)
    
    # Keep the program running for manual testing
    print("\n🎯 Program is now running for manual testing...")
    print("🎯 Close the window to exit.")
    
    return app.exec_()

if __name__ == "__main__":
    debug_all_issues()
