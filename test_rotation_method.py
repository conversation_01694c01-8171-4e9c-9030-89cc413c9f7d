#!/usr/bin/env python3
"""
Test the new rotation extraction method
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from step_viewer_tdk_modular_fixed import StepViewerTDK

def test_rotation_method():
    """Test the new rotation extraction method"""
    print("🧪 Testing rotation extraction method...")
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    
    # Test with direction vectors that represent a 45 degree X rotation
    print("\n🔧 Testing 45° X rotation vectors:")
    x_direction = [1.0, 0.0, 0.0]  # X axis unchanged
    z_direction = [0.0, -0.707106781187, 0.707106781187]  # Z rotated 45 degrees around X
    
    result = viewer._analyze_step_coordinate_system_from_vectors(x_direction, z_direction)
    print(f"📊 Result: {result}")
    print(f"📊 Expected: X=45.0, Y=0.0, Z=0.0")
    
    # Check if the result is correct
    if abs(result['x'] - 45.0) < 1.0 and abs(result['y']) < 1.0 and abs(result['z']) < 1.0:
        print("✅ SUCCESS: Method correctly extracts 45° X rotation")
    else:
        print("❌ FAILED: Method did not extract correct rotation")
    
    print("\n🔧 Testing zero rotation vectors:")
    x_direction = [1.0, 0.0, 0.0]  # Standard X axis
    z_direction = [0.0, 0.0, 1.0]  # Standard Z axis
    
    result = viewer._analyze_step_coordinate_system_from_vectors(x_direction, z_direction)
    print(f"📊 Result: {result}")
    print(f"📊 Expected: X=0.0, Y=0.0, Z=0.0")
    
    if abs(result['x']) < 1.0 and abs(result['y']) < 1.0 and abs(result['z']) < 1.0:
        print("✅ SUCCESS: Method correctly handles zero rotation")
    else:
        print("❌ FAILED: Method did not handle zero rotation correctly")
    
    print("\n🎯 Test completed!")

if __name__ == "__main__":
    test_rotation_method()
