#!/usr/bin/env python3

print("DEBUGGING XCAF COLOR EXTRACTION")
print("=" * 50)

try:
    from OCC.Core.STEPCAFControl import STEPCAFControl_Reader
    from OCC.Core.XCAFApp import XCAFApp_Application
    from OCC.Core.XCAFDoc import XCAFDoc_DocumentTool, XCAFDoc_ColorTool, XCAFDoc_ColorType
    from OCC.Core.TDocStd import TDocStd_Document
    from OCC.Core.IFSelect import IFSelect_RetDone
    from OCC.Core.TDF import TDF_LabelSequence, TDF_ChildIterator
    from OCC.Core.Quantity import Quantity_Color
    from OCC.Core.TopExp import TopExp_Explorer
    from OCC.Core.TopAbs import TopAbs_FACE
    
    print("OpenCASCADE imports successful")
    
    # Load STEP file with XCAF
    app = XCAFApp_Application.GetApplication()
    doc = TDocStd_Document("MDTV-XCAF")
    
    reader = STEPCAFControl_Reader()
    reader.SetColorMode(True)
    reader.SetNameMode(True)
    reader.SetLayerMode(True)
    reader.SetPropsMode(True)
    
    status = reader.ReadFile('SOIC16P127_1270X940X610L89X51.STEP')
    if status != IFSelect_RetDone:
        print("Failed to read STEP file")
        exit(1)
    
    reader.Transfer(doc)
    
    # Get XCAF tools
    root_label = doc.Main()
    shape_tool = XCAFDoc_DocumentTool.ShapeTool(root_label)
    color_tool = XCAFDoc_DocumentTool.ColorTool(root_label)
    
    print("XCAF document loaded successfully")
    
    # Get main shape
    free_shapes = TDF_LabelSequence()
    shape_tool.GetFreeShapes(free_shapes)
    
    if free_shapes.Length() > 0:
        main_label = free_shapes.Value(1)
        main_shape = shape_tool.GetShape(main_label)
        print(f"Main shape obtained")
    else:
        print("No free shapes found")
        exit(1)
    
    # Test 1: Check shape-level colors
    print("\nTest 1: Shape-level color extraction...")
    
    def collect_shape_colors(label, depth=0):
        indent = "  " * depth
        colors_found = []
        
        if shape_tool.IsShape(label):
            shape = shape_tool.GetShape(label)
            color = Quantity_Color()
            
            if color_tool.GetColor(shape, XCAFDoc_ColorType.XCAFDoc_ColorSurf, color):
                r_raw = color.Red()
                g_raw = color.Green()
                b_raw = color.Blue()
                r, g, b = int(r_raw * 255), int(g_raw * 255), int(b_raw * 255)
                
                print(f"{indent}Shape color: ({r_raw:.6f}, {g_raw:.6f}, {b_raw:.6f}) → RGB({r}, {g}, {b})")
                colors_found.append((r, g, b))
                
                # Check if this matches expected values
                if abs(r_raw - 0.752941) < 0.001:  # 192/255
                    print(f"{indent}  ✅ MATCHES expected light gray (192, 192, 192)")
                elif abs(r_raw - 0.250980) < 0.001:  # 63/255
                    print(f"{indent}  ✅ MATCHES expected dark gray (63, 63, 63)")
                else:
                    print(f"{indent}  ❌ UNEXPECTED COLOR VALUE")
        
        # Visit children
        child_iter = TDF_ChildIterator(label)
        while child_iter.More():
            child_colors = collect_shape_colors(child_iter.Value(), depth + 1)
            colors_found.extend(child_colors)
            child_iter.Next()
        
        return colors_found
    
    shape_colors = collect_shape_colors(root_label)
    print(f"Found {len(shape_colors)} shape colors total")
    
    # Test 2: Check face-level colors
    print("\nTest 2: Face-level color extraction...")
    
    explorer = TopExp_Explorer(main_shape, TopAbs_FACE)
    face_count = 0
    face_colors = []
    
    while explorer.More() and face_count < 10:  # Check first 10 faces
        face = explorer.Current()
        color = Quantity_Color()
        
        if color_tool.GetColor(face, XCAFDoc_ColorType.XCAFDoc_ColorSurf, color):
            r_raw = color.Red()
            g_raw = color.Green()
            b_raw = color.Blue()
            r, g, b = int(r_raw * 255), int(g_raw * 255), int(b_raw * 255)
            
            print(f"Face {face_count}: ({r_raw:.6f}, {g_raw:.6f}, {b_raw:.6f}) → RGB({r}, {g}, {b})")
            face_colors.append((r, g, b))
            
            # Check if this matches expected values
            if abs(r_raw - 0.752941) < 0.001:  # 192/255
                print(f"  ✅ MATCHES expected light gray (192, 192, 192)")
            elif abs(r_raw - 0.250980) < 0.001:  # 63/255
                print(f"  ✅ MATCHES expected dark gray (63, 63, 63)")
            else:
                print(f"  ❌ UNEXPECTED COLOR VALUE - Expected 0.752941 or 0.250980")
        else:
            print(f"Face {face_count}: No color found")
        
        face_count += 1
        explorer.Next()
    
    print(f"\nSummary:")
    print(f"Shape-level colors: {list(set(shape_colors))}")
    print(f"Face-level colors: {list(set(face_colors))}")
    
    # Compare with direct STEP parsing
    print(f"\nExpected from direct STEP parsing:")
    print(f"  Light: RGB(192, 192, 192) from 0.752941")
    print(f"  Dark: RGB(63, 63, 63) from 0.250980")

except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()

print("\nXCAF COLOR DEBUG COMPLETE")
