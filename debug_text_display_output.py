#!/usr/bin/env python3
"""
DEBUG TEXT DISPLAY OUTPUT - Verify text actually gets written to display
This will trace the complete flow from calculation to actual text display:
1. Rotation values calculated
2. update_vtk_text_overlays() called
3. Text strings generated
4. VTK text actors updated
5. Display rendered with new text
"""

import sys
import os
import time
import math
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QLabel, QTextEdit
from PyQt5.QtCore import QTimer
import vtk
from vtk.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor

class DebugTextDisplayOutput(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("DEBUG TEXT DISPLAY OUTPUT - Verify Text Writing")
        self.setGeometry(100, 100, 1400, 800)
        
        # Initialize tracking variables
        self.current_rot = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_axis = {'x': 0.0, 'y': 0.0, 'z': 1.0}
        self.current_angle = 0.0
        
        # VTK components
        self.vtk_widget = None
        self.renderer = None
        self.step_actor = None
        self.text_actor = None  # For displaying text overlay
        
        self.init_ui()
        self.setup_vtk()
        self.create_text_overlay()
        
    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QHBoxLayout(central_widget)
        
        # Left panel - controls and debug
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_panel.setFixedWidth(400)
        
        # Load button
        self.load_btn = QPushButton("Load STEP File")
        self.load_btn.clicked.connect(self.load_step_file)
        left_layout.addWidget(self.load_btn)
        
        # Rotation buttons
        left_layout.addWidget(QLabel("ROTATION BUTTONS:"))
        
        rot_layout = QHBoxLayout()
        self.x_plus_btn = QPushButton("X+15°")
        self.x_plus_btn.clicked.connect(lambda: self.debug_complete_text_flow('x', 15))
        self.y_plus_btn = QPushButton("Y+15°")
        self.y_plus_btn.clicked.connect(lambda: self.debug_complete_text_flow('y', 15))
        self.z_plus_btn = QPushButton("Z+15°")
        self.z_plus_btn.clicked.connect(lambda: self.debug_complete_text_flow('z', 15))
        
        rot_layout.addWidget(self.x_plus_btn)
        rot_layout.addWidget(self.y_plus_btn)
        rot_layout.addWidget(self.z_plus_btn)
        left_layout.addLayout(rot_layout)
        
        # Test text update button
        self.test_text_btn = QPushButton("TEST: Update Text Display")
        self.test_text_btn.clicked.connect(self.test_text_update)
        left_layout.addWidget(self.test_text_btn)
        
        # Current values display
        left_layout.addWidget(QLabel("CURRENT VALUES:"))
        self.rot_label = QLabel("ROT: X=0.0° Y=0.0° Z=0.0°")
        self.axis_label = QLabel("AXIS: X=0.0 Y=0.0 Z=1.0")
        self.angle_label = QLabel("ANGLE: 0.0°")
        
        left_layout.addWidget(self.rot_label)
        left_layout.addWidget(self.axis_label)
        left_layout.addWidget(self.angle_label)
        
        # Text content verification
        left_layout.addWidget(QLabel("VTK TEXT CONTENT:"))
        self.vtk_text_label = QLabel("(No text set)")
        self.vtk_text_label.setStyleSheet("background-color: #ffffcc; padding: 5px; border: 1px solid #ccc;")
        self.vtk_text_label.setWordWrap(True)
        left_layout.addWidget(self.vtk_text_label)
        
        # Debug output
        left_layout.addWidget(QLabel("TEXT WRITING TRACE:"))
        self.debug_text = QTextEdit()
        self.debug_text.setMaximumHeight(300)
        self.debug_text.setStyleSheet("font-family: monospace; font-size: 9pt;")
        left_layout.addWidget(self.debug_text)
        
        layout.addWidget(left_panel)
        
        # Right panel - VTK viewer
        self.vtk_widget = QVTKRenderWindowInteractor()
        layout.addWidget(self.vtk_widget)
        
    def setup_vtk(self):
        """Setup VTK renderer"""
        self.renderer = vtk.vtkRenderer()
        self.renderer.SetBackground(0.1, 0.1, 0.2)
        
        self.vtk_widget.GetRenderWindow().AddRenderer(self.renderer)
        self.vtk_widget.Initialize()
        self.vtk_widget.Start()
        
        self.debug_log("🔧 VTK setup complete")
        
    def create_text_overlay(self):
        """Create VTK text overlay for displaying rotation info"""
        try:
            # Create text actor
            self.text_actor = vtk.vtkTextActor()
            self.text_actor.SetInput("ROT: X=0.0° Y=0.0° Z=0.0°\nAXIS: X=0.0 Y=0.0 Z=1.0\nANGLE: 0.0°")
            
            # Position text in top-left corner
            self.text_actor.SetPosition(10, 10)
            
            # Set text properties
            text_prop = self.text_actor.GetTextProperty()
            text_prop.SetFontSize(14)
            text_prop.SetColor(1.0, 1.0, 1.0)  # White text
            text_prop.SetFontFamilyToArial()
            
            # Add to renderer
            self.renderer.AddActor2D(self.text_actor)
            
            self.debug_log("✅ Text overlay created and added to renderer")
            
            # Initial render
            self.vtk_widget.GetRenderWindow().Render()
            
        except Exception as e:
            self.debug_log(f"❌ Text overlay creation error: {e}")
            
    def load_step_file(self):
        """Load STEP file"""
        from PyQt5.QtWidgets import QFileDialog
        filename, _ = QFileDialog.getOpenFileName(self, "Load STEP File", "", "STEP Files (*.step *.stp)")
        
        if filename:
            self.debug_log(f"📁 Loading: {os.path.basename(filename)}")
            
            try:
                from step_loader import STEPLoader
                loader = STEPLoader()
                success, message = loader.load_step_file(filename)
                
                if success and loader.current_polydata:
                    self.display_polydata(loader.current_polydata)
                    self.debug_log(f"✅ Loaded: {message}")
                else:
                    self.debug_log(f"❌ Failed: {message}")
                    
            except Exception as e:
                self.debug_log(f"❌ Error: {e}")
                
    def display_polydata(self, polydata):
        """Display polydata"""
        try:
            if self.step_actor:
                self.renderer.RemoveActor(self.step_actor)
                
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputData(polydata)
            
            self.step_actor = vtk.vtkActor()
            self.step_actor.SetMapper(mapper)
            self.step_actor.GetProperty().SetColor(0.7, 0.7, 0.7)
            
            self.renderer.AddActor(self.step_actor)
            self.renderer.ResetCamera()
            self.vtk_widget.GetRenderWindow().Render()
            
            self.debug_log(f"🎭 Model displayed")
            
        except Exception as e:
            self.debug_log(f"❌ Display error: {e}")
            
    def debug_complete_text_flow(self, axis, degrees):
        """Debug complete flow from button click to text display"""
        self.debug_log(f"🔘 BUTTON CLICKED: {axis.upper()}{'+' if degrees > 0 else ''}{degrees}°")
        
        # Step 1: Update rotation values
        old_value = self.current_rot[axis]
        self.current_rot[axis] += degrees
        self.debug_log(f"   STEP 1: ROT updated - {axis.upper()}: {old_value:.1f}° → {self.current_rot[axis]:.1f}°")
        
        # Step 2: Calculate axis and angle
        rot_mag = math.sqrt(self.current_rot['x']**2 + self.current_rot['y']**2 + self.current_rot['z']**2)
        
        if rot_mag > 0.001:
            self.current_axis = {
                'x': self.current_rot['x'] / rot_mag,
                'y': self.current_rot['y'] / rot_mag,
                'z': self.current_rot['z'] / rot_mag
            }
            self.current_angle = rot_mag
        else:
            self.current_axis = {'x': 0.0, 'y': 0.0, 'z': 1.0}
            self.current_angle = 0.0
            
        self.debug_log(f"   STEP 2: AXIS calculated - {self.current_axis}")
        self.debug_log(f"   STEP 2: ANGLE calculated - {self.current_angle:.1f}°")
        
        # Step 3: Apply VTK rotation if model exists
        if self.step_actor:
            self.step_actor.RotateWXYZ(degrees,
                1 if axis == 'x' else 0,
                1 if axis == 'y' else 0,
                1 if axis == 'z' else 0)
            self.debug_log(f"   STEP 3: VTK rotation applied to model")
        else:
            self.debug_log(f"   STEP 3: No model - VTK rotation skipped")
        
        # Step 4: Update GUI labels
        self.update_gui_labels()
        self.debug_log(f"   STEP 4: GUI labels updated")
        
        # Step 5: Update VTK text overlay
        self.update_vtk_text_overlay()
        self.debug_log(f"   STEP 5: VTK text overlay updated")
        
        # Step 6: Render to display
        self.vtk_widget.GetRenderWindow().Render()
        self.debug_log(f"   STEP 6: VTK window rendered")
        
        self.debug_log(f"✅ COMPLETE FLOW FINISHED")
        
    def update_gui_labels(self):
        """Update PyQt GUI labels"""
        try:
            rot_text = f"ROT: X={self.current_rot['x']:.1f}° Y={self.current_rot['y']:.1f}° Z={self.current_rot['z']:.1f}°"
            axis_text = f"AXIS: X={self.current_axis['x']:.3f} Y={self.current_axis['y']:.3f} Z={self.current_axis['z']:.3f}"
            angle_text = f"ANGLE: {self.current_angle:.1f}°"
            
            self.rot_label.setText(rot_text)
            self.axis_label.setText(axis_text)
            self.angle_label.setText(angle_text)
            
            self.debug_log(f"      GUI ROT: {rot_text}")
            self.debug_log(f"      GUI AXIS: {axis_text}")
            self.debug_log(f"      GUI ANGLE: {angle_text}")
            
        except Exception as e:
            self.debug_log(f"❌ GUI label update error: {e}")
            
    def update_vtk_text_overlay(self):
        """Update VTK text overlay with current values"""
        try:
            if not self.text_actor:
                self.debug_log("❌ No text actor - cannot update VTK text")
                return
                
            # Generate text content
            text_content = (
                f"ROT: X={self.current_rot['x']:.1f}° Y={self.current_rot['y']:.1f}° Z={self.current_rot['z']:.1f}°\n"
                f"AXIS: X={self.current_axis['x']:.3f} Y={self.current_axis['y']:.3f} Z={self.current_axis['z']:.3f}\n"
                f"ANGLE: {self.current_angle:.1f}°"
            )
            
            self.debug_log(f"      VTK TEXT CONTENT GENERATED:")
            for line in text_content.split('\n'):
                self.debug_log(f"        {line}")
            
            # Set text content to VTK actor
            self.text_actor.SetInput(text_content)
            self.debug_log(f"      VTK TEXT ACTOR UPDATED")
            
            # Update the verification label
            self.vtk_text_label.setText(text_content.replace('\n', ' | '))
            self.debug_log(f"      VERIFICATION LABEL UPDATED")
            
            # Get current text from actor to verify
            current_text = self.text_actor.GetInput()
            if current_text == text_content:
                self.debug_log(f"      ✅ VTK TEXT VERIFICATION: Content matches")
            else:
                self.debug_log(f"      ❌ VTK TEXT VERIFICATION: Content mismatch!")
                self.debug_log(f"        Expected: {text_content}")
                self.debug_log(f"        Actual: {current_text}")
            
        except Exception as e:
            self.debug_log(f"❌ VTK text update error: {e}")
            
    def test_text_update(self):
        """Test text update without rotation"""
        self.debug_log("🧪 TESTING TEXT UPDATE ONLY")
        
        # Set some test values
        self.current_rot = {'x': 45.0, 'y': 30.0, 'z': 15.0}
        self.current_axis = {'x': 0.707, 'y': 0.500, 'z': 0.500}
        self.current_angle = 60.0
        
        self.debug_log("   Test values set")
        
        # Update displays
        self.update_gui_labels()
        self.update_vtk_text_overlay()
        self.vtk_widget.GetRenderWindow().Render()
        
        self.debug_log("✅ TEXT UPDATE TEST COMPLETE")
        
    def debug_log(self, message):
        """Add message to debug log"""
        timestamp = time.strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}"
        print(full_message)
        
        # Add to text widget
        self.debug_text.append(full_message)
        
        # Keep only last 100 lines
        if self.debug_text.document().blockCount() > 100:
            cursor = self.debug_text.textCursor()
            cursor.movePosition(cursor.Start)
            cursor.select(cursor.BlockUnderCursor)
            cursor.removeSelectedText()
            cursor.deletePreviousChar()

def main():
    app = QApplication(sys.argv)
    window = DebugTextDisplayOutput()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
