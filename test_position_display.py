#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Position Display - Debug the position increment/decrement issue
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QLabel
from PyQt5.QtCore import QTimer

class PositionDisplayTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Position Display Test")
        self.setGeometry(100, 100, 600, 400)
        
        # Initialize position tracking like main program
        self.orig_pos_left = {'x': 10.0, 'y': 20.0, 'z': 30.0}  # Example original position
        self.current_pos_left = self.orig_pos_left.copy()
        self.movement_delta_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        
        self.position_increment_value = 0.1  # Like main program
        
        self.setup_ui()
        
    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Display current values
        self.status_label = QLabel("Position Display Test")
        layout.addWidget(self.status_label)
        
        self.orig_label = QLabel(f"Original Position: X={self.orig_pos_left['x']:.3f} Y={self.orig_pos_left['y']:.3f} Z={self.orig_pos_left['z']:.3f}")
        layout.addWidget(self.orig_label)
        
        self.current_label = QLabel(f"Current Position: X={self.current_pos_left['x']:.3f} Y={self.current_pos_left['y']:.3f} Z={self.current_pos_left['z']:.3f}")
        layout.addWidget(self.current_label)
        
        self.delta_label = QLabel(f"Movement Delta: X={self.movement_delta_left['x']:.3f} Y={self.movement_delta_left['y']:.3f} Z={self.movement_delta_left['z']:.3f}")
        layout.addWidget(self.delta_label)
        
        # Test buttons - exact same logic as main program
        button_layout = QHBoxLayout()
        
        btn_x_minus = QPushButton("X- (should decrease)")
        btn_x_minus.clicked.connect(lambda: self.move_shape('x', -self.position_increment_value))
        button_layout.addWidget(btn_x_minus)
        
        btn_x_plus = QPushButton("X+ (should increase)")
        btn_x_plus.clicked.connect(lambda: self.move_shape('x', self.position_increment_value))
        button_layout.addWidget(btn_x_plus)
        
        btn_y_minus = QPushButton("Y- (should decrease)")
        btn_y_minus.clicked.connect(lambda: self.move_shape('y', -self.position_increment_value))
        button_layout.addWidget(btn_y_minus)
        
        btn_y_plus = QPushButton("Y+ (should increase)")
        btn_y_plus.clicked.connect(lambda: self.move_shape('y', self.position_increment_value))
        button_layout.addWidget(btn_y_plus)
        
        layout.addLayout(button_layout)
        
        # Reset button
        btn_reset = QPushButton("Reset to Original")
        btn_reset.clicked.connect(self.reset_position)
        layout.addWidget(btn_reset)
        
    def move_shape(self, axis, amount):
        """Test the exact same logic as main program"""
        print(f"🔧 TEST: move_shape called with axis='{axis}', amount={amount}")
        print(f"🔧 TEST: Expected behavior - amount={amount} should {'INCREASE' if amount > 0 else 'DECREASE'} {axis.upper()} value")
        
        # Update movement delta (this shows how much we've moved from original)
        self.movement_delta_left[axis] += amount
        print(f"🔧 TEST: movement_delta_left[{axis}] += {amount} = {self.movement_delta_left[axis]}")
        
        # Calculate display position (original + movement)
        display_pos = {
            'x': self.orig_pos_left['x'] + self.movement_delta_left['x'],
            'y': self.orig_pos_left['y'] + self.movement_delta_left['y'],
            'z': self.orig_pos_left['z'] + self.movement_delta_left['z']
        }
        print(f"🔧 TEST: display_pos[{axis}] = {self.orig_pos_left[axis]} + {self.movement_delta_left[axis]} = {display_pos[axis]}")
        
        # Update current_pos_left to reflect the new position
        self.current_pos_left = display_pos.copy()
        print(f"🔧 TEST: current_pos_left[{axis}] = {self.current_pos_left[axis]}")
        
        # Update display
        self.update_display()
        
    def reset_position(self):
        """Reset to original position"""
        self.current_pos_left = self.orig_pos_left.copy()
        self.movement_delta_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.update_display()
        print("🔧 TEST: Reset to original position")
        
    def update_display(self):
        """Update the display labels"""
        self.current_label.setText(f"Current Position: X={self.current_pos_left['x']:.3f} Y={self.current_pos_left['y']:.3f} Z={self.current_pos_left['z']:.3f}")
        self.delta_label.setText(f"Movement Delta: X={self.movement_delta_left['x']:.3f} Y={self.movement_delta_left['y']:.3f} Z={self.movement_delta_left['z']:.3f}")
        
        # Show POS text like main program
        pos_text = f"POS: X={self.current_pos_left['x']:.3f}mm Y={self.current_pos_left['y']:.3f}mm Z={self.current_pos_left['z']:.3f}mm"
        self.status_label.setText(pos_text)

def main():
    app = QApplication(sys.argv)
    test = PositionDisplayTest()
    test.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
