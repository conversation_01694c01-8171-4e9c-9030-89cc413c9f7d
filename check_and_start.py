#!/usr/bin/env python3
import ast
import subprocess
import sys

# Check syntax
try:
    with open('step_loader.py', 'r') as f:
        ast.parse(f.read())
    print("step_loader.py syntax OK")
except Exception as e:
    print(f"step_loader.py syntax ERROR: {e}")
    sys.exit(1)

# Start main program
print("Starting main program...")
subprocess.Popen([sys.executable, 'step_viewer_tdk_modular.py'])
print("Main program started")
