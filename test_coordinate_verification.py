#!/usr/bin/env python3
"""
STEP File Coordinate Verification Test
Shows original coordinates, what gets written, and what gets read back
"""

import os
import sys
import re
import numpy as np
from pathlib import Path

# Add the current directory to Python path for imports
sys.path.append(str(Path(__file__).parent))

from simple_step_modifier import SimpleSTEPModifier
import vtk

def extract_root_coordinates_from_step(step_file):
    """Extract the root coordinate system values from STEP file"""
    print(f"🔍 READING ROOT COORDINATES FROM: {step_file}")
    
    with open(step_file, 'r') as f:
        content = f.read()
    
    # Extract position from #12 (root CARTESIAN_POINT)
    point_match = re.search(r'#12\s*=\s*CARTESIAN_POINT\s*\([^;]+\)\s*;', content)
    position = [0.0, 0.0, 0.0]
    if point_match:
        point_line = point_match.group(0)
        coords_match = re.search(r'\(\s*[^,]+,\s*\(\s*([^,]+),\s*([^,]+),\s*([^)]+)\s*\)', point_line)
        if coords_match:
            position = [float(coords_match.group(1)), float(coords_match.group(2)), float(coords_match.group(3))]
            print(f"   📍 Position #12: ({position[0]:.6f}, {position[1]:.6f}, {position[2]:.6f})")
    
    # Extract Z direction from #13
    z_dir_match = re.search(r'#13\s*=\s*DIRECTION\s*\([^;]+\)\s*;', content)
    z_direction = [0.0, 0.0, 1.0]
    if z_dir_match:
        z_line = z_dir_match.group(0)
        z_coords_match = re.search(r'\(\s*[^,]+,\s*\(\s*([^,]+),\s*([^,]+),\s*([^)]+)\s*\)', z_line)
        if z_coords_match:
            z_direction = [float(z_coords_match.group(1)), float(z_coords_match.group(2)), float(z_coords_match.group(3))]
            print(f"   🔺 Z Direction #13: ({z_direction[0]:.6f}, {z_direction[1]:.6f}, {z_direction[2]:.6f})")
    
    # Extract X direction from #14
    x_dir_match = re.search(r'#14\s*=\s*DIRECTION\s*\([^;]+\)\s*;', content)
    x_direction = [1.0, 0.0, 0.0]
    if x_dir_match:
        x_line = x_dir_match.group(0)
        x_coords_match = re.search(r'\(\s*[^,]+,\s*\(\s*([^,]+),\s*([^,]+),\s*([^)]+)\s*\)', x_line)
        if x_coords_match:
            x_direction = [float(x_coords_match.group(1)), float(x_coords_match.group(2)), float(x_coords_match.group(3))]
            print(f"   ➡️  X Direction #14: ({x_direction[0]:.6f}, {x_direction[1]:.6f}, {x_direction[2]:.6f})")
    
    return {
        'position': position,
        'z_direction': z_direction,
        'x_direction': x_direction
    }

def create_test_rotation():
    """Create a simple test rotation"""
    # Create a simple 45-degree rotation around Z axis
    transform = vtk.vtkTransform()
    transform.RotateZ(45.0)  # 45 degrees around Z
    
    matrix = transform.GetMatrix()
    print(f"🔧 TEST ROTATION: 45° around Z-axis")
    print(f"   VTK Matrix:")
    for i in range(4):
        row = [matrix.GetElement(i, j) for j in range(4)]
        print(f"   [{row[0]:8.4f} {row[1]:8.4f} {row[2]:8.4f} {row[3]:8.4f}]")
    
    return matrix

def main():
    print("=" * 80)
    print("🔧 STEP FILE COORDINATE VERIFICATION TEST")
    print("=" * 80)
    
    # Test files
    original_file = "test.step"
    test_file = "coordinate_test.step"
    
    if not os.path.exists(original_file):
        print(f"❌ Original file not found: {original_file}")
        return
    
    print(f"✅ Using original file: {original_file}")
    print(f"✅ Test output file: {test_file}")
    
    # STEP 1: Read original coordinates
    print(f"\n📋 STEP 1: ORIGINAL COORDINATES")
    print("-" * 50)
    original_coords = extract_root_coordinates_from_step(original_file)
    
    # STEP 2: Create test rotation
    print(f"\n📋 STEP 2: CREATING TEST ROTATION")
    print("-" * 50)
    test_matrix = create_test_rotation()
    
    # STEP 3: Load original file and apply transformation
    print(f"\n📋 STEP 3: LOADING AND TRANSFORMING")
    print("-" * 50)

    print(f"✅ Ready to apply transformation")
    
    # STEP 4: Save with transformation
    print(f"\n📋 STEP 4: SAVING WITH TRANSFORMATION")
    print("-" * 50)

    print(f"🔧 Creating STEP modifier and applying transformation...")
    modifier = SimpleSTEPModifier()

    # Load the original file into the modifier
    if not modifier.load_step_file(original_file):
        print(f"❌ Failed to load file into modifier")
        return

    # Apply the transformation to geometry
    print(f"🔧 Applying geometry transformation...")
    if not modifier.transform_geometry_coordinates(test_matrix):
        print(f"❌ Failed to transform coordinates")
        return

    # Apply transformation to coordinate system
    print(f"🔧 Applying coordinate system transformation...")

    # Extract rotation from matrix for coordinate system
    # For 45° Z rotation, we expect:
    # Z direction: (0, 0, 1) - unchanged
    # X direction: (cos45, sin45, 0) = (0.707, 0.707, 0)
    cos45 = np.cos(np.radians(45))
    sin45 = np.sin(np.radians(45))

    new_position = [0.0, 0.0, 0.0]  # No translation
    new_z_direction = [0.0, 0.0, 1.0]  # Z unchanged for Z rotation
    new_x_direction = [cos45, sin45, 0.0]  # X rotated 45° around Z

    print(f"🔧 Setting coordinate system to:")
    print(f"   Position: ({new_position[0]:.6f}, {new_position[1]:.6f}, {new_position[2]:.6f})")
    print(f"   Z direction: ({new_z_direction[0]:.6f}, {new_z_direction[1]:.6f}, {new_z_direction[2]:.6f})")
    print(f"   X direction: ({new_x_direction[0]:.6f}, {new_x_direction[1]:.6f}, {new_x_direction[2]:.6f})")

    # Use modify_placement with rotation angles instead of direction vectors
    # For 45° Z rotation: X=0, Y=0, Z=45
    if not modifier.modify_placement(0.0, 0.0, 0.0, 0.0, 0.0, 45.0):
        print(f"❌ Failed to set coordinate system")
        return

    # Save the modified file
    print(f"🔧 Saving transformed file...")
    if not modifier.save_step_file(test_file):
        print(f"❌ Failed to save transformed file")
        return

    print(f"✅ Transformed file saved: {test_file}")
    
    # STEP 5: Read back the saved coordinates
    print(f"\n📋 STEP 5: READING SAVED COORDINATES")
    print("-" * 50)
    saved_coords = extract_root_coordinates_from_step(test_file)
    
    # STEP 6: Verification
    print(f"\n📋 STEP 6: COORDINATE VERIFICATION")
    print("-" * 50)
    
    print(f"📊 COMPARISON:")
    print(f"   ORIGINAL Position: ({original_coords['position'][0]:8.6f}, {original_coords['position'][1]:8.6f}, {original_coords['position'][2]:8.6f})")
    print(f"   SAVED    Position: ({saved_coords['position'][0]:8.6f}, {saved_coords['position'][1]:8.6f}, {saved_coords['position'][2]:8.6f})")
    
    print(f"   ORIGINAL Z-dir:    ({original_coords['z_direction'][0]:8.6f}, {original_coords['z_direction'][1]:8.6f}, {original_coords['z_direction'][2]:8.6f})")
    print(f"   SAVED    Z-dir:    ({saved_coords['z_direction'][0]:8.6f}, {saved_coords['z_direction'][1]:8.6f}, {saved_coords['z_direction'][2]:8.6f})")
    
    print(f"   ORIGINAL X-dir:    ({original_coords['x_direction'][0]:8.6f}, {original_coords['x_direction'][1]:8.6f}, {original_coords['x_direction'][2]:8.6f})")
    print(f"   SAVED    X-dir:    ({saved_coords['x_direction'][0]:8.6f}, {saved_coords['x_direction'][1]:8.6f}, {saved_coords['x_direction'][2]:8.6f})")
    
    # Calculate expected values for 45° Z rotation
    print(f"\n📊 EXPECTED VALUES FOR 45° Z ROTATION:")
    cos45 = np.cos(np.radians(45))
    sin45 = np.sin(np.radians(45))
    
    # For 45° Z rotation:
    # Z direction should remain (0,0,1)
    # X direction should become (cos45, sin45, 0) = (0.707, 0.707, 0)
    expected_z = [0.0, 0.0, 1.0]
    expected_x = [cos45, sin45, 0.0]
    
    print(f"   EXPECTED Z-dir:    ({expected_z[0]:8.6f}, {expected_z[1]:8.6f}, {expected_z[2]:8.6f})")
    print(f"   EXPECTED X-dir:    ({expected_x[0]:8.6f}, {expected_x[1]:8.6f}, {expected_x[2]:8.6f})")
    
    # Check if values match expectations
    z_match = np.allclose(saved_coords['z_direction'], expected_z, atol=1e-5)
    x_match = np.allclose(saved_coords['x_direction'], expected_x, atol=1e-5)
    
    print(f"\n🔍 VERIFICATION RESULTS:")
    if z_match:
        print(f"   ✅ Z direction matches expected values")
    else:
        print(f"   ❌ Z direction does NOT match expected values")
    
    if x_match:
        print(f"   ✅ X direction matches expected values")
    else:
        print(f"   ❌ X direction does NOT match expected values")
    
    if z_match and x_match:
        print(f"\n🎉 SUCCESS: Coordinate transformation is working correctly!")
    else:
        print(f"\n❌ FAILURE: Coordinate transformation has issues!")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
