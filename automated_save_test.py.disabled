#!/usr/bin/env python3
"""
Automated testing to verify STEP file saves are working correctly
Keep testing until we see the correct changes in the actual STEP file
"""

import sys
import os
import shutil
import math

# Add current directory to path for imports
sys.path.append('.')

from step_transformer import STEPTransformer

def analyze_step_file_coordinates(filename, description=""):
    """Analyze coordinate points in a STEP file"""
    print(f"\n🔍 ANALYZING: {filename} - {description}")
    
    if not os.path.exists(filename):
        print(f"❌ File not found: {filename}")
        return None
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find coordinate points
        import re
        coord_pattern = r'CARTESIAN_POINT\s*\(\s*\'[^\']*\'\s*,\s*\(\s*([-\d.E+-]+)\s*,\s*([-\d.E+-]+)\s*,\s*([-\d.E+-]+)\s*\)'
        coords = re.findall(coord_pattern, content)
        
        print(f"   File size: {len(content)} chars, {len(coords)} coordinate points")
        
        # Get first 5 coordinates for analysis
        sample_coords = []
        for i, (x, y, z) in enumerate(coords[:5]):
            coord = (float(x), float(y), float(z))
            sample_coords.append(coord)
            print(f"   Point {i+1}: ({coord[0]:10.6f}, {coord[1]:10.6f}, {coord[2]:10.6f})")
        
        return sample_coords
        
    except Exception as e:
        print(f"❌ Error analyzing file: {e}")
        return None

def test_manual_rotation_45_degrees():
    """Test manual 45-degree rotation to verify it works"""
    print("\n🧪 TEST 1: Manual 45-degree rotation")
    print("=" * 50)
    
    # Load original file
    transformer = STEPTransformer()
    if not transformer.load_step_file("test.step"):
        print("❌ Failed to load test.step")
        return False
    
    # Apply 45-degree Z rotation
    success = transformer.apply_transformation(
        rotation_x=0.0,
        rotation_y=0.0,
        rotation_z=45.0,
        translation_x=0.0,
        translation_y=0.0,
        translation_z=0.0
    )
    
    if not success:
        print("❌ Transformation failed")
        return False
    
    # Save result
    output_file = "test_manual_45deg.step"
    if not transformer.save_step_file(output_file):
        print("❌ Save failed")
        return False
    
    print(f"✅ Manual 45° rotation saved: {output_file}")
    return True

def test_gui_simulation():
    """Simulate what the GUI should be doing"""
    print("\n🧪 TEST 2: GUI simulation")
    print("=" * 50)
    
    # Simulate GUI scenario:
    # - Original file has 9° rotation
    # - User adds 45° more rotation
    # - Total should be 54°, but we should apply only the 45° delta
    
    print("📝 Scenario: Original 9° + User adds 45° = 54° total")
    print("   We should apply only the 45° delta to the geometry")
    
    # Method 1: Apply only the delta (45°)
    transformer1 = STEPTransformer()
    if transformer1.load_step_file("test.step"):
        success1 = transformer1.apply_transformation(
            rotation_z=45.0,  # Only the delta
            rotation_x=0.0, rotation_y=0.0,
            translation_x=0.0, translation_y=0.0, translation_z=0.0
        )
        if success1:
            transformer1.save_step_file("test_gui_delta_45.step")
            print("✅ GUI simulation (delta 45°) saved")
    
    # Method 2: Apply the total (54°) - this is wrong but let's see
    transformer2 = STEPTransformer()
    if transformer2.load_step_file("test.step"):
        success2 = transformer2.apply_transformation(
            rotation_z=54.0,  # Total rotation
            rotation_x=0.0, rotation_y=0.0,
            translation_x=0.0, translation_y=0.0, translation_z=0.0
        )
        if success2:
            transformer2.save_step_file("test_gui_total_54.step")
            print("✅ GUI simulation (total 54°) saved")
    
    return True

def test_step_viewer_integration():
    """Test the actual step viewer save functionality"""
    print("\n🧪 TEST 3: Step viewer integration test")
    print("=" * 50)
    
    # Import the step viewer modules
    try:
        from step_loader import STEPLoader
        
        # Load the file
        loader = STEPLoader()
        if not loader.load_step_file("test.step"):
            print("❌ Failed to load with STEPLoader")
            return False
        
        print("✅ Loaded with STEPLoader")
        
        # Test save without transformations
        print("📝 Testing save without transformations...")
        success1 = loader.save_step_file("test_steploader_no_transform.step")
        print(f"   Result: {success1}")
        
        # Test save with transformations (simulate 45° rotation)
        print("📝 Testing save with transformations...")
        import vtk
        transform = vtk.vtkTransform()
        transform.RotateZ(45.0)
        
        success2 = loader.save_step_file("test_steploader_with_transform.step", transform.GetMatrix())
        print(f"   Result: {success2}")
        
        return success1 and success2
        
    except Exception as e:
        print(f"❌ Step viewer integration failed: {e}")
        return False

def verify_rotation_math():
    """Verify that our rotation math is correct"""
    print("\n🧪 TEST 4: Rotation math verification")
    print("=" * 50)
    
    # Test point: (1, 0, 0) - should rotate to (cos(45°), sin(45°), 0) = (0.707, 0.707, 0)
    test_x, test_y, test_z = 1.0, 0.0, 0.0
    angle_deg = 45.0
    angle_rad = math.radians(angle_deg)
    
    # Expected result after 45° Z rotation
    expected_x = test_x * math.cos(angle_rad) - test_y * math.sin(angle_rad)
    expected_y = test_x * math.sin(angle_rad) + test_y * math.cos(angle_rad)
    expected_z = test_z
    
    print(f"   Test point: ({test_x}, {test_y}, {test_z})")
    print(f"   45° Z rotation should give: ({expected_x:.6f}, {expected_y:.6f}, {expected_z:.6f})")
    print(f"   Expected: (0.707107, 0.707107, 0.000000)")
    
    # Check if our math matches
    if abs(expected_x - 0.707107) < 0.000001 and abs(expected_y - 0.707107) < 0.000001:
        print("✅ Rotation math is correct")
        return True
    else:
        print("❌ Rotation math is wrong!")
        return False

def compare_all_results():
    """Compare all the test results to see which approach works"""
    print("\n🔍 COMPARING ALL RESULTS")
    print("=" * 60)
    
    files_to_analyze = [
        ("test.step", "Original file"),
        ("test_manual_45deg.step", "Manual 45° rotation"),
        ("test_gui_delta_45.step", "GUI delta 45°"),
        ("test_gui_total_54.step", "GUI total 54°"),
        ("test_steploader_no_transform.step", "StepLoader no transform"),
        ("test_steploader_with_transform.step", "StepLoader with transform"),
    ]
    
    results = {}
    for filename, description in files_to_analyze:
        coords = analyze_step_file_coordinates(filename, description)
        if coords:
            results[filename] = coords
    
    # Compare results
    if "test.step" in results:
        original = results["test.step"]
        print(f"\n📊 COMPARISON WITH ORIGINAL:")
        
        for filename, coords in results.items():
            if filename != "test.step" and coords:
                print(f"\n   {filename}:")
                for i, (orig, new) in enumerate(zip(original, coords)):
                    if i < 3:  # Show first 3 points
                        orig_x, orig_y, orig_z = orig
                        new_x, new_y, new_z = new
                        
                        # Calculate distance from origin
                        orig_dist = math.sqrt(orig_x**2 + orig_y**2 + orig_z**2)
                        new_dist = math.sqrt(new_x**2 + new_y**2 + new_z**2)
                        
                        # Check if it's a rotation (distance preserved)
                        dist_diff = abs(orig_dist - new_dist)
                        
                        print(f"     Point {i+1}: ({orig_x:8.4f}, {orig_y:8.4f}, {orig_z:8.4f}) → ({new_x:8.4f}, {new_y:8.4f}, {new_z:8.4f})")
                        
                        if dist_diff < 0.001:
                            print(f"       ✅ Distance preserved: {orig_dist:.4f} → {new_dist:.4f}")
                        else:
                            print(f"       ❌ Distance changed: {orig_dist:.4f} → {new_dist:.4f}")
                        
                        # For point (0,0,0), it should stay at origin for pure rotation
                        if i == 0 and abs(orig_x) < 0.001 and abs(orig_y) < 0.001 and abs(orig_z) < 0.001:
                            if abs(new_x) < 0.001 and abs(new_y) < 0.001 and abs(new_z) < 0.001:
                                print(f"       ✅ Origin correctly unchanged")
                            else:
                                print(f"       ❌ Origin moved unexpectedly")

def main():
    """Main automated testing function"""
    print("🔧 AUTOMATED STEP FILE SAVE TESTING")
    print("=" * 70)
    print("Testing until we see correct changes in actual STEP files...")
    
    # Run all tests
    test1_ok = test_manual_rotation_45_degrees()
    test2_ok = test_gui_simulation()
    test3_ok = test_step_viewer_integration()
    test4_ok = verify_rotation_math()
    
    # Analyze all results
    compare_all_results()
    
    print("\n" + "=" * 70)
    print("🎯 AUTOMATED TESTING COMPLETE")
    print(f"   Manual rotation: {'✅ OK' if test1_ok else '❌ FAILED'}")
    print(f"   GUI simulation: {'✅ OK' if test2_ok else '❌ FAILED'}")
    print(f"   StepLoader integration: {'✅ OK' if test3_ok else '❌ FAILED'}")
    print(f"   Rotation math: {'✅ OK' if test4_ok else '❌ FAILED'}")
    
    print("\n💡 Check the coordinate comparisons above to see which method produces correct rotations!")

if __name__ == "__main__":
    main()
