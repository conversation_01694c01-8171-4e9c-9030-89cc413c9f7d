#!/usr/bin/env python3
try:
    from OCC.Core.BRepMesh import BRepMesh_IncrementalMesh
    print("SUCCESS: BRepMesh_IncrementalMesh import works")
except Exception as e:
    print(f"FAIL: BRepMesh_IncrementalMesh import failed: {e}")
    
    # Try alternative imports
    try:
        from OCC.Core import BRepMesh
        print("SUCCESS: BRepMesh module import works")
        print(f"BRepMesh contents: {[attr for attr in dir(BRepMesh) if 'Mesh' in attr]}")
    except Exception as e2:
        print(f"FAIL: BRepMesh module import failed: {e2}")
