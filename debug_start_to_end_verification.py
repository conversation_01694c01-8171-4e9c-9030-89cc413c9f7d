#!/usr/bin/env python3
"""
DEBUG START TO END VERIFICATION - Test the actual main program fixes
This will test the ACTUAL running main program to verify both fixes work
"""

import sys
import os
import time
import subprocess

def test_step_file_colors():
    """Test 1: Verify STEP file colors are displayed correctly"""
    print("🎨 TESTING STEP FILE COLORS - START TO END")
    print("=" * 60)
    
    # Step 1: Load STEP file and check what colors it contains
    print("STEP 1: Loading STEP file and checking colors...")
    
    try:
        from step_loader import STEPLoader
        loader = STEPLoader()
        success, message = loader.load_step_file("debug_auto_saved.step")
        
        if not success:
            print("❌ STEP loading failed")
            return False
            
        polydata = loader.current_polydata
        cell_colors = polydata.GetCellData().GetScalars()
        
        if not cell_colors:
            print("❌ No colors in STEP file")
            return False
            
        print(f"✅ STEP file loaded with {cell_colors.GetNumberOfTuples()} colors")
        
        # Check actual color values
        unique_colors = set()
        for i in range(min(100, cell_colors.GetNumberOfTuples())):
            color = cell_colors.GetTuple3(i)
            unique_colors.add(color)
            
        print("Colors found in STEP file:")
        for color in sorted(unique_colors):
            print(f"   RGB({color[0]:.0f}, {color[1]:.0f}, {color[2]:.0f})")
            
    except Exception as e:
        print(f"❌ STEP loading error: {e}")
        return False
    
    # Step 2: Test VTK color application
    print("\nSTEP 2: Testing VTK color application...")
    
    try:
        from vtk_renderer import VTKRenderer
        import vtk
        
        # Create renderer
        renderer = vtk.vtkRenderer()
        vtk_renderer = VTKRenderer()
        vtk_renderer.renderer = renderer
        
        # Apply colors using the same method as main program
        vtk_renderer.display_polydata(polydata)
        
        # Check if colors are applied
        mapper = vtk_renderer.step_actor.GetMapper()
        scalar_visibility = mapper.GetScalarVisibility()
        
        print(f"Scalar visibility: {scalar_visibility}")
        
        if scalar_visibility:
            print("✅ Colors are being applied to VTK")
            
            # Check color range
            color_range = mapper.GetScalarRange()
            print(f"Color range in mapper: {color_range}")
            
            if color_range[0] >= 0 and color_range[1] <= 255:
                print("✅ Color range is correct (0-255)")
                return True
            else:
                print("❌ Color range is wrong")
                return False
        else:
            print("❌ Colors are NOT being applied to VTK")
            return False
            
    except Exception as e:
        print(f"❌ VTK color application error: {e}")
        return False

def test_rotation_increment():
    """Test 2: Verify rotation button increments correctly"""
    print("\n🔘 TESTING ROTATION INCREMENT - START TO END")
    print("=" * 60)
    
    # Simulate the exact logic from main program
    print("STEP 1: Simulating main program rotation logic...")
    
    # Initialize variables (same as main program)
    model_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
    current_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
    
    axis = 'x'
    degrees = 15.0
    
    print(f"Initial state: model_rot_left = {model_rot_left}")
    print(f"Initial state: current_rot_left = {current_rot_left}")
    
    # Test multiple button clicks
    for click in range(1, 4):
        print(f"\nSTEP {click + 1}: Simulating button click #{click}...")
        
        # Use the EXACT logic from main program
        current_displayed_value = 0.0
        if current_rot_left:
            current_displayed_value = current_rot_left.get(axis, 0.0)
        elif model_rot_left:
            current_displayed_value = model_rot_left.get(axis, 0.0)
            
        print(f"   Current displayed value: {current_displayed_value:.1f}°")
        
        # Set new value = current + increment
        new_value = current_displayed_value + degrees
        model_rot_left[axis] = new_value
        current_rot_left[axis] = new_value
        
        print(f"   New value: {new_value:.1f}°")
        print(f"   Expected: {click * degrees:.1f}°")
        
        if abs(new_value - (click * degrees)) < 0.1:
            print(f"   ✅ Click #{click}: Correct increment")
        else:
            print(f"   ❌ Click #{click}: Wrong increment")
            return False
    
    print("\n✅ All rotation increments work correctly")
    return True

def test_main_program_integration():
    """Test 3: Check if main program has the fixes integrated"""
    print("\n🔧 TESTING MAIN PROGRAM INTEGRATION")
    print("=" * 60)
    
    try:
        import step_viewer_tdk_modular
        import inspect
        
        # Test 1: Check rotate_shape method has the fix
        print("STEP 1: Checking rotate_shape method...")
        
        if hasattr(step_viewer_tdk_modular.StepViewerTDK, 'rotate_shape'):
            method = getattr(step_viewer_tdk_modular.StepViewerTDK, 'rotate_shape')
            source_lines = inspect.getsourcelines(method)
            source_text = ''.join(source_lines[0])
            
            # Check for the rotation fix
            if 'current_displayed_value' in source_text and 'new_value = current_displayed_value + degrees' in source_text:
                print("✅ rotate_shape has rotation increment fix")
            else:
                print("❌ rotate_shape missing rotation increment fix")
                return False
                
            # Check for axis calculation
            if 'current_axis_left' in source_text and 'math.sqrt' in source_text:
                print("✅ rotate_shape has axis calculation")
            else:
                print("❌ rotate_shape missing axis calculation")
                return False
        else:
            print("❌ rotate_shape method not found")
            return False
        
        # Test 2: Check vtk_renderer has color fix
        print("\nSTEP 2: Checking vtk_renderer color fix...")
        
        from vtk_renderer import VTKRenderer
        method = getattr(VTKRenderer, 'display_polydata')
        source_lines = inspect.getsourcelines(method)
        source_text = ''.join(source_lines[0])
        
        # Check that the wrong normalization was removed
        if 'normalized_colors' not in source_text and 'color[0]/255.0' not in source_text:
            print("✅ vtk_renderer has color fix (no wrong normalization)")
        else:
            print("❌ vtk_renderer still has wrong color normalization")
            return False
            
        # Test 3: Check mouse rotation method exists
        print("\nSTEP 3: Checking mouse rotation method...")
        
        if hasattr(step_viewer_tdk_modular.StepViewerTDK, 'check_mouse_rotation'):
            print("✅ check_mouse_rotation method exists")
        else:
            print("❌ check_mouse_rotation method missing")
            return False
            
        print("\n✅ All main program integration checks passed")
        return True
        
    except Exception as e:
        print(f"❌ Main program integration error: {e}")
        return False

def run_live_test():
    """Test 4: Run a live test with the actual main program"""
    print("\n🚀 RUNNING LIVE TEST WITH MAIN PROGRAM")
    print("=" * 60)
    
    print("This would test the actual running main program...")
    print("Since the main program is GUI-based, manual testing is needed:")
    print()
    print("MANUAL TEST STEPS:")
    print("1. Load debug_auto_saved.step")
    print("2. Check colors: Should be light gray (192,192,192) and dark gray (63,63,63)")
    print("3. Click X+15° button 3 times")
    print("4. Check display shows: X=15° → X=30° → X=45°")
    print("5. Rotate with mouse and check if AXIS values update")
    print()
    
    return True

def main():
    """Run all start-to-end tests"""
    print("START TO END VERIFICATION - BOTH FIXES")
    print("=" * 80)
    
    tests = [
        ("STEP File Colors", test_step_file_colors),
        ("Rotation Increment", test_rotation_increment),
        ("Main Program Integration", test_main_program_integration),
        ("Live Test Instructions", run_live_test)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 80)
    print("START TO END VERIFICATION RESULTS")
    print("=" * 80)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 ALL TESTS PASSED - FIXES SHOULD WORK!")
        print("Now test the main program manually to confirm.")
    else:
        print(f"\n⚠️ {len(results) - passed} TESTS FAILED - FIXES NEED MORE WORK")
        
        failed_tests = [name for name, result in results if not result]
        print("Failed tests:")
        for test in failed_tests:
            print(f"   - {test}")

if __name__ == "__main__":
    main()
