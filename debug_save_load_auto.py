#!/usr/bin/env python3
"""
Automated debug version of the main program to test save/load workflow
This will automatically:
1. Load your STEP file
2. Apply rotations and movements 
3. Save the transformed file
4. Load it in bottom viewer
5. Compare the values and show exactly what's wrong
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow
from PyQt5.QtCore import QTimer

# Import the main program components
from step_loader import STEPLoader
from vtk_renderer import VTK<PERSON>enderer
from simple_step_modifier import SimpleSTEPModifier
import vtk

class DebugSaveLoadTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("DEBUG: Automated Save/Load Test")
        self.setGeometry(100, 100, 800, 600)
        
        # Initialize components like the main program
        self.step_loader_left = STEPLoader()   # Top viewer
        self.step_loader_right = STEPLoader()  # Bottom viewer
        self.vtk_renderer_left = VTK<PERSON>enderer(self)
        self.vtk_renderer_right = VT<PERSON><PERSON>enderer(self)
        
        # Track positions and rotations like main program
        self.orig_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.orig_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        
        self.orig_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.orig_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        
        # Start the automated test after GUI is ready
        QTimer.singleShot(1000, self.run_automated_test)
        
    def run_automated_test(self):
        """Run the complete automated test"""
        print("=" * 80)
        print("🔧 AUTOMATED SAVE/LOAD DEBUG TEST")
        print("=" * 80)
        
        # STEP 1: Load STEP file in top viewer (like user clicking "Open STEP File")
        print("\n📋 STEP 1: LOAD STEP FILE IN TOP VIEWER")
        print("-" * 60)
        
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"  # Your file from screenshot
        if not os.path.exists(step_file):
            step_file = "test.step"  # Fallback
            
        print(f"🔧 Loading file: {step_file}")
        success = self.step_loader_left.load_step_file(step_file)
        
        if not success:
            print(f"❌ Failed to load {step_file}")
            return
            
        print(f"✅ Loaded {step_file} in top viewer")
        
        # Extract original position like the main program does
        self.orig_pos_left = self.step_loader_left.original_position.copy()
        print(f"📊 ORIGINAL POS (Top): X={self.orig_pos_left['x']:.3f}mm Y={self.orig_pos_left['y']:.3f}mm Z={self.orig_pos_left['z']:.3f}mm")
        
        # STEP 2: Apply transformations (like user clicking rotation buttons)
        print("\n📋 STEP 2: APPLY TRANSFORMATIONS (Simulate user clicking buttons)")
        print("-" * 60)
        
        # Apply some rotations and position changes
        test_rot_x = -8.2  # From your screenshot
        test_rot_y = -135.8
        test_rot_z = 15.0
        
        test_pos_x = -0.300  # From your screenshot  
        test_pos_y = 0.300
        test_pos_z = 3.350
        
        self.current_rot_left = {'x': test_rot_x, 'y': test_rot_y, 'z': test_rot_z}
        self.current_pos_left = {'x': test_pos_x, 'y': test_pos_y, 'z': test_pos_z}
        
        print(f"🔧 APPLIED TRANSFORMATIONS (Top Viewer):")
        print(f"   POS: X={test_pos_x:.3f}mm Y={test_pos_y:.3f}mm Z={test_pos_z:.3f}mm")
        print(f"   ROT: X={test_rot_x:.1f}° Y={test_rot_y:.1f}° Z={test_rot_z:.1f}°")
        
        # STEP 3: Save with transformations (like user clicking "Save STEP File")
        print("\n📋 STEP 3: SAVE WITH TRANSFORMATIONS")
        print("-" * 60)
        
        saved_file = "debug_auto_saved.step"
        
        try:
            # Use SimpleSTEPModifier like the main program
            modifier = SimpleSTEPModifier()
            modifier.load_step_file(step_file)
            
            # Create transformation matrix
            transform = vtk.vtkTransform()
            transform.Translate(test_pos_x - self.orig_pos_left['x'], 
                              test_pos_y - self.orig_pos_left['y'], 
                              test_pos_z - self.orig_pos_left['z'])
            transform.RotateX(test_rot_x)
            transform.RotateY(test_rot_y) 
            transform.RotateZ(test_rot_z)
            matrix = transform.GetMatrix()
            
            # Apply transformations
            modifier.transform_geometry_coordinates(matrix)
            modifier.modify_placement(test_pos_x, test_pos_y, test_pos_z, test_rot_x, test_rot_y, test_rot_z)
            
            # Save
            save_success = modifier.save_step_file(saved_file)
            
            if save_success:
                print(f"✅ Saved transformed file: {saved_file}")
            else:
                print(f"❌ Failed to save: {saved_file}")
                return
                
        except Exception as e:
            print(f"❌ Error during save: {e}")
            return
            
        # STEP 4: Load saved file in bottom viewer (like user loading in bottom)
        print("\n📋 STEP 4: LOAD SAVED FILE IN BOTTOM VIEWER")
        print("-" * 60)
        
        success2 = self.step_loader_right.load_step_file(saved_file)
        
        if not success2:
            print(f"❌ Failed to load saved file: {saved_file}")
            return
            
        print(f"✅ Loaded saved file in bottom viewer: {saved_file}")
        
        # Extract position from loaded file
        self.orig_pos_right = self.step_loader_right.original_position.copy()
        self.current_pos_right = self.orig_pos_right.copy()
        
        print(f"📊 LOADED POS (Bottom): X={self.current_pos_right['x']:.3f}mm Y={self.current_pos_right['y']:.3f}mm Z={self.current_pos_right['z']:.3f}mm")
        
        # STEP 5: Compare and analyze the problem
        print("\n📋 STEP 5: COMPARE VALUES AND FIND THE PROBLEM")
        print("-" * 60)
        
        print(f"📊 EXPECTED (Top Viewer):")
        print(f"   POS: X={test_pos_x:.3f}mm Y={test_pos_y:.3f}mm Z={test_pos_z:.3f}mm")
        print(f"   ROT: X={test_rot_x:.1f}° Y={test_rot_y:.1f}° Z={test_rot_z:.1f}°")
        
        print(f"📊 ACTUAL (Bottom Viewer):")
        print(f"   POS: X={self.current_pos_right['x']:.3f}mm Y={self.current_pos_right['y']:.3f}mm Z={self.current_pos_right['z']:.3f}mm")
        print(f"   ROT: X=0.0° Y=0.0° Z=0.0° (not extracted yet)")
        
        # Check differences
        pos_diff_x = abs(self.current_pos_right['x'] - test_pos_x)
        pos_diff_y = abs(self.current_pos_right['y'] - test_pos_y)
        pos_diff_z = abs(self.current_pos_right['z'] - test_pos_z)
        
        print(f"\n🔍 POSITION DIFFERENCES:")
        print(f"   X: {pos_diff_x:.3f}mm {'✅' if pos_diff_x < 0.1 else '❌'}")
        print(f"   Y: {pos_diff_y:.3f}mm {'✅' if pos_diff_y < 0.1 else '❌'}")
        print(f"   Z: {pos_diff_z:.3f}mm {'✅' if pos_diff_z < 0.1 else '❌'}")
        
        if pos_diff_x > 0.1 or pos_diff_y > 0.1 or pos_diff_z > 0.1:
            print(f"\n❌ PROBLEM IDENTIFIED: Position values don't match!")
            print(f"   The save/load workflow is still broken.")
            print(f"   The saved STEP file doesn't preserve the correct position.")
        else:
            print(f"\n✅ Position values match! Save/load works for position.")
            
        print("=" * 80)
        
        # Continue to analyze the STEP file content
        QTimer.singleShot(2000, self.analyze_step_file_content)
        
    def analyze_step_file_content(self):
        """Analyze the actual STEP file content to see what's wrong"""
        print("\n📋 STEP 6: ANALYZE STEP FILE CONTENT")
        print("-" * 60)
        
        saved_file = "debug_auto_saved.step"
        
        if os.path.exists(saved_file):
            print(f"🔍 Analyzing STEP file content: {saved_file}")
            
            with open(saved_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                
            # Look for coordinate system information
            import re
            
            # Find CARTESIAN_POINT entries
            point_pattern = r'#(\d+)\s*=\s*CARTESIAN_POINT\s*\(\s*\'[^\']*\'\s*,\s*\(\s*([-\d.]+)\s*,\s*([-\d.]+)\s*,\s*([-\d.]+)\s*\)\s*\)'
            points = []
            for match in re.finditer(point_pattern, content, re.IGNORECASE):
                point_id = int(match.group(1))
                x, y, z = float(match.group(2)), float(match.group(3)), float(match.group(4))
                points.append((point_id, x, y, z))
                
            print(f"🔍 Found {len(points)} CARTESIAN_POINT entries")
            if points:
                print(f"   First few points:")
                for i, (pid, x, y, z) in enumerate(points[:5]):
                    print(f"     #{pid}: ({x:.3f}, {y:.3f}, {z:.3f})")
                    
            # Look for AXIS2_PLACEMENT_3D entries (coordinate systems)
            axis_pattern = r'#(\d+)\s*=\s*AXIS2_PLACEMENT_3D\s*\(\s*\'[^\']*\'\s*,\s*#(\d+)\s*,\s*#(\d+)\s*,\s*#(\d+)\s*\)'
            axes = []
            for match in re.finditer(axis_pattern, content, re.IGNORECASE):
                axis_id = int(match.group(1))
                point_ref = int(match.group(2))
                z_dir_ref = int(match.group(3))
                x_dir_ref = int(match.group(4))
                axes.append((axis_id, point_ref, z_dir_ref, x_dir_ref))
                
            print(f"🔍 Found {len(axes)} AXIS2_PLACEMENT_3D entries")
            if axes:
                print(f"   Coordinate systems:")
                for axis_id, point_ref, z_dir_ref, x_dir_ref in axes[:3]:
                    print(f"     #{axis_id}: Point=#{point_ref}, Z-dir=#{z_dir_ref}, X-dir=#{x_dir_ref}")
                    
        print("\n🔧 ANALYSIS COMPLETE - Check the output above to see what's wrong!")
        
        # Exit after analysis
        QTimer.singleShot(3000, self.close)

def main():
    app = QApplication(sys.argv)
    
    debug_test = DebugSaveLoadTest()
    debug_test.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
