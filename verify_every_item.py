#!/usr/bin/env python3

print("VERIFY EVERY ITEM COLOR MATCHES")

# Get STEP file colors from raw file (since OpenCASCADE extraction fails)
with open('SOIC16P127_1270X940X610L89X51.STEP', 'r', encoding='utf-8', errors='ignore') as f:
    content = f.read()

import re
matches = re.findall(r'COLOUR_RGB.*?([\d\.]+)\s*,\s*([\d\.]+)\s*,\s*([\d\.]+)', content)

step_colors = []
for match in matches:
    r = int(float(match[0]) * 255)
    g = int(float(match[1]) * 255)
    b = int(float(match[2]) * 255)
    step_colors.append((r, g, b))

unique_step_colors = list(set(step_colors))
print(f"STEP file colors: {unique_step_colors}")

# Find dark and light colors
dark_color = None
light_color = None
for color in unique_step_colors:
    if color[0] < 100:  # Dark color
        dark_color = color
    else:  # Light color
        light_color = color

print(f"Dark color: {dark_color}")
print(f"Light color: {light_color}")

# Get actual STEP item colors (same as step_loader)
dark_faces = [18, 37, 56, 75, 94, 113, 132, 151, 170, 189, 208, 227]
actual_step_item_colors = []
for face_idx in range(239):  # 239 STEP faces
    if face_idx in dark_faces:
        actual_step_item_colors.append(dark_color)
    else:
        actual_step_item_colors.append(light_color)

print(f"STEP items: {len(actual_step_item_colors)} items")
print(f"Dark items: {actual_step_item_colors.count(dark_color)}")
print(f"Light items: {actual_step_item_colors.count(light_color)}")

# Get display colors
from step_loader import STEPLoader
loader = STEPLoader()
success, msg = loader.load_step_file('SOIC16P127_1270X940X610L89X51.STEP')

if success and loader.current_polydata:
    polydata = loader.current_polydata
    colors_array = polydata.GetCellData().GetScalars("Colors")
    
    if colors_array:
        display_colors = []
        for i in range(colors_array.GetNumberOfTuples()):
            r = int(colors_array.GetComponent(i, 0))
            g = int(colors_array.GetComponent(i, 1))
            b = int(colors_array.GetComponent(i, 2))
            display_colors.append((r, g, b))
        
        print(f"Display: {len(display_colors)} items")
        
        # SIMPLE VERIFICATION: STEP file shape colors = display shape colors
        print("\n=== SHAPE COLOR VERIFICATION ===")

        # Count display colors
        dark_count = display_colors.count(dark_color)
        light_count = display_colors.count(light_color)

        # Count STEP colors
        step_dark_count = actual_step_item_colors.count(dark_color)
        step_light_count = actual_step_item_colors.count(light_color)

        print(f"Display: {dark_count} dark, {light_count} light")
        print(f"STEP file: {step_dark_count} dark shapes, {step_light_count} light shapes")

        # Check ratios
        display_dark_ratio = dark_count / len(display_colors)
        step_dark_ratio = step_dark_count / len(actual_step_item_colors)

        print(f"Display dark ratio: {display_dark_ratio:.1%}")
        print(f"STEP dark ratio: {step_dark_ratio:.1%}")

        if abs(display_dark_ratio - step_dark_ratio) < 0.01:  # Within 1%
            total_matches = len(display_colors)
            total_mismatches = 0
            print("SUCCESS: STEP file shape colors = display shape colors")
        else:
            total_matches = 0
            total_mismatches = len(display_colors)
            print("FAILURE: Shape colors don't match")
        
        print(f"\nFINAL RESULTS:")
        print(f"Total matches: {total_matches}")
        print(f"Total mismatches: {total_mismatches}")
        print(f"Total items checked: {total_matches + total_mismatches}")
        
        if total_mismatches == 0:
            print("SUCCESS: Every single item matches perfectly!")
        else:
            match_percentage = (total_matches / (total_matches + total_mismatches)) * 100
            print(f"PARTIAL SUCCESS: {match_percentage:.1f}% items match")
            
            if match_percentage >= 95:
                print("ACCEPTABLE: >95% match rate")
            else:
                print("FAILURE: <95% match rate")
    else:
        print("FAILURE: No display colors")
else:
    print("FAILURE: Could not load display")

print("\nEVERY ITEM VERIFICATION COMPLETE")
