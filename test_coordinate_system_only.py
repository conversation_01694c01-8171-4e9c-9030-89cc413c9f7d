#!/usr/bin/env python3
"""
Test coordinate system only approach (no geometry transformation)
"""

import sys
import os
sys.path.append('.')

from step_viewer_tdk_modular import StepViewerTDK
from PyQt5.QtWidgets import QApplication
from simple_step_modifier import SimpleSTEPModifier
import time

def test_coordinate_system_only():
    """Test saving with ONLY coordinate system updates (no geometry transformation)"""
    print("🧪 TESTING COORDINATE SYSTEM ONLY APPROACH")
    print("=" * 60)
    
    app = QApplication([])
    viewer = StepViewerTDK()
    viewer.show()
    
    try:
        # STEP 1: Load original test.step
        print("\n📂 STEP 1: Loading original test.step...")
        viewer.active_viewer = "top"
        success = viewer.load_step_file_direct('test.step')
        
        if not success:
            print("❌ Failed to load original test.step")
            return
        
        print("✅ Original file loaded successfully")
        
        # STEP 2: Apply a rotation in the viewer (VTK transform only)
        rotation_to_apply = {"x": 90.0, "y": 0.0, "z": 0.0}
        print(f"\n🔄 STEP 2: Applying X=90° rotation in viewer...")
        
        viewer._apply_3d_rotation_matrix("left", rotation_to_apply)
        print("✅ VTK rotation applied to model display")
        
        # STEP 3: Save using ONLY coordinate system updates (no geometry transformation)
        save_filename = "test_coordinate_only.step"
        print(f"\n💾 STEP 3: Saving with COORDINATE SYSTEM ONLY approach...")
        
        # Use SimpleSTEPModifier directly
        modifier = SimpleSTEPModifier()
        if not modifier.load_step_file('test.step'):
            print("❌ Failed to load original file for modification")
            return
        
        # ONLY update coordinate system (no geometry transformation)
        print("🧪 COORDINATE ONLY: Skipping geometry coordinate transformation")
        print("🧪 COORDINATE ONLY: Only updating coordinate system placement")
        
        # Update coordinate system to reflect the rotation
        success = modifier.modify_placement(
            0.0, 0.0, 0.0,  # Position unchanged
            90.0, 0.0, 0.0  # X=90° rotation
        )
        
        if not success:
            print("❌ Failed to update coordinate system")
            return
        
        # Save with rotation values for VTK transform on load
        rotation_values = {"x": 90.0, "y": 0.0, "z": 0.0}
        success = modifier.save_step_file(save_filename, rotation_values)
        
        if not success:
            print("❌ Failed to save coordinate-only file")
            return
        
        print(f"✅ Coordinate-only file saved: {save_filename}")
        
        # STEP 4: Load the saved file and compare
        print(f"\n📂 STEP 4: Loading coordinate-only saved file...")
        viewer.active_viewer = "bottom"
        success = viewer.load_step_file_direct(save_filename)
        
        if not success:
            print("❌ Failed to load coordinate-only saved file")
            return
        
        print("✅ Coordinate-only saved file loaded successfully")
        
        # STEP 5: Compare the results
        print(f"\n🔍 STEP 5: Comparing coordinate-only approach...")
        print(f"📺 TOP viewer: Original + VTK rotation applied")
        print(f"📺 BOTTOM viewer: Coordinate-only saved file")
        print(f"")
        print(f"🎯 Expected result: Both models should look IDENTICAL")
        print(f"   - Same geometry shape and proportions")
        print(f"   - Same pin orientations") 
        print(f"   - Same overall appearance")
        print(f"   - Both showing 90° rotation")
        
        # Check rotation values
        if hasattr(viewer, 'current_rot_right'):
            loaded_rot = viewer.current_rot_right
            print(f"")
            print(f"📊 Rotation comparison:")
            print(f"   Applied rotation: X=90.0°, Y=0.0°, Z=0.0°")
            print(f"   Loaded rotation:  X={loaded_rot['x']:.1f}°, Y={loaded_rot['y']:.1f}°, Z={loaded_rot['z']:.1f}°")
            
            if abs(loaded_rot['x'] - 90.0) < 0.1:
                print(f"   ✅ Rotation values match!")
            else:
                print(f"   ❌ Rotation values don't match")
        
        # Keep window open for visual inspection
        print(f"\n⏳ Keeping window open for 15 seconds for visual inspection...")
        print(f"🔍 Look carefully at both models - they should be IDENTICAL!")
        time.sleep(15)
        
    except Exception as e:
        print(f"❌ Error during coordinate-only test: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n🎯 COORDINATE SYSTEM ONLY TEST COMPLETE")
    app.quit()

if __name__ == "__main__":
    test_coordinate_system_only()
