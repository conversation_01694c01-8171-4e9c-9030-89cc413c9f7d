#!/usr/bin/env python3
"""
Test coordinate system fix using test1.step - verify that saved files maintain standard coordinate systems
"""

import sys
import os
import tempfile
import shutil
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def extract_coordinate_system_info(step_file):
    """Extract coordinate system information from STEP file"""
    try:
        with open(step_file, 'r') as f:
            content = f.read()
        
        # Find DIRECTION entities
        directions = {}
        lines = content.split('\n')
        
        for line in lines:
            if 'DIRECTION' in line and '(' in line and ')' in line:
                # Extract the direction vector
                start = line.find('(')
                end = line.find(')', start)
                if start != -1 and end != -1:
                    vector_str = line[start+1:end]
                    try:
                        # Parse the vector components
                        components = [float(x.strip()) for x in vector_str.split(',')]
                        if len(components) == 3:
                            # Identify standard directions
                            if abs(components[2] - 1.0) < 0.001 and abs(components[0]) < 0.001 and abs(components[1]) < 0.001:
                                directions['Z'] = components
                            elif abs(components[0] - 1.0) < 0.001 and abs(components[1]) < 0.001 and abs(components[2]) < 0.001:
                                directions['X'] = components
                    except:
                        continue
        
        return directions
    except Exception as e:
        print(f"Error reading {step_file}: {e}")
        return {}

def main():
    print("🧪 COORDINATE SYSTEM FIX TEST - Using test.step")
    print("=" * 60)
    
    # Use test.step as the test file
    test_file = "test.step"

    if not os.path.exists(test_file):
        print(f"❌ Test file {test_file} not found!")
        print("   Please ensure test.step exists in the current directory")
        return False
    
    print(f"📁 Using test file: {test_file}")
    
    # Extract original coordinate system
    print("\n🔍 ORIGINAL FILE COORDINATE SYSTEM:")
    original_coords = extract_coordinate_system_info(test_file)
    for direction, vector in original_coords.items():
        print(f"   {direction} Direction: {vector}")
    
    # Import the viewer module
    try:
        from step_viewer_tdk_modular_fixed import StepViewerTDK
        from PyQt5.QtWidgets import QApplication
        import vtk
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    
    # Create a temporary save file
    with tempfile.NamedTemporaryFile(suffix='.step', delete=False) as tmp_file:
        save_file = tmp_file.name
    
    try:
        print(f"\n🔧 TESTING ROTATION SAVE WITH test.step...")
        
        # Create Qt application
        app = QApplication(sys.argv)
        
        # Create viewer
        viewer = StepViewerTDK()
        
        # Load the test file in the top viewer
        viewer.active_viewer = "top"
        loader = viewer.step_loader_left
        
        if not loader.load_step_file(test_file):
            print(f"❌ Failed to load {test_file}")
            return False
        
        print(f"✅ Loaded {test_file}")
        
        # Apply some rotations to the model using the viewer's rotation methods
        print("🔄 Applying test rotations...")

        # Use the viewer's rotation methods
        viewer.rotate_shape('x', 15)  # 15 degrees around X
        viewer.rotate_shape('y', 30)  # 30 degrees around Y
        viewer.rotate_shape('z', 45)  # 45 degrees around Z
        print("✅ Applied rotations: X=15°, Y=30°, Z=45°")

        # Save the rotated model
        print(f"💾 Saving rotated model to: {save_file}")

        success = viewer.save_step_file_option1_direct(save_file)
        
        if not success:
            print("❌ Save failed!")
            return False
        
        print("✅ Save completed")
        
        # Check file size
        if os.path.exists(save_file):
            size = os.path.getsize(save_file)
            print(f"📊 Saved file size: {size:,} bytes")
            
            if size < 1000:
                print("❌ Saved file too small - likely empty or corrupted")
                return False
        else:
            print("❌ Saved file does not exist")
            return False
        
        # Extract coordinate system from saved file
        print("\n🔍 SAVED FILE COORDINATE SYSTEM:")
        saved_coords = extract_coordinate_system_info(save_file)
        for direction, vector in saved_coords.items():
            print(f"   {direction} Direction: {vector}")
        
        # Compare coordinate systems
        print("\n📊 COORDINATE SYSTEM COMPARISON:")
        success = True
        
        for direction in ['X', 'Z']:
            if direction in original_coords and direction in saved_coords:
                orig = original_coords[direction]
                saved = saved_coords[direction]
                
                # Check if they match (within tolerance)
                matches = all(abs(orig[i] - saved[i]) < 0.001 for i in range(3))
                
                print(f"   {direction} Direction: {'✅ MATCH' if matches else '❌ DIFFERENT'}")
                if not matches:
                    print(f"      Original: {orig}")
                    print(f"      Saved:    {saved}")
                    success = False
            else:
                print(f"   {direction} Direction: ❌ MISSING")
                success = False
        
        if success:
            print("\n🎉 SUCCESS: Coordinate systems match!")
            print("   The fix is working - saved files maintain standard coordinate systems")
            print("   Both viewers should now display identical models")
        else:
            print("\n❌ FAILURE: Coordinate systems don't match")
            print("   The saved file has transformed coordinate directions")
            print("   This will cause viewer mismatch issues")
        
        return success
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Clean up
        if os.path.exists(save_file):
            os.unlink(save_file)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
