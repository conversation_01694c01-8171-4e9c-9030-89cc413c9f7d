#!/usr/bin/env python3
"""Simple box display test to verify VTK rendering is working correctly"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout
import vtk
from vtk.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor

class SimpleBoxViewer(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Simple Box Display Test")
        self.setGeometry(100, 100, 800, 600)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # VTK widget
        self.vtk_widget = QVTKRenderWindowInteractor()
        layout.addWidget(self.vtk_widget)
        
        # Create VTK renderer
        self.renderer = vtk.vtkRenderer()
        self.renderer.SetBackground(0.1, 0.1, 0.2)  # Dark blue background
        
        # Create a simple box
        box = vtk.vtkCubeSource()
        box.SetXLength(2.0)
        box.SetYLength(1.0) 
        box.SetZLength(0.5)
        box.SetCenter(0, 0, 0)
        
        # Create mapper and actor
        mapper = vtk.vtkPolyDataMapper()
        mapper.SetInputConnection(box.GetOutputPort())
        
        actor = vtk.vtkActor()
        actor.SetMapper(mapper)
        actor.GetProperty().SetColor(0.8, 0.2, 0.2)  # Red color
        
        # Add to renderer
        self.renderer.AddActor(actor)
        
        # Add red hemisphere origin marker at (0,0,0)
        sphere = vtk.vtkSphereSource()
        sphere.SetCenter(0, 0, 0.1)  # Slightly above origin
        sphere.SetRadius(0.1)
        sphere.SetStartPhi(0)
        sphere.SetEndPhi(90)  # Hemisphere
        
        sphere_mapper = vtk.vtkPolyDataMapper()
        sphere_mapper.SetInputConnection(sphere.GetOutputPort())
        
        sphere_actor = vtk.vtkActor()
        sphere_actor.SetMapper(sphere_mapper)
        sphere_actor.GetProperty().SetColor(1.0, 0.0, 0.0)  # Bright red
        
        self.renderer.AddActor(sphere_actor)
        
        # Setup render window
        render_window = self.vtk_widget.GetRenderWindow()
        render_window.AddRenderer(self.renderer)
        
        # Reset camera to show everything
        self.renderer.ResetCamera()
        camera = self.renderer.GetActiveCamera()
        camera.Zoom(0.8)
        
        print("Box bounds:", actor.GetBounds())
        print("Camera position:", camera.GetPosition())
        print("Camera focal point:", camera.GetFocalPoint())

def main():
    app = QApplication(sys.argv)
    viewer = SimpleBoxViewer()
    viewer.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
