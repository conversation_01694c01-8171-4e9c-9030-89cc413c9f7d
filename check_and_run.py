#!/usr/bin/env python3
import ast
import subprocess
import sys

# Check syntax first
try:
    with open('step_loader.py', 'r') as f:
        ast.parse(f.read())
    print("SYNTAX OK: step_loader.py")
except SyntaxError as e:
    print(f"SYNTAX ERROR: {e}")
    sys.exit(1)

# Start the main program
print("Starting main program...")
subprocess.Popen([sys.executable, 'step_viewer_tdk_modular.py'])
print("Main program started - window should appear")
