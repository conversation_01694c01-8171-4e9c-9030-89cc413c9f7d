#!/usr/bin/env python3
"""
TEST BOTH FIXES - Simple test for colors and rotation
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
import vtk
from vtk.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor

class TestBothFixes(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("TEST BOTH FIXES")
        self.setGeometry(100, 100, 1000, 600)
        
        # Rotation tracking (same as main program)
        self.model_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        
        self.vtk_widget = None
        self.renderer = None
        self.step_actor = None
        
        self.init_ui()
        self.setup_vtk()
        
    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Test buttons
        self.color_btn = QPushButton("1. Test Colors: Load STEP File")
        self.color_btn.clicked.connect(self.test_colors)
        layout.addWidget(self.color_btn)
        
        self.rotation_btn = QPushButton("2. Test Rotation: X+15°")
        self.rotation_btn.clicked.connect(self.test_rotation)
        layout.addWidget(self.rotation_btn)
        
        # Status
        self.status_label = QLabel("Click buttons to test fixes")
        layout.addWidget(self.status_label)
        
        # Current rotation display
        self.rot_display = QLabel("Current X rotation: 0.0°")
        layout.addWidget(self.rot_display)
        
        # VTK viewer
        self.vtk_widget = QVTKRenderWindowInteractor()
        layout.addWidget(self.vtk_widget)
        
    def setup_vtk(self):
        """Setup VTK"""
        self.renderer = vtk.vtkRenderer()
        self.renderer.SetBackground(0.1, 0.1, 0.2)
        
        self.vtk_widget.GetRenderWindow().AddRenderer(self.renderer)
        self.vtk_widget.Initialize()
        self.vtk_widget.Start()
        
    def test_colors(self):
        """Test color fix"""
        print("🎨 TESTING COLOR FIX")
        
        filename = "debug_auto_saved.step"
        if not os.path.exists(filename):
            self.status_label.setText("❌ Test file not found")
            return
            
        try:
            # Load STEP file
            from step_loader import STEPLoader
            loader = STEPLoader()
            success, message = loader.load_step_file(filename)
            
            if not success:
                self.status_label.setText("❌ STEP loading failed")
                return
                
            polydata = loader.current_polydata
            cell_colors = polydata.GetCellData().GetScalars()
            
            if not cell_colors:
                self.status_label.setText("❌ No colors in STEP file")
                return
                
            print(f"Colors found: {cell_colors.GetNumberOfTuples()}")
            
            # Use vtk_renderer to display (same as main program)
            from vtk_renderer import VTKRenderer
            
            if self.step_actor:
                self.renderer.RemoveActor(self.step_actor)
                
            vtk_renderer = VTKRenderer(self.renderer, None)
            vtk_renderer.display_polydata(polydata)
            self.step_actor = vtk_renderer.step_actor
            
            self.renderer.ResetCamera()
            self.vtk_widget.GetRenderWindow().Render()
            
            # Check if colors are applied
            mapper = self.step_actor.GetMapper()
            if mapper.GetScalarVisibility():
                self.status_label.setText("✅ Colors: STEP file colors displayed")
                print("✅ Colors working!")
            else:
                self.status_label.setText("❌ Colors: Not applied")
                print("❌ Colors not working")
                
        except Exception as e:
            self.status_label.setText(f"❌ Color test error: {e}")
            print(f"Color test error: {e}")
            
    def test_rotation(self):
        """Test rotation fix"""
        print("🔘 TESTING ROTATION FIX")
        
        if not self.step_actor:
            self.status_label.setText("❌ Load STEP file first")
            return
            
        axis = 'x'
        degrees = 15.0
        
        # Get current value (same logic as main program)
        current_displayed_value = 0.0
        if hasattr(self, 'current_rot_left') and self.current_rot_left:
            current_displayed_value = self.current_rot_left.get(axis, 0.0)
        elif hasattr(self, 'model_rot_left'):
            current_displayed_value = self.model_rot_left.get(axis, 0.0)
            
        print(f"Current {axis.upper()} value: {current_displayed_value:.1f}°")
        
        # Set new value = current + increment
        new_value = current_displayed_value + degrees
        self.model_rot_left[axis] = new_value
        self.current_rot_left[axis] = new_value
        
        print(f"New {axis.upper()} value: {new_value:.1f}°")
        
        # Apply VTK rotation
        self.step_actor.RotateWXYZ(degrees, 1, 0, 0)
        self.vtk_widget.GetRenderWindow().Render()
        
        # Update display
        self.rot_display.setText(f"Current X rotation: {new_value:.1f}°")
        
        # Check if value increased correctly
        if new_value == current_displayed_value + degrees:
            self.status_label.setText(f"✅ Rotation: X={new_value:.1f}° (added {degrees}° to {current_displayed_value:.1f}°)")
            print("✅ Rotation working!")
        else:
            self.status_label.setText("❌ Rotation: Not adding correctly")
            print("❌ Rotation not working")

def main():
    app = QApplication(sys.argv)
    window = TestBothFixes()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
