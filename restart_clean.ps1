Write-Host "========================================" -ForegroundColor Cyan
Write-Host "STEP VIEWER - CLEAN RESTART SCRIPT" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "1. Killing all Python processes..." -ForegroundColor Yellow
Get-Process python* -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
Start-Sleep -Seconds 2

Write-Host "2. Clearing Python cache files..." -ForegroundColor Yellow
Get-ChildItem -Path . -Recurse -Name "__pycache__" -ErrorAction SilentlyContinue | Remove-Item -Recurse -Force -ErrorAction SilentlyContinue
Get-ChildItem -Path . -Recurse -Name "*.pyc" -ErrorAction SilentlyContinue | Remove-Item -Force -ErrorAction SilentlyContinue
Get-ChildItem -Path . -Recurse -Name "*.pyo" -ErrorAction SilentlyContinue | Remove-Item -Force -ErrorAction SilentlyContinue

Write-Host "3. Setting clean Python environment..." -ForegroundColor Yellow
$env:PYTHONDONTWRITEBYTECODE = "1"
$env:PYTHONPATH = ""

Write-Host "4. Waiting for system cleanup..." -ForegroundColor Yellow
Start-Sleep -Seconds 3

Write-Host "5. Starting STEP Viewer with clean Python environment..." -ForegroundColor Green
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "PROGRAM STARTING - WATCH FOR DEBUG MESSAGES" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Start Python with no bytecode generation and clean environment
python -B step_viewer_tdk_modular_fixed.py

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "PROGRAM ENDED" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Read-Host "Press Enter to continue"
