#!/usr/bin/env python3
"""
Auto-restart the main program to ensure fresh initialization
"""

import subprocess
import sys
import time
import os

def run_fresh_program():
    """Run the main program with fresh initialization"""
    print("🚀 Starting fresh program instance...")
    
    try:
        # Kill any existing processes
        try:
            subprocess.run(["taskkill", "/f", "/im", "python.exe"], 
                         capture_output=True, check=False)
            time.sleep(1)
        except:
            pass
        
        # Start fresh program
        process = subprocess.Popen([
            sys.executable, "step_viewer_tdk_modular.py"
        ], cwd=os.getcwd())
        
        print(f"✅ Program started with PID: {process.pid}")
        print("Program should now have:")
        print("  ✅ Model display working")
        print("  ✅ Rotation buttons incrementing correctly")
        print("  ✅ Text overlay showing rotation values")
        print("  ✅ Colors from STEP file")
        
        return process
        
    except Exception as e:
        print(f"❌ Error starting program: {e}")
        return None

if __name__ == "__main__":
    process = run_fresh_program()
    
    if process:
        print("\n🎯 PROGRAM IS READY FOR TESTING:")
        print("1. Load debug_auto_saved.step")
        print("2. Click X+15° multiple times - should show 15° → 30° → 45°")
        print("3. Check that text overlay updates with rotation values")
        print("4. Verify colors display correctly")
        
        # Keep script running
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n🛑 Stopping program...")
            process.terminate()
    else:
        print("❌ Failed to start program")
