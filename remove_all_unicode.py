#!/usr/bin/env python3
"""
Remove all Unicode characters from Python files
"""

import os
import re

files_to_clean = [
    'step_loader.py',
    'step_viewer_tdk_modular.py', 
    'vtk_renderer.py',
    'complete_debug_no_intervention.py'
]

# Common Unicode characters to replace
unicode_replacements = {
    '✅': 'OK',
    '❌': 'FAIL', 
    '⚠️': 'WARN',
    '🔧': 'DEBUG',
    '🔍': 'DEBUG',
    '📁': 'FILE',
    '🎉': 'SUCCESS',
    '💡': 'INFO',
    '⭐': 'STAR',
    '🚀': 'START',
    '🛠️': 'TOOL',
    '📊': 'DATA',
    '🎯': 'TARGET'
}

for filename in files_to_clean:
    if os.path.exists(filename):
        try:
            # Read file with UTF-8
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Replace Unicode characters
            original_content = content
            for unicode_char, replacement in unicode_replacements.items():
                content = content.replace(unicode_char, replacement)
            
            # Check for any remaining non-ASCII characters
            non_ascii_found = []
            for i, char in enumerate(content):
                if ord(char) > 127:
                    non_ascii_found.append((i, char, ord(char)))
            
            if non_ascii_found:
                print(f"{filename}: Found {len(non_ascii_found)} non-ASCII characters")
                for pos, char, code in non_ascii_found[:5]:  # Show first 5
                    print(f"  Position {pos}: '{char}' (code {code})")
            
            # Write back if changed
            if content != original_content:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"{filename}: Unicode characters replaced")
            else:
                print(f"{filename}: No Unicode characters found")
                
        except Exception as e:
            print(f"{filename}: Error - {e}")
    else:
        print(f"{filename}: File not found")

print("Unicode cleanup completed")
