@echo off
echo ========================================
echo STEP VIEWER - CLEAN RESTART SCRIPT
echo ========================================
echo.

echo 1. Killing all Python processes...
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im pythonw.exe >nul 2>&1
timeout /t 2 >nul

echo 2. Clearing Python cache files...
for /d /r . %%d in (__pycache__) do @if exist "%%d" rd /s /q "%%d"
del /s /q *.pyc >nul 2>&1
del /s /q *.pyo >nul 2>&1

echo 3. Clearing environment variables...
set PYTHONDONTWRITEBYTECODE=1
set PYTHONPATH=

echo 4. Waiting for system cleanup...
timeout /t 3 >nul

echo 5. Starting STEP Viewer with clean Python environment...
echo.
echo ========================================
echo PROGRAM STARTING - WATCH FOR DEBUG MESSAGES
echo ========================================
echo.

python -B step_viewer_tdk_modular_fixed.py

echo.
echo ========================================
echo PROGRAM ENDED
echo ========================================
pause
