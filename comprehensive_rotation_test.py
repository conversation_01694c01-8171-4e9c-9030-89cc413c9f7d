#!/usr/bin/env python3
"""
Comprehensive rotation testing script
Tests multiple scenarios and edge cases
"""

import sys
import os
sys.path.append('.')

from step_viewer_tdk_modular import StepViewerTDK
from PyQt5.QtWidgets import QApplication
import time

def test_multiple_rotations():
    """Test multiple different rotation scenarios"""
    print("🧪 COMPREHENSIVE ROTATION TESTING")
    print("=" * 60)
    
    app = QApplication([])
    viewer = StepViewerTDK()
    viewer.show()
    
    test_cases = [
        {"name": "Single X rotation", "rot": {"x": 90.0, "y": 0.0, "z": 0.0}},
        {"name": "Single Y rotation", "rot": {"x": 0.0, "y": 90.0, "z": 0.0}},
        {"name": "Single Z rotation", "rot": {"x": 0.0, "y": 0.0, "z": 90.0}},
        {"name": "Negative rotations", "rot": {"x": -45.0, "y": -30.0, "z": -60.0}},
        {"name": "Large rotations", "rot": {"x": 180.0, "y": 270.0, "z": 360.0}},
        {"name": "Small rotations", "rot": {"x": 1.5, "y": 2.7, "z": 0.3}},
    ]
    
    try:
        # Load original file
        print("\n📂 Loading original test.step...")
        viewer.active_viewer = "top"
        success = viewer.load_step_file_direct('test.step')
        
        if not success:
            print("❌ Failed to load original file")
            return
        
        print("✅ Original file loaded")
        
        for i, test_case in enumerate(test_cases):
            print(f"\n🧪 TEST {i+1}: {test_case['name']}")
            print(f"   Rotation: X={test_case['rot']['x']:.1f}°, Y={test_case['rot']['y']:.1f}°, Z={test_case['rot']['z']:.1f}°")
            
            # Apply rotation
            viewer._apply_3d_rotation_matrix("left", test_case['rot'])
            
            # Save with unique filename
            filename = f"test_rotation_{i+1}.step"
            print(f"   💾 Saving as {filename}...")
            
            # Use the proper method with all required parameters
            loader = viewer.step_loader_left
            delta_pos = {"x": 0.0, "y": 0.0, "z": 0.0}
            delta_rot = test_case['rot']
            orig_pos = {"x": 0.0, "y": 0.0, "z": 0.0}
            orig_rot = {"x": 0.0, "y": 0.0, "z": 0.0}

            success = viewer._save_step_text_transform(
                filename, loader, delta_pos, delta_rot, orig_pos, orig_rot
            )
            
            if success:
                print(f"   ✅ Saved successfully")
                
                # Test loading the saved file
                print(f"   📂 Loading saved file...")
                viewer.active_viewer = "bottom"
                load_success = viewer.load_step_file_direct(filename)
                
                if load_success:
                    # Check if rotation values were preserved
                    if hasattr(viewer, 'current_rot_right'):
                        loaded_rot = viewer.current_rot_right
                        print(f"   📊 Loaded rotation: X={loaded_rot['x']:.1f}°, Y={loaded_rot['y']:.1f}°, Z={loaded_rot['z']:.1f}°")
                        
                        # Check accuracy
                        x_diff = abs(loaded_rot['x'] - test_case['rot']['x'])
                        y_diff = abs(loaded_rot['y'] - test_case['rot']['y'])
                        z_diff = abs(loaded_rot['z'] - test_case['rot']['z'])
                        
                        if x_diff < 0.1 and y_diff < 0.1 and z_diff < 0.1:
                            print(f"   ✅ Rotation values preserved accurately")
                        else:
                            print(f"   ❌ Rotation values differ: ΔX={x_diff:.3f}°, ΔY={y_diff:.3f}°, ΔZ={z_diff:.3f}°")
                    else:
                        print(f"   ❌ No rotation values found after loading")
                else:
                    print(f"   ❌ Failed to load saved file")
            else:
                print(f"   ❌ Save failed")
        
        print(f"\n🧪 TESTING CUMULATIVE ROTATIONS...")
        
        # Test applying rotation to already rotated file
        print(f"   📂 Loading test_rotation_1.step (90° X rotation)...")
        viewer.active_viewer = "top"
        success = viewer.load_step_file_direct('test_rotation_1.step')
        
        if success:
            print(f"   ✅ Pre-rotated file loaded")
            
            # Apply additional rotation
            additional_rot = {"x": 0.0, "y": 45.0, "z": 0.0}
            print(f"   🔄 Applying additional Y=45° rotation...")
            viewer._apply_3d_rotation_matrix("left", additional_rot)
            
            # Save cumulative result
            filename = "test_cumulative_rotation.step"
            print(f"   💾 Saving cumulative rotation as {filename}...")
            
            # Calculate expected cumulative rotation
            if hasattr(viewer, 'current_rot_left'):
                current_rot = viewer.current_rot_left
                expected_rot = {
                    "x": current_rot['x'] + additional_rot['x'],
                    "y": current_rot['y'] + additional_rot['y'], 
                    "z": current_rot['z'] + additional_rot['z']
                }
                
                # Use the proper method with all required parameters
                loader = viewer.step_loader_left
                delta_pos = {"x": 0.0, "y": 0.0, "z": 0.0}
                delta_rot = expected_rot
                orig_pos = {"x": 0.0, "y": 0.0, "z": 0.0}
                orig_rot = {"x": 0.0, "y": 0.0, "z": 0.0}

                success = viewer._save_step_text_transform(
                    filename, loader, delta_pos, delta_rot, orig_pos, orig_rot
                )
                
                if success:
                    print(f"   ✅ Cumulative rotation saved")
                    print(f"   📊 Expected: X={expected_rot['x']:.1f}°, Y={expected_rot['y']:.1f}°, Z={expected_rot['z']:.1f}°")
                    
                    # Test loading cumulative file
                    viewer.active_viewer = "bottom"
                    load_success = viewer.load_step_file_direct(filename)
                    
                    if load_success and hasattr(viewer, 'current_rot_right'):
                        loaded_rot = viewer.current_rot_right
                        print(f"   📊 Loaded: X={loaded_rot['x']:.1f}°, Y={loaded_rot['y']:.1f}°, Z={loaded_rot['z']:.1f}°")
                        
                        x_diff = abs(loaded_rot['x'] - expected_rot['x'])
                        y_diff = abs(loaded_rot['y'] - expected_rot['y'])
                        z_diff = abs(loaded_rot['z'] - expected_rot['z'])
                        
                        if x_diff < 0.1 and y_diff < 0.1 and z_diff < 0.1:
                            print(f"   ✅ Cumulative rotation preserved accurately")
                        else:
                            print(f"   ❌ Cumulative rotation differs: ΔX={x_diff:.3f}°, ΔY={y_diff:.3f}°, ΔZ={z_diff:.3f}°")
        
        print(f"\n🎯 COMPREHENSIVE TEST COMPLETE")
        
    except Exception as e:
        print(f"❌ Error during comprehensive test: {e}")
        import traceback
        traceback.print_exc()
    
    time.sleep(2)
    app.quit()

if __name__ == "__main__":
    test_multiple_rotations()
