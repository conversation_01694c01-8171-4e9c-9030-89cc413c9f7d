#!/usr/bin/env python3
"""
SIMPLE VIEWER - Just opens the program window, no automatic testing
"""

import sys
from PyQt5.QtWidgets import QApplication

# Import the viewer class
from step_viewer_tdk_modular import StepViewerTDK

def main():
    print("🎯 SIMPLE VIEWER - Opening program window")
    print("   The overlay functionality is working based on the test output!")
    print("   You should be able to:")
    print("   1. Load STEP files in both viewers")
    print("   2. Click 'Toggle Origin Overlay' to see both models overlaid")

    app = QApplication(sys.argv)

    # Create the viewer
    viewer = StepViewerTDK()
    
    # Show the window
    viewer.show()
    
    print("✅ Program window opened - you can now test the overlay manually")
    
    # Run the application
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
