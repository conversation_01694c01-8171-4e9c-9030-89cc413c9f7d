#!/usr/bin/env python3
"""
Check what IDs are actually used in the STEP files
"""

import re

def check_step_ids(filename):
    print(f"\n=== CHECKING {filename} ===")
    
    with open(filename, 'r') as f:
        content = f.read()
    
    # Find main AXIS2_PLACEMENT_3D
    axis_match = re.search(r'#11\s*=\s*AXIS2_PLACEMENT_3D[^;]*;', content)
    if axis_match:
        print(f"Main AXIS2_PLACEMENT_3D: {axis_match.group(0)}")
        
        # Extract referenced IDs
        refs = re.findall(r'#(\d+)', axis_match.group(0))
        print(f"Referenced IDs: {refs}")
        
        # Check each referenced ID
        for ref_id in refs[1:]:  # Skip #11 itself
            pattern = f'#{ref_id}\s*=[^;]*;'
            ref_match = re.search(pattern, content)
            if ref_match:
                print(f"  #{ref_id}: {ref_match.group(0)}")
            else:
                print(f"  #{ref_id}: NOT FOUND")
    else:
        print("No main AXIS2_PLACEMENT_3D found")

if __name__ == "__main__":
    check_step_ids("test.step")
    check_step_ids("automated_test_option1.step")
