#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Auto Fix Position Display - Fully automated test and fix
"""

import sys
import time
import os
import re
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular import StepViewerTDK

class AutoPositionFixer:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        self.results = []
        self.test_step = 0
        
    def log(self, message):
        """Log message to results and console"""
        self.results.append(message)
        print(message)
        
    def run_auto_test(self):
        """Run fully automated test"""
        self.log("="*60)
        self.log("🔧 AUTO POSITION DISPLAY FIXER - STARTING")
        self.log("="*60)
        
        # Create viewer
        self.viewer = StepViewerTDK()
        self.viewer.active_viewer = "top"
        
        # Load test file
        if not os.path.exists("test.step"):
            self.log("❌ test.step not found - creating minimal test")
            self.create_minimal_test()
            return
            
        success = self.viewer.load_step_file_direct("test.step")
        if not success:
            self.log("❌ Failed to load test.step")
            return
            
        self.log("✅ test.step loaded")
        
        # Wait for loading
        time.sleep(2)
        
        # Run tests
        self.test_coordinate_display()
        
        # Write results
        self.write_results()
        
        # Apply fix if needed
        self.apply_fix()
        
        self.app.quit()
        
    def test_coordinate_display(self):
        """Test coordinate display systematically"""
        self.log("\n📋 TESTING COORDINATE DISPLAY:")
        self.log("-" * 40)
        
        # Get initial state
        if not hasattr(self.viewer, 'current_pos_left'):
            self.log("❌ No current_pos_left - initializing")
            self.viewer.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            
        initial_pos = self.viewer.current_pos_left.copy()
        self.log(f"Initial position: {initial_pos}")
        
        # Test X+ button
        self.log("\n🔧 Testing X+ (should increase X by 1.0):")
        before_x = self.viewer.current_pos_left['x']
        self.viewer.move_shape('x', 1.0)
        time.sleep(0.1)
        after_x = self.viewer.current_pos_left['x']
        x_change = after_x - before_x
        
        self.log(f"   X value: {before_x:.3f} → {after_x:.3f} (change: {x_change:.3f})")
        
        # Check text display
        text_x = self.get_displayed_x()
        self.log(f"   Text shows X: {text_x:.3f}")
        
        # Analyze X
        x_data_correct = abs(x_change - 1.0) < 0.001
        x_display_matches = abs(text_x - after_x) < 0.001
        x_display_inverted = abs(text_x + after_x) < 0.001
        
        self.log(f"   Data change: {'✅ CORRECT' if x_data_correct else '❌ WRONG'}")
        self.log(f"   Display: {'✅ MATCHES' if x_display_matches else ('❌ INVERTED' if x_display_inverted else '❌ WRONG')}")
        
        # Reset and test Y+
        self.viewer.current_pos_left = initial_pos.copy()
        self.viewer.update_text_overlays()
        
        self.log("\n🔧 Testing Y+ (should increase Y by 1.0):")
        before_y = self.viewer.current_pos_left['y']
        self.viewer.move_shape('y', 1.0)
        time.sleep(0.1)
        after_y = self.viewer.current_pos_left['y']
        y_change = after_y - before_y
        
        self.log(f"   Y value: {before_y:.3f} → {after_y:.3f} (change: {y_change:.3f})")
        
        # Check text display
        text_y = self.get_displayed_y()
        self.log(f"   Text shows Y: {text_y:.3f}")
        
        # Analyze Y
        y_data_correct = abs(y_change - 1.0) < 0.001
        y_display_matches = abs(text_y - after_y) < 0.001
        y_display_inverted = abs(text_y + after_y) < 0.001
        
        self.log(f"   Data change: {'✅ CORRECT' if y_data_correct else '❌ WRONG'}")
        self.log(f"   Display: {'✅ MATCHES' if y_display_matches else ('❌ INVERTED' if y_display_inverted else '❌ WRONG')}")
        
        # Test Z+ for comparison
        self.viewer.current_pos_left = initial_pos.copy()
        self.viewer.update_text_overlays()
        
        self.log("\n🔧 Testing Z+ (should increase Z by 1.0):")
        before_z = self.viewer.current_pos_left['z']
        self.viewer.move_shape('z', 1.0)
        time.sleep(0.1)
        after_z = self.viewer.current_pos_left['z']
        z_change = after_z - before_z
        
        self.log(f"   Z value: {before_z:.3f} → {after_z:.3f} (change: {z_change:.3f})")
        
        # Check text display
        text_z = self.get_displayed_z()
        self.log(f"   Text shows Z: {text_z:.3f}")
        
        # Analyze Z
        z_data_correct = abs(z_change - 1.0) < 0.001
        z_display_matches = abs(text_z - after_z) < 0.001
        
        self.log(f"   Data change: {'✅ CORRECT' if z_data_correct else '❌ WRONG'}")
        self.log(f"   Display: {'✅ MATCHES' if z_display_matches else '❌ WRONG'}")
        
        # Summary
        self.log("\n📊 SUMMARY:")
        self.log(f"   X display issue: {'YES' if x_display_inverted else 'NO'}")
        self.log(f"   Y display issue: {'YES' if y_display_inverted else 'NO'}")
        self.log(f"   Z display issue: {'YES' if not z_display_matches else 'NO'}")
        
        # Store results for fix
        self.x_needs_fix = x_display_inverted
        self.y_needs_fix = y_display_inverted
        self.z_needs_fix = not z_display_matches
        
    def get_displayed_x(self):
        """Extract X value from text display"""
        try:
            text = self.viewer.combined_text_actor_left.GetInput()
            match = re.search(r'POS: X=([-\d.]+)mm', text)
            return float(match.group(1)) if match else 0.0
        except:
            return 0.0
            
    def get_displayed_y(self):
        """Extract Y value from text display"""
        try:
            text = self.viewer.combined_text_actor_left.GetInput()
            match = re.search(r'Y=([-\d.]+)mm', text)
            return float(match.group(1)) if match else 0.0
        except:
            return 0.0
            
    def get_displayed_z(self):
        """Extract Z value from text display"""
        try:
            text = self.viewer.combined_text_actor_left.GetInput()
            match = re.search(r'Z=([-\d.]+)mm', text)
            return float(match.group(1)) if match else 0.0
        except:
            return 0.0
            
    def write_results(self):
        """Write results to file"""
        with open("position_display_test_results.txt", "w") as f:
            for line in self.results:
                f.write(line + "\n")
        self.log(f"\n✅ Results written to position_display_test_results.txt")
        
    def apply_fix(self):
        """Apply the fix based on test results"""
        self.log("\n🔧 APPLYING FIX:")
        
        if self.x_needs_fix or self.y_needs_fix:
            self.log("   Coordinate display inversion detected - applying fix...")
            
            # Read the current file
            with open("step_viewer_tdk_modular.py", "r") as f:
                content = f.read()
                
            # Fix TOP viewer display
            if "display_x = -self.current_pos_left['x']" not in content:
                # Apply the fix
                old_pattern = r'move_text = f"POS: X=\{self\.current_pos_left\[\'x\'\]:.3f\}mm Y=\{self\.current_pos_left\[\'y\'\]:.3f\}mm Z=\{self\.current_pos_left\[\'z\'\]:.3f\}mm"'
                new_pattern = '''# Fix coordinate display: invert X and Y for correct text display
                    display_x = -self.current_pos_left['x']  # Invert X
                    display_y = -self.current_pos_left['y']  # Invert Y
                    display_z = self.current_pos_left['z']   # Z is correct
                    move_text = f"POS: X={display_x:.3f}mm Y={display_y:.3f}mm Z={display_z:.3f}mm"'''
                
                content = re.sub(old_pattern, new_pattern, content)
                
                # Write back
                with open("step_viewer_tdk_modular.py", "w") as f:
                    f.write(content)
                    
                self.log("   ✅ Fix applied to step_viewer_tdk_modular.py")
            else:
                self.log("   ✅ Fix already applied")
        else:
            self.log("   ✅ No fix needed - coordinates display correctly")
            
    def create_minimal_test(self):
        """Create minimal test if no test.step found"""
        self.log("Creating minimal coordinate test...")
        # Just test with default values
        self.viewer.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.viewer.orig_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.viewer.movement_delta_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.test_coordinate_display()

def main():
    fixer = AutoPositionFixer()
    fixer.run_auto_test()

if __name__ == "__main__":
    main()
