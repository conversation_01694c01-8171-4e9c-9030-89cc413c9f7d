#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug Button Flow - Test the complete flow from button click to display update
"""

import sys
import os
import time
import math
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QPushButton, QLabel, QTextEdit, QGroupBox)
from PyQt5.QtCore import Qt, QTimer

class ButtonFlowDebugger(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Button Flow Debugger - Step by Step Trace")
        self.setGeometry(100, 100, 1000, 700)
        
        # Initialize variables like main program
        self.active_viewer = "top"
        self.init_all_variables()
        self.setup_ui()
        
        # Debug log
        self.debug_messages = []
        self.log("=== BUTTON FLOW DEBUGGER STARTED ===")
        
    def init_all_variables(self):
        """Initialize ALL variables exactly like main program"""
        # TOP viewer variables
        self.current_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_axis_left = {'x': 0.0, 'y': 0.0, 'z': 1.0}
        self.current_angle_left = 0.0
        self.model_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.model_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.orig_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.orig_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.orig_axis_left = {'x': 0.0, 'y': 0.0, 'z': 1.0}
        self.orig_angle_left = 0.0
        
        # BOTTOM viewer variables
        self.current_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_axis_right = {'x': 0.0, 'y': 0.0, 'z': 1.0}
        self.current_angle_right = 0.0
        self.model_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.model_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.orig_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.orig_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.orig_axis_right = {'x': 0.0, 'y': 0.0, 'z': 1.0}
        self.orig_angle_right = 0.0
        
        # Simulate text actors exist
        self.combined_text_actor_left = True  # Simulate exists
        self.combined_text_actor_right = True  # Simulate exists
        
        self.log("All variables initialized")
        
    def setup_ui(self):
        """Setup UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Active viewer selection
        viewer_group = QGroupBox("Active Viewer")
        viewer_layout = QHBoxLayout(viewer_group)
        self.btn_top = QPushButton("Top Viewer")
        self.btn_bottom = QPushButton("Bottom Viewer")
        self.btn_top.clicked.connect(lambda: self.set_active_viewer("top"))
        self.btn_bottom.clicked.connect(lambda: self.set_active_viewer("bottom"))
        viewer_layout.addWidget(self.btn_top)
        viewer_layout.addWidget(self.btn_bottom)
        layout.addWidget(viewer_group)
        
        # Rotation buttons
        rot_group = QGroupBox("Rotation Buttons")
        rot_layout = QHBoxLayout(rot_group)
        
        btn_x_plus = QPushButton("X+")
        btn_x_minus = QPushButton("X-")
        btn_y_plus = QPushButton("Y+")
        btn_y_minus = QPushButton("Y-")
        btn_z_plus = QPushButton("Z+")
        btn_z_minus = QPushButton("Z-")
        
        # Connect buttons exactly like main program
        btn_x_plus.clicked.connect(lambda: self.debug_rotate_shape('x', 15))
        btn_x_minus.clicked.connect(lambda: self.debug_rotate_shape('x', -15))
        btn_y_plus.clicked.connect(lambda: self.debug_rotate_shape('y', 15))
        btn_y_minus.clicked.connect(lambda: self.debug_rotate_shape('y', -15))
        btn_z_plus.clicked.connect(lambda: self.debug_rotate_shape('z', 15))
        btn_z_minus.clicked.connect(lambda: self.debug_rotate_shape('z', -15))
        
        rot_layout.addWidget(btn_x_plus)
        rot_layout.addWidget(btn_x_minus)
        rot_layout.addWidget(btn_y_plus)
        rot_layout.addWidget(btn_y_minus)
        rot_layout.addWidget(btn_z_plus)
        rot_layout.addWidget(btn_z_minus)
        layout.addWidget(rot_group)
        
        # Display simulation
        display_group = QGroupBox("Display Simulation")
        display_layout = QVBoxLayout(display_group)
        
        self.top_display = QLabel("TOP: ANGLE: 0.0° AXIS: [0.00 0.00 1.00] POS: X=0.000mm Y=0.000mm Z=0.000mm")
        self.top_display.setStyleSheet("background-color: lightblue; padding: 5px; border: 1px solid black;")
        display_layout.addWidget(QLabel("TOP VIEWER:"))
        display_layout.addWidget(self.top_display)
        
        self.bottom_display = QLabel("BOTTOM: ANGLE: 0.0° AXIS: [0.00 0.00 1.00] POS: X=0.000mm Y=0.000mm Z=0.000mm")
        self.bottom_display.setStyleSheet("background-color: lightgreen; padding: 5px; border: 1px solid black;")
        display_layout.addWidget(QLabel("BOTTOM VIEWER:"))
        display_layout.addWidget(self.bottom_display)
        
        layout.addWidget(display_group)
        
        # Debug log
        layout.addWidget(QLabel("DEBUG LOG:"))
        self.debug_text = QTextEdit()
        self.debug_text.setMaximumHeight(200)
        layout.addWidget(self.debug_text)
        
        self.update_viewer_buttons()
        
    def log(self, message):
        """Log debug message"""
        timestamp = time.strftime("%H:%M:%S")
        full_msg = f"[{timestamp}] {message}"
        self.debug_messages.append(full_msg)
        if hasattr(self, 'debug_text'):
            self.debug_text.append(full_msg)
            self.debug_text.ensureCursorVisible()
        print(full_msg)
        
    def set_active_viewer(self, viewer):
        """Set active viewer"""
        self.active_viewer = viewer
        self.log(f"Active viewer set to: {viewer}")
        self.update_viewer_buttons()
        
    def update_viewer_buttons(self):
        """Update button colors"""
        if self.active_viewer == "top":
            self.btn_top.setStyleSheet("background-color: lightgreen;")
            self.btn_bottom.setStyleSheet("")
        else:
            self.btn_top.setStyleSheet("")
            self.btn_bottom.setStyleSheet("background-color: lightgreen;")
            
    def debug_rotate_shape(self, axis, degrees):
        """Debug version of rotate_shape - exact copy of main program logic"""
        self.log(f"\\n=== ROTATE_SHAPE DEBUG START ===")
        self.log(f"Called with: axis={axis}, degrees={degrees}")
        self.log(f"Active viewer: {self.active_viewer}")
        
        try:
            if self.active_viewer == "top":
                # Initialize if not exists (like main program)
                if not hasattr(self, 'model_rot_left'):
                    self.model_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                if not hasattr(self, 'current_rot_left'):
                    self.current_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                if not hasattr(self, 'orig_rot_left'):
                    self.orig_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}

                self.log(f"BEFORE update - model_rot_left[{axis}] = {self.model_rot_left[axis]}")
                self.log(f"BEFORE update - current_rot_left[{axis}] = {self.current_rot_left[axis]}")

                # Add/subtract from the CURRENT displayed rotation values (like main program)
                self.current_rot_left[axis] += degrees
                self.model_rot_left[axis] = self.current_rot_left[axis] - self.orig_rot_left[axis]

                self.log(f"AFTER update - current_rot_left[{axis}] = {self.current_rot_left[axis]}")
                self.log(f"AFTER update - model_rot_left[{axis}] = {self.model_rot_left[axis]}")

            elif self.active_viewer == "bottom":
                # Initialize if not exists
                if not hasattr(self, 'model_rot_right'):
                    self.model_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                if not hasattr(self, 'current_rot_right'):
                    self.current_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                if not hasattr(self, 'orig_rot_right'):
                    self.orig_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}

                self.log(f"BEFORE update - current_rot_right[{axis}] = {self.current_rot_right[axis]}")
                
                # Add/subtract from the CURRENT displayed rotation values
                self.current_rot_right[axis] += degrees
                self.model_rot_right[axis] = self.current_rot_right[axis] - self.orig_rot_right[axis]
                
                self.log(f"AFTER update - current_rot_right[{axis}] = {self.current_rot_right[axis]}")

        except Exception as e:
            self.log(f"Error in rotate_shape: {e}")

        # Update axis and angle calculations (like main program)
        self.calculate_display_values()
        
        # Update display (like main program)
        self.debug_update_vtk_text_overlays()
        
        self.log(f"=== ROTATE_SHAPE DEBUG END ===\\n")
        
    def calculate_display_values(self):
        """Calculate display values exactly like main program"""
        self.log("--- CALCULATING DISPLAY VALUES ---")
        
        # Calculate for LEFT viewer
        rot = self.current_rot_left
        rot_mag = math.sqrt(rot['x']**2 + rot['y']**2 + rot['z']**2)
        if rot_mag > 0.001:
            self.current_axis_left = {
                'x': rot['x'] / rot_mag,
                'y': rot['y'] / rot_mag,
                'z': rot['z'] / rot_mag
            }
        else:
            self.current_axis_left = {'x': 0.0, 'y': 0.0, 'z': 1.0}
        self.current_angle_left = rot_mag
        
        # Calculate for RIGHT viewer (uses LEFT values like main program)
        self.current_axis_right = self.current_axis_left.copy()
        self.current_angle_right = rot_mag
        self.current_pos_right = self.current_pos_left.copy()
        
        self.log(f"LEFT: angle={self.current_angle_left:.1f}, axis={self.current_axis_left}")
        self.log(f"RIGHT: angle={self.current_angle_right:.1f}, axis={self.current_axis_right}")
        
    def debug_update_vtk_text_overlays(self):
        """Debug version of update_vtk_text_overlays"""
        self.log("--- UPDATE VTK TEXT OVERLAYS ---")
        
        # Check variables exist (like main program)
        has_pos_right = hasattr(self, 'current_pos_right')
        has_angle_right = hasattr(self, 'current_angle_right') 
        has_axis_right = hasattr(self, 'current_axis_right')
        
        self.log(f"Variable check: pos_right={has_pos_right}, angle_right={has_angle_right}, axis_right={has_axis_right}")
        
        # TOP viewer display (shows ORIGINAL values)
        top_text = f"ANGLE: {self.orig_angle_left:.1f}° AXIS: [{self.orig_axis_left['x']:.2f} {self.orig_axis_left['y']:.2f} {self.orig_axis_left['z']:.2f}] POS: X={self.orig_pos_left['x']:.3f}mm Y={self.orig_pos_left['y']:.3f}mm Z={self.orig_pos_left['z']:.3f}mm"
        self.top_display.setText(f"TOP: {top_text}")
        self.log(f"TOP TEXT SET: {top_text}")
        
        # BOTTOM viewer display (shows CURRENT values)
        if has_pos_right and has_angle_right and has_axis_right:
            bottom_text = f"ANGLE: {self.current_angle_right:.1f}° AXIS: [{self.current_axis_right['x']:.2f} {self.current_axis_right['y']:.2f} {self.current_axis_right['z']:.2f}] POS: X={self.current_pos_right['x']:.3f}mm Y={self.current_pos_right['y']:.3f}mm Z={self.current_pos_right['z']:.3f}mm"
            self.log(f"BOTTOM DISPLAY: Setting text to: {bottom_text}")
        else:
            bottom_text = f"ANGLE: 0.0° AXIS: [0.00 0.00 1.00] POS: X=0.000mm Y=0.000mm Z=0.000mm"
            self.log(f"BOTTOM DISPLAY: Missing data - showing defaults: {bottom_text}")
            
        self.bottom_display.setText(f"BOTTOM: {bottom_text}")
        self.log(f"BOTTOM TEXT SET: {bottom_text}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ButtonFlowDebugger()
    window.show()
    sys.exit(app.exec_())
