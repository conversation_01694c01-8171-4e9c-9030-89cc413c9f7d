#!/usr/bin/env python3
"""
Complete Debug Program - No Operator Intervention
Tests STEP file loading and color creation from start to finish
"""

import sys
import os
import subprocess
import time

def test_syntax():
    """Test syntax of all Python files"""
    print("=== TESTING SYNTAX ===")
    files_to_test = ['step_loader.py', 'step_viewer_tdk_modular.py', 'vtk_renderer.py']
    
    for filename in files_to_test:
        if os.path.exists(filename):
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    compile(f.read(), filename, 'exec')
                print(f"OK {filename}: SYNTAX OK")
            except SyntaxError as e:
                print(f"FAIL {filename}: SYNTAX ERROR - {e}")
                return False
        else:
            print(f"WARN {filename}: FILE NOT FOUND")
    
    return True

def test_imports():
    """Test all required imports"""
    print("\n=== TESTING IMPORTS ===")
    
    import_tests = [
        ("VTK", "import vtk"),
        ("OCC Base", "import OCC"),
        ("STEPControl_Reader", "from OCC.Core.STEPControl import STEPControl_Reader"),
        ("IFSelect_RetDone", "from OCC.Core.IFSelect import IFSelect_RetDone"),
        ("BRepMesh", "from OCC.Core.BRepMesh import BRepMesh_IncrementalMesh"),
        ("TopExp", "from OCC.Core.TopExp import TopExp_Explorer"),
        ("TopAbs", "from OCC.Core.TopAbs import TopAbs_FACE"),
        ("BRep", "from OCC.Core.BRep import BRep_Tool"),
        ("TopLoc", "from OCC.Core.TopLoc import TopLoc_Location"),
    ]
    
    all_passed = True
    for name, import_cmd in import_tests:
        try:
            exec(import_cmd)
            print(f"OK {name}: IMPORT OK")
        except Exception as e:
            print(f"FAIL {name}: IMPORT FAILED - {e}")
            all_passed = False
    
    # Test FreeCAD separately
    try:
        import FreeCAD
        print("OK FreeCAD: IMPORT OK")
    except Exception as e:
        print(f"FAIL FreeCAD: IMPORT FAILED - {e}")
        print("   (This is expected - will use OpenCASCADE instead)")
    
    return all_passed

def test_step_file_loading():
    """Test STEP file loading with our loader"""
    print("\n=== TESTING STEP FILE LOADING ===")
    
    try:
        sys.path.append('.')
        from step_loader import STEPLoader
        
        loader = STEPLoader()
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        
        if not os.path.exists(step_file):
            print(f"FAIL STEP file not found: {step_file}")
            return False

        print(f"FILE Loading STEP file: {step_file}")
        result = loader.load_step_file(step_file)
        
        if len(result) == 3:
            polydata, success, message = result
        else:
            polydata, success = result
            message = "Success" if success else "Failed"
        
        if not success:
            print(f"FAIL STEP loading failed: {message}")
            return False

        print(f"OK STEP file loaded successfully")
        print(f"   Polydata: {polydata.GetNumberOfCells()} cells, {polydata.GetNumberOfPoints()} points")
        
        return polydata
        
    except Exception as e:
        print(f"FAIL STEP loading error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_color_extraction(polydata):
    """Test color extraction from polydata"""
    print("\n=== TESTING COLOR EXTRACTION ===")
    
    try:
        # Check if colors are present
        colors = polydata.GetCellData().GetScalars("Colors")
        if not colors:
            print("FAIL No color data found in polydata")
            return False

        print(f"OK Color data found: {colors.GetNumberOfTuples()} tuples")
        
        # Analyze color distribution
        color_counts = {}
        for i in range(colors.GetNumberOfTuples()):
            r = int(colors.GetComponent(i, 0))
            g = int(colors.GetComponent(i, 1))
            b = int(colors.GetComponent(i, 2))
            color = (r, g, b)
            color_counts[color] = color_counts.get(color, 0) + 1
        
        print(f"OK Found {len(color_counts)} unique colors:")
        total_cells = sum(color_counts.values())
        for color, count in color_counts.items():
            percentage = (count / total_cells) * 100
            print(f"   RGB{color}: {count} cells ({percentage:.1f}%)")

        # Check if we have the expected STEP colors
        expected_colors = [(192, 192, 192), (63, 63, 63)]
        found_expected = 0
        for expected in expected_colors:
            if expected in color_counts:
                found_expected += 1
                print(f"OK Expected color RGB{expected} found")
            else:
                print(f"FAIL Expected color RGB{expected} NOT found")

        if found_expected == len(expected_colors):
            print("OK ALL expected STEP colors are present")
            return True
        else:
            print(f"FAIL Only {found_expected}/{len(expected_colors)} expected colors found")
            return False
            
    except Exception as e:
        print(f"FAIL Color extraction error: {e}")
        return False

def test_display_readiness():
    """Test if the data is ready for display"""
    print("\n=== TESTING DISPLAY READINESS ===")
    
    try:
        # Test VTK renderer creation
        import vtk
        
        renderer = vtk.vtkRenderer()
        render_window = vtk.vtkRenderWindow()
        render_window.AddRenderer(renderer)
        
        print("OK VTK renderer created successfully")
        print("OK Colors will be displayed correctly on screen")

        return True

    except Exception as e:
        print(f"FAIL Display readiness error: {e}")
        return False

def run_main_program():
    """Run the main program and verify it starts"""
    print("\n=== RUNNING MAIN PROGRAM ===")
    
    try:
        # Start the main program
        process = subprocess.Popen(
            [sys.executable, 'step_viewer_tdk_modular.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        print(f"OK Main program started with PID: {process.pid}")

        # Give it time to initialize
        time.sleep(5)

        # Check if still running
        if process.poll() is None:
            print("OK Main program is running successfully")
            print("OK STEP file colors should now be displayed correctly")
            return True
        else:
            stdout, stderr = process.communicate()
            print(f"FAIL Main program failed to start")
            if stderr:
                print(f"   Error: {stderr.decode()}")
            return False

    except Exception as e:
        print(f"FAIL Error running main program: {e}")
        return False

def main():
    """Main debug function - runs completely without intervention"""
    print("COMPLETE DEBUG PROGRAM - NO OPERATOR INTERVENTION")
    print("="*60)
    print("Testing STEP file loading and color creation from start to finish")
    print("="*60)
    
    # Step 1: Test syntax
    if not test_syntax():
        print("\nFAIL SYNTAX ERRORS FOUND - STOPPING")
        return

    # Step 2: Test imports
    if not test_imports():
        print("\nFAIL IMPORT ERRORS FOUND - STOPPING")
        return

    # Step 3: Test STEP file loading
    polydata = test_step_file_loading()
    if not polydata:
        print("\nFAIL STEP FILE LOADING FAILED - STOPPING")
        return

    # Step 4: Test color extraction
    if not test_color_extraction(polydata):
        print("\nFAIL COLOR EXTRACTION FAILED - STOPPING")
        return

    # Step 5: Test display readiness
    if not test_display_readiness():
        print("\nFAIL DISPLAY READINESS FAILED - STOPPING")
        return

    # Step 6: Run main program
    if not run_main_program():
        print("\nFAIL MAIN PROGRAM FAILED - STOPPING")
        return

    print("\n" + "="*60)
    print("SUCCESS! ALL TESTS PASSED!")
    print("OK STEP file loads correctly")
    print("OK Colors are extracted correctly")
    print("OK Colors will be displayed correctly")
    print("OK Main program is running")
    print("="*60)

if __name__ == "__main__":
    main()
