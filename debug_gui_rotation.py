#!/usr/bin/env python3
"""
Debug script to figure out why rotation values are not showing correctly in the GUI
"""

import sys
import os
import tempfile
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Add the current directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the main viewer class
from step_viewer_tdk_modular_fixed import StepViewerTDK

def debug_rotation_issue():
    """Debug the rotation value issue step by step"""
    print("🔍 DEBUGGING ROTATION ISSUE IN GUI")
    print("=" * 50)
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create the viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    # Wait for GUI to initialize
    QTimer.singleShot(1000, lambda: step1_load_original(viewer))
    
    # Run the app
    app.exec_()

def step1_load_original(viewer):
    """Step 1: Load original file in TOP viewer"""
    print("\n🔍 STEP 1: Loading original file in TOP viewer...")

    # Load test.step in TOP viewer
    if os.path.exists("test.step"):
        print("📂 Loading test.step in TOP viewer...")
        # Set active viewer to top
        viewer.active_viewer = "top"
        success = viewer.load_step_file_direct("test.step")
        print(f"📊 TOP load result: {success}")

        # Check initial values
        print(f"📊 TOP orig_rot_left: {viewer.orig_rot_left}")
        print(f"📊 TOP current_rot_left: {viewer.current_rot_left}")

        # Wait then apply rotation
        QTimer.singleShot(2000, lambda: step2_apply_rotation(viewer))
    else:
        print("❌ test.step not found!")
        sys.exit(1)

def step2_apply_rotation(viewer):
    """Step 2: Apply 45° X rotation"""
    print("\n🔍 STEP 2: Applying 45° X rotation...")
    
    # Apply rotation using the button method
    viewer.rotate_shape('x', 45)
    
    # Check values after rotation
    print(f"📊 After rotation - TOP current_rot_left: {viewer.current_rot_left}")
    
    # Wait then save
    QTimer.singleShot(2000, lambda: step3_save_file(viewer))

def step3_save_file(viewer):
    """Step 3: Save the rotated file"""
    print("\n🔍 STEP 3: Saving rotated file...")
    
    # Create temp file
    temp_file = tempfile.mktemp(suffix=".step")
    print(f"💾 Saving to: {temp_file}")
    
    # Save the file
    try:
        viewer.save_step_file_option1_direct(temp_file)
        print(f"✅ File saved successfully")
        
        # Store temp file path for next step
        viewer.debug_temp_file = temp_file
        
        # Wait then load in bottom
        QTimer.singleShot(2000, lambda: step4_load_in_bottom(viewer))
        
    except Exception as e:
        print(f"❌ Save failed: {e}")
        sys.exit(1)

def step4_load_in_bottom(viewer):
    """Step 4: Load saved file in BOTTOM viewer"""
    print("\n🔍 STEP 4: Loading saved file in BOTTOM viewer...")

    temp_file = viewer.debug_temp_file
    print(f"📂 Loading {temp_file} in BOTTOM viewer...")

    # Check values BEFORE loading
    print(f"📊 BEFORE load - BOTTOM orig_rot_right: {viewer.orig_rot_right}")
    print(f"📊 BEFORE load - BOTTOM current_rot_right: {viewer.current_rot_right}")

    # Load in bottom viewer
    viewer.active_viewer = "bottom"
    success = viewer.load_step_file_direct(temp_file)
    print(f"📊 BOTTOM load result: {success}")

    # Wait for loading to complete then check values
    QTimer.singleShot(3000, lambda: step5_check_final_values(viewer, temp_file))

def step5_check_final_values(viewer, temp_file):
    """Step 5: Check final values and analyze"""
    print("\n🔍 STEP 5: Checking final values...")
    
    # Check values AFTER loading
    print(f"📊 AFTER load - BOTTOM orig_rot_right: {viewer.orig_rot_right}")
    print(f"📊 AFTER load - BOTTOM current_rot_right: {viewer.current_rot_right}")
    
    # Test the coordinate system extraction directly
    print("\n🔧 TESTING COORDINATE SYSTEM EXTRACTION DIRECTLY...")
    try:
        pos, rot = viewer._extract_step_coordinate_system(temp_file)
        print(f"📊 Direct extraction result - pos: {pos}, rot: {rot}")
    except Exception as e:
        print(f"❌ Direct extraction failed: {e}")
    
    # Check if the file actually contains rotation data
    print("\n🔧 CHECKING FILE CONTENTS...")
    try:
        with open(temp_file, 'r') as f:
            content = f.read()
            
        # Look for direction vectors
        import re
        directions = re.findall(r'DIRECTION\(\'\',(.*?)\)', content)
        print(f"📊 Found {len(directions)} DIRECTION entries:")
        for i, direction in enumerate(directions[:5]):  # Show first 5
            print(f"   {i+1}: {direction}")
            
        # Look for AXIS2_PLACEMENT_3D
        axis_placements = re.findall(r'AXIS2_PLACEMENT_3D\(.*?\)', content)
        print(f"📊 Found {len(axis_placements)} AXIS2_PLACEMENT_3D entries:")
        for i, placement in enumerate(axis_placements[:3]):  # Show first 3
            print(f"   {i+1}: {placement}")
            
    except Exception as e:
        print(f"❌ File analysis failed: {e}")
    
    # Check GUI labels
    print("\n🔧 CHECKING GUI LABELS...")
    if hasattr(viewer, 'lbl_orig_axis_x_bottom'):
        print(f"📊 GUI label X: {viewer.lbl_orig_axis_x_bottom.text()}")
        print(f"📊 GUI label Y: {viewer.lbl_orig_axis_y_bottom.text()}")
        print(f"📊 GUI label Z: {viewer.lbl_orig_axis_z_bottom.text()}")
    else:
        print("❌ GUI labels not found")
    
    # Summary
    print("\n" + "=" * 50)
    print("🎯 SUMMARY:")
    print(f"Expected rotation: X=45°, Y=0°, Z=0°")
    print(f"Actual rotation: X={viewer.orig_rot_right['x']}°, Y={viewer.orig_rot_right['y']}°, Z={viewer.orig_rot_right['z']}°")
    
    if viewer.orig_rot_right['x'] == 45.0:
        print("✅ SUCCESS: Rotation values are correct!")
    else:
        print("❌ FAILURE: Rotation values are wrong!")
        
        # Additional debugging
        print("\n🔧 ADDITIONAL DEBUG INFO:")
        print(f"📊 File exists: {os.path.exists(temp_file)}")
        print(f"📊 File size: {os.path.getsize(temp_file)} bytes")
        
        # Check if the extraction method is even being called
        print("🔧 Checking if extraction method was called...")
        if hasattr(viewer, '_extract_step_coordinate_system'):
            print("✅ Extraction method exists")
        else:
            print("❌ Extraction method missing")
    
    # Clean up
    try:
        os.remove(temp_file)
        print(f"🧹 Cleaned up: {temp_file}")
    except:
        pass
    
    # Close after 10 seconds
    print("\n🎯 Debug completed. GUI will close in 10 seconds...")
    QTimer.singleShot(10000, lambda: viewer.close())

if __name__ == "__main__":
    debug_rotation_issue()
