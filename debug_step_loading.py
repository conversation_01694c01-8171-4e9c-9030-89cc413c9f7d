#!/usr/bin/env python3
"""
Debug STEP file loading to see why no model appears
"""

import sys
import os

print("DEBUG: STEP file loading test")
print("=" * 40)

# Check if STEP file exists
step_file = 'SOIC16P127_1270X940X610L89X51.STEP'
if os.path.exists(step_file):
    print(f"STEP file found: {step_file}")
    print(f"File size: {os.path.getsize(step_file)} bytes")
else:
    print(f"ERROR: STEP file not found: {step_file}")
    sys.exit(1)

try:
    print("Importing step_loader...")
    from step_loader import STEPLoader
    print("step_loader imported OK")
    
    print("Creating STEPLoader...")
    loader = STEPLoader()
    print("STEPLoader created OK")
    
    print(f"Loading STEP file: {step_file}")
    result = loader.load_step_file(step_file)
    
    print(f"Result type: {type(result)}")
    print(f"Result length: {len(result)}")
    
    if len(result) == 3:
        polydata, success, message = result
        print(f"Success: {success}")
        print(f"Message: {message}")
        
        if success and polydata:
            print("STEP file loaded successfully!")
            print(f"Cells: {polydata.GetNumberOfCells()}")
            print(f"Points: {polydata.GetNumberOfPoints()}")
            
            # Check for colors
            colors = polydata.GetCellData().GetScalars("Colors")
            if colors:
                print(f"Colors: {colors.GetNumberOfTuples()} tuples")
                
                # Show some color values
                for i in range(min(5, colors.GetNumberOfTuples())):
                    r = colors.GetComponent(i, 0)
                    g = colors.GetComponent(i, 1) 
                    b = colors.GetComponent(i, 2)
                    print(f"  Color {i}: RGB({r}, {g}, {b})")
                    
                print("SUCCESS: STEP file has geometry and colors!")
            else:
                print("WARNING: No colors found in polydata")
        else:
            print(f"FAILED: STEP loading failed - {message}")
    else:
        print(f"ERROR: Unexpected result format")
        
except Exception as e:
    print(f"ERROR: {e}")
    import traceback
    traceback.print_exc()

print("=" * 40)
print("DEBUG: Test completed")
