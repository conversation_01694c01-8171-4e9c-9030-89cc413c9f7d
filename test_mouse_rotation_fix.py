#!/usr/bin/env python3
"""
Test the mouse rotation fix - verify that mouse rotations are now captured during save
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication, QFileDialog
from PyQt5.QtCore import QTimer
from step_viewer_tdk_modular import StepViewerTDK

class MouseRotationTest:
    def __init__(self):
        self.app = QApplication([])
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        print("🧪 MOUSE ROTATION FIX TEST")
        print("=" * 60)
        print("This test will verify that mouse rotations are now captured correctly:")
        print("")
        print("INSTRUCTIONS:")
        print("1. Load test.step into TOP window")
        print("2. Rotate with MOUSE (drag to rotate)")
        print("3. Save with green button")
        print("4. Load saved file into BOTTOM window")
        print("5. Check if rotation is preserved")
        print("")
        print("The debug output will show if VTK matrix rotation is extracted correctly.")
        print("=" * 60)
        
        # Override the save method to add debug
        self.original_save_method = self.viewer.save_step_file_option1
        self.viewer.save_step_file_option1 = self.debug_save_method
        
        # Override the load method to add debug
        self.original_load_method = self.viewer.load_step_file_direct
        self.viewer.load_step_file_direct = self.debug_load_method
        
        print("✅ Debug monitoring started!")
        print("🎯 Please load test.step and rotate with mouse, then save...")
        
    def debug_save_method(self):
        """Debug wrapper for save method"""
        print("\n" + "="*60)
        print("🔧 DEBUG: GREEN SAVE BUTTON CLICKED!")
        print("="*60)
        
        # Test the new rotation extraction method
        if self.viewer.active_viewer == "top":
            extracted_rot = self.viewer._extract_rotation_from_vtk_actor("top")
            model_rot = getattr(self.viewer, 'model_rot_left', {'x': 0, 'y': 0, 'z': 0})
            current_rot = getattr(self.viewer, 'current_rot_left', {'x': 0, 'y': 0, 'z': 0})
        else:
            extracted_rot = self.viewer._extract_rotation_from_vtk_actor("bottom")
            model_rot = getattr(self.viewer, 'model_rot_right', {'x': 0, 'y': 0, 'z': 0})
            current_rot = getattr(self.viewer, 'current_rot_right', {'x': 0, 'y': 0, 'z': 0})
        
        print(f"📊 ROTATION COMPARISON:")
        print(f"   VTK Matrix Rotation (NEW): {extracted_rot}")
        print(f"   model_rot (button only):   {model_rot}")
        print(f"   current_rot (camera):      {current_rot}")
        
        # Check if VTK matrix has rotation
        vtk_has_rotation = (abs(extracted_rot['x']) > 0.001 or 
                           abs(extracted_rot['y']) > 0.001 or 
                           abs(extracted_rot['z']) > 0.001)
        
        model_has_rotation = (abs(model_rot['x']) > 0.001 or 
                             abs(model_rot['y']) > 0.001 or 
                             abs(model_rot['z']) > 0.001)
        
        print(f"📊 ROTATION DETECTION:")
        print(f"   VTK Matrix has rotation: {vtk_has_rotation}")
        print(f"   model_rot has rotation:  {model_has_rotation}")
        
        if vtk_has_rotation and not model_has_rotation:
            print("✅ SUCCESS: VTK matrix captured mouse rotation that model_rot missed!")
        elif vtk_has_rotation and model_has_rotation:
            print("✅ SUCCESS: VTK matrix captured rotation (may be button + mouse)")
        elif not vtk_has_rotation and not model_has_rotation:
            print("ℹ️  INFO: No rotation detected by either method")
        else:
            print("⚠️  WARNING: model_rot has rotation but VTK matrix doesn't")
        
        # Call original save method
        result = self.original_save_method()
        
        print(f"📊 Save result: {result}")
        print("="*60)
        
        return result
        
    def debug_load_method(self, filename):
        """Debug wrapper for load method"""
        print("\n" + "="*60)
        print(f"🔧 DEBUG: LOAD FILE CALLED: {filename}")
        print("="*60)
        
        print(f"📊 Active viewer: {self.viewer.active_viewer}")
        print(f"📊 File exists: {os.path.exists(filename)}")
        
        if os.path.exists(filename):
            # Check if file has rotation marker
            with open(filename, 'r') as f:
                content = f.read()
                if "ROTATION_VALUES:" in content:
                    print("✅ File contains ROTATION_VALUES marker")
                    import re
                    match = re.search(r'ROTATION_VALUES: X=([\d.-]+) Y=([\d.-]+) Z=([\d.-]+)', content)
                    if match:
                        x, y, z = map(float, match.groups())
                        print(f"📊 Stored rotation in file: X={x}°, Y={y}°, Z={z}°")
                        
                        # Check if this matches what we extracted from VTK
                        if hasattr(self, 'last_extracted_rotation'):
                            print(f"📊 Last extracted rotation: {self.last_extracted_rotation}")
                            if (abs(x - self.last_extracted_rotation['x']) < 1.0 and
                                abs(y - self.last_extracted_rotation['y']) < 1.0 and
                                abs(z - self.last_extracted_rotation['z']) < 1.0):
                                print("✅ SUCCESS: Saved rotation matches extracted VTK rotation!")
                            else:
                                print("⚠️  WARNING: Saved rotation doesn't match extracted VTK rotation")
                else:
                    print("❌ File does NOT contain ROTATION_VALUES marker")
        
        # Call original load method
        result = self.original_load_method(filename)
        
        print(f"📊 Load result: {result}")
        print("="*60)
        
        return result

def main():
    test = MouseRotationTest()
    test.app.exec_()

if __name__ == "__main__":
    main()
