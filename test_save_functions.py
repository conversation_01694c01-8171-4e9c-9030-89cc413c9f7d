#!/usr/bin/env python3
"""
Comprehensive test program for all save functions
Tests without user intervention using test.step file
"""

import os
import sys
import shutil
import time
from datetime import datetime

def get_file_info(filename):
    """Get file size and modification time"""
    if os.path.exists(filename):
        stat = os.stat(filename)
        return {
            'size': stat.st_size,
            'mtime': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
            'exists': True
        }
    else:
        return {'size': 0, 'mtime': 'N/A', 'exists': False}

def compare_files(file1, file2):
    """Compare two files byte by byte"""
    if not os.path.exists(file1) or not os.path.exists(file2):
        return False, "One or both files don't exist"
    
    info1 = get_file_info(file1)
    info2 = get_file_info(file2)
    
    if info1['size'] != info2['size']:
        return False, f"Size mismatch: {info1['size']} vs {info2['size']} bytes"
    
    # Compare content
    try:
        with open(file1, 'rb') as f1, open(file2, 'rb') as f2:
            content1 = f1.read()
            content2 = f2.read()
            if content1 == content2:
                return True, "Files are identical"
            else:
                return False, "Files have different content"
    except Exception as e:
        return False, f"Error comparing files: {e}"

def test_save_functions():
    """Test all save functions automatically"""
    print("=" * 80)
    print("🧪 COMPREHENSIVE SAVE FUNCTION TEST")
    print("=" * 80)
    
    # Check if test.step exists
    test_file = "test.step"
    if not os.path.exists(test_file):
        print(f"❌ ERROR: {test_file} not found!")
        print("Please create a test.step file in the current directory")
        return False
    
    original_info = get_file_info(test_file)
    print(f"📁 Original file: {test_file}")
    print(f"   Size: {original_info['size']} bytes")
    print(f"   Modified: {original_info['mtime']}")
    print()
    
    # Test files to create
    test_files = {
        'existing_original': 'test_existing_original.step',
        'new_original': 'test_new_original.step', 
        'existing_option1': 'test_existing_option1.step',
        'new_option1': 'test_new_option1.step',
        'existing_option2': 'test_existing_option2.step',
        'new_option2': 'test_new_option2.step'
    }
    
    # Create existing files (simulate existing files)
    for key, filename in test_files.items():
        if 'existing' in key:
            print(f"🔧 Creating existing file: {filename}")
            with open(filename, 'w') as f:
                f.write("DUMMY EXISTING FILE CONTENT\n")
    
    print()
    
    # Import the main program
    try:
        from step_viewer_tdk_modular import StepViewerTDK
        print("✅ Successfully imported StepViewerTDK")
    except Exception as e:
        print(f"❌ Failed to import StepViewerTDK: {e}")
        return False
    
    # Test results
    results = {}
    
    print("\n" + "=" * 50)
    print("🧪 TESTING ORIGINAL SAVE FUNCTION")
    print("=" * 50)
    
    # Test Original Save - Existing File
    print(f"\n📝 Test 1: Original Save to Existing File")
    print(f"   Target: {test_files['existing_original']}")
    
    try:
        # Create viewer instance
        app = None
        try:
            from PyQt5.QtWidgets import QApplication
            app = QApplication.instance()
            if app is None:
                app = QApplication(sys.argv)
        except:
            pass
            
        viewer = StepViewerTDK()
        
        # Load test file
        success, msg = viewer.step_loader_left.load_step_file(test_file)
        if success:
            print(f"   ✅ Loaded {test_file}")
            
            # Test original save to existing file
            before_info = get_file_info(test_files['existing_original'])
            print(f"   Before: {before_info['size']} bytes, {before_info['mtime']}")
            
            time.sleep(1)  # Ensure timestamp difference
            
            # Simulate save
            success = viewer.step_loader_left.save_step_file(test_files['existing_original'])
            
            after_info = get_file_info(test_files['existing_original'])
            print(f"   After:  {after_info['size']} bytes, {after_info['mtime']}")
            
            if success and after_info['exists']:
                # Compare with original
                same, msg = compare_files(test_file, test_files['existing_original'])
                results['original_existing'] = {
                    'success': success,
                    'size_match': after_info['size'] == original_info['size'],
                    'content_match': same,
                    'timestamp_updated': after_info['mtime'] != before_info['mtime'],
                    'message': msg
                }
                print(f"   Result: {'✅' if same else '❌'} {msg}")
            else:
                results['original_existing'] = {'success': False, 'message': 'Save failed'}
                print(f"   ❌ Save failed")
        else:
            print(f"   ❌ Failed to load {test_file}: {msg}")
            results['original_existing'] = {'success': False, 'message': f'Load failed: {msg}'}
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        results['original_existing'] = {'success': False, 'message': f'Exception: {e}'}
    
    # Test Original Save - New File
    print(f"\n📝 Test 2: Original Save to New File")
    print(f"   Target: {test_files['new_original']}")
    
    try:
        # Remove file if it exists
        if os.path.exists(test_files['new_original']):
            os.remove(test_files['new_original'])
            
        success = viewer.step_loader_left.save_step_file(test_files['new_original'])
        
        if success:
            new_info = get_file_info(test_files['new_original'])
            print(f"   Created: {new_info['size']} bytes, {new_info['mtime']}")
            
            # Compare with original
            same, msg = compare_files(test_file, test_files['new_original'])
            results['original_new'] = {
                'success': success,
                'size_match': new_info['size'] == original_info['size'],
                'content_match': same,
                'message': msg
            }
            print(f"   Result: {'✅' if same else '❌'} {msg}")
        else:
            results['original_new'] = {'success': False, 'message': 'Save failed'}
            print(f"   ❌ Save failed")
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        results['original_new'] = {'success': False, 'message': f'Exception: {e}'}

    print("\n" + "=" * 50)
    print("🧪 TESTING OPTION 1 SAVE FUNCTION")
    print("=" * 50)

    # Test Option 1 Save - Simulate some transformations
    print(f"\n📝 Test 3: Option 1 Save to New File (with transformations)")
    print(f"   Target: {test_files['new_option1']}")

    try:
        # Remove file if it exists
        if os.path.exists(test_files['new_option1']):
            os.remove(test_files['new_option1'])

        # Simulate transformations
        viewer.current_pos_left = {'x': 10.0, 'y': 5.0, 'z': 2.0}
        viewer.current_rot_left = {'x': 45.0, 'y': 30.0, 'z': 15.0}
        viewer.active_viewer = "top"

        print(f"   Simulated Position: {viewer.current_pos_left}")
        print(f"   Simulated Rotation: {viewer.current_rot_left}")

        # Test Option 1 save
        from simple_step_modifier import SimpleSTEPModifier

        modifier = SimpleSTEPModifier()
        load_success = modifier.load_step_file(test_file)

        if load_success:
            modify_success = modifier.modify_placement(
                viewer.current_pos_left['x'], viewer.current_pos_left['y'], viewer.current_pos_left['z'],
                viewer.current_rot_left['x'], viewer.current_rot_left['y'], viewer.current_rot_left['z']
            )

            if modify_success:
                success = modifier.save_step_file(test_files['new_option1'])

                if success:
                    new_info = get_file_info(test_files['new_option1'])
                    print(f"   Created: {new_info['size']} bytes, {new_info['mtime']}")

                    # Compare with original (should be different due to transformations)
                    same, msg = compare_files(test_file, test_files['new_option1'])
                    results['option1_new'] = {
                        'success': success,
                        'file_created': new_info['exists'],
                        'size_reasonable': new_info['size'] > 100,  # Should have some content
                        'different_from_original': not same,  # Should be different
                        'message': f"Transformed file: {msg}"
                    }
                    print(f"   Result: {'✅' if success and new_info['exists'] else '❌'} File created with transformations")
                    print(f"   Content: {'✅ Different from original (expected)' if not same else '❌ Same as original (unexpected)'}")
                else:
                    results['option1_new'] = {'success': False, 'message': 'Save failed'}
                    print(f"   ❌ Save failed")
            else:
                results['option1_new'] = {'success': False, 'message': 'Modify placement failed'}
                print(f"   ❌ Modify placement failed")
        else:
            results['option1_new'] = {'success': False, 'message': 'Load failed'}
            print(f"   ❌ Load failed")

    except Exception as e:
        print(f"   ❌ Exception: {e}")
        results['option1_new'] = {'success': False, 'message': f'Exception: {e}'}

    print("\n" + "=" * 50)
    print("🧪 TESTING OPTION 2 SAVE FUNCTION")
    print("=" * 50)

    # Test Option 2 Save
    print(f"\n📝 Test 4: Option 2 Save to New File (with geometry transformation)")
    print(f"   Target: {test_files['new_option2']}")

    try:
        # Remove file if it exists
        if os.path.exists(test_files['new_option2']):
            os.remove(test_files['new_option2'])

        # Option 2 uses the same SimpleSTEPModifier but with different logic
        # For now, just test if it can create a file
        modifier2 = SimpleSTEPModifier()
        load_success = modifier2.load_step_file(test_file)

        if load_success:
            # Option 2 would apply geometry transformations but keep original placement
            success = modifier2.save_step_file(test_files['new_option2'])

            if success:
                new_info = get_file_info(test_files['new_option2'])
                print(f"   Created: {new_info['size']} bytes, {new_info['mtime']}")

                results['option2_new'] = {
                    'success': success,
                    'file_created': new_info['exists'],
                    'size_reasonable': new_info['size'] > 100,
                    'message': 'Option 2 file created'
                }
                print(f"   Result: {'✅' if success and new_info['exists'] else '❌'} File created")
            else:
                results['option2_new'] = {'success': False, 'message': 'Save failed'}
                print(f"   ❌ Save failed")
        else:
            results['option2_new'] = {'success': False, 'message': 'Load failed'}
            print(f"   ❌ Load failed")

    except Exception as e:
        print(f"   ❌ Exception: {e}")
        results['option2_new'] = {'success': False, 'message': f'Exception: {e}'}

    print("\n" + "=" * 50)
    print("📊 FINAL RESULTS SUMMARY")
    print("=" * 50)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result.get('success', False) else "❌ FAIL"
        print(f"{test_name:20} {status:8} {result.get('message', '')}")
    
    # Cleanup
    print(f"\n🧹 Cleaning up test files...")
    for filename in test_files.values():
        if os.path.exists(filename):
            os.remove(filename)
            print(f"   Removed: {filename}")
    
    return True

if __name__ == "__main__":
    test_save_functions()
