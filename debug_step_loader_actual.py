#!/usr/bin/env python3
"""
Debug the actual step_loader to see what's happening
"""

print("=== DEBUGGING ACTUAL STEP_LOADER ===")

# Test exactly what step_loader does
try:
    from step_loader import STEPLoader
    print("OK STEPLoader imported")

    loader = STEPLoader()
    print("OK STEPLoader created")
    
    # Test the color parsing method directly
    loader.current_filename = 'SOIC16P127_1270X940X610L89X51.STEP'
    colors = loader._parse_step_file_colors()
    
    print(f"Colors found by _parse_step_file_colors: {len(colors)}")
    for i, color in enumerate(colors):
        print(f"  Color {i}: RGB{color}")
    
    # Test the full loading process
    print("\nTesting full STEP loading...")
    success, message = loader.load_step_file('SOIC16P127_1270X940X610L89X51.STEP')
    
    print(f"Load success: {success}")
    print(f"Load message: {message}")
    
    if success and loader.current_polydata:
        polydata = loader.current_polydata
        print(f"Polydata: {polydata.GetNumberOfCells()} cells, {polydata.GetNumberOfPoints()} points")
        
        # Check colors in polydata
        colors_array = polydata.GetCellData().GetScalars("Colors")
        if colors_array:
            print(f"Colors in polydata: {colors_array.GetNumberOfTuples()} tuples")
            
            # Sample first few colors
            unique_colors = set()
            for i in range(min(10, colors_array.GetNumberOfTuples())):
                r = int(colors_array.GetComponent(i, 0))
                g = int(colors_array.GetComponent(i, 1))
                b = int(colors_array.GetComponent(i, 2))
                unique_colors.add((r, g, b))
            
            print(f"Unique colors in first 10 cells: {unique_colors}")
        else:
            print("No colors found in polydata")
    else:
        print("STEP loading failed or no polydata")
        
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()

print("=== DEBUG COMPLETE ===")
