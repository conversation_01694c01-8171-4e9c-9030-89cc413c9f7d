#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Button Sizing - Debug button size constraints
"""

import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QPushButton, QLabel, QGroupBox, QDockWidget)
from PyQt5.QtCore import Qt

class ButtonSizeTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Button Size Test")
        self.setGeometry(100, 100, 800, 600)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QHBoxLayout(central_widget)
        
        # Create dock widget like the main program
        dock = QDockWidget("Test Dock", self)
        dock.setAllowedAreas(Qt.LeftDockWidgetArea | Qt.RightDockWidgetArea)
        dock.setMinimumWidth(250)  # Same as main program
        
        panel = QWidget()
        panel.setMinimumWidth(240)  # Same as main program
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # Test different button sizes
        self.create_test_groups(layout)
        
        dock.setWidget(panel)
        self.addDockWidget(Qt.LeftDockWidgetArea, dock)
        
    def create_test_groups(self, layout):
        """Create test button groups with different sizing"""
        
        # Test 1: Current sizing (30-35px)
        test1_group = QGroupBox("Current Size (30-35px)")
        test1_layout = QVBoxLayout(test1_group)
        test1_group.setMaximumWidth(115)
        test1_group.setMinimumWidth(110)
        
        x_layout1 = QHBoxLayout()
        x_layout1.setSpacing(3)
        btn1_minus = QPushButton("X-")
        btn1_minus.setMinimumWidth(30)
        btn1_minus.setMaximumWidth(35)
        btn1_plus = QPushButton("X+")
        btn1_plus.setMinimumWidth(30)
        btn1_plus.setMaximumWidth(35)
        x_layout1.addWidget(btn1_minus)
        x_layout1.addWidget(btn1_plus)
        test1_layout.addLayout(x_layout1)
        layout.addWidget(test1_group)
        
        # Test 2: Larger sizing (40-50px)
        test2_group = QGroupBox("Larger Size (40-50px)")
        test2_layout = QVBoxLayout(test2_group)
        test2_group.setMaximumWidth(115)
        test2_group.setMinimumWidth(110)
        
        x_layout2 = QHBoxLayout()
        x_layout2.setSpacing(5)
        btn2_minus = QPushButton("X-")
        btn2_minus.setMinimumWidth(40)
        btn2_minus.setMaximumWidth(50)
        btn2_plus = QPushButton("X+")
        btn2_plus.setMinimumWidth(40)
        btn2_plus.setMaximumWidth(50)
        x_layout2.addWidget(btn2_minus)
        x_layout2.addWidget(btn2_plus)
        test2_layout.addLayout(x_layout2)
        layout.addWidget(test2_group)
        
        # Test 3: Fixed size (45px)
        test3_group = QGroupBox("Fixed Size (45px)")
        test3_layout = QVBoxLayout(test3_group)
        test3_group.setMaximumWidth(115)
        test3_group.setMinimumWidth(110)
        
        x_layout3 = QHBoxLayout()
        x_layout3.setSpacing(5)
        btn3_minus = QPushButton("X-")
        btn3_minus.setFixedWidth(45)
        btn3_plus = QPushButton("X+")
        btn3_plus.setFixedWidth(45)
        x_layout3.addWidget(btn3_minus)
        x_layout3.addWidget(btn3_plus)
        test3_layout.addLayout(x_layout3)
        layout.addWidget(test3_group)
        
        # Test 4: No size constraints
        test4_group = QGroupBox("No Size Constraints")
        test4_layout = QVBoxLayout(test4_group)
        test4_group.setMaximumWidth(115)
        test4_group.setMinimumWidth(110)
        
        x_layout4 = QHBoxLayout()
        x_layout4.setSpacing(5)
        btn4_minus = QPushButton("X-")
        btn4_plus = QPushButton("X+")
        x_layout4.addWidget(btn4_minus)
        x_layout4.addWidget(btn4_plus)
        test4_layout.addLayout(x_layout4)
        layout.addWidget(test4_group)

def main():
    app = QApplication(sys.argv)
    window = ButtonSizeTest()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
