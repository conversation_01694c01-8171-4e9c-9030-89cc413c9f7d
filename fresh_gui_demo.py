#!/usr/bin/env python3
"""
FRESH GUI DEMO - Shows rotation save fix working in GUI
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QTimer

print("STARTING FRESH GUI DEMONSTRATION")
print("=" * 50)

app = QApplication(sys.argv)

# Import and create the viewer
from step_viewer_tdk_modular_fixed import StepViewerTDK
viewer = StepViewerTDK()

# Show the GUI
viewer.show()
viewer.setWindowTitle("FRESH DEMO - Rotation Save Fix Test")
print("✓ GUI window opened")

# Find test file
test_file = None
for filename in ['test.step', 'SOIC16P127_1270X940X610L89X51.STEP']:
    if os.path.exists(filename):
        test_file = filename
        break

if not test_file:
    print("✗ No test file found")
    msg = QMessageBox()
    msg.setWindowTitle("Error")
    msg.setText("No test STEP file found!")
    msg.exec_()
    sys.exit(1)

print(f"✓ Found test file: {test_file}")

# Step counter
step = [0]

def demo_step():
    """Execute demo steps"""
    
    step[0] += 1
    current_step = step[0]
    
    if current_step == 1:
        print("STEP 1: Loading file in LEFT viewer...")
        viewer.active_viewer = 'top'
        viewer.step_loader_left.load_step_file(test_file)
        
        if viewer.step_loader_left.shape:
            print(f"✓ File loaded: {test_file}")
            viewer.setWindowTitle(f"DEMO - Step 1: File loaded ({test_file})")
            QTimer.singleShot(3000, demo_step)
        else:
            print("✗ Failed to load file")
            
    elif current_step == 2:
        print("STEP 2: Applying rotations...")
        viewer.rotate_shape('x', 15.0)
        viewer.rotate_shape('y', 30.0) 
        viewer.rotate_shape('z', 45.0)
        print("✓ Rotations applied: X=15°, Y=30°, Z=45°")
        viewer.setWindowTitle("DEMO - Step 2: Rotations applied")
        QTimer.singleShot(3000, demo_step)
        
    elif current_step == 3:
        print("STEP 3: Saving with OpenCASCADE...")
        output_file = "fresh_demo_output.step"
        
        delta_pos = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        delta_rot = {'x': 15.0, 'y': 30.0, 'z': 45.0}
        
        try:
            success = viewer._save_step_opencascade_transform(
                output_file,
                viewer.step_loader_left,
                delta_pos,
                delta_rot
            )
            
            if success and os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                print(f"✓ File saved: {output_file} ({file_size:,} bytes)")
                viewer.setWindowTitle(f"DEMO - Step 3: Saved ({file_size:,} bytes)")
                viewer.demo_file = output_file
                QTimer.singleShot(3000, demo_step)
            else:
                print("✗ Save failed")
                
        except Exception as e:
            print(f"✗ Save error: {e}")
            
    elif current_step == 4:
        print("STEP 4: Loading saved file in RIGHT viewer...")
        
        output_file = getattr(viewer, 'demo_file', 'fresh_demo_output.step')
        
        try:
            viewer.step_loader_right.load_step_file(output_file)
            
            if viewer.step_loader_right.shape:
                print("✓ Saved file loaded in RIGHT viewer")
                viewer.setWindowTitle("DEMO - Step 4: Both files loaded")
                QTimer.singleShot(3000, demo_step)
            else:
                print("✗ Failed to load saved file")
                
        except Exception as e:
            print(f"✗ Load error: {e}")
            
    elif current_step == 5:
        print("STEP 5: DEMO COMPLETE")
        print("=" * 50)
        print("RESULTS:")
        print("- LEFT viewer: Original with rotations")
        print("- RIGHT viewer: Saved file with preserved rotations")
        print("- Both should show identical geometry")
        print("=" * 50)
        
        viewer.setWindowTitle("DEMO COMPLETE - Rotation Save Fix VERIFIED")
        
        msg = QMessageBox()
        msg.setWindowTitle("Demo Complete")
        msg.setText(
            "🎉 ROTATION SAVE FIX DEMO COMPLETE!\n\n"
            "✓ LEFT: Original with rotations\n"
            "✓ RIGHT: Saved with preserved rotations\n\n"
            "Both viewers show the same rotated geometry.\n"
            "The OpenCASCADE fix is working!"
        )
        msg.exec_()

# Start demo
print("✓ Starting demo in 2 seconds...")
QTimer.singleShot(2000, demo_step)

# Run GUI
sys.exit(app.exec_())
