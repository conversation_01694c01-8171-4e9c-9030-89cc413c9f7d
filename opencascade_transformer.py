#!/usr/bin/env python3
"""
OpenCASCADE Transformer - Proper STEP file transformation using OpenCASCADE
Based on the Medium article and StackOverflow examples
"""

import os
import math

class OpenCASCADETransformer:
    """
    Proper OpenCASCADE-based STEP file transformer
    Uses BRepBuilderAPI_Transform and gp_Trsf for accurate transformations
    """
    
    def __init__(self):
        self.shape = None
        self.current_filename = None
        
    def load_step_file(self, filename):
        """Load STEP file using OpenCASCADE"""
        try:
            # Import OpenCASCADE modules
            from OCC.Core import STEPControl_Reader, IFSelect_ReturnStatus
            from OCC.Core import TopAbs_FACE, TopExp_Explorer
            from OCC.Core import TopoDS_Shape
            
            print(f"🔧 OPENCASCADE: Loading STEP file: {filename}")
            
            # Create STEP reader
            step_reader = STEPControl_Reader()
            
            # Read the file
            status = step_reader.ReadFile(filename)
            
            if status != IFSelect_ReturnStatus.IFSelect_RetDone:
                print(f"❌ OPENCASCADE: Failed to read STEP file: {filename}")
                return False, f"Failed to read STEP file: {status}"
            
            # Transfer the shape
            step_reader.TransferRoots()
            
            # Get the shape
            self.shape = step_reader.OneShape()
            self.current_filename = filename
            
            if self.shape.IsNull():
                print(f"❌ OPENCASCADE: No shape found in STEP file")
                return False, "No shape found in STEP file"
            
            print(f"✅ OPENCASCADE: Successfully loaded STEP file")
            return True, "Success"
            
        except Exception as e:
            print(f"❌ OPENCASCADE: Exception loading STEP file: {e}")
            return False, f"Exception: {e}"
    
    def apply_transformations(self, position, rotation):
        """
        Apply position and rotation transformations to the loaded shape
        position: {'x': float, 'y': float, 'z': float} in mm
        rotation: {'x': float, 'y': float, 'z': float} in degrees
        """
        try:
            # Import OpenCASCADE transformation modules
            from OCC.Core import gp_Trsf, gp_Ax1, gp_Pnt, gp_Dir, gp_Vec
            from OCC.Core import BRepBuilderAPI_Transform
            
            print(f"🔧 OPENCASCADE: Applying transformations")
            print(f"   Position: {position}")
            print(f"   Rotation: {rotation}")
            
            if self.shape is None or self.shape.IsNull():
                print(f"❌ OPENCASCADE: No shape loaded")
                return False, "No shape loaded"
            
            # Create combined transformation
            combined_transform = gp_Trsf()
            
            # Apply rotations (in degrees, convert to radians)
            if rotation['x'] != 0 or rotation['y'] != 0 or rotation['z'] != 0:
                print(f"🔧 OPENCASCADE: Applying rotations")
                
                # X-axis rotation
                if rotation['x'] != 0:
                    x_rotation = gp_Trsf()
                    x_axis = gp_Ax1(gp_Pnt(0, 0, 0), gp_Dir(1, 0, 0))
                    x_rotation.SetRotation(x_axis, math.radians(rotation['x']))
                    combined_transform.Multiply(x_rotation)
                    print(f"   X rotation: {rotation['x']}°")
                
                # Y-axis rotation
                if rotation['y'] != 0:
                    y_rotation = gp_Trsf()
                    y_axis = gp_Ax1(gp_Pnt(0, 0, 0), gp_Dir(0, 1, 0))
                    y_rotation.SetRotation(y_axis, math.radians(rotation['y']))
                    combined_transform.Multiply(y_rotation)
                    print(f"   Y rotation: {rotation['y']}°")
                
                # Z-axis rotation
                if rotation['z'] != 0:
                    z_rotation = gp_Trsf()
                    z_axis = gp_Ax1(gp_Pnt(0, 0, 0), gp_Dir(0, 0, 1))
                    z_rotation.SetRotation(z_axis, math.radians(rotation['z']))
                    combined_transform.Multiply(z_rotation)
                    print(f"   Z rotation: {rotation['z']}°")
            
            # Apply translation (in mm)
            if position['x'] != 0 or position['y'] != 0 or position['z'] != 0:
                print(f"🔧 OPENCASCADE: Applying translation")
                translation = gp_Trsf()
                translation_vector = gp_Vec(position['x'], position['y'], position['z'])
                translation.SetTranslation(translation_vector)
                combined_transform.Multiply(translation)
                print(f"   Translation: ({position['x']}, {position['y']}, {position['z']}) mm")
            
            # Apply the combined transformation to the shape
            print(f"🔧 OPENCASCADE: Applying combined transformation to shape")
            shape_transform = BRepBuilderAPI_Transform(self.shape, combined_transform)
            
            # Get the transformed shape
            self.shape = shape_transform.Shape()
            
            if self.shape.IsNull():
                print(f"❌ OPENCASCADE: Transformation resulted in null shape")
                return False, "Transformation resulted in null shape"
            
            print(f"✅ OPENCASCADE: Transformations applied successfully")
            return True, "Transformations applied successfully"
            
        except Exception as e:
            print(f"❌ OPENCASCADE: Exception applying transformations: {e}")
            return False, f"Exception: {e}"
    
    def save_step_file(self, filename):
        """Save the transformed shape to a STEP file"""
        try:
            # Import OpenCASCADE STEP writer modules
            from OCC.Core import STEPControl_Writer, STEPControl_StepModelType
            from OCC.Core import IFSelect_ReturnStatus
            
            print(f"🔧 OPENCASCADE: Saving transformed shape to: {filename}")
            
            if self.shape is None or self.shape.IsNull():
                print(f"❌ OPENCASCADE: No shape to save")
                return False, "No shape to save"
            
            # Create STEP writer
            step_writer = STEPControl_Writer()
            
            # Transfer the shape
            status = step_writer.Transfer(self.shape, STEPControl_StepModelType.STEPControl_AsIs)
            
            if status != IFSelect_ReturnStatus.IFSelect_RetDone:
                print(f"❌ OPENCASCADE: Failed to transfer shape for writing")
                return False, f"Failed to transfer shape: {status}"
            
            # Write the file
            status = step_writer.Write(filename)
            
            if status != IFSelect_ReturnStatus.IFSelect_RetDone:
                print(f"❌ OPENCASCADE: Failed to write STEP file")
                return False, f"Failed to write STEP file: {status}"
            
            # Verify file was created
            if not os.path.exists(filename):
                print(f"❌ OPENCASCADE: Output file was not created")
                return False, "Output file was not created"
            
            file_size = os.path.getsize(filename)
            print(f"✅ OPENCASCADE: Successfully saved STEP file ({file_size} bytes)")
            return True, f"Successfully saved STEP file ({file_size} bytes)"
            
        except Exception as e:
            print(f"❌ OPENCASCADE: Exception saving STEP file: {e}")
            return False, f"Exception: {e}"
    
    def transform_and_save(self, input_filename, output_filename, position, rotation):
        """
        Complete workflow: load, transform, and save STEP file
        """
        print(f"🎯 OPENCASCADE: Complete transformation workflow")
        print(f"   Input: {input_filename}")
        print(f"   Output: {output_filename}")
        print(f"   Position: {position}")
        print(f"   Rotation: {rotation}")
        
        # Load the input file
        success, msg = self.load_step_file(input_filename)
        if not success:
            return False, f"Load failed: {msg}"
        
        # Apply transformations
        success, msg = self.apply_transformations(position, rotation)
        if not success:
            return False, f"Transform failed: {msg}"
        
        # Save the output file
        success, msg = self.save_step_file(output_filename)
        if not success:
            return False, f"Save failed: {msg}"
        
        print(f"✅ OPENCASCADE: Complete workflow successful")
        return True, "Complete workflow successful"


def test_opencascade_transformer():
    """Test the OpenCASCADE transformer"""
    print("🧪 Testing OpenCASCADE Transformer")
    
    # Test file
    input_file = "test.step"
    output_file = "test_opencascade_transformed.step"
    
    if not os.path.exists(input_file):
        print(f"❌ Test file {input_file} not found")
        return False
    
    # Test transformations
    position = {'x': 10.0, 'y': 5.0, 'z': 2.0}
    rotation = {'x': 45.0, 'y': 30.0, 'z': 15.0}
    
    # Create transformer
    transformer = OpenCASCADETransformer()
    
    # Run complete workflow
    success, msg = transformer.transform_and_save(input_file, output_file, position, rotation)
    
    if success:
        print(f"✅ OpenCASCADE Transformer test successful!")
        
        # Check output file
        if os.path.exists(output_file):
            size = os.path.getsize(output_file)
            print(f"   Output file: {output_file} ({size} bytes)")
        
        return True
    else:
        print(f"❌ OpenCASCADE Transformer test failed: {msg}")
        return False


if __name__ == "__main__":
    test_opencascade_transformer()
