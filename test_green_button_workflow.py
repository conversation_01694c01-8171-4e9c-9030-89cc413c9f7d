#!/usr/bin/env python3
"""
Test the exact green button workflow:
1. Load test.step in TOP viewer
2. Rotate it
3. Save with green button 
4. Load saved file in BOTTOM viewer
5. Verify both viewers match in view and numbers
"""

import sys
import os
import tempfile
import time
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    print("🧪 GREEN BUTTON WORKFLOW TEST")
    print("=" * 60)
    print("Testing the exact workflow:")
    print("1. Load test.step in TOP viewer")
    print("2. Apply rotations")
    print("3. Save with GREEN BUTTON (save_step_file_option1)")
    print("4. Load saved file in BOTTOM viewer")
    print("5. Compare view numbers and display")
    print("=" * 60)
    
    # Use test.step as the test file
    test_file = "test.step"
    
    if not os.path.exists(test_file):
        print(f"❌ Test file {test_file} not found!")
        return False
    
    print(f"📁 Using test file: {test_file}")
    
    # Import the viewer module
    try:
        from step_viewer_tdk_modular_fixed import StepViewerTDK
        from PyQt5.QtWidgets import QApplication
        import vtk
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    
    # Create save file in the save directory
    save_dir = "e:\\python\\viewer\\save"
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    save_file = os.path.join(save_dir, "test_rotated_REV001.step")
    
    try:
        print(f"\n🔧 STARTING GREEN BUTTON WORKFLOW TEST...")
        
        # Create Qt application
        app = QApplication(sys.argv)
        
        # Create viewer
        viewer = StepViewerTDK()
        
        print("\n📋 STEP 1: Load test.step in TOP viewer")
        print("-" * 50)
        
        # Load the test file in the top viewer
        viewer.active_viewer = "top"
        loader_top = viewer.step_loader_left
        
        if not loader_top.load_step_file(test_file):
            print(f"❌ Failed to load {test_file} in TOP viewer")
            return False
        
        print(f"✅ Loaded {test_file} in TOP viewer")
        
        # Get initial TOP viewer state
        print("\n🔍 TOP VIEWER INITIAL STATE:")
        top_initial_rot = {
            'x': viewer.current_rot_left.get('x', 0),
            'y': viewer.current_rot_left.get('y', 0), 
            'z': viewer.current_rot_left.get('z', 0)
        }
        print(f"   Rotation: X={top_initial_rot['x']}°, Y={top_initial_rot['y']}°, Z={top_initial_rot['z']}°")
        
        # Calculate initial total angle
        import math
        top_initial_angle = math.sqrt(top_initial_rot['x']**2 + top_initial_rot['y']**2 + top_initial_rot['z']**2)
        print(f"   Total Angle: {top_initial_angle:.1f}°")
        
        print("\n📋 STEP 2: Apply rotations to TOP viewer")
        print("-" * 50)
        
        # Apply specific rotations
        print("🔄 Applying rotations...")
        viewer.rotate_shape('x', 25)  # 25 degrees around X
        viewer.rotate_shape('y', 40)  # 40 degrees around Y  
        viewer.rotate_shape('z', 60)  # 60 degrees around Z
        print("✅ Applied rotations: X=25°, Y=40°, Z=60°")
        
        # Get TOP viewer state after rotation
        print("\n🔍 TOP VIEWER AFTER ROTATION:")
        top_rotated_rot = {
            'x': viewer.current_rot_left.get('x', 0),
            'y': viewer.current_rot_left.get('y', 0), 
            'z': viewer.current_rot_left.get('z', 0)
        }
        print(f"   Rotation: X={top_rotated_rot['x']}°, Y={top_rotated_rot['y']}°, Z={top_rotated_rot['z']}°")
        
        # Calculate total angle for TOP after rotation
        top_rotated_angle = math.sqrt(top_rotated_rot['x']**2 + top_rotated_rot['y']**2 + top_rotated_rot['z']**2)
        print(f"   Total Angle: {top_rotated_angle:.1f}°")
        
        print("\n📋 STEP 3: Save with GREEN BUTTON")
        print("-" * 50)
        
        # Use the GREEN BUTTON save method (save_step_file_option1_direct)
        print(f"💾 Saving with GREEN BUTTON to: {save_file}")
        
        # Make sure we're using the TOP viewer for saving
        viewer.active_viewer = "top"
        
        success = viewer.save_step_file_option1_direct(save_file)
        
        if not success:
            print("❌ GREEN BUTTON save failed!")
            return False
        
        print("✅ GREEN BUTTON save completed")
        
        # Check saved file
        if os.path.exists(save_file):
            size = os.path.getsize(save_file)
            print(f"📊 Saved file size: {size:,} bytes")
            
            if size < 1000:
                print("❌ Saved file too small")
                return False
        else:
            print("❌ Saved file does not exist")
            return False
        
        print("\n📋 STEP 4: Load saved file in BOTTOM viewer")
        print("-" * 50)
        
        # Load the saved file in the bottom viewer using the proper method
        viewer.active_viewer = "bottom"

        # Use the main viewer's load method which calls extract_step_transformation_data
        success = viewer.load_step_file_direct(save_file)
        if not success:
            print(f"❌ Failed to load saved file in BOTTOM viewer")
            return False

        print(f"✅ Loaded saved file in BOTTOM viewer")
        
        # Get BOTTOM viewer state
        print("\n🔍 BOTTOM VIEWER STATE:")
        bottom_rot = {
            'x': viewer.current_rot_right.get('x', 0),
            'y': viewer.current_rot_right.get('y', 0), 
            'z': viewer.current_rot_right.get('z', 0)
        }
        print(f"   Rotation: X={bottom_rot['x']}°, Y={bottom_rot['y']}°, Z={bottom_rot['z']}°")
        
        # Calculate total angle for BOTTOM
        bottom_angle = math.sqrt(bottom_rot['x']**2 + bottom_rot['y']**2 + bottom_rot['z']**2)
        print(f"   Total Angle: {bottom_angle:.1f}°")
        
        print("\n📋 STEP 5: Compare viewers - VIEW AND NUMBERS")
        print("-" * 50)
        
        print("🔍 DETAILED COMPARISON:")
        
        # Compare individual rotation values
        tolerance = 1.0  # Allow 1 degree tolerance
        matches = []
        
        for axis in ['x', 'y', 'z']:
            top_val = top_rotated_rot[axis]
            bottom_val = bottom_rot[axis]
            diff = abs(top_val - bottom_val)
            
            match = diff <= tolerance
            matches.append(match)
            
            status = "✅ MATCH" if match else "❌ DIFFERENT"
            print(f"   {axis.upper()} Rotation: {status}")
            print(f"      TOP:    {top_val:6.1f}°")
            print(f"      BOTTOM: {bottom_val:6.1f}°")
            print(f"      Diff:   {diff:6.1f}°")
        
        # Compare total angles
        angle_diff = abs(top_rotated_angle - bottom_angle)
        angle_match = angle_diff <= tolerance
        matches.append(angle_match)
        
        status = "✅ MATCH" if angle_match else "❌ DIFFERENT"
        print(f"   Total Angle: {status}")
        print(f"      TOP:    {top_rotated_angle:6.1f}°")
        print(f"      BOTTOM: {bottom_angle:6.1f}°")
        print(f"      Diff:   {angle_diff:6.1f}°")
        
        # Overall result
        all_match = all(matches)
        
        print("\n📊 FINAL RESULTS:")
        print("=" * 60)
        
        if all_match:
            print("🎉 SUCCESS: GREEN BUTTON WORKFLOW WORKS!")
            print("   ✅ TOP and BOTTOM viewers show IDENTICAL models")
            print("   ✅ View numbers MATCH between both viewers")
            print("   ✅ Coordinate system fix is working correctly")
            print("   ✅ Green button saves transformations properly")
            print("\n💡 This confirms:")
            print("   • The saved STEP file preserves all rotations")
            print("   • Both viewers display the same visual result")
            print("   • The coordinate system remains standard")
            print("   • The dual-viewer system works as expected")
        else:
            print("❌ FAILURE: GREEN BUTTON WORKFLOW HAS ISSUES")
            print("   • TOP and BOTTOM viewers show different models")
            print("   • View numbers don't match")
            print("   • There are still coordinate system problems")
        
        # Keep GUI open for visual inspection
        print("\n🔍 GUI is now open - you can visually inspect both viewers")
        print("   Close the window when you're done inspecting")

        # Show the window and keep it open
        viewer.show()
        viewer.raise_()
        viewer.activateWindow()

        # Run the event loop to keep GUI responsive
        app.exec_()

        return all_match

    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False

    finally:
        # Clean up
        if os.path.exists(save_file):
            try:
                os.unlink(save_file)
                print(f"🧹 Cleaned up test file: {save_file}")
            except:
                pass

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
