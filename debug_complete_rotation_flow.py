#!/usr/bin/env python3
"""
COMPLETE ROTATION DEBUG - Trace everything from mouse to display
This will debug the complete flow:
1. Mouse rotation detection
2. VTK actor orientation changes
3. Axis calculation from rotation
4. Display value updates
5. Text overlay writing
"""

import sys
import os
import time
import math
from PyQt5.QtWidgets import <PERSON>A<PERSON><PERSON>, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QLabel, QTextEdit
from PyQt5.QtCore import QTimer
import vtk
from vtk.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor

class CompleteRotationDebug(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("COMPLETE ROTATION DEBUG - Mouse to Display Flow")
        self.setGeometry(100, 100, 1400, 800)
        
        # Initialize tracking variables
        self.current_rot = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_axis = {'x': 0.0, 'y': 0.0, 'z': 1.0}
        self.current_angle = 0.0
        self.last_orientation = [0.0, 0.0, 0.0]
        
        # VTK components
        self.vtk_widget = None
        self.renderer = None
        self.step_actor = None
        self.interactor = None
        
        # Debug tracking
        self.debug_count = 0
        self.mouse_events = 0
        self.rotation_updates = 0
        
        self.init_ui()
        self.setup_vtk()
        
        # Multiple timers for comprehensive tracking
        self.rotation_timer = QTimer()
        self.rotation_timer.timeout.connect(self.track_rotation_changes)
        self.rotation_timer.start(100)  # Every 100ms
        
        self.display_timer = QTimer()
        self.display_timer.timeout.connect(self.update_all_displays)
        self.display_timer.start(200)  # Every 200ms
        
    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QHBoxLayout(central_widget)
        
        # Left panel - controls and debug
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_panel.setFixedWidth(400)
        
        # Load button
        self.load_btn = QPushButton("Load STEP File")
        self.load_btn.clicked.connect(self.load_step_file)
        left_layout.addWidget(self.load_btn)
        
        # Rotation buttons
        left_layout.addWidget(QLabel("ROTATION BUTTONS:"))
        
        rot_layout = QHBoxLayout()
        self.x_plus_btn = QPushButton("X+15°")
        self.x_plus_btn.clicked.connect(lambda: self.debug_button_rotation('x', 15))
        self.y_plus_btn = QPushButton("Y+15°")
        self.y_plus_btn.clicked.connect(lambda: self.debug_button_rotation('y', 15))
        self.z_plus_btn = QPushButton("Z+15°")
        self.z_plus_btn.clicked.connect(lambda: self.debug_button_rotation('z', 15))
        
        rot_layout.addWidget(self.x_plus_btn)
        rot_layout.addWidget(self.y_plus_btn)
        rot_layout.addWidget(self.z_plus_btn)
        left_layout.addLayout(rot_layout)
        
        # Current values display
        left_layout.addWidget(QLabel("CURRENT VALUES:"))
        self.rot_label = QLabel("ROT: X=0.0° Y=0.0° Z=0.0°")
        self.axis_label = QLabel("AXIS: X=0.0 Y=0.0 Z=1.0")
        self.angle_label = QLabel("ANGLE: 0.0°")
        self.stats_label = QLabel("Events: Mouse=0 Updates=0")
        
        left_layout.addWidget(self.rot_label)
        left_layout.addWidget(self.axis_label)
        left_layout.addWidget(self.angle_label)
        left_layout.addWidget(self.stats_label)
        
        # Debug output
        left_layout.addWidget(QLabel("LIVE DEBUG TRACE:"))
        self.debug_text = QTextEdit()
        self.debug_text.setMaximumHeight(300)
        self.debug_text.setStyleSheet("font-family: monospace; font-size: 9pt;")
        left_layout.addWidget(self.debug_text)
        
        # Clear button
        self.clear_btn = QPushButton("Clear Debug Log")
        self.clear_btn.clicked.connect(self.debug_text.clear)
        left_layout.addWidget(self.clear_btn)
        
        layout.addWidget(left_panel)
        
        # Right panel - VTK viewer
        self.vtk_widget = QVTKRenderWindowInteractor()
        layout.addWidget(self.vtk_widget)
        
    def setup_vtk(self):
        """Setup VTK renderer with comprehensive event tracking"""
        self.renderer = vtk.vtkRenderer()
        self.renderer.SetBackground(0.1, 0.1, 0.2)
        
        self.vtk_widget.GetRenderWindow().AddRenderer(self.renderer)
        self.interactor = self.vtk_widget.GetRenderWindow().GetInteractor()
        
        # Add comprehensive event observers
        self.interactor.AddObserver("MouseMoveEvent", self.on_mouse_move)
        self.interactor.AddObserver("LeftButtonPressEvent", self.on_mouse_press)
        self.interactor.AddObserver("LeftButtonReleaseEvent", self.on_mouse_release)
        self.interactor.AddObserver("InteractionEvent", self.on_interaction)
        self.interactor.AddObserver("EndInteractionEvent", self.on_end_interaction)
        
        self.vtk_widget.Initialize()
        self.vtk_widget.Start()
        
        self.debug_log("🔧 VTK setup complete with event observers")
        
    def load_step_file(self):
        """Load STEP file with debug tracing"""
        from PyQt5.QtWidgets import QFileDialog
        filename, _ = QFileDialog.getOpenFileName(self, "Load STEP File", "", "STEP Files (*.step *.stp)")
        
        if filename:
            self.debug_log(f"📁 Loading: {os.path.basename(filename)}")
            
            try:
                from step_loader import STEPLoader
                loader = STEPLoader()
                success, message = loader.load_step_file(filename)
                
                if success and loader.current_polydata:
                    self.display_polydata(loader.current_polydata)
                    self.debug_log(f"✅ Loaded: {message}")
                else:
                    self.debug_log(f"❌ Failed: {message}")
                    
            except Exception as e:
                self.debug_log(f"❌ Error: {e}")
                
    def display_polydata(self, polydata):
        """Display polydata with debug info"""
        try:
            if self.step_actor:
                self.renderer.RemoveActor(self.step_actor)
                
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputData(polydata)
            
            self.step_actor = vtk.vtkActor()
            self.step_actor.SetMapper(mapper)
            self.step_actor.GetProperty().SetColor(0.7, 0.7, 0.7)
            
            self.renderer.AddActor(self.step_actor)
            self.renderer.ResetCamera()
            self.vtk_widget.GetRenderWindow().Render()
            
            # Initialize tracking
            self.last_orientation = list(self.step_actor.GetOrientation())
            self.debug_log(f"🎭 Actor created, initial orientation: {self.last_orientation}")
            
        except Exception as e:
            self.debug_log(f"❌ Display error: {e}")
            
    def on_mouse_move(self, obj, event):
        """Track mouse movement"""
        self.mouse_events += 1
        if self.mouse_events % 10 == 0:  # Log every 10th event to avoid spam
            self.debug_log(f"🖱️ Mouse move #{self.mouse_events}")
            
    def on_mouse_press(self, obj, event):
        """Track mouse press"""
        self.debug_log("🖱️ Mouse press - rotation may start")
        
    def on_mouse_release(self, obj, event):
        """Track mouse release"""
        self.debug_log("🖱️ Mouse release - rotation may end")
        self.check_orientation_change("mouse_release")
        
    def on_interaction(self, obj, event):
        """Track interaction events"""
        self.debug_log("🔄 Interaction event")
        
    def on_end_interaction(self, obj, event):
        """Track end of interaction"""
        self.debug_log("🔄 End interaction event")
        self.check_orientation_change("end_interaction")
        
    def check_orientation_change(self, source):
        """Check if actor orientation changed"""
        if not self.step_actor:
            return
            
        try:
            current_orientation = list(self.step_actor.GetOrientation())
            
            # Check for significant change
            changed = False
            for i in range(3):
                if abs(current_orientation[i] - self.last_orientation[i]) > 0.5:
                    changed = True
                    break
                    
            if changed:
                self.debug_log(f"📐 ORIENTATION CHANGED ({source}):")
                self.debug_log(f"   Old: {[f'{x:.1f}' for x in self.last_orientation]}")
                self.debug_log(f"   New: {[f'{x:.1f}' for x in current_orientation]}")
                
                # Update rotation values
                self.current_rot['x'] = current_orientation[0]
                self.current_rot['y'] = current_orientation[1]
                self.current_rot['z'] = current_orientation[2]
                
                # Calculate axis and angle
                self.calculate_axis_angle()
                
                # Update displays
                self.update_all_displays()
                
                self.last_orientation = current_orientation
                self.rotation_updates += 1
                
                self.debug_log(f"✅ Updated rotation values from {source}")
                
        except Exception as e:
            self.debug_log(f"❌ Orientation check error: {e}")
            
    def track_rotation_changes(self):
        """Timer-based rotation tracking"""
        if self.step_actor:
            try:
                current_orientation = list(self.step_actor.GetOrientation())
                
                # Check for any change
                changed = False
                for i in range(3):
                    if abs(current_orientation[i] - self.last_orientation[i]) > 0.1:
                        changed = True
                        break
                        
                if changed:
                    self.debug_log(f"⏰ Timer detected rotation change:")
                    self.debug_log(f"   {[f'{x:.1f}' for x in current_orientation]}")
                    
                    self.current_rot['x'] = current_orientation[0]
                    self.current_rot['y'] = current_orientation[1]
                    self.current_rot['z'] = current_orientation[2]
                    
                    self.calculate_axis_angle()
                    self.last_orientation = current_orientation
                    self.rotation_updates += 1
                    
            except:
                pass
                
    def calculate_axis_angle(self):
        """Calculate axis and angle from rotation with debug"""
        try:
            self.debug_log(f"🧮 Calculating axis/angle from ROT: {self.current_rot}")
            
            rot_mag = math.sqrt(self.current_rot['x']**2 + self.current_rot['y']**2 + self.current_rot['z']**2)
            self.debug_log(f"   Magnitude: {rot_mag:.3f}")
            
            if rot_mag > 0.001:
                self.current_axis = {
                    'x': self.current_rot['x'] / rot_mag,
                    'y': self.current_rot['y'] / rot_mag,
                    'z': self.current_rot['z'] / rot_mag
                }
                self.current_angle = rot_mag
                self.debug_log(f"   Calculated AXIS: {self.current_axis}")
                self.debug_log(f"   Calculated ANGLE: {self.current_angle:.1f}°")
            else:
                self.current_axis = {'x': 0.0, 'y': 0.0, 'z': 1.0}
                self.current_angle = 0.0
                self.debug_log("   Zero rotation - using default axis")
                
        except Exception as e:
            self.debug_log(f"❌ Axis calculation error: {e}")
            
    def debug_button_rotation(self, axis, degrees):
        """Debug button rotation with complete tracing"""
        self.debug_log(f"🔘 BUTTON: {axis.upper()}{'+' if degrees > 0 else ''}{degrees}°")
        
        # Update rotation values
        old_value = self.current_rot[axis]
        self.current_rot[axis] += degrees
        self.debug_log(f"   {axis.upper()} rotation: {old_value:.1f}° → {self.current_rot[axis]:.1f}°")
        
        # Apply VTK rotation
        if self.step_actor:
            self.debug_log(f"   Applying VTK rotation...")
            self.step_actor.RotateWXYZ(degrees,
                1 if axis == 'x' else 0,
                1 if axis == 'y' else 0,
                1 if axis == 'z' else 0)
            
            # Get new orientation
            new_orientation = list(self.step_actor.GetOrientation())
            self.debug_log(f"   New VTK orientation: {[f'{x:.1f}' for x in new_orientation]}")
            
            self.vtk_widget.GetRenderWindow().Render()
            self.last_orientation = new_orientation
            
        # Calculate new axis/angle
        self.calculate_axis_angle()
        
        # Update displays
        self.update_all_displays()
        
        self.debug_log(f"✅ Button rotation complete")
        
    def update_all_displays(self):
        """Update all display elements with debug"""
        try:
            # Update labels
            self.rot_label.setText(f"ROT: X={self.current_rot['x']:.1f}° Y={self.current_rot['y']:.1f}° Z={self.current_rot['z']:.1f}°")
            self.axis_label.setText(f"AXIS: X={self.current_axis['x']:.3f} Y={self.current_axis['y']:.3f} Z={self.current_axis['z']:.3f}")
            self.angle_label.setText(f"ANGLE: {self.current_angle:.1f}°")
            self.stats_label.setText(f"Events: Mouse={self.mouse_events} Updates={self.rotation_updates}")
            
            # Debug the display update
            if self.debug_count % 20 == 0:  # Log every 20th update
                self.debug_log(f"📺 Display updated #{self.debug_count}")
                
            self.debug_count += 1
            
        except Exception as e:
            self.debug_log(f"❌ Display update error: {e}")
            
    def debug_log(self, message):
        """Add message to debug log"""
        timestamp = time.strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}"
        print(full_message)
        
        # Add to text widget
        self.debug_text.append(full_message)
        
        # Keep only last 100 lines
        if self.debug_text.document().blockCount() > 100:
            cursor = self.debug_text.textCursor()
            cursor.movePosition(cursor.Start)
            cursor.select(cursor.BlockUnderCursor)
            cursor.removeSelectedText()
            cursor.deletePreviousChar()

def main():
    app = QApplication(sys.argv)
    window = CompleteRotationDebug()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
