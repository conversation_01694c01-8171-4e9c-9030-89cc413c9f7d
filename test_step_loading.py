#!/usr/bin/env python3
"""
Test STEP file loading directly
"""

print("Testing STEP file loading...")

try:
    from step_loader import STEPLoader
    print("STEPLoader imported successfully")
    
    loader = STEPLoader()
    print("STEPLoader created")
    
    result = loader.load_step_file('SOIC16P127_1270X940X610L89X51.STEP')
    print(f"Load result: {result}")
    
    if len(result) == 3:
        polydata, success, message = result
        print(f"Success: {success}")
        print(f"Message: {message}")
        if success and polydata:
            print(f"Polydata: {polydata.GetNumberOfCells()} cells, {polydata.GetNumberOfPoints()} points")
            
            # Check colors
            colors = polydata.GetCellData().GetScalars("Colors")
            if colors:
                print(f"Colors found: {colors.GetNumberOfTuples()} tuples")
                print("STEP file loaded successfully with colors!")
            else:
                print("No colors found in polydata")
        else:
            print("STEP file loading failed")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
