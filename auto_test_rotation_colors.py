#!/usr/bin/env python3
"""
AUTOMATED TEST - Test rotation tracking and color handling
This will automatically test and report issues with:
1. Mouse rotation tracking
2. Button rotation tracking  
3. Color handling
"""

import sys
import os
import time
import vtk
from step_loader import <PERSON><PERSON><PERSON>oader

def test_rotation_tracking():
    """Test rotation tracking functionality"""
    print("=" * 60)
    print("TESTING ROTATION TRACKING")
    print("=" * 60)
    
    # Test 1: Basic rotation value tracking
    print("\n1. Testing rotation value tracking...")
    
    current_rot = {'x': 0.0, 'y': 0.0, 'z': 0.0}
    
    # Simulate button clicks
    print("   Simulating X+15° button click...")
    current_rot['x'] += 15.0
    print(f"   Result: {current_rot}")
    
    print("   Simulating Y+15° button click...")
    current_rot['y'] += 15.0
    print(f"   Result: {current_rot}")
    
    print("   Simulating another X+15° button click...")
    current_rot['x'] += 15.0
    print(f"   Result: {current_rot}")
    
    # Check if values increment properly
    if current_rot['x'] == 30.0 and current_rot['y'] == 15.0:
        print("   ✅ PASS: Rotation values increment correctly")
    else:
        print("   ❌ FAIL: Rotation values not incrementing properly")
    
    # Test 2: Axis calculation from rotation
    print("\n2. Testing axis calculation from rotation...")
    
    import math
    rot_mag = math.sqrt(current_rot['x']**2 + current_rot['y']**2 + current_rot['z']**2)
    
    if rot_mag > 0.001:
        current_axis = {
            'x': current_rot['x'] / rot_mag,
            'y': current_rot['y'] / rot_mag,
            'z': current_rot['z'] / rot_mag
        }
        current_angle = rot_mag
        print(f"   Calculated axis: {current_axis}")
        print(f"   Calculated angle: {current_angle:.1f}°")
        print("   ✅ PASS: Axis calculation working")
    else:
        print("   ❌ FAIL: Axis calculation failed")
    
    return True

def test_vtk_rotation():
    """Test VTK actor rotation tracking"""
    print("\n3. Testing VTK actor rotation...")
    
    try:
        # Create a simple VTK actor
        source = vtk.vtkCubeSource()
        mapper = vtk.vtkPolyDataMapper()
        mapper.SetInputConnection(source.GetOutputPort())
        
        actor = vtk.vtkActor()
        actor.SetMapper(mapper)
        
        # Test rotation
        print("   Applying 15° X rotation to VTK actor...")
        actor.RotateX(15.0)
        
        # Get orientation
        orientation = actor.GetOrientation()
        print(f"   VTK actor orientation: {orientation}")
        
        # Apply another rotation
        print("   Applying another 15° X rotation...")
        actor.RotateX(15.0)
        
        new_orientation = actor.GetOrientation()
        print(f"   New VTK actor orientation: {new_orientation}")
        
        # Check if rotation accumulated
        if abs(new_orientation[0] - 30.0) < 1.0:
            print("   ✅ PASS: VTK rotation accumulates correctly")
        else:
            print("   ❌ FAIL: VTK rotation not accumulating properly")
            
        return True
        
    except Exception as e:
        print(f"   ❌ FAIL: VTK rotation test error: {e}")
        return False

def test_step_file_loading():
    """Test STEP file loading and color extraction"""
    print("\n4. Testing STEP file loading...")
    
    try:
        # Find a STEP file to test with
        step_files = []
        for root, dirs, files in os.walk("."):
            for file in files:
                if file.lower().endswith(('.step', '.stp')):
                    step_files.append(os.path.join(root, file))
                    if len(step_files) >= 3:  # Just need a few for testing
                        break
            if step_files:
                break
                
        if not step_files:
            print("   ⚠️ SKIP: No STEP files found for testing")
            return True
            
        print(f"   Found {len(step_files)} STEP files for testing")
        
        # Test loading first file
        test_file = step_files[0]
        print(f"   Testing with: {os.path.basename(test_file)}")
        
        loader = STEPLoader()
        success, message = loader.load_step_file(test_file)
        
        if success and loader.current_polydata:
            print(f"   ✅ PASS: File loaded successfully - {message}")
            
            # Check for colors
            polydata = loader.current_polydata
            cell_colors = polydata.GetCellData().GetScalars()
            point_colors = polydata.GetPointData().GetScalars()
            
            print(f"   Cells: {polydata.GetNumberOfCells()}")
            print(f"   Points: {polydata.GetNumberOfPoints()}")
            
            if cell_colors:
                print(f"   ✅ Found cell colors: {cell_colors.GetNumberOfTuples()} tuples")
            else:
                print("   ⚠️ No cell colors found")
                
            if point_colors:
                print(f"   ✅ Found point colors: {point_colors.GetNumberOfTuples()} tuples")
            else:
                print("   ⚠️ No point colors found")
                
            return True
        else:
            print(f"   ❌ FAIL: File loading failed - {message}")
            return False
            
    except Exception as e:
        print(f"   ❌ FAIL: STEP loading test error: {e}")
        return False

def test_color_application():
    """Test silver color application"""
    print("\n5. Testing silver color application...")
    
    try:
        # Create test polydata
        source = vtk.vtkCubeSource()
        source.Update()
        polydata = source.GetOutput()
        
        # Apply silver colors
        colors = vtk.vtkUnsignedCharArray()
        colors.SetNumberOfComponents(3)
        colors.SetName("Colors")
        
        # Define silver colors
        light_silver = (192, 192, 192)  # Light silver
        dark_silver = (128, 128, 128)   # Dark silver
        
        num_cells = polydata.GetNumberOfCells()
        print(f"   Applying colors to {num_cells} cells...")
        
        for i in range(num_cells):
            if i % 2 == 0:
                colors.InsertNextTuple3(*light_silver)
            else:
                colors.InsertNextTuple3(*dark_silver)
                
        polydata.GetCellData().SetScalars(colors)
        
        # Check if colors were applied
        applied_colors = polydata.GetCellData().GetScalars()
        if applied_colors and applied_colors.GetNumberOfTuples() == num_cells:
            print("   ✅ PASS: Silver colors applied successfully")
            
            # Check first few color values
            for i in range(min(3, num_cells)):
                color = applied_colors.GetTuple3(i)
                expected = light_silver if i % 2 == 0 else dark_silver
                if color == expected:
                    print(f"   ✅ Cell {i}: {color} (correct)")
                else:
                    print(f"   ❌ Cell {i}: {color} (expected {expected})")
                    
            return True
        else:
            print("   ❌ FAIL: Colors not applied properly")
            return False
            
    except Exception as e:
        print(f"   ❌ FAIL: Color application test error: {e}")
        return False

def main():
    """Run all tests"""
    print("AUTOMATED ROTATION & COLOR TESTING")
    print("=" * 60)
    
    tests = [
        ("Rotation Tracking", test_rotation_tracking),
        ("VTK Rotation", test_vtk_rotation),
        ("STEP File Loading", test_step_file_loading),
        ("Color Application", test_color_application)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! The core functionality is working.")
    else:
        print("⚠️ Some tests failed. Issues identified for fixing.")
    
    print("\nPress Enter to exit...")
    input()

if __name__ == "__main__":
    main()
