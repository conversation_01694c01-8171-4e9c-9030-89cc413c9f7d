#!/usr/bin/env python3
"""
Console-based rotation save test
This will test the rotation save functionality and provide console output
"""

import sys
import os
import time

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test if we can import the required modules"""
    print("🔧 Testing imports...")
    
    try:
        from step_loader import STEPLoader
        print("✅ STEPLoader imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import STEPLoader: {e}")
        return False
        
    try:
        from vtk_renderer import VTKRenderer
        print("✅ VTKRenderer imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import VTKRenderer: {e}")
        return False
        
    try:
        from gui_components import create_tool_dock
        print("✅ gui_components imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import gui_components: {e}")
        return False
        
    return True

def find_step_files():
    """Find available STEP files"""
    print("\n🔧 Looking for STEP files...")
    
    step_files = []
    for f in os.listdir('.'):
        if f.endswith('.STEP') or f.endswith('.step'):
            step_files.append(f)
            
    if step_files:
        print(f"✅ Found {len(step_files)} STEP files:")
        for f in step_files:
            size = os.path.getsize(f)
            print(f"   - {f} ({size:,} bytes)")
        return step_files
    else:
        print("❌ No STEP files found")
        return []

def test_step_loader():
    """Test the STEP loader functionality"""
    print("\n🔧 Testing STEP loader...")
    
    step_files = find_step_files()
    if not step_files:
        return False
        
    try:
        from step_loader import STEPLoader
        loader = STEPLoader()
        print("✅ STEPLoader instance created")
        
        # Try to load a file
        test_file = step_files[0]
        print(f"   Testing with file: {test_file}")
        
        # Check if loader has the expected methods
        if hasattr(loader, 'load_step_file'):
            print("✅ load_step_file method found")
        else:
            print("❌ load_step_file method not found")
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing STEP loader: {e}")
        return False

def test_file_operations():
    """Test basic file operations that would be used in save/load"""
    print("\n🔧 Testing file operations...")
    
    step_files = find_step_files()
    if not step_files:
        return False
        
    test_file = step_files[0]
    test_output = "test_console_output.step"
    
    try:
        # Test reading
        print(f"   Reading {test_file}...")
        with open(test_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        print(f"✅ Read {len(content):,} characters from {test_file}")
        
        # Test writing
        print(f"   Writing to {test_output}...")
        with open(test_output, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ Wrote content to {test_output}")
        
        # Verify written file
        if os.path.exists(test_output):
            size = os.path.getsize(test_output)
            print(f"✅ Output file created: {size:,} bytes")
            
            # Clean up
            os.remove(test_output)
            print("✅ Test file cleaned up")
            return True
        else:
            print("❌ Output file not created")
            return False
            
    except Exception as e:
        print(f"❌ Error in file operations: {e}")
        return False

def check_existing_test_results():
    """Check if there are existing test result files"""
    print("\n🔧 Checking for existing test results...")
    
    test_files = [
        "test_mouse_rotation_fixed.step",
        "test_rotation_save_verification.step",
        "test_mouse_rotation_save_fix.step"
    ]
    
    found_files = []
    for f in test_files:
        if os.path.exists(f):
            size = os.path.getsize(f)
            mtime = os.path.getmtime(f)
            time_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(mtime))
            found_files.append(f)
            print(f"✅ Found: {f} ({size:,} bytes, modified: {time_str})")
            
    if found_files:
        print(f"\n✅ Found {len(found_files)} existing test result files")
        print("   This suggests previous tests have run successfully")
        return found_files
    else:
        print("❌ No existing test result files found")
        return []

def main():
    """Main test function"""
    print("🔧 CONSOLE ROTATION SAVE TEST")
    print("=" * 50)
    print("This test will verify the components needed for rotation save functionality")
    print("=" * 50)
    
    # Test 1: Imports
    if not test_imports():
        print("\n❌ Import test failed - cannot proceed")
        return False
        
    # Test 2: Find STEP files
    step_files = find_step_files()
    if not step_files:
        print("\n❌ No STEP files found - cannot test")
        return False
        
    # Test 3: STEP loader
    if not test_step_loader():
        print("\n❌ STEP loader test failed")
        return False
        
    # Test 4: File operations
    if not test_file_operations():
        print("\n❌ File operations test failed")
        return False
        
    # Test 5: Check existing results
    existing_files = check_existing_test_results()
    
    print("\n" + "=" * 50)
    print("✅ CONSOLE TEST COMPLETED SUCCESSFULLY")
    print("=" * 50)
    print("SUMMARY:")
    print("   ✅ All imports working")
    print("   ✅ STEP files available")
    print("   ✅ STEP loader functional")
    print("   ✅ File operations working")
    
    if existing_files:
        print(f"   ✅ {len(existing_files)} existing test result files found")
        print("\nRECOMMENDATION:")
        print("   The components are working. Try running the GUI application manually:")
        print("   1. Run: python step_viewer_tdk_modular_fixed.py")
        print("   2. Load a STEP file into TOP viewer")
        print("   3. Apply rotations using the rotation buttons")
        print("   4. Save using the green 'Save STEP File (Improved Method)' button")
        print("   5. Load the saved file into BOTTOM viewer")
        print("   6. Compare both viewers to verify they show the same rotated model")
    else:
        print("   ⚠️  No existing test results - GUI test may not have run yet")
        
    print("=" * 50)
    return True

if __name__ == "__main__":
    main()
