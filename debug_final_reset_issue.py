#!/usr/bin/env python3
"""
Final Reset Debug - Find the EXACT root cause and fix it
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular import Step<PERSON>ie<PERSON>TD<PERSON>

def debug_final_reset_issue():
    """Final comprehensive debug to solve the reset issue once and for all"""
    
    print("🔧 FINAL RESET DEBUG - FIND THE EXACT ROOT CAUSE")
    print("=" * 60)
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    # Process events to ensure GUI is ready
    app.processEvents()
    time.sleep(1)
    
    # Find a STEP file to test with
    test_files = [
        "AMPHENOL_U77-A1118-200T.STEP",
        "test.step",
        "sample.stp"
    ]
    
    test_file = None
    for filename in test_files:
        if os.path.exists(filename):
            test_file = filename
            break
    
    if not test_file:
        print("❌ No STEP file found for testing")
        return False
    
    print(f"📁 Using test file: {test_file}")
    
    # Step 1: Load STEP file
    print(f"\n📋 STEP 1: LOADING {test_file}...")
    success = viewer.load_step_file_direct(test_file)
    app.processEvents()
    time.sleep(2)
    
    if not success:
        print("❌ Failed to load STEP file")
        return False
    
    renderer = viewer.vtk_renderer_left
    
    # Get ALL actors and their details
    all_actors = []
    if hasattr(renderer, 'renderer') and renderer.renderer:
        actor_collection = renderer.renderer.GetActors()
        actor_collection.InitTraversal()
        
        actor = actor_collection.GetNextActor()
        while actor:
            all_actors.append(actor)
            actor = actor_collection.GetNextActor()
    
    print(f"\n🔍 INITIAL STATE - ALL ACTORS:")
    for i, actor in enumerate(all_actors):
        pos = actor.GetPosition()
        orient = actor.GetOrientation()
        visible = actor.GetVisibility()
        bounds = actor.GetBounds()
        
        print(f"  Actor {i}: Pos={pos}, Orient={orient}, Visible={visible}")
        print(f"    Bounds: {bounds}")
        
        # Identify actor type
        is_multi = False
        is_single = False
        is_bbox = False
        
        if hasattr(renderer, 'step_actors') and renderer.step_actors:
            for j, multi_actor in enumerate(renderer.step_actors):
                if multi_actor == actor:
                    is_multi = True
                    print(f"    *** MULTI-ACTOR {j} ***")
                    
        if hasattr(renderer, 'step_actor') and renderer.step_actor == actor:
            is_single = True
            print(f"    *** SINGLE-ACTOR ***")
            
        if hasattr(renderer, 'bbox_actor') and renderer.bbox_actor == actor:
            is_bbox = True
            print(f"    *** BOUNDING BOX ACTOR ***")
            
        if not (is_multi or is_single or is_bbox):
            print(f"    *** UNKNOWN ACTOR ***")
    
    print(f"\n👁️ INITIAL MODEL - Note position (3 seconds)")
    time.sleep(3)
    
    # Step 2: Apply HUGE transformation to ONE specific actor
    print(f"\n📋 STEP 2: TESTING INDIVIDUAL ACTOR CONTROL...")
    
    # Find the multi-actor (the one we think should be displayed)
    multi_actor = None
    if hasattr(renderer, 'step_actors') and renderer.step_actors:
        multi_actor = renderer.step_actors[0]
        print(f"🔧 Found multi-actor: {hex(id(multi_actor))}")
        
        # Apply HUGE transformation directly to multi-actor
        print(f"🔧 Applying HUGE transformation directly to multi-actor:")
        print(f"   Moving +200mm X, Rotating +180° Z")
        
        orig_pos = multi_actor.GetPosition()
        orig_orient = multi_actor.GetOrientation()
        print(f"   Before: Pos={orig_pos}, Orient={orig_orient}")
        
        multi_actor.AddPosition(200, 0, 0)
        multi_actor.RotateWXYZ(180, 0, 0, 1)
        multi_actor.Modified()
        
        # Force render
        renderer.render_window.Render()
        app.processEvents()
        time.sleep(1)
        
        new_pos = multi_actor.GetPosition()
        new_orient = multi_actor.GetOrientation()
        print(f"   After: Pos={new_pos}, Orient={new_orient}")
        
        print(f"👁️ MULTI-ACTOR TRANSFORMED - Can you see movement? (5 seconds)")
        time.sleep(5)
        
        # Reset multi-actor
        print(f"🔧 Resetting multi-actor back...")
        multi_actor.SetPosition(orig_pos)
        multi_actor.SetOrientation(orig_orient)
        multi_actor.Modified()
        renderer.render_window.Render()
        app.processEvents()
        time.sleep(1)
        
        print(f"👁️ MULTI-ACTOR RESET - Did it move back? (3 seconds)")
        time.sleep(3)
    
    # Step 3: Test if there's another actor that's actually displayed
    print(f"\n📋 STEP 3: TESTING ALL OTHER ACTORS...")
    
    for i, actor in enumerate(all_actors):
        if actor == multi_actor:
            print(f"🔧 Skipping Actor {i} - already tested (multi-actor)")
            continue
            
        if not actor.GetVisibility():
            print(f"🔧 Skipping Actor {i} - not visible")
            continue
            
        print(f"\n🔧 Testing Actor {i} ({hex(id(actor))})...")
        
        orig_pos = actor.GetPosition()
        orig_orient = actor.GetOrientation()
        print(f"   Before: Pos={orig_pos}, Orient={orig_orient}")
        
        # Apply HUGE transformation
        print(f"   Applying HUGE transformation: Move +200mm X, Rotate +180° Z")
        actor.AddPosition(200, 0, 0)
        actor.RotateWXYZ(180, 0, 0, 1)
        actor.Modified()
        
        # Force render
        renderer.render_window.Render()
        app.processEvents()
        time.sleep(1)
        
        new_pos = actor.GetPosition()
        new_orient = actor.GetOrientation()
        print(f"   After: Pos={new_pos}, Orient={new_orient}")
        
        print(f"👁️ ACTOR {i} TRANSFORMED - Can you see movement? (5 seconds)")
        time.sleep(5)
        
        # Reset this actor
        print(f"   Resetting Actor {i}...")
        actor.SetPosition(orig_pos)
        actor.SetOrientation(orig_orient)
        actor.Modified()
        renderer.render_window.Render()
        app.processEvents()
        time.sleep(1)
        
        print(f"👁️ ACTOR {i} RESET - Did it move back? (3 seconds)")
        time.sleep(3)
    
    # Step 4: Check if the issue is with the original transforms storage
    print(f"\n📋 STEP 4: CHECKING ORIGINAL TRANSFORMS STORAGE...")
    
    if hasattr(viewer, 'original_actor_transforms_left'):
        print(f"Original transforms stored: {len(viewer.original_actor_transforms_left)}")
        for i, orig_state in enumerate(viewer.original_actor_transforms_left):
            print(f"  Original {i}:")
            print(f"    Position: {orig_state['position']}")
            print(f"    Orientation: {orig_state['orientation']}")
            print(f"    Transform: {orig_state['transform']}")
    else:
        print("❌ No original transforms stored!")
    
    # Step 5: Final analysis and conclusion
    print(f"\n📋 STEP 5: FINAL ANALYSIS")
    print("=" * 60)
    
    print(f"🎯 KEY QUESTIONS TO ANSWER:")
    print(f"1. Which actor transformation caused visible movement?")
    print(f"2. If NO actor caused movement, the issue is deeper (camera, rendering, etc.)")
    print(f"3. If multi-actor caused movement, why doesn't GUI reset work?")
    print(f"4. If another actor caused movement, we're transforming the wrong one!")
    
    print(f"\n🔍 DIAGNOSIS:")
    print(f"- If multi-actor moved visually: GUI should work, check reset logic")
    print(f"- If bounding box moved visually: Bounding box is the displayed model!")
    print(f"- If unknown actor moved visually: That's the real model actor")
    print(f"- If NO actor moved visually: Rendering or camera issue")
    
    # Keep window open
    print(f"\n👁️ Window will stay open for final inspection...")
    QTimer.singleShot(15000, app.quit)
    app.exec_()
    
    return True

if __name__ == "__main__":
    debug_final_reset_issue()
