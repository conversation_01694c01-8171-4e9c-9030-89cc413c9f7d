#!/usr/bin/env python3

import sys
try:
    from step_viewer_tdk_modular_fixed import StepViewerTDK
    from PyQt5.QtWidgets import QApplication
    print('Imports successful')
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    print('Viewer created')
    
    # Check if cursor labels exist
    if hasattr(viewer, 'lbl_cursor_x'):
        print('Cursor labels found')
    else:
        print('ERROR: Cursor labels missing')
    
    viewer.show()
    print('Viewer shown')
    
    app.exec_()
    
except Exception as e:
    import traceback
    print(f'ERROR: {e}')
    traceback.print_exc()
