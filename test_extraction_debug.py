#!/usr/bin/env python3
"""
Test the coordinate system extraction from the saved file
"""

import re
import math

def extract_coordinate_system_debug(filename):
    """Debug the coordinate system extraction"""
    print(f"🔍 DEBUGGING COORDINATE SYSTEM EXTRACTION FROM: {filename}")
    print("=" * 60)
    
    with open(filename, 'r') as f:
        content = f.read()
    
    # Step 1: Find AXIS2_PLACEMENT_3D entries
    print("STEP 1: Finding AXIS2_PLACEMENT_3D entries...")
    axis_pattern = r'(#\d+)\s*=\s*AXIS2_PLACEMENT_3D\s*\([^;]+\);'
    axis_matches = re.findall(axis_pattern, content)
    print(f"Found {len(axis_matches)} AXIS2_PLACEMENT_3D entries: {axis_matches}")
    
    # Step 2: Look for the main one (#11)
    print("\nSTEP 2: Checking main AXIS2_PLACEMENT_3D (#11)...")
    main_axis_pattern = r'#11\s*=\s*AXIS2_PLACEMENT_3D\s*\([^,]*,\s*#(\d+)\s*,\s*#(\d+)\s*,\s*#(\d+)\s*\)\s*;'
    main_axis_match = re.search(main_axis_pattern, content)
    
    if main_axis_match:
        origin_id = main_axis_match.group(1)
        z_dir_id = main_axis_match.group(2)
        x_dir_id = main_axis_match.group(3)
        print(f"✅ Found main AXIS2_PLACEMENT_3D: origin=#{origin_id}, z_dir=#{z_dir_id}, x_dir=#{x_dir_id}")
    else:
        print("❌ Could not find main AXIS2_PLACEMENT_3D (#11)")
        return
    
    # Step 3: Extract origin point
    print(f"\nSTEP 3: Extracting origin point (#{origin_id})...")
    origin_pattern = f'#{origin_id}\\s*=\\s*CARTESIAN_POINT\\s*\\([^\\(]*\\(([^\\)]+)\\)'
    origin_match = re.search(origin_pattern, content)

    if origin_match:
        origin_coords = [float(x.strip()) for x in origin_match.group(1).split(',')]
        print(f"✅ Origin: ({origin_coords[0]:.6f}, {origin_coords[1]:.6f}, {origin_coords[2]:.6f})")
    else:
        print(f"❌ Could not extract origin point #{origin_id}")
        origin_coords = [0, 0, 0]

    # Step 4: Extract Z direction
    print(f"\nSTEP 4: Extracting Z direction (#{z_dir_id})...")
    z_dir_pattern = f'#{z_dir_id}\\s*=\\s*DIRECTION\\s*\\([^\\(]*\\(([^\\)]+)\\)'
    z_dir_match = re.search(z_dir_pattern, content)

    if z_dir_match:
        z_direction = [float(x.strip()) for x in z_dir_match.group(1).split(',')]
        print(f"✅ Z Direction: ({z_direction[0]:.6f}, {z_direction[1]:.6f}, {z_direction[2]:.6f})")
    else:
        print(f"❌ Could not extract Z direction #{z_dir_id}")
        z_direction = [0, 0, 1]

    # Step 5: Extract X direction
    print(f"\nSTEP 5: Extracting X direction (#{x_dir_id})...")
    x_dir_pattern = f'#{x_dir_id}\\s*=\\s*DIRECTION\\s*\\([^\\(]*\\(([^\\)]+)\\)'
    x_dir_match = re.search(x_dir_pattern, content)

    if x_dir_match:
        x_direction = [float(x.strip()) for x in x_dir_match.group(1).split(',')]
        print(f"✅ X Direction: ({x_direction[0]:.6f}, {x_direction[1]:.6f}, {x_direction[2]:.6f})")
    else:
        print(f"❌ Could not extract X direction #{x_dir_id}")
        x_direction = [1, 0, 0]
    
    # Step 6: Calculate rotation
    print(f"\nSTEP 6: Calculating rotation from direction vectors...")
    
    # Check if it's standard orientation
    is_standard = (abs(z_direction[2] - 1.0) < 0.001 and abs(x_direction[0] - 1.0) < 0.001)
    
    if is_standard:
        orig_rot = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        print("✅ Standard orientation detected")
    else:
        # Calculate Euler angles from direction vectors
        z_rot = math.degrees(math.atan2(x_direction[1], x_direction[0]))
        x_rot = math.degrees(math.atan2(z_direction[1], z_direction[2]))
        y_rot = math.degrees(math.atan2(-z_direction[0], math.sqrt(z_direction[1]**2 + z_direction[2]**2)))
        
        orig_rot = {'x': x_rot, 'y': y_rot, 'z': z_rot}
        print("✅ Custom orientation detected")
    
    print(f"\n📊 FINAL RESULTS:")
    print(f"   Origin: {origin_coords}")
    print(f"   Z Direction: {z_direction}")
    print(f"   X Direction: {x_direction}")
    print(f"   Calculated Rotation: {orig_rot}")
    
    # Step 7: Show the actual STEP lines for verification
    print(f"\n🔍 ACTUAL STEP FILE LINES:")
    lines = content.split('\n')
    for line in lines:
        if f'#{origin_id}=' in line and 'CARTESIAN_POINT' in line:
            print(f"   Origin: {line.strip()}")
        elif f'#{z_dir_id}=' in line and 'DIRECTION' in line:
            print(f"   Z Dir:  {line.strip()}")
        elif f'#{x_dir_id}=' in line and 'DIRECTION' in line:
            print(f"   X Dir:  {line.strip()}")
    
    return origin_coords, z_direction, x_direction, orig_rot

if __name__ == "__main__":
    # Test both files
    print("🚀 COORDINATE SYSTEM EXTRACTION DEBUG")
    
    # Test original file
    print("\n" + "="*80)
    extract_coordinate_system_debug("test.step")
    
    # Test saved file
    print("\n" + "="*80)
    extract_coordinate_system_debug("automated_test_option1.step")
