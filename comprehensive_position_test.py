#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Comprehensive Position Test - Test ALL position buttons and show results
"""

import sys
import time
import os
import re
from PyQt5.QtWidgets import QApplication
from step_viewer_tdk_modular import <PERSON><PERSON>iewerTD<PERSON>

def comprehensive_position_test():
    """Test ALL position buttons comprehensively"""
    print("🔧 COMPREHENSIVE POSITION BUTTON TEST")
    print("="*60)
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.active_viewer = "top"
    
    # Load actual test file if available
    if os.path.exists("test.step"):
        print("📁 Loading test.step...")
        success = viewer.load_step_file_direct("test.step")
        if success:
            print("✅ test.step loaded successfully")
            time.sleep(2)  # Wait for loading
        else:
            print("❌ Failed to load test.step, using default values")
            viewer.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            viewer.orig_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            viewer.movement_delta_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
    else:
        print("📁 No test.step found, using default values")
        viewer.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        viewer.orig_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        viewer.movement_delta_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
    
    # Ensure text actor exists
    if not hasattr(viewer, 'combined_text_actor_left') or not viewer.combined_text_actor_left:
        import vtk
        viewer.combined_text_actor_left = vtk.vtkTextActor()
        viewer.update_text_overlays()
    
    print(f"\n📊 INITIAL STATE:")
    print(f"   Position: X={viewer.current_pos_left['x']:.3f}, Y={viewer.current_pos_left['y']:.3f}, Z={viewer.current_pos_left['z']:.3f}")
    
    def get_displayed_position():
        """Extract position from text display"""
        try:
            text = viewer.combined_text_actor_left.GetInput()
            pos_match = re.search(r'POS: X=([-\d.]+)mm Y=([-\d.]+)mm Z=([-\d.]+)mm', text)
            if pos_match:
                return {
                    'x': float(pos_match.group(1)),
                    'y': float(pos_match.group(2)),
                    'z': float(pos_match.group(3))
                }
        except:
            pass
        return {'x': 0.0, 'y': 0.0, 'z': 0.0}
    
    def test_button(axis, amount, button_name):
        """Test a specific button and return results"""
        print(f"\n🔧 TESTING {button_name} BUTTON:")
        
        # Get before state
        pos_before = viewer.current_pos_left.copy()
        display_before = get_displayed_position()
        
        print(f"   BEFORE: Data={pos_before[axis]:.3f}, Display={display_before[axis]:.3f}")
        
        # Press button
        viewer.move_shape(axis, amount)
        time.sleep(0.1)  # Allow processing
        
        # Get after state
        pos_after = viewer.current_pos_left.copy()
        display_after = get_displayed_position()
        
        print(f"   AFTER:  Data={pos_after[axis]:.3f}, Display={display_after[axis]:.3f}")
        
        # Calculate changes
        data_change = pos_after[axis] - pos_before[axis]
        display_change = display_after[axis] - display_before[axis]
        
        print(f"   CHANGE: Data={data_change:.3f}, Display={display_change:.3f}")
        print(f"   EXPECTED: {amount:.3f}")
        
        # Check correctness
        data_correct = abs(data_change - amount) < 0.001
        display_correct = abs(display_change - amount) < 0.001
        display_matches_data = abs(display_after[axis] - pos_after[axis]) < 0.001
        
        print(f"   DATA CHANGE: {'✅ CORRECT' if data_correct else '❌ WRONG'}")
        print(f"   DISPLAY CHANGE: {'✅ CORRECT' if display_correct else '❌ WRONG'}")
        print(f"   DISPLAY MATCHES DATA: {'✅ YES' if display_matches_data else '❌ NO'}")
        
        overall_correct = data_correct and display_correct and display_matches_data
        print(f"   OVERALL: {'✅ PASS' if overall_correct else '❌ FAIL'}")
        
        return overall_correct
    
    # Test all buttons
    results = {}
    
    # Reset to known state
    viewer.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
    viewer.movement_delta_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
    viewer.update_text_overlays()
    
    # Test X+ button
    results['X+'] = test_button('x', 1.0, 'X+')
    
    # Reset and test X- button
    viewer.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
    viewer.movement_delta_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
    viewer.update_text_overlays()
    results['X-'] = test_button('x', -1.0, 'X-')
    
    # Reset and test Y+ button
    viewer.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
    viewer.movement_delta_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
    viewer.update_text_overlays()
    results['Y+'] = test_button('y', 1.0, 'Y+')
    
    # Reset and test Y- button
    viewer.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
    viewer.movement_delta_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
    viewer.update_text_overlays()
    results['Y-'] = test_button('y', -1.0, 'Y-')
    
    # Reset and test Z+ button
    viewer.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
    viewer.movement_delta_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
    viewer.update_text_overlays()
    results['Z+'] = test_button('z', 1.0, 'Z+')
    
    # Reset and test Z- button
    viewer.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
    viewer.movement_delta_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
    viewer.update_text_overlays()
    results['Z-'] = test_button('z', -1.0, 'Z-')
    
    # Final summary
    print(f"\n" + "="*60)
    print("📊 FINAL TEST RESULTS:")
    print("="*60)
    
    all_passed = True
    for button, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"   {button} button: {status}")
        if not passed:
            all_passed = False
    
    print(f"\n🎯 OVERALL RESULT: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    
    if all_passed:
        print("\n🎉 SUCCESS! All position buttons are working correctly:")
        print("   • X+ increases X value in both data and display")
        print("   • X- decreases X value in both data and display")
        print("   • Y+ increases Y value in both data and display")
        print("   • Y- decreases Y value in both data and display")
        print("   • Z+ increases Z value in both data and display")
        print("   • Z- decreases Z value in both data and display")
        print("   • Display values match data values exactly")
    else:
        print("\n❌ FAILURE! Some position buttons are not working correctly.")
        print("   Check the individual test results above for details.")
    
    print("="*60)
    
    app.quit()
    return all_passed

if __name__ == "__main__":
    comprehensive_position_test()
