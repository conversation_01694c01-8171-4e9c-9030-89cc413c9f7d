#!/usr/bin/env python3
"""
Deep Pipeline Analysis: STEP File → Display → Overlay
Traces the entire pipeline to find all issues
"""

import sys
import os
import vtk
from step_loader import <PERSON><PERSON><PERSON>oader

def analyze_step_file_raw(filename):
    """Analyze the raw STEP file data"""
    print(f"\n{'='*80}")
    print(f"📁 RAW STEP FILE ANALYSIS: {filename}")
    print(f"{'='*80}")
    
    if not os.path.exists(filename):
        print(f"❌ File not found: {filename}")
        return None
    
    # Load with STEPLoader
    loader = STEPLoader()
    result = loader.load_step_file(filename)
    
    if len(result) == 3:
        polydata, success, message = result
    else:
        success, message = result
        polydata = loader.current_polydata
    
    if not success or not polydata:
        print(f"❌ Failed to load: {message}")
        return None
    
    print(f"✅ Loaded: {message}")
    
    # Analyze raw polydata
    print(f"\n📊 RAW POLYDATA ANALYSIS:")
    print(f"   Points: {polydata.GetNumberOfPoints()}")
    print(f"   Cells: {polydata.GetNumberOfCells()}")
    print(f"   Bounds: {polydata.GetBounds()}")
    
    # Analyze color data in detail
    cell_data = polydata.GetCellData()
    color_array = cell_data.GetScalars("Colors")
    
    if color_array:
        print(f"\n🎨 COLOR DATA ANALYSIS:")
        print(f"   Color tuples: {color_array.GetNumberOfTuples()}")
        print(f"   Components per tuple: {color_array.GetNumberOfComponents()}")
        
        # Count unique colors
        color_counts = {}
        for i in range(min(color_array.GetNumberOfTuples(), 10)):  # Sample first 10
            if color_array.GetNumberOfComponents() >= 3:
                r = int(color_array.GetComponent(i, 0))
                g = int(color_array.GetComponent(i, 1))
                b = int(color_array.GetComponent(i, 2))
                color = (r, g, b)
            else:
                color = (int(color_array.GetValue(i)),)
            
            if color not in color_counts:
                color_counts[color] = 0
            color_counts[color] += 1
        
        print(f"   Sample colors (first 10 cells):")
        for color, count in color_counts.items():
            print(f"      RGB{color}: {count} cells")
    else:
        print(f"\n❌ NO COLOR DATA FOUND")
    
    return polydata

def analyze_vtk_actor_creation(polydata, filename):
    """Analyze VTK actor creation process"""
    print(f"\n{'='*80}")
    print(f"🎭 VTK ACTOR CREATION ANALYSIS: {filename}")
    print(f"{'='*80}")
    
    if not polydata:
        print("❌ No polydata to analyze")
        return []
    
    # Simulate the VTK renderer process
    print(f"🔍 Simulating VTK renderer display_polydata()...")
    
    # Check for colors
    cell_colors = polydata.GetCellData().GetScalars("Colors")
    
    if cell_colors and cell_colors.GetNumberOfTuples() > 0:
        print(f"✅ Found colors - will create multi-color actors")
        
        # Group cells by color
        color_groups = {}
        for cell_id in range(cell_colors.GetNumberOfTuples()):
            r = int(cell_colors.GetComponent(cell_id, 0))
            g = int(cell_colors.GetComponent(cell_id, 1))
            b = int(cell_colors.GetComponent(cell_id, 2))
            color_key = (r, g, b)
            
            if color_key not in color_groups:
                color_groups[color_key] = []
            color_groups[color_key].append(cell_id)
        
        print(f"🎨 Color groups: {len(color_groups)}")
        for color, cells in color_groups.items():
            print(f"   RGB{color}: {len(cells)} cells")
        
        # Create actors for each color
        actors = []
        for i, (color_rgb, cell_ids) in enumerate(color_groups.items()):
            print(f"\n🎭 Creating actor {i} for RGB{color_rgb} ({len(cell_ids)} cells)...")
            
            # Create polydata like the real code
            color_polydata = vtk.vtkPolyData()
            color_polydata.SetPoints(polydata.GetPoints())
            
            # Create cells
            color_cells = vtk.vtkCellArray()
            for cell_id in cell_ids:
                cell = polydata.GetCell(cell_id)
                color_cells.InsertNextCell(cell)
            
            color_polydata.SetPolys(color_cells)
            
            # Create actor
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputData(color_polydata)
            mapper.SetScalarVisibility(False)
            
            actor = vtk.vtkActor()
            actor.SetMapper(mapper)
            
            # Set color
            r, g, b = color_rgb
            actor.GetProperty().SetColor(r/255.0, g/255.0, b/255.0)
            
            # Get bounds
            bounds = actor.GetBounds()
            print(f"   📏 Actor bounds: {bounds}")
            
            # Analyze geometry
            x_range = bounds[1] - bounds[0]
            y_range = bounds[3] - bounds[2]
            z_range = bounds[5] - bounds[4]
            print(f"   📐 Dimensions: X={x_range:.3f}, Y={y_range:.3f}, Z={z_range:.3f}")
            
            # Check for problematic bounds
            if x_range > 10 or y_range > 10:  # Suspiciously large
                print(f"   ⚠️  WARNING: Actor has suspiciously large dimensions!")
                print(f"   🔍 This might be the giant black box issue!")
                
                # Analyze actual cell bounds vs actor bounds
                points = polydata.GetPoints()
                actual_bounds = [float('inf'), float('-inf'), float('inf'), float('-inf'), float('inf'), float('-inf')]
                
                for cell_id in cell_ids[:5]:  # Check first 5 cells
                    cell = polydata.GetCell(cell_id)
                    for j in range(cell.GetNumberOfPoints()):
                        point_id = cell.GetPointId(j)
                        point = points.GetPoint(point_id)
                        
                        actual_bounds[0] = min(actual_bounds[0], point[0])
                        actual_bounds[1] = max(actual_bounds[1], point[0])
                        actual_bounds[2] = min(actual_bounds[2], point[1])
                        actual_bounds[3] = max(actual_bounds[3], point[1])
                        actual_bounds[4] = min(actual_bounds[4], point[2])
                        actual_bounds[5] = max(actual_bounds[5], point[2])
                
                print(f"   📏 Actual cell bounds: {actual_bounds}")
                print(f"   📏 VTK actor bounds: {bounds}")
                print(f"   ❓ Bounds mismatch indicates VTK is using ALL points, not just cell points!")
            
            actors.append({
                'actor': actor,
                'color': color_rgb,
                'bounds': bounds,
                'cell_count': len(cell_ids)
            })
        
        return actors
    
    else:
        print(f"❌ No colors found - would create single gray actor")
        return []

def analyze_overlay_process(actors_top, actors_bottom):
    """Analyze the overlay creation process"""
    print(f"\n{'='*80}")
    print(f"🔄 OVERLAY PROCESS ANALYSIS")
    print(f"{'='*80}")
    
    print(f"📊 Input actors:")
    print(f"   TOP actors: {len(actors_top)}")
    print(f"   BOTTOM actors: {len(actors_bottom)}")
    
    print(f"\n🔍 TOP actors analysis:")
    for i, actor_info in enumerate(actors_top):
        bounds = actor_info['bounds']
        color = actor_info['color']
        cell_count = actor_info['cell_count']
        print(f"   Actor {i}: RGB{color}, {cell_count} cells, bounds={bounds}")
        
        # Check if this would create overlay issues
        x_range = bounds[1] - bounds[0]
        y_range = bounds[3] - bounds[2]
        if x_range > 10 or y_range > 10:
            print(f"   ⚠️  This actor will create the giant black box in overlay!")
    
    print(f"\n🔍 BOTTOM actors analysis:")
    for i, actor_info in enumerate(actors_bottom):
        bounds = actor_info['bounds']
        color = actor_info['color']
        cell_count = actor_info['cell_count']
        print(f"   Actor {i}: RGB{color}, {cell_count} cells, bounds={bounds}")

def main():
    """Run comprehensive pipeline analysis"""
    print("🔬 DEEP PIPELINE ANALYSIS")
    print("=" * 80)
    
    # Analyze both STEP files
    print("\n" + "🔴" * 40 + " 16-PIN SOIC " + "🔴" * 40)
    polydata_16pin = analyze_step_file_raw("SOIC16P127_1270X940X610L89X51.STEP")
    actors_16pin = analyze_vtk_actor_creation(polydata_16pin, "16-PIN SOIC") if polydata_16pin else []
    
    print("\n" + "🔵" * 40 + " 8-PIN SOIC " + "🔵" * 40)
    polydata_8pin = analyze_step_file_raw("test.step")
    actors_8pin = analyze_vtk_actor_creation(polydata_8pin, "8-PIN SOIC") if polydata_8pin else []
    
    # Analyze overlay process
    analyze_overlay_process(actors_16pin, actors_8pin)
    
    print(f"\n{'='*80}")
    print(f"🏁 ANALYSIS COMPLETE")
    print(f"{'='*80}")
    
    # Summary of issues found
    print(f"\n📋 ISSUES IDENTIFIED:")
    
    issues_found = 0
    
    # Check for missing pins (multiple actors expected for 16-pin)
    if len(actors_16pin) < 2:
        print(f"❌ Issue 1: 16-pin SOIC has only {len(actors_16pin)} actor(s) - pins might be missing!")
        issues_found += 1
    
    # Check for giant box issue
    for i, actor_info in enumerate(actors_16pin):
        bounds = actor_info['bounds']
        x_range = bounds[1] - bounds[0]
        y_range = bounds[3] - bounds[2]
        if x_range > 10 or y_range > 10:
            print(f"❌ Issue 2: Actor {i} has giant dimensions ({x_range:.1f} x {y_range:.1f}) - this is the black box!")
            issues_found += 1
    
    if issues_found == 0:
        print(f"✅ No obvious issues found in pipeline analysis")
    else:
        print(f"⚠️  Found {issues_found} issues that need fixing!")

if __name__ == "__main__":
    main()
