#!/usr/bin/env python3
"""
Independent test script for mouse rotation save issue
This will simulate the exact problem and test the fix
"""

import sys
import os
import tempfile
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

def test_mouse_rotation_save():
    """Test mouse rotation save functionality independently"""
    print("🧪 INDEPENDENT MOUSE ROTATION SAVE TEST")
    print("=" * 50)
    
    try:
        # Create QApplication
        app = QApplication([])
        
        # Import the main program
        import step_viewer_tdk_modular
        
        # Create the viewer
        viewer = step_viewer_tdk_modular.StepViewerTDK()
        viewer.show()
        
        print("✅ Viewer created successfully")
        
        # Test 1: Check if we have a test STEP file
        test_files = ["test.step", "SOIC16P127_1270X940X610L89X51.STEP"]
        test_file = None
        
        for filename in test_files:
            if os.path.exists(filename):
                test_file = filename
                break
                
        if not test_file:
            print("❌ No test STEP file found. Creating a simple test...")
            # Create a minimal test STEP file
            test_file = "test_rotation.step"
            with open(test_file, 'w') as f:
                f.write("""ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Test file for rotation'),'2;1');
FILE_NAME('test_rotation.step','2025-01-01T00:00:00',('Test'),('Test'),'Test','Test','');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN'));
ENDSEC;
DATA;
#1 = CARTESIAN_POINT('Origin',(0.,0.,0.));
#2 = DIRECTION('Z',(0.,0.,1.));
#3 = DIRECTION('X',(1.,0.,0.));
#4 = AXIS2_PLACEMENT_3D('',#1,#2,#3);
ENDSEC;
END-ISO-10303-21;""")
        
        print(f"📁 Using test file: {test_file}")
        
        # Test 2: Load the STEP file
        def load_and_test():
            print("\n🔧 Step 2: Loading STEP file...")
            
            # Set active viewer to bottom (where the issue was reported)
            viewer.active_viewer = "bottom"
            viewer.update_viewer_highlights()
            print(f"✅ Set active viewer to: {viewer.active_viewer}")
            
            # Load the file
            if hasattr(viewer.step_loader_right, 'load_step_file'):
                success, message = viewer.step_loader_right.load_step_file(test_file)
                if success:
                    print(f"✅ STEP file loaded: {message}")
                    
                    # Add to VTK renderer
                    if viewer.vtk_renderer_right and viewer.step_loader_right.current_polydata:
                        viewer.vtk_renderer_right.display_polydata(viewer.step_loader_right.current_polydata)
                        print("✅ Added to VTK renderer")
                        
                        # Test 3: Simulate mouse rotation
                        simulate_mouse_rotation()
                    else:
                        print("❌ VTK renderer or polydata not available")
                else:
                    print(f"❌ Failed to load STEP file: {message}")
            else:
                print("❌ step_loader_right.load_step_file method not found")
        
        def simulate_mouse_rotation():
            print("\n🔧 Step 3: Simulating mouse rotation...")
            
            # Get the camera and simulate mouse rotation
            if viewer.vtk_renderer_right and viewer.vtk_renderer_right.renderer:
                camera = viewer.vtk_renderer_right.renderer.GetActiveCamera()
                if camera:
                    # Simulate rotating the camera (like mouse drag would do)
                    print("🖱️  Simulating mouse rotation: X = 45 degrees")
                    
                    # Method 1: Try setting camera orientation directly
                    camera.SetPosition(0, -100, 50)  # Move camera to create rotation effect
                    camera.SetFocalPoint(0, 0, 0)
                    camera.SetViewUp(0, 0, 1)
                    
                    # Get the resulting orientation
                    orientation = camera.GetOrientation()
                    print(f"📐 Camera orientation after rotation: X={orientation[0]:.1f}°, Y={orientation[1]:.1f}°, Z={orientation[2]:.1f}°")
                    
                    # Update the viewer's rotation tracking
                    viewer.current_rot_right = {
                        'x': orientation[0],
                        'y': orientation[1],
                        'z': orientation[2]
                    }
                    
                    # Render the change
                    viewer.vtk_renderer_right.render_window.Render()
                    
                    # Test 4: Test the rotation extraction
                    test_rotation_extraction()
                else:
                    print("❌ No camera found")
            else:
                print("❌ No VTK renderer found")
        
        def test_rotation_extraction():
            print("\n🔧 Step 4: Testing rotation extraction...")

            # Test BOTH viewers
            print("🔧 Testing TOP viewer rotation extraction:")
            top_extracted_rot = viewer._extract_rotation_from_vtk_actor("top")
            print(f"🔧 TOP viewer extracted rotation: {top_extracted_rot}")

            print("\n🔧 Testing BOTTOM viewer rotation extraction:")
            bottom_extracted_rot = viewer._extract_rotation_from_vtk_actor("bottom")
            print(f"🔧 BOTTOM viewer extracted rotation: {bottom_extracted_rot}")

            # Compare the values
            print(f"\n📊 COMPARISON:")
            print(f"   TOP viewer:    X={top_extracted_rot['x']:.1f}°, Y={top_extracted_rot['y']:.1f}°, Z={top_extracted_rot['z']:.1f}°")
            print(f"   BOTTOM viewer: X={bottom_extracted_rot['x']:.1f}°, Y={bottom_extracted_rot['y']:.1f}°, Z={bottom_extracted_rot['z']:.1f}°")

            # Check if they match
            x_diff = abs(top_extracted_rot['x'] - bottom_extracted_rot['x'])
            y_diff = abs(top_extracted_rot['y'] - bottom_extracted_rot['y'])
            z_diff = abs(top_extracted_rot['z'] - bottom_extracted_rot['z'])

            if x_diff < 1.0 and y_diff < 1.0 and z_diff < 1.0:
                print("✅ TOP and BOTTOM viewer rotations MATCH (within 1°)")
            else:
                print("❌ TOP and BOTTOM viewer rotations DO NOT MATCH")
                print(f"   Differences: X={x_diff:.1f}°, Y={y_diff:.1f}°, Z={z_diff:.1f}°")

            # Test 5: Test the save process with bottom viewer
            test_save_process(bottom_extracted_rot)
        
        def test_save_process(expected_rotation):
            print("\n🔧 Step 5: Testing save process...")
            
            # Create a temporary file for testing
            temp_file = tempfile.mktemp(suffix='.step')
            print(f"📁 Test save file: {temp_file}")
            
            # Test the save method directly
            try:
                # Get current transformation values (like the save method does)
                loader = viewer.step_loader_right
                current_rot = viewer._extract_rotation_from_vtk_actor("bottom")
                current_pos = getattr(viewer, 'current_pos_right', {'x': 0, 'y': 0, 'z': 0})
                orig_rot = getattr(viewer, 'orig_rot_right', {'x': 0, 'y': 0, 'z': 0})
                orig_pos = getattr(viewer, 'orig_pos_right', {'x': 0, 'y': 0, 'z': 0})
                
                print(f"📊 Save parameters:")
                print(f"   Current rotation: {current_rot}")
                print(f"   Expected rotation: {expected_rotation}")
                print(f"   Current position: {current_pos}")
                print(f"   Original rotation: {orig_rot}")
                print(f"   Original position: {orig_pos}")
                
                # Test the transformation save system
                success = viewer._save_step_with_transformations(temp_file, loader, current_pos, current_rot, orig_pos, orig_rot)
                
                if success and os.path.exists(temp_file):
                    file_size = os.path.getsize(temp_file)
                    print(f"✅ Save test successful! File size: {file_size} bytes")
                    
                    # Verify the saved file contains the rotation
                    verify_saved_rotation(temp_file, expected_rotation)
                else:
                    print("❌ Save test failed")
                    
            except Exception as e:
                print(f"❌ Save test error: {e}")
                import traceback
                traceback.print_exc()
        
        def verify_saved_rotation(filename, expected_rotation):
            print(f"\n🔧 Step 6: Verifying saved rotation in {filename}...")
            
            try:
                with open(filename, 'r') as f:
                    content = f.read()
                    
                # Look for rotation values in the STEP file
                if "AXIS2_PLACEMENT_3D" in content:
                    print("✅ Found AXIS2_PLACEMENT_3D in saved file")
                    
                    # Extract coordinate system information
                    lines = content.split('\n')
                    for line in lines:
                        if "AXIS2_PLACEMENT_3D" in line:
                            print(f"📐 Coordinate system line: {line}")
                            
                # Check if the rotation was actually applied
                print(f"📊 Expected rotation: {expected_rotation}")
                print(f"📊 File contains rotation data: {'DIRECTION' in content}")
                
                # Clean up
                os.remove(filename)
                print("🧹 Cleaned up test file")
                
            except Exception as e:
                print(f"❌ Verification error: {e}")
        
        # Start the test sequence
        QTimer.singleShot(1000, load_and_test)
        
        # Run for a short time then exit
        QTimer.singleShot(10000, app.quit)
        
        print("🚀 Starting test sequence...")
        app.exec_()
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_mouse_rotation_save()
