#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test with loaded file - Test the actual problem with a loaded file
"""

import sys
import time
import os
import re
from PyQt5.QtWidgets import QApplication
from step_viewer_tdk_modular import StepViewerTDK

def test_with_loaded_file():
    """Test position buttons with an actual loaded file"""
    print("🔧 TESTING POSITION BUTTONS WITH LOADED FILE")
    print("="*60)
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.active_viewer = "top"
    
    # Load actual test file
    if not os.path.exists("test.step"):
        print("❌ test.step not found")
        return
        
    print("📁 Loading test.step...")
    success = viewer.load_step_file_direct("test.step")
    if not success:
        print("❌ Failed to load test.step")
        return
        
    print("✅ test.step loaded successfully")
    time.sleep(2)  # Wait for loading
    
    def get_displayed_position():
        """Extract position from text display"""
        try:
            text = viewer.combined_text_actor_left.GetInput()
            pos_match = re.search(r'POS: X=([-\d.]+)mm Y=([-\d.]+)mm Z=([-\d.]+)mm', text)
            if pos_match:
                return {
                    'x': float(pos_match.group(1)),
                    'y': float(pos_match.group(2)),
                    'z': float(pos_match.group(3))
                }
        except:
            pass
        return {'x': 0.0, 'y': 0.0, 'z': 0.0}
    
    # Show initial state after file load
    print(f"\n📊 INITIAL STATE AFTER FILE LOAD:")
    if hasattr(viewer, 'current_pos_left'):
        print(f"   current_pos_left: {viewer.current_pos_left}")
    if hasattr(viewer, 'orig_pos_left'):
        print(f"   orig_pos_left: {viewer.orig_pos_left}")
    
    display_pos = get_displayed_position()
    print(f"   Text display shows: {display_pos}")
    
    # Test X+ button with loaded file
    print(f"\n🔧 TESTING X+ BUTTON WITH LOADED FILE:")
    print(f"   BEFORE - Data: {viewer.current_pos_left['x']:.3f}, Display: {display_pos['x']:.3f}")
    
    # Press X+ button
    viewer.move_shape('x', 1.0)
    time.sleep(0.2)
    
    # Check after
    display_pos_after = get_displayed_position()
    print(f"   AFTER  - Data: {viewer.current_pos_left['x']:.3f}, Display: {display_pos_after['x']:.3f}")
    
    data_change = viewer.current_pos_left['x'] - (-4.19)  # Original position was -4.19
    display_change = display_pos_after['x'] - display_pos['x']
    
    print(f"   Data change: {data_change:.3f}")
    print(f"   Display change: {display_change:.3f}")
    print(f"   Expected: +1.000")
    
    if abs(display_change - 1.0) < 0.001:
        print("   ✅ Display change is CORRECT")
    else:
        print("   ❌ Display change is WRONG")
        print(f"      The problem you described: Display shows {display_change:.3f} instead of +1.000")
    
    app.quit()

if __name__ == "__main__":
    test_with_loaded_file()
