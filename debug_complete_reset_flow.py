#!/usr/bin/env python3
"""
Debug Complete Reset Flow - Follow the path from button to display update
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular import Step<PERSON>iewerTDK

def debug_complete_reset_flow():
    """Debug the complete reset flow from button to display"""
    
    print("🔧 DEBUG COMPLETE RESET FLOW")
    print("=" * 60)
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    # Process events to ensure GUI is ready
    app.processEvents()
    time.sleep(1)
    
    # Find a STEP file to test with
    test_files = [
        "AMPHENOL_U77-A1118-200T.STEP",
        "test.step",
        "sample.stp"
    ]
    
    test_file = None
    for filename in test_files:
        if os.path.exists(filename):
            test_file = filename
            break
    
    if not test_file:
        print("❌ No STEP file found for testing")
        return False
    
    print(f"📁 Using test file: {test_file}")
    
    # Step 1: Load model
    print(f"\n📋 STEP 1: LOADING MODEL...")
    success = viewer.load_step_file_direct(test_file)
    app.processEvents()
    time.sleep(2)
    
    if not success:
        print("❌ Failed to load STEP file")
        return False
    
    renderer = viewer.vtk_renderer_left
    
    # Get initial positions
    def get_actor_positions():
        positions = {}
        all_actors = []
        if hasattr(renderer, 'renderer') and renderer.renderer:
            actor_collection = renderer.renderer.GetActors()
            actor_collection.InitTraversal()
            
            actor = actor_collection.GetNextActor()
            while actor:
                all_actors.append(actor)
                actor = actor_collection.GetNextActor()
        
        for i, actor in enumerate(all_actors):
            pos = actor.GetPosition()
            orient = actor.GetOrientation()
            bounds = actor.GetBounds()
            visible = actor.GetVisibility()
            
            positions[f"Actor_{i}"] = {
                'position': pos,
                'orientation': orient,
                'bounds': bounds,
                'visible': visible,
                'actor_ref': actor
            }
            
            # Identify actor type
            actor_type = "UNKNOWN"
            if hasattr(renderer, 'step_actors') and renderer.step_actors:
                for j, multi_actor in enumerate(renderer.step_actors):
                    if multi_actor == actor:
                        actor_type = f"MULTI-ACTOR_{j}"
                        
            if hasattr(renderer, 'bbox_actor') and renderer.bbox_actor == actor:
                actor_type = "BOUNDING_BOX"
                
            if hasattr(renderer, 'step_actor') and renderer.step_actor == actor:
                actor_type = "SINGLE_ACTOR"
            
            positions[f"Actor_{i}"]['type'] = actor_type
        
        return positions
    
    print(f"\n🔍 INITIAL POSITIONS:")
    initial_positions = get_actor_positions()
    for actor_id, data in initial_positions.items():
        print(f"{actor_id} ({data['type']}): Pos={data['position']}, Orient={data['orientation']}, Visible={data['visible']}")
    
    # Step 2: Apply transformation
    print(f"\n📋 STEP 2: APPLYING TRANSFORMATION...")
    
    viewer.active_viewer = "top"
    
    print(f"🔧 Moving +100mm X")
    viewer.move_shape("x", 100)
    app.processEvents()
    time.sleep(1)
    
    print(f"🔧 Rotating +90° Z")
    viewer.rotate_shape("z", 90)
    app.processEvents()
    time.sleep(1)
    
    print(f"\n🔍 POSITIONS AFTER TRANSFORMATION:")
    transformed_positions = get_actor_positions()
    for actor_id, data in transformed_positions.items():
        print(f"{actor_id} ({data['type']}): Pos={data['position']}, Orient={data['orientation']}, Visible={data['visible']}")
    
    # Step 3: Trace the complete reset flow
    print(f"\n📋 STEP 3: TRACING COMPLETE RESET FLOW...")
    
    print(f"\n🔧 BEFORE RESET - Current positions:")
    before_reset_positions = get_actor_positions()
    for actor_id, data in before_reset_positions.items():
        print(f"{actor_id} ({data['type']}): Pos={data['position']}, Orient={data['orientation']}")
    
    # Patch the reset method to add detailed tracing
    original_reset = viewer.reset_to_original
    
    def traced_reset():
        print(f"\n🔧 TRACED RESET: Starting reset_to_original()")
        
        # Call original reset
        original_reset()
        
        print(f"🔧 TRACED RESET: reset_to_original() completed")
        
        # Check positions immediately after reset
        print(f"\n🔧 TRACED RESET: Positions immediately after reset:")
        after_reset_positions = get_actor_positions()
        for actor_id, data in after_reset_positions.items():
            print(f"  {actor_id} ({data['type']}): Pos={data['position']}, Orient={data['orientation']}")
    
    # Replace with traced version
    viewer.reset_to_original = traced_reset
    
    # Call the traced reset
    print(f"\n🔧 CALLING TRACED RESET...")
    viewer.reset_to_original()
    app.processEvents()
    time.sleep(2)
    
    # Step 4: Check final positions after all processing
    print(f"\n📋 STEP 4: FINAL POSITION CHECK...")
    
    print(f"\n🔍 FINAL POSITIONS AFTER ALL PROCESSING:")
    final_positions = get_actor_positions()
    for actor_id, data in final_positions.items():
        print(f"{actor_id} ({data['type']}): Pos={data['position']}, Orient={data['orientation']}, Visible={data['visible']}")
    
    # Step 5: Analysis
    print(f"\n📋 STEP 5: ANALYSIS")
    print("=" * 60)
    
    print(f"\n🎯 POSITION COMPARISON:")
    
    for actor_id in initial_positions.keys():
        initial = initial_positions[actor_id]
        final = final_positions[actor_id]
        
        print(f"\n{actor_id} ({initial['type']}):")
        print(f"  Initial:  Pos={initial['position']}, Orient={initial['orientation']}")
        print(f"  Final:    Pos={final['position']}, Orient={final['orientation']}")
        
        # Check if reset worked
        pos_diff = sum(abs(a - b) for a, b in zip(initial['position'], final['position']))
        orient_diff = sum(abs(a - b) for a, b in zip(initial['orientation'], final['orientation']))
        
        if pos_diff < 0.01 and orient_diff < 0.01:
            print(f"  ✅ RESET WORKED - Position and orientation match initial")
        else:
            print(f"  ❌ RESET FAILED - Position diff: {pos_diff:.3f}, Orient diff: {orient_diff:.3f}")
    
    print(f"\n🔍 DIAGNOSIS:")
    print(f"1. Which actors changed during transformation?")
    print(f"2. Which actors were reset back to original?")
    print(f"3. Are there any actors that didn't reset properly?")
    
    # Step 6: Manual verification
    print(f"\n📋 STEP 6: MANUAL VERIFICATION...")
    
    # Try manual reset of each actor
    print(f"\n🔧 MANUAL RESET TEST:")
    
    # Re-apply transformation first
    print(f"Re-applying transformation for manual test...")
    viewer.move_shape("x", 100)
    viewer.rotate_shape("z", 90)
    app.processEvents()
    time.sleep(1)
    
    print(f"\n🔧 Manual reset of each actor:")
    manual_positions = get_actor_positions()
    
    for actor_id, data in manual_positions.items():
        if data['visible']:
            print(f"\nManually resetting {actor_id} ({data['type']})...")
            actor = data['actor_ref']
            
            # Get current position
            current_pos = actor.GetPosition()
            current_orient = actor.GetOrientation()
            print(f"  Before manual reset: Pos={current_pos}, Orient={current_orient}")
            
            # Manual reset
            actor.SetPosition(0, 0, 0)
            actor.SetOrientation(0, 0, 0)
            actor.SetUserTransform(None)
            actor.Modified()
            
            # Check after manual reset
            new_pos = actor.GetPosition()
            new_orient = actor.GetOrientation()
            print(f"  After manual reset:  Pos={new_pos}, Orient={new_orient}")
    
    # Force render
    renderer.render_window.Render()
    app.processEvents()
    time.sleep(1)
    
    print(f"\n🔍 FINAL MANUAL POSITIONS:")
    manual_final_positions = get_actor_positions()
    for actor_id, data in manual_final_positions.items():
        if data['visible']:
            print(f"{actor_id} ({data['type']}): Pos={data['position']}, Orient={data['orientation']}")
    
    print(f"\n🎯 CONCLUSION:")
    print(f"If manual reset worked but GUI reset didn't, the GUI reset logic has a bug.")
    print(f"If neither worked, there's a deeper VTK rendering issue.")
    
    # Keep window open
    print(f"\n👁️ Window will stay open for visual inspection...")
    QTimer.singleShot(20000, app.quit)
    app.exec_()
    
    return True

if __name__ == "__main__":
    debug_complete_reset_flow()
