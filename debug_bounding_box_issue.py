#!/usr/bin/env python3
"""
Debug Bounding Box Issue - Find why bounding box is wrong and reset fails
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular import Step<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def debug_bounding_box_issue():
    """Debug the bounding box and reset issues"""
    
    print("🔧 DEBUG BOUNDING BOX AND RESET ISSUES")
    print("=" * 60)
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    # Process events to ensure GUI is ready
    app.processEvents()
    time.sleep(1)
    
    # Find a STEP file to test with
    test_files = [
        "AMPHENOL_U77-A1118-200T.STEP",
        "test.step",
        "sample.stp"
    ]
    
    test_file = None
    for filename in test_files:
        if os.path.exists(filename):
            test_file = filename
            break
    
    if not test_file:
        print("❌ No STEP file found for testing")
        return False
    
    print(f"📁 Using test file: {test_file}")
    
    # Step 1: Load STEP file and examine initial state
    print(f"\n📋 STEP 1: LOADING AND EXAMINING INITIAL STATE...")
    success = viewer.load_step_file_direct(test_file)
    app.processEvents()
    time.sleep(2)
    
    if not success:
        print("❌ Failed to load STEP file")
        return False
    
    renderer = viewer.vtk_renderer_left
    
    # Examine all actors and their bounds
    print(f"\n🔍 EXAMINING ALL ACTORS AFTER LOAD:")
    
    all_actors = []
    if hasattr(renderer, 'renderer') and renderer.renderer:
        actor_collection = renderer.renderer.GetActors()
        actor_collection.InitTraversal()
        
        actor = actor_collection.GetNextActor()
        while actor:
            all_actors.append(actor)
            actor = actor_collection.GetNextActor()
    
    for i, actor in enumerate(all_actors):
        bounds = actor.GetBounds()
        pos = actor.GetPosition()
        orient = actor.GetOrientation()
        visible = actor.GetVisibility()
        
        print(f"  Actor {i}: Visible={visible}")
        print(f"    Position: {pos}")
        print(f"    Orientation: {orient}")
        print(f"    Bounds: {bounds}")
        
        # Identify actor type
        if hasattr(renderer, 'step_actors') and renderer.step_actors:
            for j, multi_actor in enumerate(renderer.step_actors):
                if multi_actor == actor:
                    print(f"    *** MULTI-ACTOR {j} ***")
                    
        if hasattr(renderer, 'step_actor') and renderer.step_actor == actor:
            print(f"    *** SINGLE-ACTOR ***")
            
        if hasattr(renderer, 'bbox_actor') and renderer.bbox_actor == actor:
            print(f"    *** BOUNDING BOX ACTOR ***")
    
    print(f"\n👁️ INITIAL STATE - Is the bounding box correct? (5 seconds)")
    time.sleep(5)
    
    # Step 2: Test bounding box recreation manually
    print(f"\n📋 STEP 2: TESTING BOUNDING BOX RECREATION...")
    
    # Remove current bounding box
    if hasattr(renderer, 'bbox_actor') and renderer.bbox_actor:
        print(f"🔧 Removing current bounding box...")
        renderer.renderer.RemoveActor(renderer.bbox_actor)
        renderer.bbox_actor = None
        renderer.render_window.Render()
        app.processEvents()
        time.sleep(2)
        
        print(f"👁️ Bounding box removed - Can you see the difference? (3 seconds)")
        time.sleep(3)
        
        # Recreate bounding box
        print(f"🔧 Recreating bounding box...")
        renderer.toggle_bounding_box(True)
        app.processEvents()
        time.sleep(2)
        
        print(f"👁️ Bounding box recreated - Is it correct now? (3 seconds)")
        time.sleep(3)
    
    # Step 3: Apply transformation and check bounds
    print(f"\n📋 STEP 3: APPLYING TRANSFORMATION AND CHECKING BOUNDS...")
    
    viewer.active_viewer = "top"
    
    print(f"🔧 Applying transformation: +50mm X, +45° Z")
    viewer.move_shape("x", 50)
    viewer.rotate_shape("z", 45)
    app.processEvents()
    time.sleep(2)
    
    # Check bounds after transformation
    print(f"\n🔍 BOUNDS AFTER TRANSFORMATION:")
    for i, actor in enumerate(all_actors):
        bounds = actor.GetBounds()
        pos = actor.GetPosition()
        orient = actor.GetOrientation()
        
        print(f"  Actor {i}:")
        print(f"    Position: {pos}")
        print(f"    Orientation: {orient}")
        print(f"    Bounds: {bounds}")
    
    print(f"\n👁️ AFTER TRANSFORMATION - Note the position (3 seconds)")
    time.sleep(3)
    
    # Step 4: Test reset and examine what happens
    print(f"\n📋 STEP 4: TESTING RESET AND EXAMINING RESULTS...")
    
    print(f"🔧 Calling reset_to_original()...")
    try:
        viewer.reset_to_original()
        app.processEvents()
        time.sleep(2)
        print(f"✅ Reset completed without errors")
    except Exception as e:
        print(f"❌ Reset failed with error: {e}")
        import traceback
        traceback.print_exc()
    
    # Check bounds after reset
    print(f"\n🔍 BOUNDS AFTER RESET:")
    for i, actor in enumerate(all_actors):
        bounds = actor.GetBounds()
        pos = actor.GetPosition()
        orient = actor.GetOrientation()
        
        print(f"  Actor {i}:")
        print(f"    Position: {pos}")
        print(f"    Orientation: {orient}")
        print(f"    Bounds: {bounds}")
    
    print(f"\n👁️ AFTER RESET - Did it move back visually? (5 seconds)")
    time.sleep(5)
    
    # Step 5: Check original transforms storage
    print(f"\n📋 STEP 5: CHECKING ORIGINAL TRANSFORMS STORAGE...")
    
    if hasattr(viewer, 'original_actor_transforms_left'):
        print(f"Original transforms stored: {len(viewer.original_actor_transforms_left)}")
        for i, orig_state in enumerate(viewer.original_actor_transforms_left):
            print(f"  Original {i}:")
            print(f"    Position: {orig_state['position']}")
            print(f"    Orientation: {orig_state['orientation']}")
            print(f"    Transform matrix: {orig_state['transform']}")
    else:
        print("❌ No original transforms stored!")
    
    # Step 6: Final analysis
    print(f"\n📋 STEP 6: FINAL ANALYSIS")
    print("=" * 60)
    
    print(f"🎯 KEY QUESTIONS:")
    print(f"1. Is the initial bounding box the correct size and position?")
    print(f"2. Does bounding box recreation work correctly?")
    print(f"3. Do the actor bounds change during transformation?")
    print(f"4. Do the actor bounds reset during reset?")
    print(f"5. Are the original transforms being stored correctly?")
    
    print(f"\n🔍 DIAGNOSIS:")
    print(f"- If bounding box is wrong size: Using wrong actor for bounds")
    print(f"- If bounds don't change during transform: Transformations not applied")
    print(f"- If bounds don't reset: Reset logic not working")
    print(f"- If no original transforms: Storage logic broken")
    
    # Keep window open
    print(f"\n👁️ Window will stay open for final inspection...")
    QTimer.singleShot(15000, app.quit)
    app.exec_()
    
    return True

if __name__ == "__main__":
    debug_bounding_box_issue()
