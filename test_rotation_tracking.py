#!/usr/bin/env python3
"""
Test rotation button tracking to see if model_rot_left gets updated
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer_tdk_modular import StepViewerTDK

class RotationTrackingTest:
    def __init__(self):
        self.app = QApplication([])
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        print("🧪 ROTATION TRACKING TEST")
        print("=" * 50)
        
        # Start the test
        QTimer.singleShot(1000, self.load_and_test)
        
    def load_and_test(self):
        """Load test.step and test rotation tracking"""
        print("\n🔄 Step 1: Loading test.step...")
        
        if not os.path.exists("test.step"):
            print("❌ test.step not found!")
            self.app.quit()
            return
            
        # Set active viewer to top
        self.viewer.active_viewer = "top"
        self.viewer.update_viewer_highlights()
        
        # Load the file
        success = self.viewer.load_step_file_direct("test.step")
        
        if not success:
            print("❌ Failed to load test.step")
            self.app.quit()
            return
            
        print("✅ test.step loaded")
        
        # Check initial values
        print(f"\n📊 Initial values:")
        if hasattr(self.viewer, 'model_rot_left'):
            print(f"   model_rot_left: {self.viewer.model_rot_left}")
        else:
            print("   model_rot_left: NOT SET")
            
        if hasattr(self.viewer, 'current_rot_left'):
            print(f"   current_rot_left: {self.viewer.current_rot_left}")
        else:
            print("   current_rot_left: NOT SET")
        
        # Test rotation buttons
        QTimer.singleShot(2000, self.test_rotation_buttons)
        
    def test_rotation_buttons(self):
        """Test clicking rotation buttons"""
        print(f"\n🔄 Step 2: Testing rotation buttons...")
        
        # Simulate clicking X+15° button 3 times (should be 45° total)
        print("   Clicking X+15° button 3 times...")
        
        for i in range(3):
            print(f"   Click {i+1}: X+15°")
            self.viewer.rotate_shape('x', 15.0)
            
            # Check values after each click
            if hasattr(self.viewer, 'model_rot_left'):
                print(f"      model_rot_left: {self.viewer.model_rot_left}")
            if hasattr(self.viewer, 'current_rot_left'):
                print(f"      current_rot_left: {self.viewer.current_rot_left}")
        
        # Final check
        QTimer.singleShot(1000, self.final_check)
        
    def final_check(self):
        """Final check of rotation values"""
        print(f"\n📊 Final values after 3x X+15° rotations:")
        
        if hasattr(self.viewer, 'model_rot_left'):
            print(f"   model_rot_left: {self.viewer.model_rot_left}")
            expected_x = 45.0
            actual_x = self.viewer.model_rot_left.get('x', 0.0)
            if abs(actual_x - expected_x) < 0.1:
                print(f"   ✅ model_rot_left['x'] = {actual_x}° (expected {expected_x}°)")
            else:
                print(f"   ❌ model_rot_left['x'] = {actual_x}° (expected {expected_x}°)")
        else:
            print("   ❌ model_rot_left: NOT SET")
            
        if hasattr(self.viewer, 'current_rot_left'):
            print(f"   current_rot_left: {self.viewer.current_rot_left}")
        else:
            print("   ❌ current_rot_left: NOT SET")
        
        # Test save values
        print(f"\n🔄 Step 3: Testing save logic...")
        self.test_save_values()
        
    def test_save_values(self):
        """Test what values would be used for saving"""
        
        # Simulate the save logic from save_step_file_option1
        if hasattr(self.viewer, 'model_rot_left'):
            current_rot = self.viewer.model_rot_left
        else:
            current_rot = {'x': 0, 'y': 0, 'z': 0}
            
        orig_rot = {'x': 0, 'y': 0, 'z': 0}  # From STEP file
        
        # Calculate delta (what save method uses)
        delta_rot = {
            'x': current_rot['x'] - orig_rot['x'],
            'y': current_rot['y'] - orig_rot['y'],
            'z': current_rot['z'] - orig_rot['z']
        }
        
        print(f"   Save would use:")
        print(f"      current_rot: {current_rot}")
        print(f"      orig_rot: {orig_rot}")
        print(f"      delta_rot: {delta_rot}")
        
        # Check if rotation would be detected
        rot_changed = (abs(delta_rot['x']) > 0.001 or abs(delta_rot['y']) > 0.001 or abs(delta_rot['z']) > 0.001)
        print(f"      rotation_changed: {rot_changed}")
        
        if rot_changed:
            print("   ✅ Save would detect rotation and apply transformations")
        else:
            print("   ❌ Save would NOT detect rotation (would just copy original file)")
        
        # Quit after test
        QTimer.singleShot(2000, self.app.quit)

def main():
    test = RotationTrackingTest()
    test.app.exec_()

if __name__ == "__main__":
    main()
