#!/usr/bin/env python3
"""
Simple test to verify cursor position logic without VTK rendering issues
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt

class SimpleCursorTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Cursor Position Test")
        self.setGeometry(100, 100, 800, 600)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create layout
        layout = QVBoxLayout(central_widget)
        
        # Create labels to show cursor position
        self.cursor_label = QLabel("Cursor Position: X=0.000 Y=0.000")
        self.cursor_label.setStyleSheet("font-size: 16px; color: yellow; background-color: black; padding: 10px;")
        layout.addWidget(self.cursor_label)
        
        # Create test area
        self.test_area = QLabel("Move mouse over this area to test cursor tracking")
        self.test_area.setStyleSheet("font-size: 14px; background-color: blue; color: white; padding: 50px;")
        self.test_area.setAlignment(Qt.AlignCenter)
        self.test_area.setMouseTracking(True)  # Enable mouse tracking
        layout.addWidget(self.test_area)
        
        # Enable mouse tracking for the main window
        self.setMouseTracking(True)
        central_widget.setMouseTracking(True)
        
        # Test the STEP file angle calculation
        self.test_angle_calculation()
        
    def test_angle_calculation(self):
        """Test the STEP file angle calculation logic"""
        print("=== STEP FILE ANGLE CALCULATION TEST ===")
        
        # STEP file data from your example
        direction = [0.000000000000000, 0.910400000000000, -0.413800000000000]
        ref_direction = [0.000000000000000, 0.413800000000000, 0.910400000000000]
        
        print(f"Direction: {direction}")
        print(f"Ref Direction: {ref_direction}")
        
        # Calculate rotation angles from DIRECTION vectors
        import math
        angle_x = math.degrees(math.atan2(direction[2], direction[1]))  # Rotation around X
        angle_y = math.degrees(math.atan2(-direction[0], math.sqrt(direction[1]**2 + direction[2]**2)))  # Rotation around Y  
        angle_z = math.degrees(math.atan2(ref_direction[0], ref_direction[1]))  # Rotation around Z
        
        print(f"Calculated Angles: X={angle_x:.3f}°, Y={angle_y:.3f}°, Z={angle_z:.3f}°")
        
        # Update the cursor label to show the calculated angles
        self.cursor_label.setText(f"STEP Angles: X={angle_x:.3f}° Y={angle_y:.3f}° Z={angle_z:.3f}° | Cursor: X=0.000 Y=0.000")
        
    def mouseMoveEvent(self, event):
        """Handle mouse move events to update cursor position"""
        # Get mouse position relative to the window
        x = event.x()
        y = event.y()
        
        # Convert to a coordinate system (simple example)
        # In a real 3D viewer, this would be world coordinates
        world_x = (x - 400) / 100.0  # Center at 400, scale by 100
        world_y = (300 - y) / 100.0  # Invert Y, center at 300, scale by 100
        
        # Update the cursor display
        current_text = self.cursor_label.text()
        if "STEP Angles:" in current_text:
            # Keep the STEP angles, update cursor
            step_part = current_text.split(" | ")[0]
            self.cursor_label.setText(f"{step_part} | Cursor: X={world_x:.3f} Y={world_y:.3f}")
        else:
            self.cursor_label.setText(f"Cursor Position: X={world_x:.3f} Y={world_y:.3f}")
        
        # Call parent implementation
        super().mouseMoveEvent(event)

def main():
    app = QApplication(sys.argv)
    
    # Create and show the test window
    window = SimpleCursorTest()
    window.show()
    
    print("Simple cursor test running...")
    print("Move mouse over the window to test cursor tracking")
    print("The yellow text should update with cursor position")
    
    # Run the application
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
