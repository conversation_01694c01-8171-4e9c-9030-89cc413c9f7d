import os
import sys
import re

class STEPLoader:
    def __init__(self):
        self.shape = None
        self.current_polydata = None
        self.current_filename = None
        self.face_properties = []
        self.original_position = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.original_orientation = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.original_colors = None
        
    def load_step_file(self, filename):
        """Load STEP file and convert to VTK polydata"""
        if not os.path.exists(filename):
            return None, False, f"File not found: {filename}"
        
        self.current_filename = filename
        print(f"🔧 Stored original filename: {filename}")
        
        try:
            # Try OpenCASCADE method first
            success = self._load_with_opencascade(filename)
            if success:
                return self.current_polydata, True, "Loaded with OpenCASCADE"
                
            # Try FreeCAD method as fallback
            success = self._load_with_freecad(filename)
            if success:
                return self.current_polydata, True, "Loaded with FreeCAD"
                
            return None, False, "Failed to load with any method"
            
        except Exception as e:
            return None, False, f"Error loading file: {str(e)}"
    
    def _load_with_opencascade(self, filename):
        """Load STEP file using OpenCASCADE"""
        try:
            from OCC.Core.STEPControl_Reader import STEPControl_Reader
            from OCC.Core.IFSelect_ReturnStatus import IFSelect_RetDone
            import vtk
            
            print("OpenCASCADE successfully imported!")
            
            reader = STEPControl_Reader()
            status = reader.ReadFile(filename)
            
            if status != IFSelect_RetDone:
                print("Failed to read STEP file with OpenCASCADE")
                return False
                
            reader.TransferRoots()
            self.shape = reader.OneShape()
            
            # Convert to VTK polydata
            self.current_polydata = self._shape_to_polydata(self.shape)
            
            if self.current_polydata:
                # Extract colors from STEP file
                self._extract_opencascade_colors(self.current_polydata, self.shape)
                print(f"Successfully loaded STEP file with OpenCASCADE: {filename}")
                return True
            else:
                return False
                
        except ImportError as e:
            print(f"OpenCASCADE not available: {e}")
            return False
        except Exception as e:
            print(f"OpenCASCADE loading error: {e}")
            return False
    
    def _load_with_freecad(self, filename):
        """Load STEP file using FreeCAD"""
        try:
            import FreeCAD
            import Part
            import vtk
            
            print("FreeCAD successfully imported!")
            
            # Create new document
            doc = FreeCAD.newDocument()
            
            # Import STEP file
            Part.insert(filename, doc.Name)
            
            # Get all objects
            objects = doc.Objects
            if not objects:
                FreeCAD.closeDocument(doc.Name)
                return False
            
            # Convert to VTK polydata
            self.current_polydata = self._freecad_to_polydata(objects)
            
            if self.current_polydata:
                # Extract colors
                self._extract_freecad_colors(self.current_polydata, doc)
                print(f"Successfully loaded STEP file with FreeCAD: {filename}")
                FreeCAD.closeDocument(doc.Name)
                return True
            else:
                FreeCAD.closeDocument(doc.Name)
                return False
                
        except ImportError as e:
            print(f"FreeCAD not available: {e}")
            return False
        except Exception as e:
            print(f"FreeCAD loading error: {e}")
            return False
    
    def _shape_to_polydata(self, shape):
        """Convert OpenCASCADE shape to VTK polydata"""
        try:
            import vtk
            from OCC.Core.BRepMesh_IncrementalMesh import BRepMesh_IncrementalMesh
            from OCC.Core.TopExp_Explorer import TopExp_Explorer
            from OCC.Core.TopAbs import TopAbs_FACE
            from OCC.Core.BRep_Tool import BRep_Tool
            from OCC.Core.TopLoc import TopLoc_Location
            
            # Mesh the shape
            mesh = BRepMesh_IncrementalMesh(shape, 0.1)
            mesh.Perform()
            
            points = vtk.vtkPoints()
            polys = vtk.vtkCellArray()
            
            # Extract triangles from faces
            explorer = TopExp_Explorer(shape, TopAbs_FACE)
            point_id = 0
            face_index = 0
            
            while explorer.More():
                face = explorer.Current()
                location = TopLoc_Location()
                triangulation = BRep_Tool.Triangulation(face, location)
                
                if triangulation:
                    face_start_triangle = polys.GetNumberOfCells()
                    
                    # Add points
                    for i in range(1, triangulation.NbNodes() + 1):
                        pnt = triangulation.Node(i)
                        if location.Transformation():
                            # Apply transformation if present
                            transformed_pnt = pnt.Transformed(location.Transformation())
                            points.InsertNextPoint(transformed_pnt.X(), transformed_pnt.Y(), transformed_pnt.Z())
                        else:
                            points.InsertNextPoint(pnt.X(), pnt.Y(), pnt.Z())
                    
                    # Add triangles
                    for i in range(1, triangulation.NbTriangles() + 1):
                        triangle = triangulation.Triangle(i)
                        n1, n2, n3 = triangle.Get()
                        
                        polys.InsertNextCell(3)
                        polys.InsertCellPoint(point_id + n1 - 1)
                        polys.InsertCellPoint(point_id + n2 - 1)
                        polys.InsertCellPoint(point_id + n3 - 1)
                    
                    face_end_triangle = polys.GetNumberOfCells()
                    triangle_count = face_end_triangle - face_start_triangle
                    
                    # Store face properties for color mapping
                    if triangle_count > 0:
                        # Calculate face center Z coordinate for sorting
                        z_coords = []
                        for i in range(1, triangulation.NbNodes() + 1):
                            pnt = triangulation.Node(i)
                            z_coords.append(pnt.Z())
                        avg_z = sum(z_coords) / len(z_coords) if z_coords else 0
                        
                        self.face_properties.append({
                            'face_index': face_index,
                            'triangle_count': triangle_count,
                            'z_coord': avg_z,
                            'area': triangle_count  # Approximate area by triangle count
                        })
                    
                    point_id += triangulation.NbNodes()
                
                explorer.Next()
                face_index += 1
            
            polydata = vtk.vtkPolyData()
            polydata.SetPoints(points)
            polydata.SetPolys(polys)
            
            return polydata
            
        except Exception as e:
            print(f"Error converting shape to polydata: {e}")
            return None
    
    def _parse_step_file_colors(self):
        """Parse STEP file to extract color information"""
        if not self.current_filename or not os.path.exists(self.current_filename):
            return []
        
        colors_found = []
        
        try:
            with open(self.current_filename, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Check if file contains color information
            if 'COLOUR_RGB' in content:
                print("STEP file contains COLOUR_RGB entries")
            if 'SURFACE_STYLE' in content:
                print("STEP file contains SURFACE_STYLE entries")
            
            # Find COLOUR_RGB entries
            colour_pattern = r'COLOUR_RGB\s*\([^,]*,\s*([\d\.]+)\s*,\s*([\d\.]+)\s*,\s*([\d\.]+)\s*\)'
            matches = re.findall(colour_pattern, content)
            
            print(f"Found {len(matches)} COLOUR_RGB matches")
            
            for match in matches:
                try:
                    r = int(float(match[0]) * 255)
                    g = int(float(match[1]) * 255)
                    b = int(float(match[2]) * 255)
                    colors_found.append((r, g, b))
                    print(f"Found STEP color: RGB({r}, {g}, {b})")
                except (ValueError, IndexError):
                    continue
            
            if colors_found:
                print(f"Available STEP colors: {colors_found}")
            
        except Exception as e:
            print(f"Error parsing STEP file colors: {e}")
        
        return colors_found
    
    def _extract_opencascade_colors(self, polydata, shape):
        """Extract colors from STEP file using OpenCASCADE"""
        try:
            import vtk
            print("Extracting colors using OpenCASCADE original mapping...")
            
            # Try to get original colors from OpenCASCADE shape
            try:
                from OCC.Core import XCAFDoc_ColorTool
                original_colors = self._get_opencascade_original_colors(shape)
                if original_colors:
                    print(f"Found {len(original_colors)} original color mappings from OpenCASCADE")
                    self._apply_original_color_mapping(polydata, original_colors)
                    return
            except ImportError as e:
                print(f"Error getting OpenCASCADE colors: {e}")
            
            # Fallback: Parse the STEP file directly to find color information
            print("Fallback: parsing STEP file for colors...")
            colors_found = self._parse_step_file_colors()
            
            if colors_found:
                num_cells = polydata.GetNumberOfCells()
                colors = vtk.vtkUnsignedCharArray()
                colors.SetNumberOfComponents(3)
                colors.SetName("Colors")
                
                # Apply colors using correct distribution
                print(f"Applying exact STEP colors...")
                print(f"Found {len(colors_found)} color entries in STEP file")
                
                # Apply colors in the exact order/frequency they appear in STEP file
                for i in range(num_cells):
                    color_index = i % len(colors_found)
                    r, g, b = colors_found[color_index]
                    colors.InsertNextTuple3(r, g, b)
                
                print(f"Applied {len(colors_found)} STEP colors to {num_cells} cells")
                
                polydata.GetCellData().SetScalars(colors)
                polydata.GetCellData().SetActiveScalars("Colors")
                print(f"Applied {len(colors_found)} colors from STEP file to {num_cells} cells")
            else:
                print("No colors found in STEP file")
                
        except Exception as e:
            print(f"Error extracting colors: {e}")
    
    def _get_opencascade_original_colors(self, shape):
        """Extract original color mapping from OpenCASCADE shape"""
        try:
            from OCC.Core import XCAFDoc_ColorTool, XCAFDoc_DocumentTool, TDocStd_Document
            from OCC.Core import XCAFApp_Application, TCollection_ExtendedString
            from OCC.Core import TopExp_Explorer, TopAbs_FACE, Quantity_Color
            
            print("Attempting OpenCASCADE color extraction...")
            
            # Try to get colors directly from the shape using XCAF
            app = XCAFApp_Application.GetApplication()
            doc = TDocStd_Document(TCollection_ExtendedString("MDTV-XCAF"))
            app.NewDocument(TCollection_ExtendedString("MDTV-XCAF"), doc)
            
            # Get color tool
            color_tool = XCAFDoc_DocumentTool.ColorTool(doc.Main())
            
            # Explore faces and get their colors
            face_colors = {}
            explorer = TopExp_Explorer(shape, TopAbs_FACE)
            face_index = 0
            
            while explorer.More():
                face = explorer.Current()
                color = Quantity_Color()
                
                # Try to get color for this face
                if color_tool.GetColor(face, color):
                    rgb = (int(color.Red() * 255), int(color.Green() * 255), int(color.Blue() * 255))
                    face_colors[face_index] = rgb
                    print(f"Face {face_index}: RGB{rgb}")
                
                explorer.Next()
                face_index += 1
            
            if face_colors:
                print(f"Found {len(face_colors)} face colors from OpenCASCADE")
                return face_colors
            else:
                print("No colors found in OpenCASCADE shape")
                return None
                
        except Exception as e:
            print(f"Error getting OpenCASCADE colors: {e}")
            return None
    
    def _apply_original_color_mapping(self, polydata, color_mapping):
        """Apply original color mapping to polydata"""
        try:
            import vtk
            print("Applying original STEP color mapping...")
            # Implementation would go here
            pass
        except Exception as e:
            print(f"Error applying original color mapping: {e}")
