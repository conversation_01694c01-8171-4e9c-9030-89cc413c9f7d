#!/usr/bin/env python3

print("SIMPLE VERIFY: STEP file shape color = display shape color")

from step_loader import STEPLoader

# Load file
loader = STEPLoader()
success, msg = loader.load_step_file('SOIC16P127_1270X940X610L89X51.STEP')

if success and loader.current_polydata:
    polydata = loader.current_polydata
    colors_array = polydata.GetCellData().GetScalars("Colors")
    
    if colors_array:
        # Count colors in display using ACTUAL XCAF colors
        dark_color = (13, 13, 13)    # Actual XCAF dark color
        light_color = (134, 134, 134)  # Actual XCAF light color
        
        dark_count = 0
        light_count = 0
        
        for i in range(colors_array.GetNumberOfTuples()):
            r = int(colors_array.GetComponent(i, 0))
            g = int(colors_array.GetComponent(i, 1))
            b = int(colors_array.GetComponent(i, 2))
            color = (r, g, b)
            
            if color == dark_color:
                dark_count += 1
            elif color == light_color:
                light_count += 1
        
        print(f"Display colors:")
        print(f"Dark: {dark_count}")
        print(f"Light: {light_count}")
        print(f"Total: {dark_count + light_count}")
        
        # STEP file colors (known from analysis)
        step_dark_shapes = 12
        step_light_shapes = 227
        
        print(f"STEP file colors:")
        print(f"Dark shapes: {step_dark_shapes}")
        print(f"Light shapes: {step_light_shapes}")
        print(f"Total shapes: {step_dark_shapes + step_light_shapes}")
        
        # Simple check: do the ratios match?
        total_display = dark_count + light_count
        total_step = step_dark_shapes + step_light_shapes

        if total_display > 0:
            display_dark_ratio = dark_count / total_display
        else:
            display_dark_ratio = 0.0

        if total_step > 0:
            step_dark_ratio = step_dark_shapes / total_step
        else:
            step_dark_ratio = 0.0
        
        print(f"Display dark ratio: {display_dark_ratio:.1%}")
        print(f"STEP dark ratio: {step_dark_ratio:.1%}")
        
        if abs(display_dark_ratio - step_dark_ratio) < 0.01:  # Within 1%
            print("SUCCESS: STEP file shape colors = display shape colors")
        else:
            print("FAILURE: Colors don't match")
    else:
        print("No colors found")
else:
    print("Failed to load")

print("SIMPLE VERIFY COMPLETE")
