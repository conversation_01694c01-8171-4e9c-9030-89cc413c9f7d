@echo off
echo Running STEP viewer with Python 3.11...
echo Python path: C:\Users\<USER>\Miniforge3\envs\step_viewer\python.exe

REM Add conda environment paths to PATH for DLL loading
set PATH=C:\Users\<USER>\Miniforge3\envs\step_viewer;C:\Users\<USER>\Miniforge3\envs\step_viewer\Scripts;C:\Users\<USER>\Miniforge3\envs\step_viewer\Library\bin;C:\Users\<USER>\Miniforge3\envs\step_viewer\DLLs;%PATH%

C:\Users\<USER>\Miniforge3\envs\step_viewer\python.exe --version
echo.
echo Starting application...
C:\Users\<USER>\Miniforge3\envs\step_viewer\python.exe step_viewer_tdk_modular.py
pause
