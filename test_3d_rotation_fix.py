#!/usr/bin/env python3
"""
Test the 3D Rotation Matrix Fix
This will test if the new rotation system preserves angles correctly
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main program
sys.path.append('.')
from step_viewer_tdk_modular import StepViewerTDK

class Test3DRotationFix:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        
    def run_test(self):
        """Run the 3D rotation fix test"""
        print("🔧 TESTING 3D ROTATION MATRIX FIX")
        print("=" * 60)
        
        # Create viewer
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        # Start test sequence
        QTimer.singleShot(1000, self.step1_load_file)
        
        # Run the application
        self.app.exec_()
        
    def step1_load_file(self):
        """Step 1: Load test file"""
        print("\n🔄 STEP 1: Loading test.step...")
        
        if not os.path.exists('test.step'):
            print("❌ test.step not found!")
            return
            
        success = self.viewer.load_step_file_direct('test.step')
        if success:
            print("✅ File loaded successfully")
            QTimer.singleShot(2000, self.step2_apply_complex_rotation)
        else:
            print("❌ Failed to load file")
            
    def step2_apply_complex_rotation(self):
        """Step 2: Apply complex multi-axis rotation"""
        print("\n🔄 STEP 2: Applying complex multi-axis rotation...")
        
        # Set active viewer to top
        self.viewer.active_viewer = "top"
        
        # Apply complex rotation similar to your manual test
        print("🔧 Applying X=30°, Y=45°, Z=60° rotation...")
        self.viewer.rotate_shape("x", 30.0)
        self.viewer.rotate_shape("y", 45.0)
        self.viewer.rotate_shape("z", 60.0)
        
        # Capture the rotation values
        if hasattr(self.viewer, 'current_rot_left'):
            self.original_rotation = self.viewer.current_rot_left.copy()
            print(f"📊 APPLIED ROTATION: {self.original_rotation}")
        
        QTimer.singleShot(2000, self.step3_save_file)
        
    def step3_save_file(self):
        """Step 3: Save the file"""
        print("\n🔄 STEP 3: Saving rotated file...")
        
        try:
            # Get current values
            current_pos = getattr(self.viewer, 'current_pos_left', {'x': 0, 'y': 0, 'z': 0})
            current_rot = getattr(self.viewer, 'current_rot_left', {'x': 0, 'y': 0, 'z': 0})
            orig_pos = getattr(self.viewer, 'orig_pos_left', {'x': 0, 'y': 0, 'z': 0})
            orig_rot = getattr(self.viewer, 'orig_rot_left', {'x': 0, 'y': 0, 'z': 0})
            
            print(f"🔍 BEFORE SAVE:")
            print(f"   Current Rotation: {current_rot}")
            
            # Save the file
            save_filename = "test_3d_rotation_fix.step"
            loader = self.viewer.step_loader_left
            
            success = self.viewer._save_step_with_transformations(
                save_filename, loader, current_pos, current_rot, orig_pos, orig_rot
            )
            
            if success and os.path.exists(save_filename):
                print(f"✅ File saved: {save_filename}")
                self.saved_filename = save_filename
                QTimer.singleShot(2000, self.step4_load_saved_file)
            else:
                print("❌ Save failed")
                QTimer.singleShot(1000, self.step5_analyze_results)
                
        except Exception as e:
            print(f"❌ Save error: {e}")
            QTimer.singleShot(1000, self.step5_analyze_results)
            
    def step4_load_saved_file(self):
        """Step 4: Load saved file in bottom viewer"""
        print("\n🔄 STEP 4: Loading saved file in bottom viewer...")
        
        try:
            # Switch to bottom viewer
            self.viewer.active_viewer = "bottom"
            
            # Load the saved file
            success = self.viewer.load_step_file_direct(self.saved_filename)
            
            if success:
                print("✅ Saved file loaded in bottom viewer")
                QTimer.singleShot(3000, self.step5_analyze_results)
            else:
                print("❌ Failed to load saved file")
                QTimer.singleShot(1000, self.step5_analyze_results)
                
        except Exception as e:
            print(f"❌ Load error: {e}")
            QTimer.singleShot(1000, self.step5_analyze_results)
            
    def step5_analyze_results(self):
        """Step 5: Analyze the results"""
        print("\n🔄 STEP 5: ANALYZING RESULTS...")
        print("=" * 60)
        
        if hasattr(self, 'original_rotation'):
            print(f"📊 ORIGINAL ROTATION: {self.original_rotation}")
            
        if hasattr(self.viewer, 'current_rot_right'):
            loaded_rotation = self.viewer.current_rot_right
            print(f"📊 LOADED ROTATION:   {loaded_rotation}")
            
            # Calculate differences
            if hasattr(self, 'original_rotation'):
                diff_x = abs(self.original_rotation['x'] - loaded_rotation['x'])
                diff_y = abs(self.original_rotation['y'] - loaded_rotation['y'])
                diff_z = abs(self.original_rotation['z'] - loaded_rotation['z'])
                
                print(f"\n🔍 ROTATION DIFFERENCES:")
                print(f"   X: {diff_x:.2f}°")
                print(f"   Y: {diff_y:.2f}°")
                print(f"   Z: {diff_z:.2f}°")
                
                # Check if rotation is preserved (within 1 degree tolerance)
                if diff_x < 1.0 and diff_y < 1.0 and diff_z < 1.0:
                    print("\n✅ SUCCESS: 3D ROTATION MATRIX FIX WORKING!")
                    print("   Complex multi-axis rotation preserved correctly")
                else:
                    print("\n❌ ISSUE: Rotation not preserved accurately")
                    print("   The 3D matrix fix needs further adjustment")
        else:
            print("❌ Could not get loaded rotation values")
            
        print("=" * 60)
        print("🎯 TEST COMPLETE")
        
        # Close after delay
        QTimer.singleShot(5000, self.app.quit)

if __name__ == "__main__":
    test = Test3DRotationFix()
    test.run_test()
