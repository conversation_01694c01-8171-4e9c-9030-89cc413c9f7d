#!/usr/bin/env python3
"""Test which version of the program is running"""

print("=== VERSION TEST ===")
print("Testing which debug message is in the current file...")

# Read the current file and check for debug messages
try:
    with open('step_viewer_tdk_modular.py', 'r') as f:
        content = f.read()
        
    if "DEBUG: Camera update timer working" in content:
        print("✅ CORRECT VERSION: Found 'Camera update timer working' - Current TOP should update")
    elif "DEBUG: Timer working" in content:
        print("❌ OLD VERSION: Found 'Timer working' - Current TOP will NOT update")
    else:
        print("⚠️  UNKNOWN: No debug timer message found")
        
    print("\nNow running the main program...")
    print("=" * 50)
    
    # Import and run the main program
    exec(open('step_viewer_tdk_modular.py').read())
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
