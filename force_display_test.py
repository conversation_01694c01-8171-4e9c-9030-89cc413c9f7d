#!/usr/bin/env python3
"""
Force a display test that must show output
"""

print("FORCE DISPLAY TEST STARTING")
print("=" * 50)

try:
    print("Testing VTK import...")
    import vtk
    print("VTK imported successfully")
    
    print("Testing STEP loader import...")
    from step_loader import STEPLoader
    print("STEPLoader imported successfully")
    
    print("Creating STEP loader...")
    loader = STEPLoader()
    print("STEP loader created")
    
    print("Testing STEP file loading...")
    result = loader.load_step_file('SOIC16P127_1270X940X610L89X51.STEP')
    print(f"Load result type: {type(result)}")
    print(f"Load result: {result}")
    
    if len(result) == 3:
        polydata, success, message = result
        print(f"Success: {success}")
        print(f"Message: {message}")
        
        if success and polydata:
            print(f"STEP file loaded successfully!")
            print(f"Polydata: {polydata.GetNumberOfCells()} cells, {polydata.GetNumberOfPoints()} points")
            
            # Check colors
            colors = polydata.GetCellData().GetScalars("Colors")
            if colors:
                print(f"Colors found: {colors.GetNumberOfTuples()} tuples")
                print("SUCCESS: STEP file has colors!")
            else:
                print("WARNING: No colors found")
        else:
            print("FAILED: STEP file loading failed")
    else:
        print(f"UNEXPECTED: Result has {len(result)} elements")
        
except Exception as e:
    print(f"ERROR: {e}")
    import traceback
    traceback.print_exc()

print("=" * 50)
print("FORCE DISPLAY TEST COMPLETED")
