#!/usr/bin/env python3

print("VERIFY EACH STEP FILE ITEM MATCHES EACH DISPLAY ITEM")

# 1. Get STEP file items and their colors in order
print("=== STEP FILE ITEMS ===")
try:
    from OCC.Core.STEPControl import ST<PERSON><PERSON><PERSON>rol_Reader
    from OCC.Core.IFSelect import IF<PERSON>elect_RetDone
    from OCC.Core.STEPCAFControl import STEPCAFControl_Reader
    from OCC.Core.XCAFDoc import XCAFDoc_ColorTool, XCAFDoc_ColorType
    from OCC.Core.XCAFApp import XCAFApp_Application
    from OCC.Core.TDocStd import TDocStd_Document
    from OCC.Core.TopExp import TopExp_Explorer
    from OCC.Core.TopAbs import TopAbs_FACE
    from OCC.Core.Quantity import Quantity_Color
    
    # Read STEP file with colors
    app = XCAFApp_Application.GetApplication()
    doc = TDocStd_Document("MDTV-XCAF")
    
    reader = STEPCAFControl_Reader()
    reader.SetColorMode(True)
    reader.ReadFile('SOIC16P127_1270X940X610L89X51.STEP')
    reader.Transfer(doc)
    color_tool = XCAFDoc_ColorTool.Set(doc.Main())
    
    # Get shape
    basic_reader = STEPControl_Reader()
    basic_reader.ReadFile('SOIC16P127_1270X940X610L89X51.STEP')
    basic_reader.TransferRoots()
    shape = basic_reader.OneShape()
    
    # Get each face and its color in order
    explorer = TopExp_Explorer(shape, TopAbs_FACE)
    step_face_colors = []
    face_index = 0
    
    while explorer.More():
        face = explorer.Current()
        color = Quantity_Color()
        
        try:
            if color_tool.GetColor(face, XCAFDoc_ColorType.XCAFDoc_ColorSurf, color):
                r = int(color.Red() * 255)
                g = int(color.Green() * 255)
                b = int(color.Blue() * 255)
                face_color = (r, g, b)
            else:
                face_color = (192, 192, 192)  # Default
        except:
            face_color = (192, 192, 192)  # Default
        
        step_face_colors.append(face_color)
        print(f"STEP Face {face_index}: RGB{face_color}")
        face_index += 1
        explorer.Next()
    
    print(f"STEP file has {len(step_face_colors)} faces")
    
except Exception as e:
    print(f"Error reading STEP file: {e}")
    step_face_colors = []

# 2. Get display items and their colors in order
print("\n=== DISPLAY ITEMS ===")
from step_loader import STEPLoader

loader = STEPLoader()
success, msg = loader.load_step_file('SOIC16P127_1270X940X610L89X51.STEP')

if success and loader.current_polydata:
    polydata = loader.current_polydata
    colors_array = polydata.GetCellData().GetScalars("Colors")
    
    if colors_array:
        display_colors = []
        for i in range(colors_array.GetNumberOfTuples()):
            r = int(colors_array.GetComponent(i, 0))
            g = int(colors_array.GetComponent(i, 1))
            b = int(colors_array.GetComponent(i, 2))
            display_colors.append((r, g, b))
            
            if i < 10:  # Show first 10
                print(f"Display Cell {i}: RGB({r}, {g}, {b})")
        
        print(f"Display has {len(display_colors)} cells")
        
        # 3. Verify item-by-item match
        print("\n=== ITEM-BY-ITEM VERIFICATION ===")
        
        if len(step_face_colors) == 0:
            print("Cannot verify - no STEP face colors available")
        elif len(step_face_colors) == len(display_colors):
            # Same number of items - check one-to-one
            matches = 0
            for i in range(len(step_face_colors)):
                if step_face_colors[i] == display_colors[i]:
                    matches += 1
                else:
                    print(f"MISMATCH at item {i}: STEP RGB{step_face_colors[i]} vs Display RGB{display_colors[i]}")
            
            if matches == len(step_face_colors):
                print(f"SUCCESS: All {matches} items match exactly")
            else:
                print(f"FAILURE: Only {matches}/{len(step_face_colors)} items match")
        else:
            # Different number of items - check if colors are preserved
            step_unique = set(step_face_colors)
            display_unique = set(display_colors)
            
            print(f"Different item counts: STEP {len(step_face_colors)} vs Display {len(display_colors)}")
            print(f"STEP unique colors: {step_unique}")
            print(f"Display unique colors: {display_unique}")
            
            if step_unique == display_unique:
                print("SUCCESS: Same colors preserved (different triangulation)")
            else:
                print("FAILURE: Colors not preserved")
    else:
        print("No colors in display data")
else:
    print("Failed to load with step_loader")

print("\nVERIFICATION COMPLETE")
