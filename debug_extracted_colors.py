#!/usr/bin/env python3

print("DEBUG EXTRACTED COLORS FROM STEP FILE")

from step_loader import STEPLoader

# Create loader and extract colors
loader = STEPLoader()
loader.current_filename = 'SOIC16P127_1270X940X610L89X51.STEP'

# Get the actual extracted colors
extracted_colors = loader._get_actual_step_face_colors()

print(f"Extracted {len(extracted_colors)} face colors")

# Show first 50 face colors
print("\nFirst 50 face colors:")
for i in range(min(50, len(extracted_colors))):
    color = extracted_colors[i]
    color_name = "DARK" if color[0] < 100 else "LIGHT"
    print(f"Face {i}: {color} ({color_name})")

# Count colors
if extracted_colors:
    dark_count = sum(1 for color in extracted_colors if color[0] < 100)
    light_count = len(extracted_colors) - dark_count
    
    print(f"\nColor counts:")
    print(f"Dark faces: {dark_count}")
    print(f"Light faces: {light_count}")
    
    # Check specific faces that should be dark
    dark_faces = [18, 37, 56, 75, 94, 113, 132, 151, 170, 189, 208, 227]
    print(f"\nChecking expected dark faces:")
    for face_idx in dark_faces:
        if face_idx < len(extracted_colors):
            color = extracted_colors[face_idx]
            is_dark = color[0] < 100
            status = "CORRECT" if is_dark else "WRONG"
            print(f"Face {face_idx}: {color} - {status}")
else:
    print("No colors extracted")

print("\nEXTRACTED COLORS DEBUG COMPLETE")
