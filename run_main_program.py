#!/usr/bin/env python3
"""
Simple script to run the main program
"""

import subprocess
import sys

if __name__ == "__main__":
    print("🚀 Starting the main 3D viewer program...")
    try:
        subprocess.run([sys.executable, "step_viewer_tdk_modular.py"])
    except KeyboardInterrupt:
        print("\n👋 Program interrupted by user")
    except Exception as e:
        print(f"❌ Error running program: {e}")
