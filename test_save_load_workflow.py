#!/usr/bin/env python3
"""
Test the exact save/load workflow that's been broken for a week:
1. Load STEP file 
2. Apply rotations (simulate user rotating in GUI)
3. Save with transformations
4. Load saved file
5. Verify POS/ROT values match
"""

import sys
import os
from step_loader import ST<PERSON><PERSON>oader
from simple_step_modifier import SimpleSTEPModifier
import vtk

def test_save_load_workflow():
    print("=" * 80)
    print("🔧 TESTING SAVE/LOAD WORKFLOW (The problem that's been broken for a week)")
    print("=" * 80)
    
    original_file = "test.step"
    saved_file = "test_workflow_output.step"
    
    # STEP 1: Load original file (simulate loading in top viewer)
    print("\n📋 STEP 1: LOAD ORIGINAL FILE (Top Viewer)")
    print("-" * 60)
    
    loader = STEPLoader()
    success = loader.load_step_file(original_file)
    if not success:
        print(f"❌ Failed to load {original_file}")
        return False
    
    print(f"✅ Loaded {original_file}")
    
    # Extract original POS/ROT from the loaded file
    orig_pos = loader.original_position
    print(f"📊 ORIGINAL POS: X={orig_pos['x']:.3f}mm Y={orig_pos['y']:.3f}mm Z={orig_pos['z']:.3f}mm")
    print(f"📊 ORIGINAL ROT: X=0.000° Y=0.000° Z=0.000° (no rotations applied yet)")
    
    # STEP 2: Apply rotations (simulate user clicking rotation buttons)
    print("\n📋 STEP 2: APPLY ROTATIONS (User clicks X+, Y+, Z+ buttons)")
    print("-" * 60)
    
    # Simulate user rotating: 45° around X, 30° around Y, 15° around Z
    test_rot_x = 45.0
    test_rot_y = 30.0  
    test_rot_z = 15.0
    
    # Also simulate some position change
    test_pos_x = orig_pos['x'] + 5.0
    test_pos_y = orig_pos['y'] + 3.0
    test_pos_z = orig_pos['z'] + 2.0
    
    print(f"🔧 SIMULATED USER TRANSFORMATIONS:")
    print(f"   New POS: X={test_pos_x:.3f}mm Y={test_pos_y:.3f}mm Z={test_pos_z:.3f}mm")
    print(f"   New ROT: X={test_rot_x:.3f}° Y={test_rot_y:.3f}° Z={test_rot_z:.3f}°")
    
    # STEP 3: Save with transformations (simulate clicking "Save STEP File")
    print("\n📋 STEP 3: SAVE WITH TRANSFORMATIONS (User clicks 'Save STEP File')")
    print("-" * 60)
    
    # Create transformation matrix
    transform = vtk.vtkTransform()
    transform.Translate(test_pos_x - orig_pos['x'], test_pos_y - orig_pos['y'], test_pos_z - orig_pos['z'])
    transform.RotateX(test_rot_x)
    transform.RotateY(test_rot_y)
    transform.RotateZ(test_rot_z)
    matrix = transform.GetMatrix()
    
    # Use SimpleSTEPModifier to save (same as GUI)
    modifier = SimpleSTEPModifier()
    modifier.load_step_file(original_file)
    
    # Apply transformations
    modifier.transform_geometry_coordinates(matrix)
    modifier.modify_placement(test_pos_x, test_pos_y, test_pos_z, test_rot_x, test_rot_y, test_rot_z)
    
    # Save
    save_success = modifier.save_step_file(saved_file)
    if not save_success:
        print(f"❌ Failed to save {saved_file}")
        return False
    
    print(f"✅ Saved transformed file: {saved_file}")
    
    # STEP 4: Load saved file (simulate loading in bottom viewer)
    print("\n📋 STEP 4: LOAD SAVED FILE (Bottom Viewer)")
    print("-" * 60)
    
    loader2 = STEPLoader()
    success2 = loader2.load_step_file(saved_file)
    if not success2:
        print(f"❌ Failed to load saved file {saved_file}")
        return False
    
    print(f"✅ Loaded saved file: {saved_file}")
    
    # Extract POS/ROT from saved file
    saved_pos = loader2.original_position
    print(f"📊 LOADED POS: X={saved_pos['x']:.3f}mm Y={saved_pos['y']:.3f}mm Z={saved_pos['z']:.3f}mm")
    print(f"📊 LOADED ROT: (Need to extract rotation from STEP file)")
    
    # STEP 5: Compare values
    print("\n📋 STEP 5: COMPARE VALUES (This is where it fails)")
    print("-" * 60)
    
    print(f"📊 EXPECTED VALUES (what top viewer shows):")
    print(f"   POS: X={test_pos_x:.3f}mm Y={test_pos_y:.3f}mm Z={test_pos_z:.3f}mm")
    print(f"   ROT: X={test_rot_x:.3f}° Y={test_rot_y:.3f}° Z={test_rot_z:.3f}°")
    
    print(f"📊 ACTUAL VALUES (what bottom viewer shows):")
    print(f"   POS: X={saved_pos['x']:.3f}mm Y={saved_pos['y']:.3f}mm Z={saved_pos['z']:.3f}mm")
    print(f"   ROT: (Need to implement rotation extraction)")
    
    # Check if position matches
    pos_tolerance = 0.1  # 0.1mm tolerance
    pos_x_match = abs(saved_pos['x'] - test_pos_x) < pos_tolerance
    pos_y_match = abs(saved_pos['y'] - test_pos_y) < pos_tolerance
    pos_z_match = abs(saved_pos['z'] - test_pos_z) < pos_tolerance
    
    pos_match = pos_x_match and pos_y_match and pos_z_match
    
    print(f"\n🔍 POSITION MATCH CHECK:")
    print(f"   X: Expected={test_pos_x:.3f}, Got={saved_pos['x']:.3f}, Match={'✅' if pos_x_match else '❌'}")
    print(f"   Y: Expected={test_pos_y:.3f}, Got={saved_pos['y']:.3f}, Match={'✅' if pos_y_match else '❌'}")
    print(f"   Z: Expected={test_pos_z:.3f}, Got={saved_pos['z']:.3f}, Match={'✅' if pos_z_match else '❌'}")
    print(f"   Overall Position: {'✅ PASS' if pos_match else '❌ FAIL'}")
    
    if pos_match:
        print(f"\n🎉 SUCCESS: Position values are preserved correctly!")
        print(f"   The save/load workflow works for position!")
    else:
        print(f"\n❌ FAILURE: Position values are NOT preserved!")
        print(f"   This is the bug that's been broken for a week!")
        print(f"   The saved STEP file doesn't contain the correct position values!")
    
    print("=" * 80)
    return pos_match

if __name__ == "__main__":
    test_save_load_workflow()
