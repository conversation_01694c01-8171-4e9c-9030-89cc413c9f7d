#!/usr/bin/env python3

print("PARSING STEP GEOMETRY-TO-COLOR MAPPING")

# Read STEP file as text
with open('SOIC16P127_1270X940X610L89X51.STEP', 'r', encoding='utf-8', errors='ignore') as f:
    step_text = f.read()

lines = step_text.split('\n')

# Step 1: Extract RGB values from color entries
import re
colors = {}
for line in lines:
    if 'COLOUR_RGB' in line:
        # Parse: #8 = COLOUR_RGB ( '',0.75294117647059, 0.75294117647059, 0.75294117647059 ) ;
        match = re.search(r'#(\d+)\s*=\s*COLOUR_RGB.*?([\d\.]+)\s*,\s*([\d\.]+)\s*,\s*([\d\.]+)', line)
        if match:
            color_id = match.group(1)
            r = int(float(match.group(2)) * 255)
            g = int(float(match.group(3)) * 255)
            b = int(float(match.group(4)) * 255)
            colors[color_id] = (r, g, b)
            print(f"Color #{color_id}: RGB({r}, {g}, {b})")

print(f"\nExtracted {len(colors)} colors")

# Step 2: Debug STYLED_ITEM parsing first
print("\nDebugging STYLED_ITEM entries:")
styled_item_count = 0
for line in lines:
    if 'STYLED_ITEM' in line:
        styled_item_count += 1
        if styled_item_count <= 5:  # Show first 5
            print(f"Line: {line}")

            # Try different regex patterns
            pattern1 = r'STYLED_ITEM.*?\(\s*#(\d+)\s*\).*?#(\d+)'
            pattern2 = r'STYLED_ITEM.*?#(\d+).*?#(\d+)'

            match1 = re.search(pattern1, line)
            match2 = re.search(pattern2, line)

            print(f"  Pattern1 result: {match1.groups() if match1 else 'No match'}")
            print(f"  Pattern2 result: {match2.groups() if match2 else 'No match'}")

print(f"\nTotal STYLED_ITEM entries found: {styled_item_count}")

# Step 3: Build reference chain to trace from PRESENTATION_STYLE_ASSIGNMENT to COLOUR_RGB
reference_chain = {}
for line in lines:
    # Find all reference patterns like #14 = SOMETHING ( #13 )
    match = re.search(r'#(\d+)\s*=\s*\w+.*?#(\d+)', line)
    if match:
        from_id = match.group(1)
        to_id = match.group(2)
        reference_chain[from_id] = to_id

print(f"\nBuilt reference chain with {len(reference_chain)} entries")

# Step 4: Function to trace color reference chain
def trace_to_color(ref_id):
    visited = set()
    current = ref_id

    while current and current not in visited:
        visited.add(current)
        if current in colors:
            return colors[current]
        if current in reference_chain:
            current = reference_chain[current]
        else:
            break
    return None

# Step 5: Parse style entries and trace to actual colors
geometry_to_color = {}
for line in lines:
    if 'STYLED_ITEM' in line:
        match = re.search(r'STYLED_ITEM.*?#(\d+).*?#(\d+)', line)
        if match:
            color_ref = match.group(1)
            geometry_id = match.group(2)

            # Trace the color reference chain to find actual RGB
            actual_color = trace_to_color(color_ref)
            if actual_color:
                geometry_to_color[geometry_id] = actual_color

print(f"\nCreated geometry-to-color mapping for {len(geometry_to_color)} pieces")

# Show color distribution
color_counts = {}
for color in geometry_to_color.values():
    if color in color_counts:
        color_counts[color] += 1
    else:
        color_counts[color] = 1

print(f"\nColor distribution:")
for color, count in color_counts.items():
    print(f"RGB{color}: {count} geometry pieces")

print("\nPARSING STEP GEOMETRY-TO-COLOR MAPPING COMPLETE")
