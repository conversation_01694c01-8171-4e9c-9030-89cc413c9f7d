#!/usr/bin/env python3
"""
Debug What You Actually See - Test what's really being displayed
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular import Step<PERSON>iewerTDK

def debug_what_you_actually_see():
    """Debug what you actually see on screen"""
    
    print("🔧 DEBUG WHAT YOU ACTUALLY SEE")
    print("=" * 60)
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    # Process events to ensure G<PERSON> is ready
    app.processEvents()
    time.sleep(1)
    
    # Find a STEP file to test with
    test_files = [
        "AMPHENOL_U77-A1118-200T.STEP",
        "test.step",
        "sample.stp"
    ]
    
    test_file = None
    for filename in test_files:
        if os.path.exists(filename):
            test_file = filename
            break
    
    if not test_file:
        print("❌ No STEP file found for testing")
        return False
    
    print(f"📁 Using test file: {test_file}")
    
    # Step 1: Load STEP file
    print(f"\n📋 STEP 1: LOADING {test_file}...")
    success = viewer.load_step_file_direct(test_file)
    app.processEvents()
    time.sleep(2)
    
    if not success:
        print("❌ Failed to load STEP file")
        return False
    
    renderer = viewer.vtk_renderer_left
    
    print(f"\n👁️ STEP 1 RESULT: Model loaded - Note what you see")
    print(f"Press Enter when you've noted the initial position...")
    input()
    
    # Step 2: Apply transformation using GUI
    print(f"\n📋 STEP 2: APPLYING GUI TRANSFORMATION...")
    
    viewer.active_viewer = "top"
    
    print(f"🔧 Applying GUI transformation: +100mm X")
    viewer.move_shape("x", 100)
    app.processEvents()
    time.sleep(1)
    
    print(f"\n👁️ STEP 2 RESULT: After GUI move +100mm X")
    print(f"Did you see the model move? (y/n): ", end="")
    move_visible = input().lower().startswith('y')
    
    print(f"🔧 Applying GUI rotation: +90° Z")
    viewer.rotate_shape("z", 90)
    app.processEvents()
    time.sleep(1)
    
    print(f"\n👁️ STEP 2 RESULT: After GUI rotate +90° Z")
    print(f"Did you see the model rotate? (y/n): ", end="")
    rotate_visible = input().lower().startswith('y')
    
    # Step 3: Manual transformation test
    print(f"\n📋 STEP 3: MANUAL TRANSFORMATION TEST...")
    
    # Get all actors
    all_actors = []
    if hasattr(renderer, 'renderer') and renderer.renderer:
        actor_collection = renderer.renderer.GetActors()
        actor_collection.InitTraversal()
        
        actor = actor_collection.GetNextActor()
        while actor:
            all_actors.append(actor)
            actor = actor_collection.GetNextActor()
    
    print(f"🔧 Found {len(all_actors)} actors in renderer")
    
    # Test each actor individually
    for i, actor in enumerate(all_actors):
        print(f"\n🔧 Testing Actor {i}:")
        
        # Get original state
        orig_pos = actor.GetPosition()
        orig_orient = actor.GetOrientation()
        visible = actor.GetVisibility()
        
        print(f"  Original: Pos={orig_pos}, Orient={orig_orient}, Visible={visible}")
        
        if not visible:
            print(f"  Skipping - actor not visible")
            continue
        
        # Apply HUGE manual transformation
        print(f"  Applying HUGE manual transformation: +200mm X, +180° Z")
        actor.SetPosition(orig_pos[0] + 200, orig_pos[1], orig_pos[2])
        actor.RotateWXYZ(180, 0, 0, 1)
        actor.Modified()
        
        # Force render
        renderer.render_window.Render()
        app.processEvents()
        time.sleep(1)
        
        print(f"\n👁️ MANUAL TRANSFORM RESULT: Actor {i} transformed")
        print(f"Did you see the model move? (y/n): ", end="")
        manual_visible = input().lower().startswith('y')
        
        if manual_visible:
            print(f"✅ Actor {i} is the VISIBLE actor!")
            
            # Test manual reset
            print(f"\n🔧 Testing manual reset of Actor {i}...")
            actor.SetPosition(orig_pos)
            actor.SetOrientation(orig_orient)
            actor.Modified()
            renderer.render_window.Render()
            app.processEvents()
            time.sleep(1)
            
            print(f"\n👁️ MANUAL RESET RESULT: Actor {i} reset")
            print(f"Did you see the model move back? (y/n): ", end="")
            manual_reset_visible = input().lower().startswith('y')
            
            if manual_reset_visible:
                print(f"✅ Manual reset works on Actor {i}!")
            else:
                print(f"❌ Manual reset doesn't work on Actor {i}")
        else:
            print(f"❌ Actor {i} is not visible")
            # Reset this actor
            actor.SetPosition(orig_pos)
            actor.SetOrientation(orig_orient)
            actor.Modified()
    
    # Force final render
    renderer.render_window.Render()
    app.processEvents()
    time.sleep(1)
    
    # Step 4: GUI reset test
    print(f"\n📋 STEP 4: GUI RESET TEST...")
    
    # Re-apply GUI transformation
    print(f"🔧 Re-applying GUI transformation for reset test...")
    viewer.move_shape("x", 100)
    viewer.rotate_shape("z", 90)
    app.processEvents()
    time.sleep(1)
    
    print(f"\n👁️ Before GUI reset - Note the position")
    print(f"Press Enter when ready for GUI reset...")
    input()
    
    # GUI reset
    print(f"🔧 Calling GUI reset...")
    viewer.reset_to_original()
    app.processEvents()
    time.sleep(2)
    
    print(f"\n👁️ GUI RESET RESULT:")
    print(f"Did you see the model move back to original? (y/n): ", end="")
    gui_reset_visible = input().lower().startswith('y')
    
    # Step 5: Analysis
    print(f"\n📋 STEP 5: ANALYSIS")
    print("=" * 60)
    
    print(f"🎯 RESULTS:")
    print(f"GUI move visible: {move_visible}")
    print(f"GUI rotate visible: {rotate_visible}")
    print(f"GUI reset visible: {gui_reset_visible}")
    
    print(f"\n🔍 DIAGNOSIS:")
    if not move_visible and not rotate_visible:
        print(f"❌ GUI transformations don't work - GUI is transforming wrong actor")
    elif not gui_reset_visible:
        print(f"❌ GUI reset doesn't work - Reset logic has issue")
    else:
        print(f"✅ Everything should be working")
    
    print(f"\n🔧 RECOMMENDATION:")
    if not move_visible and not rotate_visible:
        print(f"Fix: Make GUI transform the visible actor")
    elif not gui_reset_visible:
        print(f"Fix: Make GUI reset transform the visible actor back")
    
    # Keep window open
    print(f"\n👁️ Window will stay open for final inspection...")
    print(f"Press Enter to close...")
    input()
    
    return True

if __name__ == "__main__":
    debug_what_you_actually_see()
