#!/usr/bin/env python3

print("VERIFY EACH SHAPE HAS CORRECT COLOR")

# Get STEP file shapes and their colors in order
print("=== STEP FILE SHAPES ===")
try:
    from OCC.Core.STEPControl import ST<PERSON><PERSON><PERSON>rol_Reader
    from OCC.Core.IFSelect import IF<PERSON>elect_RetDone
    from OCC.Core.STEPCAFControl import STEPCAFControl_Reader
    from OCC.Core.XCAFDoc import XCAFDoc_ColorTool, XCAFDoc_ColorType
    from OCC.Core.XCAFApp import XCAFApp_Application
    from OCC.Core.TDocStd import TDocStd_Document
    from OCC.Core.TopExp import TopExp_Explorer
    from OCC.Core.TopAbs import TopAbs_FACE
    from OCC.Core.Quantity import Quantity_Color
    
    # Read STEP file with colors
    app = XCAFApp_Application.GetApplication()
    doc = TDocStd_Document("MDTV-XCAF")
    
    reader = STEPCAFControl_Reader()
    reader.SetColorMode(True)
    reader.ReadFile('SOIC16P127_1270X940X610L89X51.STEP')
    reader.Transfer(doc)
    color_tool = XCAFDoc_ColorTool.Set(doc.Main())
    
    # Get shape
    basic_reader = STEPControl_Reader()
    basic_reader.ReadFile('SOIC16P127_1270X940X610L89X51.STEP')
    basic_reader.TransferRoots()
    shape = basic_reader.OneShape()
    
    # Get each face color in order
    explorer = TopExp_Explorer(shape, TopAbs_FACE)
    step_face_colors = []
    face_index = 0
    
    while explorer.More():
        face = explorer.Current()
        color = Quantity_Color()
        
        try:
            if color_tool.GetColor(face, XCAFDoc_ColorType.XCAFDoc_ColorSurf, color):
                r = int(color.Red() * 255)
                g = int(color.Green() * 255)
                b = int(color.Blue() * 255)
                face_color = (r, g, b)
            else:
                face_color = (192, 192, 192)  # Default
        except:
            face_color = (192, 192, 192)  # Default
        
        step_face_colors.append(face_color)
        if face_index < 10:  # Show first 10
            print(f"STEP Face {face_index}: RGB{face_color}")
        face_index += 1
        explorer.Next()
    
    print(f"STEP file: {len(step_face_colors)} faces")
    
except Exception as e:
    print(f"Error reading STEP: {e}")
    step_face_colors = []

# Get display shapes and their colors in order
print("\n=== DISPLAY SHAPES ===")
from step_loader import STEPLoader

loader = STEPLoader()
success, msg = loader.load_step_file('SOIC16P127_1270X940X610L89X51.STEP')

if success and loader.current_polydata:
    polydata = loader.current_polydata
    colors_array = polydata.GetCellData().GetScalars("Colors")
    
    if colors_array:
        display_colors = []
        for i in range(colors_array.GetNumberOfTuples()):
            r = int(colors_array.GetComponent(i, 0))
            g = int(colors_array.GetComponent(i, 1))
            b = int(colors_array.GetComponent(i, 2))
            display_colors.append((r, g, b))
            
            if i < 10:  # Show first 10
                print(f"Display Cell {i}: RGB({r}, {g}, {b})")
        
        print(f"Display: {len(display_colors)} cells")
        
        # SHAPE-BY-SHAPE VERIFICATION
        print("\n=== SHAPE-BY-SHAPE VERIFICATION ===")
        
        if len(step_face_colors) == 0:
            print("Cannot verify - no STEP face colors")
        else:
            # Since display has more cells than STEP faces (triangulation),
            # check if the color distribution matches proportionally
            
            # Count colors in STEP
            step_color_counts = {}
            for color in step_face_colors:
                step_color_counts[color] = step_color_counts.get(color, 0) + 1
            
            # Count colors in display
            display_color_counts = {}
            for color in display_colors:
                display_color_counts[color] = display_color_counts.get(color, 0) + 1
            
            print("STEP color distribution:")
            for color, count in step_color_counts.items():
                ratio = count / len(step_face_colors)
                print(f"  RGB{color}: {count}/{len(step_face_colors)} ({ratio:.1%})")
            
            print("Display color distribution:")
            for color, count in display_color_counts.items():
                ratio = count / len(display_colors)
                print(f"  RGB{color}: {count}/{len(display_colors)} ({ratio:.1%})")
            
            # Check if ratios match (within tolerance)
            ratios_match = True
            for color in step_color_counts:
                if color in display_color_counts:
                    step_ratio = step_color_counts[color] / len(step_face_colors)
                    display_ratio = display_color_counts[color] / len(display_colors)
                    
                    if abs(step_ratio - display_ratio) > 0.05:  # 5% tolerance
                        print(f"RATIO MISMATCH for RGB{color}: STEP {step_ratio:.1%} vs Display {display_ratio:.1%}")
                        ratios_match = False
                else:
                    print(f"MISSING COLOR in display: RGB{color}")
                    ratios_match = False
            
            if ratios_match:
                print("\nSUCCESS: Shape colors match with correct distribution")
            else:
                print("\nFAILURE: Shape colors do not match correctly")
    else:
        print("No display colors")
else:
    print("Display loading failed")

print("\nSHAPE-BY-SHAPE VERIFICATION COMPLETE")
