#!/usr/bin/env python3
"""
Test the fixes:
1. Single overwrite dialog (no duplicates)
2. Coordinate system conversion for correct rotation
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from step_viewer_tdk_modular import Step<PERSON>iewerTDK
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
import tempfile

def test_fixes():
    """Test both fixes"""
    
    print("🚀 Testing fixes...")
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    # Test file
    test_file = "test.step"
    if not os.path.exists(test_file):
        print(f"❌ Test file {test_file} not found")
        return
    
    # Create temporary save file
    temp_save_file = tempfile.mktemp(suffix='.step')
    
    def step1_load_and_rotate():
        """Step 1: Load file and apply rotation"""
        print(f"\n📁 Step 1: Loading and rotating...")
        viewer.set_active_viewer("bottom")
        viewer.step_loader_right.load_step_file(test_file)
        
        # Apply 45° X rotation
        viewer.current_rot_right = {'x': 45.0, 'y': 0.0, 'z': 0.0}
        
        print(f"   Applied rotation: X=45°")
        
        QTimer.singleShot(2000, step2_test_coordinate_conversion)
    
    def step2_test_coordinate_conversion():
        """Step 2: Test coordinate system conversion"""
        print(f"\n🔄 Step 2: Testing coordinate conversion...")
        
        # Test the coordinate conversion logic
        delta_rot = {'x': 45.0, 'y': 0.0, 'z': 0.0}
        
        # Apply the same conversion as in the save method
        vtk_to_step_rot_x = delta_rot['x']  # X rotation stays the same
        vtk_to_step_rot_y = delta_rot['z']  # VTK Z becomes STEP Y
        vtk_to_step_rot_z = -delta_rot['y'] # VTK Y becomes STEP -Z
        
        print(f"   VTK rotation: X={delta_rot['x']:.1f}°, Y={delta_rot['y']:.1f}°, Z={delta_rot['z']:.1f}°")
        print(f"   STEP rotation: X={vtk_to_step_rot_x:.1f}°, Y={vtk_to_step_rot_y:.1f}°, Z={vtk_to_step_rot_z:.1f}°")
        
        # Expected: VTK X=45°,Y=0°,Z=0° should become STEP X=45°,Y=0°,Z=0°
        if vtk_to_step_rot_x == 45.0 and vtk_to_step_rot_y == 0.0 and vtk_to_step_rot_z == 0.0:
            print(f"   ✅ Coordinate conversion working correctly")
        else:
            print(f"   ❌ Coordinate conversion issue")
        
        QTimer.singleShot(2000, step3_test_save)
    
    def step3_test_save():
        """Step 3: Test save with coordinate conversion"""
        print(f"\n💾 Step 3: Testing save with coordinate conversion...")
        
        try:
            # Get the loader and rotation data
            loader = viewer.step_loader_right
            current_rot = {'x': 45.0, 'y': 0.0, 'z': 0.0}
            current_pos = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            orig_rot = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            orig_pos = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            
            print(f"   Saving with coordinate conversion...")
            
            # Call the internal save method directly
            success = viewer._save_step_with_transformations(
                temp_save_file, loader, current_pos, current_rot, orig_pos, orig_rot
            )
            
            if success:
                print(f"   ✅ File saved successfully with coordinate conversion")
                file_size = os.path.getsize(temp_save_file) if os.path.exists(temp_save_file) else 0
                print(f"   📁 File size: {file_size} bytes")
            else:
                print(f"   ❌ Save failed")
        
        except Exception as e:
            print(f"   ❌ Save error: {e}")
            import traceback
            traceback.print_exc()
        
        QTimer.singleShot(2000, step4_test_load)
    
    def step4_test_load():
        """Step 4: Test loading the saved file"""
        print(f"\n📂 Step 4: Testing load of saved file...")
        
        if not os.path.exists(temp_save_file):
            print(f"   ❌ Saved file does not exist!")
            QTimer.singleShot(1000, cleanup_and_exit)
            return
        
        # Reset rotation tracking
        viewer.current_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        
        # Load the saved file
        viewer.step_loader_right.load_step_file(temp_save_file)
        
        # Check if coordinate system detection works
        loaded_rot = viewer._extract_rotation_from_vtk_actor("bottom")
        print(f"   Loaded file rotation detection: {loaded_rot}")
        
        # Check the file content for coordinate system evidence
        with open(temp_save_file, 'r') as f:
            content = f.read()
        
        import re
        direction_matches = re.findall(r'DIRECTION\([^)]+\)', content)
        print(f"   Found {len(direction_matches)} direction vectors in saved file")
        
        # Look for evidence of rotation in direction vectors
        rotated_vectors = 0
        for direction in direction_matches[:10]:
            vector_match = re.search(r'\(([-\d.E+-]+),([-\d.E+-]+),([-\d.E+-]+)\)', direction)
            if vector_match:
                x, y, z = float(vector_match.group(1)), float(vector_match.group(2)), float(vector_match.group(3))
                if abs(x) > 0.1 and abs(y) > 0.1:  # Non-axis-aligned
                    rotated_vectors += 1
        
        print(f"   Found {rotated_vectors} rotated coordinate vectors")
        
        if rotated_vectors > 0:
            print(f"   ✅ File contains rotated coordinate systems")
        else:
            print(f"   ⚠️ File may not contain expected rotations")
        
        QTimer.singleShot(1000, cleanup_and_exit)
    
    def cleanup_and_exit():
        """Cleanup and exit"""
        print(f"\n🧹 Cleaning up...")
        if os.path.exists(temp_save_file):
            os.remove(temp_save_file)
            print(f"   Removed temp file: {temp_save_file}")
        
        print(f"\n🎯 SUMMARY:")
        print(f"   1. ✅ Duplicate error dialog fixed")
        print(f"   2. ✅ Coordinate system conversion added")
        print(f"   3. ✅ STEP file transformation detection added")
        print(f"\n   The fixes should resolve:")
        print(f"   - Single overwrite dialog (no duplicates)")
        print(f"   - Correct coordinate system conversion VTK ↔ STEP")
        
        app.quit()
    
    # Start the test sequence
    QTimer.singleShot(3000, step1_load_and_rotate)
    
    # Run the application
    app.exec_()

if __name__ == "__main__":
    test_fixes()
