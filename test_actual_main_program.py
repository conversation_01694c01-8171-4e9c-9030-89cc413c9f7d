#!/usr/bin/env python3
"""
TEST ACTUAL MAIN PROGRAM - Directly test the running main program
This will actually instantiate and test the main program class
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import <PERSON><PERSON><PERSON><PERSON>

def test_main_program_directly():
    """Test the actual main program class directly"""
    print("🚀 TESTING ACTUAL MAIN PROGRAM DIRECTLY")
    print("=" * 60)
    
    try:
        # Set up QApplication
        app = QApplication([])
        
        # Import and create the main program
        import step_viewer_tdk_modular
        main_window = step_viewer_tdk_modular.StepViewerTDK()
        
        print("✅ Main program instantiated successfully")
        
        # Test 1: Load STEP file
        print("\n🔍 TEST 1: Loading STEP file...")
        
        filename = "debug_auto_saved.step"
        if not os.path.exists(filename):
            print("❌ Test file not found")
            return False
            
        # Simulate loading STEP file
        try:
            from step_loader import STEPLoader
            loader = STEPLoader()
            success, message = loader.load_step_file(filename)
            
            if success and loader.current_polydata:
                print(f"✅ STEP file loaded: {message}")
                
                # Load into main program's left viewer
                if hasattr(main_window, 'vtk_renderer_left'):
                    main_window.vtk_renderer_left.display_polydata(loader.current_polydata)
                    print("✅ Polydata loaded into left viewer")
                    
                    # Check if colors are applied
                    if main_window.vtk_renderer_left.step_actor:
                        mapper = main_window.vtk_renderer_left.step_actor.GetMapper()
                        if mapper.GetScalarVisibility():
                            print("✅ Colors are applied in main program")
                        else:
                            print("❌ Colors are NOT applied in main program")
                            
                        # Check color range
                        color_range = mapper.GetScalarRange()
                        print(f"Color range in main program: {color_range}")
                        
                else:
                    print("❌ vtk_renderer_left not found")
                    
            else:
                print(f"❌ STEP loading failed: {message}")
                return False
                
        except Exception as e:
            print(f"❌ STEP loading error: {e}")
            return False
        
        # Test 2: Test rotation button
        print("\n🔘 TEST 2: Testing rotation button...")
        
        try:
            # Initialize rotation tracking (same as main program)
            if not hasattr(main_window, 'model_rot_left'):
                main_window.model_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            if not hasattr(main_window, 'current_rot_left'):
                main_window.current_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                
            main_window.active_viewer = "top"  # Set active viewer
            
            print(f"Initial rotation: {main_window.current_rot_left}")
            
            # Test 3 button clicks
            for click in range(1, 4):
                print(f"\nButton click #{click}:")
                
                # Call the actual rotate_shape method
                main_window.rotate_shape('x', 15.0)
                
                current_x = main_window.current_rot_left.get('x', 0.0)
                expected_x = click * 15.0
                
                print(f"   Current X: {current_x:.1f}°")
                print(f"   Expected X: {expected_x:.1f}°")
                
                if abs(current_x - expected_x) < 0.1:
                    print(f"   ✅ Click #{click}: Correct")
                else:
                    print(f"   ❌ Click #{click}: Wrong")
                    return False
            
            print("✅ Rotation button test passed")
            
        except Exception as e:
            print(f"❌ Rotation button error: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # Test 3: Test mouse rotation detection
        print("\n🖱️ TEST 3: Testing mouse rotation detection...")
        
        try:
            # Check if mouse rotation method exists
            if hasattr(main_window, 'check_mouse_rotation'):
                print("✅ check_mouse_rotation method exists")
                
                # Test if it can be called without error
                main_window.check_mouse_rotation()
                print("✅ check_mouse_rotation method runs without error")
                
            else:
                print("❌ check_mouse_rotation method missing")
                return False
                
        except Exception as e:
            print(f"❌ Mouse rotation test error: {e}")
            return False
        
        # Test 4: Test text overlay update
        print("\n📝 TEST 4: Testing text overlay update...")
        
        try:
            # Check if text overlay method exists
            if hasattr(main_window, 'update_vtk_text_overlays'):
                print("✅ update_vtk_text_overlays method exists")
                
                # Test if it can be called without error
                main_window.update_vtk_text_overlays()
                print("✅ update_vtk_text_overlays method runs without error")
                
            else:
                print("❌ update_vtk_text_overlays method missing")
                return False
                
        except Exception as e:
            print(f"❌ Text overlay test error: {e}")
            return False
        
        print("\n🎉 ALL MAIN PROGRAM TESTS PASSED!")
        
        # Clean up
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ Main program test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the actual main program test"""
    print("TESTING ACTUAL MAIN PROGRAM")
    print("=" * 80)
    
    # Set environment for headless testing
    os.environ['QT_QPA_PLATFORM'] = 'offscreen'
    
    success = test_main_program_directly()
    
    if success:
        print("\n🎉 MAIN PROGRAM TESTS PASSED!")
        print("Both fixes are working in the actual main program.")
    else:
        print("\n❌ MAIN PROGRAM TESTS FAILED!")
        print("Fixes are not working correctly in the main program.")
    
    return success

if __name__ == "__main__":
    main()
