#!/usr/bin/env python3
"""
New Automated Rotation Save Test - Updated for Current Application
Tests the complete rotation save workflow:
1. Load STEP file into TOP viewer
2. Apply rotations (X=15°, Y=30°, Z=45°)
3. Save with OpenCASCADE transformation
4. Load saved file into BOTTOM viewer
5. Verify transformation was preserved
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the main viewer
from step_viewer_tdk_modular_fixed import StepViewerTDK

class NewAutomatedRotationSaveTest:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = StepViewerTDK()
        self.test_filename = "test_rotation_save_new.step"
        self.original_filename = None
        self.test_results = []
        
    def log_result(self, step, success, message):
        """Log test results"""
        status = "✅" if success else "❌"
        result = f"{status} {step}: {message}"
        print(result)
        self.test_results.append((step, success, message))
        
    def step1_load_file(self):
        """Step 1: Load STEP file into TOP viewer"""
        print("\n🔧 Step 1: Loading STEP file into TOP viewer...")
        
        # Find a STEP file to load
        step_files = [f for f in os.listdir('.') if f.endswith('.STEP') or f.endswith('.step')]
        if not step_files:
            self.log_result("Step 1", False, "No STEP files found in directory")
            return False
            
        self.original_filename = step_files[0]
        print(f"Loading {self.original_filename}...")
        
        try:
            # Set active viewer to TOP
            self.viewer.active_viewer = "top"
            
            # Load file using the viewer's method
            success = self.viewer.step_loader_left.load_step_file(self.original_filename)
            if success and hasattr(self.viewer.step_loader_left, 'polydata'):
                # Display in TOP viewer
                self.viewer.vtk_renderer_left.display_polydata(
                    self.viewer.step_loader_left.polydata,
                    getattr(self.viewer.step_loader_left, 'colors', None)
                )
                self.log_result("Step 1", True, f"File {self.original_filename} loaded successfully")
                return True
            else:
                self.log_result("Step 1", False, f"Failed to load {self.original_filename}")
                return False
                
        except Exception as e:
            self.log_result("Step 1", False, f"Exception loading file: {e}")
            return False
            
    def step2_apply_rotations(self):
        """Step 2: Apply rotations to the model"""
        print("\n🔧 Step 2: Applying rotations...")
        
        try:
            # Apply rotations using the viewer's rotate_shape method
            print("   - Applying X+15° rotation...")
            self.viewer.rotate_shape('x', 15.0)
            
            print("   - Applying Y+30° rotation...")
            self.viewer.rotate_shape('y', 30.0)
            
            print("   - Applying Z+45° rotation...")
            self.viewer.rotate_shape('z', 45.0)
            
            # Verify rotations were applied
            current_rot = self.viewer.model_rot_left
            expected = {'x': 15.0, 'y': 30.0, 'z': 45.0}
            
            success = all(abs(current_rot[axis] - expected[axis]) < 0.1 for axis in ['x', 'y', 'z'])
            
            if success:
                self.log_result("Step 2", True, f"Rotations applied: X={current_rot['x']:.1f}°, Y={current_rot['y']:.1f}°, Z={current_rot['z']:.1f}°")
                return True
            else:
                self.log_result("Step 2", False, f"Rotation values incorrect: {current_rot}")
                return False
                
        except Exception as e:
            self.log_result("Step 2", False, f"Failed to apply rotations: {e}")
            return False
            
    def step3_save_file(self):
        """Step 3: Save the rotated file"""
        print(f"\n🔧 Step 3: Saving rotated file as {self.test_filename}...")
        
        try:
            # Remove existing test file
            if os.path.exists(self.test_filename):
                os.remove(self.test_filename)
                
            # Get original file size
            original_size = os.path.getsize(self.original_filename)
            
            # Save using OpenCASCADE transformation
            loader = self.viewer.step_loader_left
            delta_pos = self.viewer.current_pos_left
            delta_rot = self.viewer.model_rot_left
            
            success = self.viewer._save_step_opencascade_transform(
                self.test_filename, loader, delta_pos, delta_rot
            )
            
            if success and os.path.exists(self.test_filename):
                saved_size = os.path.getsize(self.test_filename)
                self.log_result("Step 3", True, f"File saved: {saved_size} bytes (original: {original_size} bytes)")
                
                # Check if transformation was applied (file size should change)
                if saved_size != original_size:
                    print("   ✅ File size changed - OpenCASCADE transformation applied")
                else:
                    print("   ⚠️  File size unchanged - may be simple copy")
                    
                return True
            else:
                self.log_result("Step 3", False, "Save operation failed")
                return False
                
        except Exception as e:
            self.log_result("Step 3", False, f"Save failed: {e}")
            return False
            
    def step4_load_saved_file(self):
        """Step 4: Load saved file into BOTTOM viewer"""
        print(f"\n🔧 Step 4: Loading saved file into BOTTOM viewer...")
        
        if not os.path.exists(self.test_filename):
            self.log_result("Step 4", False, f"Saved file {self.test_filename} not found")
            return False
            
        try:
            # Set active viewer to BOTTOM
            self.viewer.active_viewer = "bottom"
            
            # Load saved file
            success = self.viewer.step_loader_right.load_step_file(self.test_filename)
            if success and hasattr(self.viewer.step_loader_right, 'polydata'):
                # Display in BOTTOM viewer
                self.viewer.vtk_renderer_right.display_polydata(
                    self.viewer.step_loader_right.polydata,
                    getattr(self.viewer.step_loader_right, 'colors', None)
                )
                self.log_result("Step 4", True, "Saved file loaded successfully into BOTTOM viewer")
                return True
            else:
                self.log_result("Step 4", False, "Failed to load saved file")
                return False
                
        except Exception as e:
            self.log_result("Step 4", False, f"Failed to load saved file: {e}")
            return False
            
    def step5_verify_transformation(self):
        """Step 5: Verify the transformation was preserved"""
        print("\n🔧 Step 5: Verifying transformation preservation...")
        
        try:
            # Compare visual results by checking if both viewers have models loaded
            top_has_model = (hasattr(self.viewer.vtk_renderer_left, 'step_actor') and 
                           self.viewer.vtk_renderer_left.step_actor is not None) or \
                          (hasattr(self.viewer.vtk_renderer_left, 'multi_actors') and 
                           len(self.viewer.vtk_renderer_left.multi_actors) > 0)
                           
            bottom_has_model = (hasattr(self.viewer.vtk_renderer_right, 'step_actor') and 
                              self.viewer.vtk_renderer_right.step_actor is not None) or \
                             (hasattr(self.viewer.vtk_renderer_right, 'multi_actors') and 
                              len(self.viewer.vtk_renderer_right.multi_actors) > 0)
            
            if top_has_model and bottom_has_model:
                self.log_result("Step 5", True, "Both viewers have models loaded - transformation test complete")
                
                # Additional verification: check file properties
                original_size = os.path.getsize(self.original_filename)
                saved_size = os.path.getsize(self.test_filename)
                
                print(f"   Original file: {original_size:,} bytes")
                print(f"   Saved file: {saved_size:,} bytes")
                print(f"   Size difference: {saved_size - original_size:,} bytes")
                
                if saved_size > original_size:
                    print("   ✅ Saved file is larger - likely contains transformation data")
                elif saved_size == original_size:
                    print("   ⚠️  Files are same size - may be simple copy")
                else:
                    print("   ❓ Saved file is smaller - unexpected")
                    
                return True
            else:
                self.log_result("Step 5", False, f"Missing models - TOP: {top_has_model}, BOTTOM: {bottom_has_model}")
                return False
                
        except Exception as e:
            self.log_result("Step 5", False, f"Verification failed: {e}")
            return False
            
    def run_test(self):
        """Run the complete test sequence"""
        print("🚀 NEW AUTOMATED ROTATION SAVE TEST")
        print("=" * 60)
        
        # Show viewer
        self.viewer.show()
        
        # Wait for initialization
        time.sleep(2)
        
        # Run test steps
        steps = [
            self.step1_load_file,
            self.step2_apply_rotations,
            self.step3_save_file,
            self.step4_load_saved_file,
            self.step5_verify_transformation
        ]
        
        results = []
        for step_func in steps:
            result = step_func()
            results.append(result)
            if not result:
                break
            time.sleep(1)
        
        # Print summary
        self.print_summary()
        
        return all(results)
        
    def print_summary(self):
        """Print test summary"""
        print("\n" + "=" * 60)
        print("📊 NEW AUTOMATED TEST SUMMARY")
        print("=" * 60)
        
        for step, success, message in self.test_results:
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{status} - {step}: {message}")
            
        passed = sum(1 for _, success, _ in self.test_results if success)
        total = len(self.test_results)
        
        print(f"\n📈 Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 ALL TESTS PASSED! OpenCASCADE rotation save is working!")
        else:
            print("⚠️  SOME TESTS FAILED. Check the issues above.")

if __name__ == "__main__":
    test = NewAutomatedRotationSaveTest()
    success = test.run_test()
    
    # Keep application running for visual inspection
    print("\n🔧 Application will stay open for manual verification...")
    print("   Close the window when done inspecting.")
    
    test.app.exec_()
    sys.exit(0 if success else 1)
