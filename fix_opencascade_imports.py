#!/usr/bin/env python3
"""
Fix OpenCASCADE import issues and test the correct import paths
"""

print("🔧 FIXING OpenCASCADE Import Issues")
print("=" * 50)

# Test the correct import patterns for pythonocc-core 7.8.1.1
print("🔍 Testing correct import patterns...")

# Method 1: Try the module-based imports
try:
    from OCC.Core.STEPControl import STEPControl_Reader, STEPControl_Writer
    print("✅ STEPControl_Reader/Writer: SUCCESS (from STEPControl module)")
    step_reader_available = True
except Exception as e:
    print(f"❌ STEPControl from module: FAILED - {e}")
    step_reader_available = False

# Method 2: Try direct imports
try:
    from OCC.Core import gp_Trsf, gp_Pnt, gp_Vec, gp_Ax1, gp_Dir
    print("✅ Geometry primitives (gp_*): SUCCESS")
    geometry_available = True
except Exception as e:
    print(f"❌ Geometry primitives: FAILED - {e}")
    geometry_available = False

# Method 3: Try transformation imports
try:
    from OCC.Core import BRepBuilderAPI_Transform
    print("✅ BRepBuilderAPI_Transform: SUCCESS")
    transform_available = True
except Exception as e:
    print(f"❌ BRepBuilderAPI_Transform: FAILED - {e}")
    transform_available = False

print()
print("🔧 Creating corrected import functions...")

def get_step_reader():
    """Get STEP reader with correct import path"""
    try:
        from OCC.Core.STEPControl import STEPControl_Reader
        return STEPControl_Reader()
    except:
        try:
            from OCC.Core import STEPControl_Reader
            return STEPControl_Reader()
        except Exception as e:
            print(f"❌ Cannot create STEP reader: {e}")
            return None

def get_step_writer():
    """Get STEP writer with correct import path"""
    try:
        from OCC.Core.STEPControl import STEPControl_Writer
        return STEPControl_Writer()
    except:
        try:
            from OCC.Core import STEPControl_Writer
            return STEPControl_Writer()
        except Exception as e:
            print(f"❌ Cannot create STEP writer: {e}")
            return None

def get_transformation():
    """Get transformation object with correct import path"""
    try:
        from OCC.Core import gp_Trsf
        return gp_Trsf()
    except Exception as e:
        print(f"❌ Cannot create transformation: {e}")
        return None

# Test the functions
print("🧪 Testing corrected functions...")
reader = get_step_reader()
writer = get_step_writer()
transform = get_transformation()

if reader:
    print("✅ STEP Reader: Working")
else:
    print("❌ STEP Reader: Failed")

if writer:
    print("✅ STEP Writer: Working")
else:
    print("❌ STEP Writer: Failed")

if transform:
    print("✅ Transformation: Working")
else:
    print("❌ Transformation: Failed")

print()
if reader and writer and transform:
    print("🎉 SUCCESS: All critical OpenCASCADE components are working!")
    print("   The rotation save functionality can be fixed!")
else:
    print("❌ FAILURE: Some OpenCASCADE components are still missing")
    print("   Need to investigate alternative approaches")

print()
print("🔧 Fix complete!")
