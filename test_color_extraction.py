#!/usr/bin/env python3

print("TESTING COLOR EXTRACTION")

from step_loader import STEP<PERSON>oader

loader = STEPLoader()
success, msg = loader.load_step_file('SOIC16P127_1270X940X610L89X51.STEP')

if success and loader.current_polydata:
    polydata = loader.current_polydata
    colors_array = polydata.GetCellData().GetScalars("Colors")
    
    if colors_array:
        # Count colors
        color_counts = {}
        for i in range(colors_array.GetNumberOfTuples()):
            r = int(colors_array.GetComponent(i, 0))
            g = int(colors_array.GetComponent(i, 1))
            b = int(colors_array.GetComponent(i, 2))
            color = (r, g, b)
            color_counts[color] = color_counts.get(color, 0) + 1
        
        print("Display colors:")
        for color, count in color_counts.items():
            print(f"  RGB{color}: {count} cells")
        
        # Check if we have STEP file colors
        expected_light = (192, 192, 192)  # 0.75294117647059 * 255
        expected_dark = (64, 64, 64)      # 0.25098039215686 * 255
        
        if expected_light in color_counts or expected_dark in color_counts:
            print("SUCCESS: STEP file colors found")
        else:
            print("FAILURE: STEP file colors missing")
            print("Expected light silver RGB(192, 192, 192)")
            print("Expected dark silver RGB(64, 64, 64)")
    else:
        print("FAILURE: No colors in polydata")
else:
    print("FAILURE: STEP loading failed")

print("COLOR EXTRACTION TEST COMPLETE")
