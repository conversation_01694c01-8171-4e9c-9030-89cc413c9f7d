=== DEBU<PERSON><PERSON><PERSON> STEP COLOR TO GEOMETRY MAPPING ===
Looking for color-to-surface relationships...
Color entry: #8 = COLOUR_RGB ( '',0.75294117647059, 0.75294117647059, 0.75294117647059 ) ;
Color entry: #487 = COLOUR_RGB ( '',0.75294117647059, 0.75294117647059, 0.75294117647059 ) ;
Color entry: #961 = COLOUR_RGB ( '',0.75294117647059, 0.75294117647059, 0.75294117647059 ) ;
Color entry: #1435 = COLOUR_RGB ( '',0.75294117647059, 0.75294117647059, 0.75294117647059 ) ;
Color entry: #1909 = COLOUR_RGB ( '',0.75294117647059, 0.75294117647059, 0.75294117647059 ) ;
Color entry: #2383 = COLOUR_RGB ( '',0.75294117647059, 0.75294117647059, 0.75294117647059 ) ;
Color entry: #2857 = COLOUR_RGB ( '',0.75294117647059, 0.75294117647059, 0.75294117647059 ) ;
Color entry: #3331 = COLOUR_RGB ( '',0.75294117647059, 0.75294117647059, 0.75294117647059 ) ;
Color entry: #3805 = COLOUR_RGB ( '',0.75294117647059, 0.75294117647059, 0.75294117647059 ) ;
Color entry: #4279 = COLOUR_RGB ( '',0.75294117647059, 0.75294117647059, 0.75294117647059 ) ;
Color entry: #4753 = COLOUR_RGB ( '',0.75294117647059, 0.75294117647059, 0.75294117647059 ) ;
Color entry: #5227 = COLOUR_RGB ( '',0.75294117647059, 0.75294117647059, 0.75294117647059 ) ;
Color entry: #5701 = COLOUR_RGB ( '',0.75294117647059, 0.75294117647059, 0.75294117647059 ) ;
Color entry: #6175 = COLOUR_RGB ( '',0.75294117647059, 0.75294117647059, 0.75294117647059 ) ;
Color entry: #6649 = COLOUR_RGB ( '',0.75294117647059, 0.75294117647059, 0.75294117647059 ) ;
Color entry: #7123 = COLOUR_RGB ( '',0.75294117647059, 0.75294117647059, 0.75294117647059 ) ;
Color entry: #7597 = COLOUR_RGB ( '',0.75294117647059, 0.75294117647059, 0.75294117647059 ) ;
Color entry: #7982 = COLOUR_RGB ( '',0.25098039215686, 0.25098039215686, 0.25098039215686 ) ;
Color entry: #8034 = COLOUR_RGB ( '',0.25098039215686, 0.25098039215686, 0.25098039215686 ) ;

Found 19 color entries
Fill style: #9 = FILL_AREA_STYLE_COLOUR ( '', #8 ) ;
Fill style: #488 = FILL_AREA_STYLE_COLOUR ( '', #487 ) ;
Fill style: #962 = FILL_AREA_STYLE_COLOUR ( '', #961 ) ;
Fill style: #1436 = FILL_AREA_STYLE_COLOUR ( '', #1435 ) ;
Fill style: #1910 = FILL_AREA_STYLE_COLOUR ( '', #1909 ) ;
Fill style: #2384 = FILL_AREA_STYLE_COLOUR ( '', #2383 ) ;
Fill style: #2858 = FILL_AREA_STYLE_COLOUR ( '', #2857 ) ;
Fill style: #3332 = FILL_AREA_STYLE_COLOUR ( '', #3331 ) ;
Fill style: #3806 = FILL_AREA_STYLE_COLOUR ( '', #3805 ) ;
Fill style: #4280 = FILL_AREA_STYLE_COLOUR ( '', #4279 ) ;
Fill style: #4754 = FILL_AREA_STYLE_COLOUR ( '', #4753 ) ;
Fill style: #5228 = FILL_AREA_STYLE_COLOUR ( '', #5227 ) ;
Fill style: #5702 = FILL_AREA_STYLE_COLOUR ( '', #5701 ) ;
Fill style: #6176 = FILL_AREA_STYLE_COLOUR ( '', #6175 ) ;
Fill style: #6650 = FILL_AREA_STYLE_COLOUR ( '', #6649 ) ;
Fill style: #7124 = FILL_AREA_STYLE_COLOUR ( '', #7123 ) ;
Fill style: #7598 = FILL_AREA_STYLE_COLOUR ( '', #7597 ) ;
Fill style: #7983 = FILL_AREA_STYLE_COLOUR ( '', #7982 ) ;
Fill style: #8035 = FILL_AREA_STYLE_COLOUR ( '', #8034 ) ;

Found 19 fill style entries
Surface style: #11 = SURFACE_STYLE_FILL_AREA ( #10 ) ;
Surface style: #13 = SURFACE_STYLE_USAGE ( .BOTH. , #12 ) ;
Surface style: #490 = SURFACE_STYLE_FILL_AREA ( #489 ) ;
Surface style: #492 = SURFACE_STYLE_USAGE ( .BOTH. , #491 ) ;
Surface style: #964 = SURFACE_STYLE_FILL_AREA ( #963 ) ;
Surface style: #966 = SURFACE_STYLE_USAGE ( .BOTH. , #965 ) ;
Surface style: #1438 = SURFACE_STYLE_FILL_AREA ( #1437 ) ;
Surface style: #1440 = SURFACE_STYLE_USAGE ( .BOTH. , #1439 ) ;
Surface style: #1912 = SURFACE_STYLE_FILL_AREA ( #1911 ) ;
Surface style: #1914 = SURFACE_STYLE_USAGE ( .BOTH. , #1913 ) ;
Surface style: #2386 = SURFACE_STYLE_FILL_AREA ( #2385 ) ;
Surface style: #2388 = SURFACE_STYLE_USAGE ( .BOTH. , #2387 ) ;
Surface style: #2860 = SURFACE_STYLE_FILL_AREA ( #2859 ) ;
Surface style: #2862 = SURFACE_STYLE_USAGE ( .BOTH. , #2861 ) ;
Surface style: #3334 = SURFACE_STYLE_FILL_AREA ( #3333 ) ;
Surface style: #3336 = SURFACE_STYLE_USAGE ( .BOTH. , #3335 ) ;
Surface style: #3808 = SURFACE_STYLE_FILL_AREA ( #3807 ) ;
Surface style: #3810 = SURFACE_STYLE_USAGE ( .BOTH. , #3809 ) ;
Surface style: #4282 = SURFACE_STYLE_FILL_AREA ( #4281 ) ;
Surface style: #4284 = SURFACE_STYLE_USAGE ( .BOTH. , #4283 ) ;
Surface style: #4756 = SURFACE_STYLE_FILL_AREA ( #4755 ) ;
Surface style: #4758 = SURFACE_STYLE_USAGE ( .BOTH. , #4757 ) ;
Surface style: #5230 = SURFACE_STYLE_FILL_AREA ( #5229 ) ;
Surface style: #5232 = SURFACE_STYLE_USAGE ( .BOTH. , #5231 ) ;
Surface style: #5704 = SURFACE_STYLE_FILL_AREA ( #5703 ) ;
Surface style: #5706 = SURFACE_STYLE_USAGE ( .BOTH. , #5705 ) ;
Surface style: #6178 = SURFACE_STYLE_FILL_AREA ( #6177 ) ;
Surface style: #6180 = SURFACE_STYLE_USAGE ( .BOTH. , #6179 ) ;
Surface style: #6652 = SURFACE_STYLE_FILL_AREA ( #6651 ) ;
Surface style: #6654 = SURFACE_STYLE_USAGE ( .BOTH. , #6653 ) ;
Surface style: #7126 = SURFACE_STYLE_FILL_AREA ( #7125 ) ;
Surface style: #7128 = SURFACE_STYLE_USAGE ( .BOTH. , #7127 ) ;
Surface style: #7600 = SURFACE_STYLE_FILL_AREA ( #7599 ) ;
Surface style: #7602 = SURFACE_STYLE_USAGE ( .BOTH. , #7601 ) ;
Surface style: #7985 = SURFACE_STYLE_FILL_AREA ( #7984 ) ;
Surface style: #7987 = SURFACE_STYLE_USAGE ( .BOTH. , #7986 ) ;
Surface style: #8037 = SURFACE_STYLE_FILL_AREA ( #8036 ) ;
Surface style: #8039 = SURFACE_STYLE_USAGE ( .BOTH. , #8038 ) ;

Found 38 surface style entries

Looking for geometry references...
Found 78 geometry entries with color references:
  #11 = SURFACE_STYLE_FILL_AREA ( #10 ) ;
  #12 = SURFACE_SIDE_STYLE ( '',( #11 ) ) ;
  #13 = SURFACE_STYLE_USAGE ( .BOTH. , #12 ) ;
  #490 = SURFACE_STYLE_FILL_AREA ( #489 ) ;
  #491 = SURFACE_SIDE_STYLE ( '',( #490 ) ) ;

=== ANALYSIS COMPLETE ===
The STEP file has a complex color-to-geometry mapping
Need to parse this structure to apply colors correctly
