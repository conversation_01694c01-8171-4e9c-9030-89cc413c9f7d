#!/usr/bin/env python3
"""
Test different OpenCASCADE import paths to find the correct one
"""

print("Testing OpenCASCADE import paths...")

# Test 1: Direct module import
try:
    from OCC.Core.STEPControl_Reader import STEPControl_Reader
    print("✅ Method 1: from OCC.Core.STEPControl_Reader import STEPControl_Reader")
    method1_works = True
except ImportError as e:
    print(f"❌ Method 1 failed: {e}")
    method1_works = False

# Test 2: Import from Core
try:
    from OCC.Core import STEPControl_Reader
    reader = STEPControl_Reader.STEPControl_Reader()
    print("✅ Method 2: from OCC.Core import STEPControl_Reader")
    method2_works = True
except ImportError as e:
    print(f"❌ Method 2 failed: {e}")
    method2_works = False
except AttributeError as e:
    print(f"❌ Method 2 attribute error: {e}")
    method2_works = False

# Test 3: Check what's available in OCC.Core
try:
    from OCC import Core
    print("✅ OCC.Core module available")
    print("Available modules in OCC.Core:")
    import pkgutil
    for importer, modname, ispkg in pkgutil.iter_modules(Core.__path__):
        if 'STEP' in modname:
            print(f"  - {modname}")
except ImportError as e:
    print(f"❌ OCC.Core not available: {e}")

# Test 4: Try specific STEP modules
step_modules = [
    'STEPControl_Reader',
    'STEPCAFControl_Reader', 
    'STEP',
    'STEPFile_Read'
]

for module in step_modules:
    try:
        exec(f"from OCC.Core.{module} import {module}")
        print(f"✅ Method 4: from OCC.Core.{module} import {module}")
    except ImportError as e:
        print(f"❌ Method 4 {module} failed: {e}")

print("\nTesting complete!")
