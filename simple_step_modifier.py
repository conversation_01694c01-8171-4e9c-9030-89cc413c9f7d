#!/usr/bin/env python3
"""
Simple STEP file modifier - just read, change position/rotation, write back out
"""

import re
import math

class SimpleSTEPModifier:
    def __init__(self):
        self.step_content = ""
        
    def load_step_file(self, filename):
        """Load STEP file content"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                self.step_content = f.read()
            print(f"✅ Loaded STEP file: {filename}")
            return True
        except Exception as e:
            print(f"❌ Failed to load STEP file: {e}")
            return False
    
    def save_step_file(self, filename, rotation_values=None):
        """Save modified STEP file content with optional rotation values stored as comments"""
        try:
            # Add rotation values as comments if provided
            if rotation_values:
                x_rot = rotation_values.get('x', 0.0)
                y_rot = rotation_values.get('y', 0.0)
                z_rot = rotation_values.get('z', 0.0)

                # Add comment after the HEADER section
                rotation_comment = f"/* ROTATION_VALUES: X={x_rot} Y={y_rot} Z={z_rot} */\n"

                # Find the end of HEADER section and insert/replace comment
                # First, remove any existing rotation comment
                import re
                self.step_content = re.sub(r'/\* ROTATION_VALUES:.*?\*/', '', self.step_content)

                # Find ENDSEC; and DATA; separately to handle cases where there might be content between them
                endsec_pos = self.step_content.find('ENDSEC;')
                data_pos = self.step_content.find('DATA;')

                if endsec_pos != -1 and data_pos != -1:
                    # Insert after ENDSEC; and before DATA;
                    insert_pos = endsec_pos + len('ENDSEC;\n')
                    self.step_content = (self.step_content[:insert_pos] +
                                       rotation_comment +
                                       self.step_content[insert_pos:])
                    print(f"🎯 ADDED ROTATION COMMENT: X={x_rot} Y={y_rot} Z={z_rot}")

            # DEBUG: Show the actual STEP file content around the placement BEFORE saving
            print(f"🔍 DEBUG: Checking STEP file content before saving to {filename}...")
            lines = self.step_content.split('\n')

            # Find and show the main placement line (DYNAMIC)
            for i, line in enumerate(lines):
                if 'AXIS2_PLACEMENT_3D' in line and '#11' in line:  # Look for main coordinate system
                    print(f"🔍 DEBUG: Found main placement line {i+1}: {line}")
                    break

            # Show the coordinate system references (DYNAMIC)
            for line_num, line in enumerate(lines):
                if '#12=' in line and 'CARTESIAN_POINT' in line:  # Origin point
                    print(f"🔍 DEBUG: BEFORE SAVE - Origin point line {line_num+1}: {line}")
                elif '#13=' in line and 'DIRECTION' in line:  # Z direction
                    print(f"🔍 DEBUG: BEFORE SAVE - Z direction line {line_num+1}: {line}")
                elif '#14=' in line and 'DIRECTION' in line:  # X direction
                    print(f"🔍 DEBUG: BEFORE SAVE - X direction line {line_num+1}: {line}")

            # Save the file
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.step_content)
            print(f"✅ Saved STEP file: {filename}")

            # DEBUG: Verify the saved file by reading it back
            print(f"🔍 DEBUG: Verifying saved file content by reading back...")
            with open(filename, 'r', encoding='utf-8') as f:
                saved_content = f.read()

            saved_lines = saved_content.split('\n')
            for line_num, line in enumerate(saved_lines):
                if '#12=' in line and 'CARTESIAN_POINT' in line:  # Origin point
                    print(f"🔍 DEBUG: AFTER SAVE - Origin point line {line_num+1}: {line}")
                elif '#13=' in line and 'DIRECTION' in line:  # Z direction
                    print(f"🔍 DEBUG: AFTER SAVE - Z direction line {line_num+1}: {line}")
                elif '#14=' in line and 'DIRECTION' in line:  # X direction
                    print(f"🔍 DEBUG: AFTER SAVE - X direction line {line_num+1}: {line}")

            return True
        except Exception as e:
            print(f"❌ Failed to save STEP file: {e}")
            return False
    
    def modify_placement(self, new_x, new_y, new_z, new_rx, new_ry, new_rz):
        """
        Modify placement in STEP file
        new_x, new_y, new_z: new position in mm
        new_rx, new_ry, new_rz: new rotation in degrees
        """
        print(f"🔧 Modifying placement to:")
        print(f"   Position: X={new_x:.3f}mm Y={new_y:.3f}mm Z={new_z:.3f}mm")
        print(f"   Rotation: X={new_rx:.3f}° Y={new_ry:.3f}° Z={new_rz:.3f}°")

        # DEBUG: Show original content before modification (DYNAMIC)
        print(f"🔍 DEBUG: ORIGINAL STEP file content before modification:")
        lines = self.step_content.split('\n')
        for line_num, line in enumerate(lines):
            if '#12=' in line and 'CARTESIAN_POINT' in line:  # Origin point
                print(f"🔍 DEBUG: ORIGINAL - Origin point line {line_num+1}: {line}")
            elif '#13=' in line and 'DIRECTION' in line:  # Z direction
                print(f"🔍 DEBUG: ORIGINAL - Z direction line {line_num+1}: {line}")
            elif '#14=' in line and 'DIRECTION' in line:  # X direction
                print(f"🔍 DEBUG: ORIGINAL - X direction line {line_num+1}: {line}")
        
        # Convert rotation to radians
        rx_rad = math.radians(new_rx)
        ry_rad = math.radians(new_ry)
        rz_rad = math.radians(new_rz)
        
        # Create rotation matrix (XYZ order to match VTK)
        # VTK applies: RotateX -> RotateY -> RotateZ
        cos_x, sin_x = math.cos(rx_rad), math.sin(rx_rad)
        cos_y, sin_y = math.cos(ry_rad), math.sin(ry_rad)
        cos_z, sin_z = math.cos(rz_rad), math.sin(rz_rad)

        # Combined rotation matrix (XYZ order)
        r11 = cos_y * cos_z
        r12 = -cos_y * sin_z
        r13 = sin_y
        r21 = cos_x * sin_z + sin_x * sin_y * cos_z
        r22 = cos_x * cos_z - sin_x * sin_y * sin_z
        r23 = -sin_x * cos_y
        r31 = sin_x * sin_z - cos_x * sin_y * cos_z
        r32 = sin_x * cos_z + cos_x * sin_y * sin_z
        r33 = cos_x * cos_y
        
        # Z-axis (3rd column of rotation matrix)
        z_axis = [r13, r23, r33]
        # X-axis (1st column of rotation matrix)
        x_axis = [r11, r21, r31]
        
        print(f"   Z-axis: ({z_axis[0]:.6f}, {z_axis[1]:.6f}, {z_axis[2]:.6f})")
        print(f"   X-axis: ({x_axis[0]:.6f}, {x_axis[1]:.6f}, {x_axis[2]:.6f})")
        
        # DYNAMIC COORDINATE SYSTEM FIX: Find the actual coordinate system that controls the entire model
        # Look for ADVANCED_BREP_SHAPE_REPRESENTATION which contains the main coordinate system

        # Find the ADVANCED_BREP_SHAPE_REPRESENTATION line and extract the coordinate system ID
        # Format: ADVANCED_BREP_SHAPE_REPRESENTATION ( 'name', ( objects... ), #coordinate_system_id )
        brep_pattern = r'ADVANCED_BREP_SHAPE_REPRESENTATION\s*\([^,]+,\s*\([^)]+\),\s*#(\d+)\s*\)'
        brep_match = re.search(brep_pattern, self.step_content, re.IGNORECASE)

        if not brep_match:
            print("❌ Could not find ADVANCED_BREP_SHAPE_REPRESENTATION")
            return

        # This is actually the geometric representation context, not the coordinate system
        # We need to find the AXIS2_PLACEMENT_3D that's listed in the objects array

        # Find the AXIS2_PLACEMENT_3D that appears FIRST in the objects list
        # Look for the pattern: ADVANCED_BREP_SHAPE_REPRESENTATION ( 'name', ( #axis_id, ... ), #context )
        axis_in_brep_pattern = r'ADVANCED_BREP_SHAPE_REPRESENTATION\s*\([^,]+,\s*\(\s*#(\d+)'
        axis_in_brep_match = re.search(axis_in_brep_pattern, self.step_content, re.IGNORECASE)

        if not axis_in_brep_match:
            print("❌ Could not find first ID in ADVANCED_BREP_SHAPE_REPRESENTATION")
            return

        # Get the first ID and verify it's an AXIS2_PLACEMENT_3D
        first_id = axis_in_brep_match.group(1)

        # Check if this ID is actually an AXIS2_PLACEMENT_3D
        axis_check_pattern = f"#{first_id}\\s*=\\s*AXIS2_PLACEMENT_3D"
        if re.search(axis_check_pattern, self.step_content, re.IGNORECASE):
            axis_placement_id = first_id
        else:
            print(f"❌ First ID #{first_id} is not an AXIS2_PLACEMENT_3D, searching for any AXIS2_PLACEMENT_3D...")
            # Fallback: find any AXIS2_PLACEMENT_3D in the file
            fallback_pattern = r'#(\d+)\s*=\s*AXIS2_PLACEMENT_3D'
            fallback_match = re.search(fallback_pattern, self.step_content, re.IGNORECASE)
            if fallback_match:
                axis_placement_id = fallback_match.group(1)
                print(f"🔧 Using fallback AXIS2_PLACEMENT_3D: #{axis_placement_id}")
            else:
                print("❌ No AXIS2_PLACEMENT_3D found in file")
                return
        print(f"🔍 Found main coordinate system: #{axis_placement_id}")

        # Find the AXIS2_PLACEMENT_3D definition - FIXED REGEX PATTERN
        # AXIS2_PLACEMENT_3D can have 3 or 4 parameters: ('name', point, z_dir, x_dir) or ('name', point, z_dir)
        axis_pattern = f"#{axis_placement_id}\\s*=\\s*AXIS2_PLACEMENT_3D\\s*\\([^,]+,\\s*#(\\d+)\\s*,\\s*#(\\d+)\\s*(?:,\\s*#(\\d+))?\\s*\\)"
        axis_match = re.search(axis_pattern, self.step_content, re.IGNORECASE)

        if not axis_match:
            print(f"❌ Could not find AXIS2_PLACEMENT_3D definition for #{axis_placement_id}")
            print(f"🔍 DEBUG: Looking for pattern: {axis_pattern}")
            # Try to find any AXIS2_PLACEMENT_3D with this ID for debugging
            debug_pattern = f"#{axis_placement_id}\\s*=\\s*AXIS2_PLACEMENT_3D.*"
            debug_match = re.search(debug_pattern, self.step_content, re.IGNORECASE)
            if debug_match:
                print(f"🔍 DEBUG: Found AXIS2_PLACEMENT_3D line: {debug_match.group(0)}")
            return

        point_id = axis_match.group(1)    # CARTESIAN_POINT for position
        z_dir_id = axis_match.group(2)    # DIRECTION for Z-axis
        x_dir_id = axis_match.group(3) if axis_match.group(3) else None    # DIRECTION for X-axis (optional)

        print(f"🔧 DYNAMIC COORDINATE SYSTEM FIX: Found main coordinate system that controls entire model:")
        print(f"   Main AXIS2_PLACEMENT_3D: #{axis_placement_id}")
        print(f"   Root Position: #{point_id} (CARTESIAN_POINT)")
        print(f"   Root Z direction: #{z_dir_id} (DIRECTION)")
        if x_dir_id:
            print(f"   Root X direction: #{x_dir_id} (DIRECTION)")
        else:
            print(f"   Root X direction: None (using default)")

        print(f"🔧 Modifying coordinate system entities:")
        if x_dir_id:
            print(f"   Root Point: #{point_id}, Root Z-dir: #{z_dir_id}, Root X-dir: #{x_dir_id}")
        else:
            print(f"   Root Point: #{point_id}, Root Z-dir: #{z_dir_id}, Root X-dir: None")
        
        modifications = 0
        
        # 1. Modify the origin point
        point_pattern = f"#{point_id}\\s*=\\s*CARTESIAN_POINT\\s*\\([^;]+\\)\\s*;"
        point_match = re.search(point_pattern, self.step_content)
        if point_match:
            old_point_line = point_match.group(0)
            # Extract the name from the old line
            name_match = re.search(r"'([^']*)'", old_point_line)
            point_name = name_match.group(1) if name_match else ''
            
            new_point_line = f"#{point_id} = CARTESIAN_POINT ( '{point_name}',  ( {new_x:.15f}, {new_y:.15f}, {new_z:.15f} ) ) ;"
            self.step_content = self.step_content.replace(old_point_line, new_point_line)
            print(f"   ✅ Updated origin point #{point_id}")
            modifications += 1
        
        # 2. Modify the Z direction
        z_dir_pattern = f"#{z_dir_id}\\s*=\\s*DIRECTION\\s*\\([^;]+\\)\\s*;"
        z_dir_match = re.search(z_dir_pattern, self.step_content)
        if z_dir_match:
            old_z_line = z_dir_match.group(0)
            # Extract the name from the old line
            name_match = re.search(r"'([^']*)'", old_z_line)
            z_name = name_match.group(1) if name_match else ''
            
            new_z_line = f"#{z_dir_id} = DIRECTION ( '{z_name}',  ( {z_axis[0]:.15f}, {z_axis[1]:.15f}, {z_axis[2]:.15f} ) ) ;"
            self.step_content = self.step_content.replace(old_z_line, new_z_line)
            print(f"   ✅ Updated Z direction #{z_dir_id}")
            modifications += 1
        
        # 3. Modify the X direction (if it exists)
        if x_dir_id:
            x_dir_pattern = f"#{x_dir_id}\\s*=\\s*DIRECTION\\s*\\([^;]+\\)\\s*;"
            x_dir_match = re.search(x_dir_pattern, self.step_content)
            if x_dir_match:
                old_x_line = x_dir_match.group(0)
                # Extract the name from the old line
                name_match = re.search(r"'([^']*)'", old_x_line)
                x_name = name_match.group(1) if name_match else ''

                new_x_line = f"#{x_dir_id} = DIRECTION ( '{x_name}',  ( {x_axis[0]:.15f}, {x_axis[1]:.15f}, {x_axis[2]:.15f} ) ) ;"
                self.step_content = self.step_content.replace(old_x_line, new_x_line)
                print(f"   ✅ Updated X direction #{x_dir_id}")
                modifications += 1
            else:
                print(f"   ⚠️ Could not find X direction #{x_dir_id} definition")
        else:
            print(f"   ℹ️ No X direction specified (using default X-axis)")
            # When no X direction is specified, STEP uses default (1,0,0) which is fine
        
        print(f"✅ Made {modifications} modifications")
        return modifications > 0

    def transform_geometry_coordinates(self, transform_matrix):
        """
        Transform all CARTESIAN_POINT coordinates in the STEP file using the VTK transformation matrix
        This actually moves the geometry, not just the coordinate system
        """
        print(f"🔧 Transforming all CARTESIAN_POINT coordinates in STEP file")

        # Find all CARTESIAN_POINT entries
        point_pattern = r'(#\d+\s*=\s*CARTESIAN_POINT\s*\(\s*\'[^\']*\'\s*,\s*\(\s*)([-\d.E+-]+)\s*,\s*([-\d.E+-]+)\s*,\s*([-\d.E+-]+)(\s*\)\s*\)\s*;)'

        def transform_point(match):
            prefix = match.group(1)
            x = float(match.group(2))
            y = float(match.group(3))
            z = float(match.group(4))
            suffix = match.group(5)

            # Apply VTK transformation matrix
            # VTK matrices are 4x4 and stored in column-major order
            # For point transformation: [x', y', z', 1] = Matrix * [x, y, z, 1]
            x_new = (transform_matrix.GetElement(0, 0) * x +
                     transform_matrix.GetElement(0, 1) * y +
                     transform_matrix.GetElement(0, 2) * z +
                     transform_matrix.GetElement(0, 3))

            y_new = (transform_matrix.GetElement(1, 0) * x +
                     transform_matrix.GetElement(1, 1) * y +
                     transform_matrix.GetElement(1, 2) * z +
                     transform_matrix.GetElement(1, 3))

            z_new = (transform_matrix.GetElement(2, 0) * x +
                     transform_matrix.GetElement(2, 1) * y +
                     transform_matrix.GetElement(2, 2) * z +
                     transform_matrix.GetElement(2, 3))

            return f"{prefix}{x_new:.15f}, {y_new:.15f}, {z_new:.15f}{suffix}"

        # Count matches before transformation
        matches = list(re.finditer(point_pattern, self.step_content))
        print(f"🔧 Found {len(matches)} CARTESIAN_POINT entries to transform")

        if len(matches) == 0:
            print("❌ No CARTESIAN_POINT entries found")
            return False

        # Apply transformation to all points
        self.step_content = re.sub(point_pattern, transform_point, self.step_content)

        print(f"✅ Transformed {len(matches)} CARTESIAN_POINT coordinates")
        return True

# Test code removed - this module is now used only as an import
