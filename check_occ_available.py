#!/usr/bin/env python3

import os
import sys

print("Checking what OCC modules are actually available...")

try:
    import OCC
    print(f"OCC base module: {OCC}")
    
    import OCC.Core
    core_dir = os.path.dirname(OCC.Core.__file__)
    print(f"OCC.Core directory: {core_dir}")
    
    # List all available modules
    modules = [f[:-3] for f in os.listdir(core_dir) if f.endswith('.py') and not f.startswith('__')]
    print(f"Available modules: {len(modules)}")
    
    # Check for specific modules we need
    needed = ['BRepMesh', 'STEPControl', 'TopExp', 'TopAbs', 'BRep', 'TopLoc', 'IFSelect']
    
    for module in needed:
        if module in modules:
            print(f"✓ {module} - available")
            try:
                exec(f"from OCC.Core import {module}")
                print(f"  Import successful")
            except Exception as e:
                print(f"  Import failed: {e}")
        else:
            print(f"✗ {module} - NOT FOUND")
    
    # Test specific classes
    print("\nTesting specific classes:")
    tests = [
        ("BRepMesh_IncrementalMesh", "from OCC.Core.BRepMesh import BRepMesh_IncrementalMesh"),
        ("STEPControl_Reader", "from OCC.Core.STEPControl import STEPControl_Reader"),
    ]
    
    for name, cmd in tests:
        try:
            exec(cmd)
            print(f"✓ {name} - OK")
        except Exception as e:
            print(f"✗ {name} - FAILED: {e}")
            
except Exception as e:
    print(f"Error checking OCC: {e}")
    import traceback
    traceback.print_exc()

print("OCC check completed")
