#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
STEP Viewer TDK Modular - Dual Viewer Version
Enhanced with top/bottom viewer selection and compact transform display
"""


import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QSplitter, QPushButton, QLabel,
                            QFileDialog, QComboBox, QSpinBox, QGroupBox)
from PyQt5.QtCore import Qt, QTimer

# Import custom modules
from step_loader import STEPLoader
# Create a minimal VTK renderer fallback first
class VTKRendererFallback:
    def __init__(self, parent=None):
        print("VTK fallback: VTK<PERSON>enderer created")
        self.step_actor = None
        self.renderer = None
        self.render_window = None
        self.parent = parent
        self.vtk_widget = None
        self.interactor = None
        self.bbox_actor = None
        self.origin_actor = None

    def load_step_file(self, *args, **kwargs):
        print("VTK fallback: load_step_file called - SIMULATING SUCCESS")
        # Simulate successful STEP file loading for testing rotation updates
        return True

    def render(self):
        print("VTK fallback: render called")

    def clear(self):
        print("VTK fallback: clear called")

    def update_bounding_box(self):
        print("VTK fallback: update_bounding_box called")

    def get_widget(self):
        print("VTK fallback: get_widget called")
        from PyQt5.QtWidgets import QLabel
        return QLabel("VTK Not Available")

try:
    from vtk_renderer import VTKRenderer
    print("VTK renderer imported successfully")
    print(f"VTKRenderer class: {VTKRenderer}")
except ImportError as e:
    print(f"VTK renderer import failed: {e}")
    print("Using minimal fallback...")
    VTKRenderer = VTKRendererFallback
    print(f"VTKRenderer fallback class: {VTKRenderer}")
from gui_components import create_tool_dock

class StepViewerTDK(QMainWindow):
    def __init__(self):
        print("DEBUG: Starting StepViewerTDK initialization...")
        try:
            super().__init__()
            print("DEBUG: QMainWindow initialized successfully")
        except Exception as e:
            print(f"DEBUG: QMainWindow initialization failed: {e}")
            raise
        self.setWindowTitle("VTK TDK STEP Viewer (Dual View) - Enhanced")
        self.setGeometry(200, 200, 1200, 800)  # More conservative size and position

        # Initialize dual components
        self.step_loader_left = STEPLoader()
        self.step_loader_right = STEPLoader()
        self.vtk_renderer_left = VTKRenderer(self)
        self.vtk_renderer_right = VTKRenderer(self)

        # Data tracking for both viewers - bounding box on by default
        self.bbox_visible_left = True
        self.bbox_visible_right = True

        # Transform data for left (top) viewer
        self.orig_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.orig_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.model_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}  # Track button rotations

        # Transform data for right (bottom) viewer
        self.orig_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.orig_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.model_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}  # Track button rotations

        # Active viewer tracking
        self.active_viewer = "top"

        # Setup UI
        self.init_ui()

        # BRILLIANT SOLUTION: Move numbers to VTK screens directly!
        print("DEBUG: 🔧 Setting up VTK screen overlays for numbers")

        # Setup VTK text overlays after VTK widgets are created (longer delay for proper initialization)
        QTimer.singleShot(1000, self.setup_vtk_text_overlays)

        # Initialize tracking variables - separate cursor for each viewer
        self.cursor_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.cursor_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}

        # Setup mouse tracking after UI is created (text overlays only after STEP file loads)
        QTimer.singleShot(300, self.setup_mouse_tracking)

        # Track previous camera positions to detect mouse rotation
        self.prev_camera_pos_left = None
        self.prev_camera_pos_right = None

        self.statusBar().showMessage("Ready - Select TOP or BOTTOM viewer, then load STEP files")

    def init_ui(self):
        # Central widget with splitter
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout
        main_layout = QHBoxLayout(central_widget)

        # Create splitter for dual view
        splitter = QSplitter(Qt.Vertical)

        # Top viewer container
        top_container = QWidget()
        top_layout = QVBoxLayout(top_container)

        # Top file label
        self.top_file_label = QLabel("TOP VIEWER - No file loaded")
        self.top_file_label.setStyleSheet("color: blue; font-size: 12px; padding: 5px; background-color: lightgray;")
        top_layout.addWidget(self.top_file_label)

        # Top VTK widget
        self.vtk_widget_left = self.vtk_renderer_left.vtk_widget
        if self.vtk_widget_left:
            print(f"Adding TOP VTK widget: {type(self.vtk_widget_left)}")
            # Set minimum size and size policy for proper resizing
            self.vtk_widget_left.setMinimumSize(400, 300)
            from PyQt5.QtWidgets import QSizePolicy
            self.vtk_widget_left.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
            # Enable mouse tracking for cursor position
            self.vtk_widget_left.setMouseTracking(True)
            top_layout.addWidget(self.vtk_widget_left)
        else:
            print("ERROR: TOP VTK widget is None")

        # Bottom viewer container
        bottom_container = QWidget()
        bottom_layout = QVBoxLayout(bottom_container)

        # Bottom file label
        self.bottom_file_label = QLabel("BOTTOM VIEWER - No file loaded")
        self.bottom_file_label.setStyleSheet("color: blue; font-size: 12px; padding: 5px; background-color: lightgray;")
        bottom_layout.addWidget(self.bottom_file_label)

        # Bottom VTK widget
        self.vtk_widget_right = self.vtk_renderer_right.vtk_widget
        if self.vtk_widget_right:
            print(f"Adding BOTTOM VTK widget: {type(self.vtk_widget_right)}")
            # Set minimum size and size policy for proper resizing
            self.vtk_widget_right.setMinimumSize(400, 300)
            self.vtk_widget_right.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
            # Enable mouse tracking for cursor position
            self.vtk_widget_right.setMouseTracking(True)
            bottom_layout.addWidget(self.vtk_widget_right)
        else:
            print("ERROR: BOTTOM VTK widget is None")

        # Add containers to splitter
        splitter.addWidget(top_container)
        splitter.addWidget(bottom_container)
        splitter.setSizes([400, 400])

        # Set splitter to expand properly
        from PyQt5.QtWidgets import QSizePolicy
        splitter.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        main_layout.addWidget(splitter)

        # Create tool dock
        dock = create_tool_dock(self)
        self.addDockWidget(Qt.LeftDockWidgetArea, dock)

        # Set initial active viewer
        self.update_viewer_highlights()

        # Setup rotation monitoring timer
        self.setup_rotation_monitor()

        # Initialize test mode for rotation updates (since VTK is not working)
        # self.setup_test_mode()  # DISABLED - test mode removed

    def setup_test_mode(self):
        """Setup test mode to simulate STEP file loading and test rotation updates"""
        print("🔧 SETTING UP TEST MODE for rotation updates")

        # Initialize all the variables that would normally be set when loading a STEP file
        # LEFT viewer (TOP)
        self.orig_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.orig_angle_left = 45.0  # Test angle
        self.orig_axis_left = {'x': 0.0, 'y': 0.0, 'z': 1.0}  # Test axis
        self.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_angle_left = 45.0  # Same as original
        self.current_axis_left = {'x': 0.0, 'y': 0.0, 'z': 1.0}  # Will change with rotation
        self.current_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}

        # RIGHT viewer (BOTTOM)
        self.orig_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.orig_angle_right = 45.0  # Test angle
        self.orig_axis_right = {'x': 0.0, 'y': 0.0, 'z': 1.0}  # Test axis
        self.current_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_angle_right = 45.0  # Same as original
        self.current_axis_right = {'x': 0.0, 'y': 0.0, 'z': 1.0}  # Will change with rotation
        self.current_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}

        print("🔧 TEST MODE: All rotation variables initialized")
        print(f"🔧 TEST: current_angle_left = {self.current_angle_left}")
        print(f"🔧 TEST: current_axis_left = {self.current_axis_left}")
        print(f"🔧 TEST: current_angle_right = {self.current_angle_right}")
        print(f"🔧 TEST: current_axis_right = {self.current_axis_right}")

        # Update the display to show initial values
        self.update_vtk_text_overlays()

        # AUTO-LOAD debug file on fresh startup
        QTimer.singleShot(2000, self.auto_load_debug_file)

    def auto_load_debug_file(self):
        """Auto-load debug file on fresh startup"""
        import os
        debug_file = "debug_auto_saved.step"
        if os.path.exists(debug_file):
            print(f"🚀 AUTO-LOADING: {debug_file}")
            self.active_viewer = "top"

            # Load using the existing method
            success, message = self.step_loader_left.load_step_file(debug_file)
            if success:
                self.vtk_renderer_left.clear_view()
                self.vtk_renderer_left.display_polydata(self.step_loader_left.current_polydata)
                self.vtk_renderer_left.fit_view()
                self.top_file_label.setText(f"TOP: {os.path.basename(debug_file)}")
                self.extract_step_transformation_data("top")
                QTimer.singleShot(500, self.update_vtk_text_overlays)
                QTimer.singleShot(250, self.update_transform_display)
                print(f"✅ AUTO-LOADED: {debug_file}")
            else:
                print(f"❌ AUTO-LOAD FAILED: {message}")
        else:
            print(f"❌ AUTO-LOAD: {debug_file} not found")

    def ensure_model_display(self):
        """Ensure the 3D model is properly displayed"""
        try:
            print("🔧 ENSURING MODEL DISPLAY...")
            print(f"🔍 DEBUG: ensure_model_display called")

            # Detailed debugging
            print(f"🔍 DEBUG: step_loader_left exists: {hasattr(self, 'step_loader_left')}")
            if hasattr(self, 'step_loader_left'):
                print(f"🔍 DEBUG: step_loader_left: {self.step_loader_left}")
                print(f"🔍 DEBUG: current_polydata exists: {hasattr(self.step_loader_left, 'current_polydata')}")
                if hasattr(self.step_loader_left, 'current_polydata'):
                    print(f"🔍 DEBUG: current_polydata is not None: {self.step_loader_left.current_polydata is not None}")
                    if self.step_loader_left.current_polydata:
                        print(f"🔍 DEBUG: Polydata cells: {self.step_loader_left.current_polydata.GetNumberOfCells()}")
                        print(f"🔍 DEBUG: Polydata points: {self.step_loader_left.current_polydata.GetNumberOfPoints()}")

            print(f"🔍 DEBUG: vtk_renderer_left exists: {hasattr(self, 'vtk_renderer_left')}")
            if hasattr(self, 'vtk_renderer_left'):
                print(f"🔍 DEBUG: vtk_renderer_left: {self.vtk_renderer_left}")
                print(f"🔍 DEBUG: step_actor exists: {hasattr(self.vtk_renderer_left, 'step_actor')}")
                if hasattr(self.vtk_renderer_left, 'step_actor'):
                    print(f"🔍 DEBUG: step_actor is not None: {self.vtk_renderer_left.step_actor is not None}")
                    if self.vtk_renderer_left.step_actor:
                        print(f"🔍 DEBUG: Actor visibility: {self.vtk_renderer_left.step_actor.GetVisibility()}")

            # Check if STEP loader has polydata
            if hasattr(self, 'step_loader_left') and self.step_loader_left.current_polydata:
                print("✅ STEP polydata found, re-displaying...")

                # Re-display the polydata
                print("🔍 DEBUG: Clearing view...")
                self.vtk_renderer_left.clear_view()
                print("🔍 DEBUG: Displaying polydata...")
                self.vtk_renderer_left.display_polydata(self.step_loader_left.current_polydata)
                print("🔍 DEBUG: Fitting view...")
                self.vtk_renderer_left.fit_view()

                # Force render
                if hasattr(self, 'vtk_widget_left'):
                    print("🔍 DEBUG: Forcing render...")
                    self.vtk_widget_left.GetRenderWindow().Render()

                print("✅ MODEL RE-DISPLAYED")

            elif hasattr(self, 'vtk_renderer_left') and self.vtk_renderer_left.step_actor:
                print("✅ VTK actor found, ensuring visibility...")

                # Make sure the actor is visible
                self.vtk_renderer_left.step_actor.SetVisibility(True)

                # Fit the view to show the model
                self.vtk_renderer_left.fit_view()

                # Force render
                if hasattr(self, 'vtk_widget_left'):
                    self.vtk_widget_left.GetRenderWindow().Render()

                print("✅ MODEL VISIBILITY ENSURED")
            else:
                print("❌ NO MODEL DATA FOUND - DETAILED DEBUG:")
                print(f"   step_loader_left exists: {hasattr(self, 'step_loader_left')}")
                if hasattr(self, 'step_loader_left'):
                    print(f"   step_loader_left value: {self.step_loader_left}")
                    print(f"   polydata exists: {hasattr(self.step_loader_left, 'current_polydata')}")
                    if hasattr(self.step_loader_left, 'current_polydata'):
                        print(f"   polydata value: {self.step_loader_left.current_polydata}")
                print(f"   vtk_renderer_left exists: {hasattr(self, 'vtk_renderer_left')}")
                if hasattr(self, 'vtk_renderer_left'):
                    print(f"   vtk_renderer_left value: {self.vtk_renderer_left}")
                    print(f"   step_actor exists: {hasattr(self.vtk_renderer_left, 'step_actor')}")
                    if hasattr(self.vtk_renderer_left, 'step_actor'):
                        print(f"   step_actor value: {self.vtk_renderer_left.step_actor}")

        except Exception as e:
            print(f"❌ ERROR ENSURING MODEL DISPLAY: {e}")
            import traceback
            traceback.print_exc()

    def set_active_viewer(self, viewer):
        """Set the active viewer (top or bottom)"""
        self.active_viewer = viewer
        self.update_viewer_highlights()
        self.update_transform_display()
        self.statusBar().showMessage(f"Active viewer: {viewer.upper()}")

    def update_viewer_highlights(self):
        """Update button highlights to show active viewer"""
        if hasattr(self, 'top_btn') and hasattr(self, 'bottom_btn'):
            if self.active_viewer == "top":
                self.top_btn.setStyleSheet("QPushButton { background-color: lightgreen; font-weight: bold; padding: 8px; }")
                self.bottom_btn.setStyleSheet("QPushButton { padding: 8px; }")
            else:
                self.top_btn.setStyleSheet("QPushButton { padding: 8px; }")
                self.bottom_btn.setStyleSheet("QPushButton { background-color: lightgreen; font-weight: bold; padding: 8px; }")

    def load_step_file(self):
        """Load STEP file into active viewer"""
        filename, _ = QFileDialog.getOpenFileName(
            self, "Open STEP File", "", "STEP Files (*.step *.stp);;All Files (*)"
        )

        if filename:
            print(f"Loading STEP file: {filename}")
            if self.active_viewer == "top":
                success, message = self.step_loader_left.load_step_file(filename)
                print(f"TOP load result: success={success}, message={message}")
                if success:
                    print(f"Polydata available: {self.step_loader_left.current_polydata is not None}")
                    if self.step_loader_left.current_polydata:
                        print(f"Polydata points: {self.step_loader_left.current_polydata.GetNumberOfPoints()}")
                        print(f"Polydata cells: {self.step_loader_left.current_polydata.GetNumberOfCells()}")

                    # Reset any previous transformations before loading new model
                    self.vtk_renderer_left.clear_view()

                    # Display the polydata
                    display_success = self.vtk_renderer_left.display_polydata(self.step_loader_left.current_polydata)
                    print(f"Display success: {display_success}")

                    self.vtk_renderer_left.fit_view()  # Auto-fit the view
                    # Show bounding box by default
                    self.vtk_renderer_left.toggle_bounding_box(True)
                    self.top_file_label.setText(f"TOP: {os.path.basename(filename)}")
                    self.extract_step_transformation_data("top")

                    # Re-setup text overlays after STEP file loads (longer delay)
                    QTimer.singleShot(500, self.setup_vtk_text_overlays)
                    # Update display immediately with STEP file coordinates
                    QTimer.singleShot(800, self.update_vtk_text_overlays)
                    # Update GUI labels with extracted values
                    QTimer.singleShot(250, self.update_transform_display)
                    # Check for and apply saved transformations
                    QTimer.singleShot(300, lambda: self.load_and_apply_transforms(filename, "top"))
                else:
                    print(f"Load failed: {message}")
                    self.top_file_label.setText("TOP: Load failed")
            else:
                success, message = self.step_loader_right.load_step_file(filename)
                if success:
                    # Reset any previous transformations before loading new model
                    self.vtk_renderer_right.clear_view()
                    self.vtk_renderer_right.display_polydata(self.step_loader_right.current_polydata)
                    self.vtk_renderer_right.fit_view()  # Auto-fit the view
                    # Show bounding box by default
                    self.vtk_renderer_right.toggle_bounding_box(True)
                    self.bottom_file_label.setText(f"BOTTOM: {os.path.basename(filename)}")
                    self.extract_step_transformation_data("bottom")

                    # Re-setup text overlays after STEP file loads (longer delay)
                    QTimer.singleShot(500, self.setup_vtk_text_overlays)
                    # Update display immediately with STEP file coordinates
                    QTimer.singleShot(800, self.update_vtk_text_overlays)
                    # Update GUI labels with extracted values
                    QTimer.singleShot(250, self.update_transform_display)
                    # Check for and apply saved transformations
                    QTimer.singleShot(300, lambda: self.load_and_apply_transforms(filename, "bottom"))
                else:
                    self.bottom_file_label.setText("BOTTOM: Load failed")

            self.statusBar().showMessage(f"{self.active_viewer.title()}: {message}")
            self.update_transform_display()

    def load_and_apply_transforms(self, step_filename, viewer):
        """Load and apply saved transformations from companion file"""
        try:
            import os
            base_name = os.path.splitext(step_filename)[0]
            transform_file = base_name + "_transforms.txt"

            if os.path.exists(transform_file):
                print(f"🔧 LOAD DEBUG: Found transform file: {transform_file}")

                with open(transform_file, 'r') as f:
                    lines = f.readlines()

                # Parse saved rotation and position
                saved_rot = {'x': 0, 'y': 0, 'z': 0}
                saved_pos = {'x': 0, 'y': 0, 'z': 0}

                for line in lines:
                    if 'Rotation:' in line:
                        parts = line.split()
                        for i, part in enumerate(parts):
                            if 'X=' in part:
                                saved_rot['x'] = float(part.split('=')[1].replace('°', ''))
                            elif 'Y=' in part:
                                saved_rot['y'] = float(part.split('=')[1].replace('°', ''))
                            elif 'Z=' in part:
                                saved_rot['z'] = float(part.split('=')[1].replace('°', ''))
                    elif 'Position:' in line:
                        parts = line.split()
                        for i, part in enumerate(parts):
                            if 'X=' in part:
                                saved_pos['x'] = float(part.split('=')[1].replace('mm', ''))
                            elif 'Y=' in part:
                                saved_pos['y'] = float(part.split('=')[1].replace('mm', ''))
                            elif 'Z=' in part:
                                saved_pos['z'] = float(part.split('=')[1].replace('mm', ''))

                print(f"🔧 LOAD DEBUG: Applying saved transforms - Rot: {saved_rot}, Pos: {saved_pos}")

                # Apply the saved transformations
                if viewer == "top":
                    self.current_rot_left = saved_rot.copy()
                    self.current_pos_left = saved_pos.copy()
                    # Apply to VTK actor
                    if hasattr(self, 'vtk_renderer_left') and self.vtk_renderer_left.step_actor:
                        self.apply_transforms_to_actor(self.vtk_renderer_left.step_actor, saved_rot, saved_pos)
                        # Update bounding box after applying transformations
                        if hasattr(self, 'bbox_visible_left') and self.bbox_visible_left:
                            self.vtk_renderer_left.update_bounding_box()
                            print(f"🔧 Updated bounding box after loading transforms for TOP viewer")
                else:
                    self.current_rot_right = saved_rot.copy()
                    self.current_pos_right = saved_pos.copy()
                    # Apply to VTK actor
                    if hasattr(self, 'vtk_renderer_right') and self.vtk_renderer_right.step_actor:
                        self.apply_transforms_to_actor(self.vtk_renderer_right.step_actor, saved_rot, saved_pos)
                        # Update bounding box after applying transformations
                        if hasattr(self, 'bbox_visible_right') and self.bbox_visible_right:
                            self.vtk_renderer_right.update_bounding_box()
                            print(f"🔧 Updated bounding box after loading transforms for BOTTOM viewer")

                # Update display
                self.update_vtk_text_overlays()
                print(f"✅ LOAD DEBUG: Transforms applied successfully")

        except Exception as e:
            print(f"❌ LOAD DEBUG: Failed to load transforms: {e}")

    def apply_transforms_to_actor(self, actor, rotation, position):
        """Apply rotation and position transforms to VTK actor"""
        try:
            import vtk
            transform = vtk.vtkTransform()

            # Apply rotations in correct order
            transform.RotateX(rotation['x'])
            transform.RotateY(rotation['y'])
            transform.RotateZ(rotation['z'])

            # Apply translation
            transform.Translate(position['x'], position['y'], position['z'])

            # Set transform to actor
            actor.SetUserTransform(transform)
            print(f"✅ LOAD DEBUG: Applied transforms to actor - Rot: {rotation}, Pos: {position}")

        except Exception as e:
            print(f"❌ LOAD DEBUG: Failed to apply transforms to actor: {e}")
            import traceback
            traceback.print_exc()

    def extract_step_transformation_data(self, viewer):
        """Extract transformation data from loaded STEP file"""
        if viewer == "top":
            loader = self.step_loader_left
        else:
            loader = self.step_loader_right

        # READ ACTUAL TRANSFORMATION FROM STEP FILE
        orig_pos = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        orig_rot = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        orig_angle = 0.0  # Single angle value like FreeCAD
        orig_axis = {'x': 0.0, 'y': 0.0, 'z': 1.0}  # Axis vector like FreeCAD [0.00 0.00 1.00]

        if hasattr(loader, 'original_filename') and loader.original_filename:
            print(f"🔍 READING STEP transformation from: {loader.original_filename}")

            try:
                with open(loader.original_filename, 'r') as f:
                    step_content = f.read()

                # Extract position from #8041 line (CARTESIAN_POINT) - for POS display
                import re
                point_match = re.search(r'#8041\s*=\s*CARTESIAN_POINT\s*\([^;]+\)\s*;', step_content)
                if point_match:
                    point_line = point_match.group(0)
                    # Extract the three numbers
                    coords_match = re.search(r'\(\s*[^,]+,\s*\(\s*([^,]+),\s*([^,]+),\s*([^)]+)\s*\)', point_line)
                    if coords_match:
                        orig_pos['x'] = float(coords_match.group(1))
                        orig_pos['y'] = float(coords_match.group(2))
                        orig_pos['z'] = float(coords_match.group(3))
                        print(f"🔍 STEP file position: X={orig_pos['x']:.3f}, Y={orig_pos['y']:.3f}, Z={orig_pos['z']:.3f}")

                # Extract rotation from #8042 and #8043 lines (DIRECTION vectors) - for ROT display
                z_dir_match = re.search(r'#8042\s*=\s*DIRECTION\s*\([^;]+\)\s*;', step_content)
                x_dir_match = re.search(r'#8043\s*=\s*DIRECTION\s*\([^;]+\)\s*;', step_content)

                if z_dir_match and x_dir_match:
                    z_line = z_dir_match.group(0)
                    x_line = x_dir_match.group(0)

                    # Extract Z direction vector
                    z_coords_match = re.search(r'\(\s*[^,]+,\s*\(\s*([^,]+),\s*([^,]+),\s*([^)]+)\s*\)', z_line)
                    x_coords_match = re.search(r'\(\s*[^,]+,\s*\(\s*([^,]+),\s*([^,]+),\s*([^)]+)\s*\)', x_line)

                    if z_coords_match and x_coords_match:
                        z_dir = [float(z_coords_match.group(1)), float(z_coords_match.group(2)), float(z_coords_match.group(3))]
                        x_dir = [float(x_coords_match.group(1)), float(x_coords_match.group(2)), float(x_coords_match.group(3))]

                        print(f"🔍 STEP file Z direction: ({z_dir[0]:.3f}, {z_dir[1]:.3f}, {z_dir[2]:.3f})")
                        print(f"🔍 STEP file X direction: ({x_dir[0]:.3f}, {x_dir[1]:.3f}, {x_dir[2]:.3f})")

                        # Convert direction vectors to Euler angles
                        import math
                        # Simple rotation extraction - if not identity matrix, there's rotation
                        if not (abs(z_dir[0]) < 0.001 and abs(z_dir[1]) < 0.001 and abs(z_dir[2] - 1.0) < 0.001 and
                               abs(x_dir[0] - 1.0) < 0.001 and abs(x_dir[1]) < 0.001 and abs(x_dir[2]) < 0.001):

                            # Calculate approximate Euler angles from direction vectors
                            # This is a simplified conversion - for display purposes
                            orig_rot['x'] = math.degrees(math.atan2(z_dir[1], z_dir[2]))
                            orig_rot['y'] = math.degrees(math.atan2(-z_dir[0], math.sqrt(z_dir[1]**2 + z_dir[2]**2)))
                            orig_rot['z'] = math.degrees(math.atan2(x_dir[1], x_dir[0]))

                            print(f"🔍 STEP file rotation: X={orig_rot['x']:.3f}°, Y={orig_rot['y']:.3f}°, Z={orig_rot['z']:.3f}°")
                        else:
                            print(f"🔍 STEP file has identity rotation (no rotation)")
                            # For identity rotation, angle is 0 and axis is [0, 0, 1] (Z-axis)
                            orig_angle = 0.0
                            orig_axis = {'x': 0.0, 'y': 0.0, 'z': 1.0}

                        # If we have rotation, calculate the rotation angle and axis
                        if not (abs(orig_rot['x']) < 0.001 and abs(orig_rot['y']) < 0.001 and abs(orig_rot['z']) < 0.001):
                            # Convert Euler angles to rotation angle and axis
                            import math
                            # Calculate the rotation magnitude as the angle
                            orig_angle = math.sqrt(orig_rot['x']**2 + orig_rot['y']**2 + orig_rot['z']**2)
                            # Normalize the rotation vector to get the axis
                            if orig_angle > 0.001:
                                orig_axis = {
                                    'x': orig_rot['x'] / orig_angle,
                                    'y': orig_rot['y'] / orig_angle,
                                    'z': orig_rot['z'] / orig_angle
                                }
                                print(f"🔍 STEP file angle: {orig_angle:.2f}°, axis: [{orig_axis['x']:.2f} {orig_axis['y']:.2f} {orig_axis['z']:.2f}]")
                            else:
                                orig_angle = 0.0
                                orig_axis = {'x': 0.0, 'y': 0.0, 'z': 1.0}
                        else:
                            # No rotation, angle is 0 and default axis
                            orig_angle = 0.0
                            orig_axis = {'x': 0.0, 'y': 0.0, 'z': 1.0}
                            print(f"🔍 STEP file angle: 0.00°, axis (default): [0.00 0.00 1.00]")

            except Exception as e:
                print(f"❌ Failed to read STEP transformation: {e}")

        # If STEP coordinate system is at origin (0,0,0), use geometry center instead
        if (abs(orig_pos['x']) < 0.001 and abs(orig_pos['y']) < 0.001 and abs(orig_pos['z']) < 0.001 and
            abs(orig_rot['x']) < 0.001 and abs(orig_rot['y']) < 0.001 and abs(orig_rot['z']) < 0.001):

            print(f"🔍 STEP coordinate system is at origin, using geometry center instead")
            if hasattr(loader, 'current_polydata') and loader.current_polydata:
                bounds = loader.current_polydata.GetBounds()
                orig_pos = {'x': (bounds[0] + bounds[1]) / 2.0, 'y': (bounds[2] + bounds[3]) / 2.0, 'z': (bounds[4] + bounds[5]) / 2.0}
                print(f"🔍 Using geometry center: X={orig_pos['x']:.3f}, Y={orig_pos['y']:.3f}, Z={orig_pos['z']:.3f}")

        if viewer == "top":
            self.orig_pos_left = orig_pos
            self.orig_rot_left = orig_rot
            self.orig_angle_left = orig_angle
            self.orig_axis_left = orig_axis
            # Copy original values to current values as starting point
            self.current_pos_left = orig_pos.copy()
            self.current_rot_left = orig_rot.copy()
            self.current_angle_left = orig_angle
            self.current_axis_left = orig_axis.copy()
            # Initialize model rotation tracking
            self.model_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            print(f"TOP: Set position: {orig_pos}, rotation: {orig_rot}, angle: {orig_angle:.2f}°, axis: [{orig_axis['x']:.2f} {orig_axis['y']:.2f} {orig_axis['z']:.2f}]")
        else:
            self.orig_pos_right = orig_pos
            self.orig_rot_right = orig_rot
            self.orig_angle_right = orig_angle
            self.orig_axis_right = orig_axis
            # Copy original values to current values as starting point
            self.current_pos_right = orig_pos.copy()
            self.current_rot_right = orig_rot.copy()
            self.current_angle_right = orig_angle
            self.current_axis_right = orig_axis.copy()
            # Initialize model rotation tracking
            self.model_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            print(f"🔍 BOTTOM: Set position: {orig_pos}, rotation: {orig_rot}, angle: {orig_angle:.2f}°, axis: [{orig_axis['x']:.2f} {orig_axis['y']:.2f} {orig_axis['z']:.2f}]")
            print(f"🔍 BOTTOM: orig_pos_right = {self.orig_pos_right}")
            print(f"🔍 BOTTOM: orig_rot_right = {self.orig_rot_right}")
            print(f"🔍 BOTTOM: orig_angle_right = {self.orig_angle_right}")
            print(f"🔍 BOTTOM: current_pos_right = {self.current_pos_right}")
            print(f"🔍 BOTTOM: current_rot_right = {self.current_rot_right}")
            print(f"🔍 BOTTOM: current_angle_right = {self.current_angle_right}")

    def clear_view(self):
        """Clear the active viewer and reset numbers to zero"""
        if self.active_viewer == "top":
            self.vtk_renderer_left.clear_view()
            self.top_file_label.setText("TOP VIEWER - No file loaded")
            # Reset all transform values to zero
            self.orig_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.orig_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.current_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.model_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        else:
            self.vtk_renderer_right.clear_view()
            self.bottom_file_label.setText("BOTTOM VIEWER - No file loaded")
            # Reset all transform values to zero
            self.orig_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.orig_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.current_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.current_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.model_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}

        # Update the display to show zeros
        self.update_transform_display()
        self.statusBar().showMessage(f"{self.active_viewer.title()} view cleared")

    def fit_view(self):
        """Fit view in active viewer - IMPROVED with better feedback"""
        print("🔧 DEBUG: fit_view() called!")
        print(f"🔧 DEBUG: active_viewer = {self.active_viewer}")

        if self.active_viewer == "top":
            if self.vtk_renderer_left and self.vtk_renderer_left.step_actor:
                self.vtk_renderer_left.fit_view()
                print("✅ Fitted TOP view to show full model")
                self.statusBar().showMessage("TOP view fitted to show full model")
            else:
                print("❌ No model loaded in TOP viewer")
                self.statusBar().showMessage("No model loaded in TOP viewer to fit")
        else:
            if self.vtk_renderer_right and self.vtk_renderer_right.step_actor:
                self.vtk_renderer_right.fit_view()
                print("✅ Fitted BOTTOM view to show full model")
                self.statusBar().showMessage("BOTTOM view fitted to show full model")
            else:
                print("❌ No model loaded in BOTTOM viewer")
                self.statusBar().showMessage("No model loaded in BOTTOM viewer to fit")

    def save_transformed_step(self):
        """Save transformed STEP file from active viewer"""
        filename, _ = QFileDialog.getSaveFileName(
            self, "Save STEP File", "", "STEP Files (*.step);;STL Files (*.stl);;All Files (*)"
        )

        if filename:
            if self.active_viewer == "top":
                loader = self.step_loader_left
                renderer = self.vtk_renderer_left
                current_rot = self.current_rot_left
                current_pos = self.current_pos_left
            else:
                loader = self.step_loader_right
                renderer = self.vtk_renderer_right
                current_rot = self.current_rot_right
                current_pos = self.current_pos_right

            # SIMPLE: Use the 6 numbers that are already being displayed
            print(f"🔧 SAVE DEBUG: Active viewer = {self.active_viewer}")
            print(f"🔧 SAVE DEBUG: Using displayed values:")
            print(f"🔧 SAVE DEBUG: Position = X={current_pos['x']:.3f}mm Y={current_pos['y']:.3f}mm Z={current_pos['z']:.3f}mm")
            print(f"🔧 SAVE DEBUG: Rotation = X={current_rot['x']:.3f}° Y={current_rot['y']:.3f}° Z={current_rot['z']:.3f}°")

            # Create transformation matrix from the 6 numbers
            import vtk
            transform = vtk.vtkTransform()
            transform.RotateX(current_rot['x'])
            transform.RotateY(current_rot['y'])
            transform.RotateZ(current_rot['z'])
            transform.Translate(current_pos['x'], current_pos['y'], current_pos['z'])
            transform_matrix = transform.GetMatrix()

            # Check if there are any transformations applied
            has_transforms = (abs(current_rot['x']) > 0.001 or abs(current_rot['y']) > 0.001 or abs(current_rot['z']) > 0.001 or
                            abs(current_pos['x']) > 0.001 or abs(current_pos['y']) > 0.001 or abs(current_pos['z']) > 0.001)

            # CRITICAL DEBUG: Just before saving file
            print(f"🔧 SAVE DEBUG: About to save file with transformations")
            print(f"🔧 SAVE DEBUG: Transform matrix exists = {transform_matrix is not None}")
            if has_transforms:
                print(f"🔧 SAVE DEBUG: Will apply transformations to STEP file")
            else:
                print(f"🔧 SAVE DEBUG: No transformations to apply - saving original")

            # Save rotation and position data to companion file
            self.save_transform_data(filename, current_rot, current_pos, transform_matrix)

            # CRITICAL FIX: Use simple_step_modifier to actually embed transformations in STEP file
            print(f"🔧 SAVE DEBUG: Using simple_step_modifier to embed transformations...")
            try:
                from simple_step_modifier import SimpleSTEPModifier

                # Create modifier and load original STEP file (same as working test)
                modifier = SimpleSTEPModifier()
                modifier.load_step_file(loader.original_filename)

                # Apply transformations using the EXACT same method as the working test
                if transform_matrix and has_transforms:
                    print(f"🔧 SAVE DEBUG: Applying transformations using working test method...")

                    # Step 1: Transform all geometry coordinates
                    modifier.transform_geometry_coordinates(transform_matrix)

                    # Step 2: Set coordinate system placement (THIS IS THE KEY - makes POS values show up!)
                    modifier.modify_placement(
                        current_pos['x'], current_pos['y'], current_pos['z'],
                        current_rot['x'], current_rot['y'], current_rot['z']
                    )

                    print(f"✅ SAVE DEBUG: Applied both geometry transformation AND coordinate system placement")
                    print(f"✅ SAVE DEBUG: POS: X={current_pos['x']:.3f} Y={current_pos['y']:.3f} Z={current_pos['z']:.3f}")
                    print(f"✅ SAVE DEBUG: ROT: X={current_rot['x']:.3f} Y={current_rot['y']:.3f} Z={current_rot['z']:.3f}")
                else:
                    print(f"🔧 SAVE DEBUG: No transformations to apply - copying original")

                # Save the modified STEP file
                success = modifier.save_step_file(filename)

                if success:
                    self.statusBar().showMessage(f"Saved: {filename}")
                    print(f"✅ SAVE DEBUG: File saved successfully with embedded transformations: {filename}")
                    print(f"✅ SAVE DEBUG: Reload this file to verify transformations are preserved")
                else:
                    self.statusBar().showMessage("Save failed")
                    print(f"❌ SAVE DEBUG: Save failed: {filename}")

            except ImportError as e:
                print(f"❌ SAVE DEBUG: simple_step_modifier not available: {e}")
                # Fallback to original method
                success = loader.save_step_file(filename, transform_matrix)
            except Exception as e:
                print(f"❌ SAVE DEBUG: Error using simple_step_modifier: {e}")
                # Fallback to original method
                success = loader.save_step_file(filename, transform_matrix)

    def save_step_file_option1(self):
        """OPTION 1: Save original geometry but update position/rotation to match GUI display"""
        filename, _ = QFileDialog.getSaveFileName(
            self, "Save STEP File - Option 1", "", "STEP Files (*.step);;All Files (*)"
        )

        if filename:
            if self.active_viewer == "top":
                loader = self.step_loader_left
                current_rot = self.current_rot_left
                current_pos = self.current_pos_left
            else:
                loader = self.step_loader_right
                current_rot = self.current_rot_right
                current_pos = self.current_pos_right

            print(f"🔧 SAVE OPTION 1: Original geometry + GUI position/rotation values")
            print(f"🔧 GUI Position: X={current_pos['x']:.3f}mm Y={current_pos['y']:.3f}mm Z={current_pos['z']:.3f}mm")
            print(f"🔧 GUI Rotation: X={current_rot['x']:.3f}° Y={current_rot['y']:.3f}° Z={current_rot['z']:.3f}°")

            try:
                from simple_step_modifier import SimpleSTEPModifier

                # Load original STEP file
                modifier = SimpleSTEPModifier()
                modifier.load_step_file(loader.original_filename)

                # Update ONLY the coordinate system placement to match GUI values
                # Keep original geometry unchanged
                modifier.modify_placement(
                    current_pos['x'], current_pos['y'], current_pos['z'],
                    current_rot['x'], current_rot['y'], current_rot['z']
                )

                # Save the file
                success = modifier.save_step_file(filename)

                if success:
                    self.statusBar().showMessage(f"Option 1 saved: {filename}")
                    print(f"✅ OPTION 1: Saved with GUI position/rotation values")
                else:
                    self.statusBar().showMessage("Option 1 save failed")
                    print(f"❌ OPTION 1: Save failed")

            except Exception as e:
                print(f"❌ OPTION 1: Error: {e}")
                self.statusBar().showMessage(f"Option 1 error: {e}")

    def save_step_file_option2(self):
        """OPTION 2: Save transformed geometry but keep original position/rotation values"""
        filename, _ = QFileDialog.getSaveFileName(
            self, "Save STEP File - Option 2", "", "STEP Files (*.step);;All Files (*)"
        )

        if filename:
            if self.active_viewer == "top":
                loader = self.step_loader_left
                current_rot = self.current_rot_left
                current_pos = self.current_pos_left
                orig_rot = self.orig_rot_left
                orig_pos = self.orig_pos_left
            else:
                loader = self.step_loader_right
                current_rot = self.current_rot_right
                current_pos = self.current_pos_right
                orig_rot = self.orig_rot_right
                orig_pos = self.orig_pos_right

            print(f"🔧 SAVE OPTION 2: Transformed geometry + original position/rotation values")
            print(f"🔧 Will transform geometry by: X={current_rot['x']:.3f}° Y={current_rot['y']:.3f}° Z={current_rot['z']:.3f}°")
            print(f"🔧 But keep original pos/rot values: Pos=({orig_pos['x']:.3f},{orig_pos['y']:.3f},{orig_pos['z']:.3f})")

            try:
                from simple_step_modifier import SimpleSTEPModifier
                import vtk

                # Load original STEP file
                modifier = SimpleSTEPModifier()
                modifier.load_step_file(loader.original_filename)

                # Create transformation matrix from current GUI values
                transform = vtk.vtkTransform()
                transform.RotateX(current_rot['x'])
                transform.RotateY(current_rot['y'])
                transform.RotateZ(current_rot['z'])
                transform.Translate(current_pos['x'], current_pos['y'], current_pos['z'])
                transform_matrix = transform.GetMatrix()

                # Transform all geometry coordinates
                modifier.transform_geometry_coordinates(transform_matrix)

                # But keep original coordinate system placement values
                modifier.modify_placement(
                    orig_pos['x'], orig_pos['y'], orig_pos['z'],
                    orig_rot['x'], orig_rot['y'], orig_rot['z']
                )

                # Save the file
                success = modifier.save_step_file(filename)

                if success:
                    self.statusBar().showMessage(f"Option 2 saved: {filename}")
                    print(f"✅ OPTION 2: Saved with transformed geometry + original values")
                else:
                    self.statusBar().showMessage("Option 2 save failed")
                    print(f"❌ OPTION 2: Save failed")

            except Exception as e:
                print(f"❌ OPTION 2: Error: {e}")
                self.statusBar().showMessage(f"Option 2 error: {e}")

    def save_original_step(self):
        """Save original STEP file without transformations"""
        filename, _ = QFileDialog.getSaveFileName(
            self, "Save Original STEP File", "", "STEP Files (*.step);;All Files (*)"
        )

        if filename:
            if self.active_viewer == "top":
                loader = self.step_loader_left
            else:
                loader = self.step_loader_right

            if hasattr(loader, 'original_filename') and loader.original_filename:
                try:
                    import shutil
                    shutil.copy2(loader.original_filename, filename)
                    self.statusBar().showMessage(f"Saved original: {filename}")
                    print(f"✅ Copied original STEP file: {filename}")
                except Exception as e:
                    self.statusBar().showMessage("Failed to save original")
                    print(f"❌ Failed to copy original: {e}")
            else:
                self.statusBar().showMessage("No original file available")
                print("❌ No original filename stored")

    def save_transform_data(self, step_filename, rotation, position, transform_matrix=None):
        """Save rotation and position data to companion file - ENHANCED"""
        try:
            # Create companion file with same name but .txt extension
            import os
            base_name = os.path.splitext(step_filename)[0]
            transform_filename = base_name + "_transform.txt"

            with open(transform_filename, 'w') as f:
                f.write("# STEP File Transform Data\n")
                f.write(f"# Generated for: {os.path.basename(step_filename)}\n")
                f.write(f"# Date: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("# NOTE: FreeCAD not available - transformations not applied to STEP file\n")
                f.write("# This file contains the transformation data that should be applied\n")
                f.write("\n")
                f.write("[ROTATION]\n")
                f.write(f"X = {rotation['x']:.6f}  # degrees\n")
                f.write(f"Y = {rotation['y']:.6f}  # degrees\n")
                f.write(f"Z = {rotation['z']:.6f}  # degrees\n")
                f.write("\n")
                f.write("[POSITION]\n")
                f.write(f"X = {position['x']:.6f}  # mm\n")
                f.write(f"Y = {position['y']:.6f}  # mm\n")
                f.write(f"Z = {position['z']:.6f}  # mm\n")
                f.write("\n")

                # Add VTK transform matrix if available
                if transform_matrix:
                    f.write("[VTK_TRANSFORM_MATRIX]\n")
                    f.write("# 4x4 transformation matrix from VTK\n")
                    for i in range(4):
                        row = []
                        for j in range(4):
                            row.append(f"{transform_matrix.GetElement(i, j):.6f}")
                        f.write(f"Row{i} = {' '.join(row)}\n")
                    f.write("\n")

                f.write("# To apply these transformations:\n")
                f.write("# 1. Install FreeCAD Python module\n")
                f.write("# 2. Re-save the STEP file to apply transformations\n")
                f.write("# 3. Or use this data in your CAD software\n")

            print(f"✅ Saved enhanced transform data: {transform_filename}")
            print(f"✅ Rotation: X={rotation['x']:.3f}° Y={rotation['y']:.3f}° Z={rotation['z']:.3f}°")
            print(f"✅ Position: X={position['x']:.3f}mm Y={position['y']:.3f}mm Z={position['z']:.3f}mm")
            if transform_matrix:
                print(f"✅ VTK transform matrix included in file")

        except Exception as e:
            print(f"❌ Failed to save transform data: {e}")

    def test_transformations(self):
        """Test function to verify transformations are working"""
        print("🔧 TESTING TRANSFORMATIONS:")
        print(f"🔧 Active viewer: {self.active_viewer}")

        if self.active_viewer == "top":
            renderer = self.vtk_renderer_left
            viewer_name = "TOP"
        else:
            renderer = self.vtk_renderer_right
            viewer_name = "BOTTOM"

        if renderer.step_actor:
            print(f"🔧 {viewer_name} has step_actor: YES")

            # Check user transform
            user_transform = renderer.step_actor.GetUserTransform()
            if user_transform:
                print(f"🔧 {viewer_name} has user transform: YES")
                matrix = user_transform.GetMatrix()
                print(f"🔧 Transform matrix exists: YES")
            else:
                print(f"🔧 {viewer_name} has user transform: NO")

            # Check position and orientation
            pos = renderer.step_actor.GetPosition()
            orient = renderer.step_actor.GetOrientation()
            print(f"🔧 Actor position: {pos}")
            print(f"🔧 Actor orientation: {orient}")

        else:
            print(f"🔧 {viewer_name} has step_actor: NO")

        self.statusBar().showMessage("Transform test completed - check console")

    def check_current_transforms(self):
        """Check current VTK transforms - DEBUG FUNCTION"""
        print("🔧 CHECKING CURRENT VTK TRANSFORMS:")

        if self.active_viewer == "top":
            renderer = self.vtk_renderer_left
            viewer_name = "TOP"
        else:
            renderer = self.vtk_renderer_right
            viewer_name = "BOTTOM"

        if renderer.step_actor:
            print(f"🔧 {viewer_name} step_actor exists: YES")

            # Check user transform
            user_transform = renderer.step_actor.GetUserTransform()
            if user_transform:
                print(f"🔧 {viewer_name} has user transform: YES")
                matrix = user_transform.GetMatrix()
                print(f"🔧 Transform Matrix:")
                for i in range(4):
                    row = []
                    for j in range(4):
                        row.append(f"{matrix.GetElement(i, j):.3f}")
                    print(f"   [{' '.join(row)}]")

                # Check if it's identity
                is_identity = True
                for i in range(4):
                    for j in range(4):
                        expected = 1.0 if i == j else 0.0
                        if abs(matrix.GetElement(i, j) - expected) > 0.001:
                            is_identity = False
                            break
                    if not is_identity:
                        break

                print(f"🔧 Matrix is identity: {is_identity}")

            else:
                print(f"🔧 {viewer_name} has user transform: NO")

            # Check position and orientation
            pos = renderer.step_actor.GetPosition()
            orient = renderer.step_actor.GetOrientation()
            print(f"🔧 Actor position: {pos}")
            print(f"🔧 Actor orientation: {orient}")

        else:
            print(f"🔧 {viewer_name} step_actor exists: NO")

        self.statusBar().showMessage(f"{viewer_name}: Transform check completed - see console")

    def show_help(self):
        """Show help dialog explaining what each button does"""
        help_text = """
🔧 STEP VIEWER HELP - Simple Guide for Beginners:

📁 FILE BUTTONS:
• "Open STEP File" - Click to browse and load a 3D model file (.step or .stp)
• "Save STEP File" - Save your rotated model as a new file (keeps rotations!)
• "Save Original STEP" - Save the original model without any changes

🎯 VIEWER SELECTION (Choose which screen to work with):
• "Top Viewer" - Work with the model in the top window
• "Bottom Viewer" - Work with the model in the bottom window
(The green button shows which one is active)

🔄 ROTATION BUTTONS (Turn your model):
• "X+" / "X-" - Spin model left/right (like turning a steering wheel)
• "Y+" / "Y-" - Tip model forward/backward (like nodding yes/no)
• "Z+" / "Z-" - Rotate model clockwise/counter-clockwise (like a record player)
(Each click rotates 15 degrees)

⚙️ CONTROL BUTTONS:
• "Clear Active View" - Remove the model from the current viewer
• "Fit Active View" - Zoom to show the whole model nicely
• "Reset to Original" - Put model back to starting position (undo all rotations)
• "Align Bottom-Center" - Move model so its bottom sits on the floor at center
• "Update from Current View" - Update the rotation numbers to match what you see

📊 DISPLAY BUTTONS:
• "Toggle Bounding Box" - Show/hide a wire box around your model
• "HELP" - Show this help message

🔧 SIMPLE STEPS TO USE:
1. Click "Top Viewer" or "Bottom Viewer" to choose which screen
2. Click "Open STEP File" and pick your 3D model
3. Use X+/X-/Y+/Y-/Z+/Z- buttons to rotate until it looks right
4. Click "Save STEP File" to save your rotated model
5. The yellow numbers show current rotation angles

💡 TIPS:
- The red dot shows the center point (0,0,0)
- Yellow text shows rotation angles and cursor position
- Your rotations are permanently saved to the new file
- Use "Reset to Original" if you mess up and want to start over
        """

        from PyQt5.QtWidgets import QMessageBox
        msg = QMessageBox()
        msg.setWindowTitle("STEP Viewer Help")
        msg.setText(help_text)
        msg.setIcon(QMessageBox.Information)
        msg.exec_()

    def reset_to_original(self):
        """Reset active viewer to original transform"""
        print("🔧 DEBUG: reset_to_original() called!")
        print(f"🔧 DEBUG: active_viewer = {self.active_viewer}")
        if self.active_viewer == "top":
            # Reset all rotation tracking to 0,0,0
            self.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.current_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.model_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            # Reset the actual VTK actor rotation and camera
            if self.vtk_renderer_left.step_actor:
                # Reset transform completely
                import vtk
                transform = vtk.vtkTransform()
                transform.Identity()
                self.vtk_renderer_left.step_actor.SetUserTransform(transform)
                self.vtk_renderer_left.step_actor.SetOrientation(0, 0, 0)
                self.vtk_renderer_left.step_actor.SetPosition(0, 0, 0)
                # Update bounding box after reset
                if self.bbox_visible_left:
                    self.vtk_renderer_left.update_bounding_box()
                self.vtk_renderer_left.fit_view()  # Also reset camera view
                self.vtk_renderer_left.render_window.Render()
                print("Reset TOP viewer to original")
        else:
            # Reset all rotation tracking to 0,0,0
            self.current_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.current_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.model_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            # Reset the actual VTK actor rotation and camera
            if self.vtk_renderer_right.step_actor:
                # Reset transform completely
                import vtk
                transform = vtk.vtkTransform()
                transform.Identity()
                self.vtk_renderer_right.step_actor.SetUserTransform(transform)
                self.vtk_renderer_right.step_actor.SetOrientation(0, 0, 0)
                self.vtk_renderer_right.step_actor.SetPosition(0, 0, 0)
                # Update bounding box after reset
                if self.bbox_visible_right:
                    self.vtk_renderer_right.update_bounding_box()
                self.vtk_renderer_right.fit_view()  # Also reset camera view
                self.vtk_renderer_right.render_window.Render()
                print("Reset BOTTOM viewer to original")

        self.update_transform_display()
        self.statusBar().showMessage(f"{self.active_viewer.title()} reset to original")

    def align_bottom_center(self):
        """Align model to bottom-center - FIXED"""
        print("🔧 DEBUG: align_bottom_center() called!")
        print(f"🔧 DEBUG: active_viewer = {self.active_viewer}")
        try:
            if self.active_viewer == "top":
                renderer = self.vtk_renderer_left
                viewer_name = "TOP"
            else:
                renderer = self.vtk_renderer_right
                viewer_name = "BOTTOM"

            if not renderer.step_actor:
                self.statusBar().showMessage("No model loaded to align")
                return

            # Get model bounds
            bounds = renderer.step_actor.GetBounds()
            print(f"🔧 {viewer_name} model bounds: {bounds}")

            # Calculate current model center and bottom
            model_center_x = (bounds[0] + bounds[1]) / 2.0
            model_center_y = (bounds[2] + bounds[3]) / 2.0
            model_bottom_z = bounds[4]  # Minimum Z (bottom)

            print(f"🔧 Model center X,Y: ({model_center_x:.3f}, {model_center_y:.3f})")
            print(f"🔧 Model bottom Z: {model_bottom_z:.3f}")

            # Calculate translation needed to center X,Y at origin and bottom at Z=0
            translate_x = -model_center_x
            translate_y = -model_center_y
            translate_z = -model_bottom_z

            print(f"🔧 Translation needed: X={translate_x:.3f}, Y={translate_y:.3f}, Z={translate_z:.3f}")

            # Apply translation to the actor
            current_pos = renderer.step_actor.GetPosition()
            new_pos = [
                current_pos[0] + translate_x,
                current_pos[1] + translate_y,
                current_pos[2] + translate_z
            ]

            renderer.step_actor.SetPosition(new_pos)

            # Update bounding box
            if self.active_viewer == "top" and self.bbox_visible_left:
                renderer.update_bounding_box()
            elif self.active_viewer == "bottom" and self.bbox_visible_right:
                renderer.update_bounding_box()

            # Update position tracking
            if self.active_viewer == "top":
                self.current_pos_left['x'] += translate_x
                self.current_pos_left['y'] += translate_y
                self.current_pos_left['z'] += translate_z
            else:
                self.current_pos_right['x'] += translate_x
                self.current_pos_right['y'] += translate_y
                self.current_pos_right['z'] += translate_z

            # Render and update display
            renderer.render_window.Render()
            self.update_transform_display()

            self.statusBar().showMessage(f"{viewer_name}: Model aligned to bottom-center")
            print(f"✅ {viewer_name} model aligned to bottom-center")

        except Exception as e:
            print(f"❌ Error aligning model: {e}")
            self.statusBar().showMessage(f"Error aligning model: {e}")

    def rotate_shape(self, axis, degrees):
        """Rotate shape in active viewer - FIXED VERSION"""
        print(f"🔧 ROTATE: {axis}{'+' if degrees > 0 else ''}{degrees}° on {self.active_viewer} viewer")

        if self.active_viewer == "top":
            # Initialize rotation tracking if needed
            if not hasattr(self, 'model_rot_left'):
                self.model_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}

            # CRITICAL FIX: Read current BUTTON rotation value and add to it
            # Use model_rot_left which tracks button rotations, NOT current_rot_left which gets overwritten by mouse
            current_displayed_value = 0.0
            if hasattr(self, 'model_rot_left') and self.model_rot_left:
                current_displayed_value = self.model_rot_left.get(axis, 0.0)

            print(f"   Reading from model_rot_left: {self.model_rot_left}")
            print(f"   Current {axis.upper()} button value: {current_displayed_value:.1f}°")

            # Set new value = current + increment
            new_value = current_displayed_value + degrees
            self.model_rot_left[axis] = new_value

            print(f"   New {axis.upper()} button value: {new_value:.1f}° (was {current_displayed_value:.1f}°)")

            # Apply rotation to VTK actor
            if hasattr(self, 'vtk_renderer_left') and self.vtk_renderer_left.step_actor:
                self.vtk_renderer_left.step_actor.RotateWXYZ(degrees,
                    1 if axis == 'x' else 0,
                    1 if axis == 'y' else 0,
                    1 if axis == 'z' else 0)
                self.vtk_renderer_left.render_window.Render()

            # CRITICAL: Do NOT update current_rot_left here - it gets overwritten by mouse
            # Keep model_rot_left separate for button tracking

            # Calculate axis and angle for LEFT viewer
            import math
            rot_mag = math.sqrt(self.model_rot_left['x']**2 + self.model_rot_left['y']**2 + self.model_rot_left['z']**2)

            if rot_mag > 0.001:
                self.current_axis_left = {
                    'x': self.model_rot_left['x'] / rot_mag,
                    'y': self.model_rot_left['y'] / rot_mag,
                    'z': self.model_rot_left['z'] / rot_mag
                }
                self.current_angle_left = rot_mag
            else:
                self.current_axis_left = {'x': 0.0, 'y': 0.0, 'z': 1.0}
                self.current_angle_left = 0.0

        else:  # bottom viewer
            # Initialize rotation tracking if needed
            if not hasattr(self, 'model_rot_right'):
                self.model_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}

            # CRITICAL FIX: Read current BUTTON rotation value and add to it
            # Use model_rot_right which tracks button rotations, NOT current_rot_right which gets overwritten by mouse
            current_displayed_value = 0.0
            if hasattr(self, 'model_rot_right') and self.model_rot_right:
                current_displayed_value = self.model_rot_right.get(axis, 0.0)

            print(f"   Reading from model_rot_right: {self.model_rot_right}")
            print(f"   Current {axis.upper()} button value: {current_displayed_value:.1f}°")

            # Set new value = current + increment
            new_value = current_displayed_value + degrees
            self.model_rot_right[axis] = new_value

            print(f"   New {axis.upper()} button value: {new_value:.1f}° (was {current_displayed_value:.1f}°)")

            # Apply rotation to VTK actor
            if hasattr(self, 'vtk_renderer_right') and self.vtk_renderer_right.step_actor:
                self.vtk_renderer_right.step_actor.RotateWXYZ(degrees,
                    1 if axis == 'x' else 0,
                    1 if axis == 'y' else 0,
                    1 if axis == 'z' else 0)
                self.vtk_renderer_right.render_window.Render()

            # CRITICAL: Do NOT update current_rot_right here - it gets overwritten by mouse
            # Keep model_rot_right separate for button tracking

            # Calculate axis and angle for RIGHT viewer
            import math
            rot_mag = math.sqrt(self.model_rot_right['x']**2 + self.model_rot_right['y']**2 + self.model_rot_right['z']**2)

            if rot_mag > 0.001:
                self.current_axis_right = {
                    'x': self.model_rot_right['x'] / rot_mag,
                    'y': self.model_rot_right['y'] / rot_mag,
                    'z': self.model_rot_right['z'] / rot_mag
                }
                self.current_angle_right = rot_mag
            else:
                self.current_axis_right = {'x': 0.0, 'y': 0.0, 'z': 1.0}
                self.current_angle_right = 0.0

        # Update the text display
        self.update_vtk_text_overlays()

        print(f"✅ New rotation values: {self.model_rot_left if self.active_viewer == 'top' else self.model_rot_right}")
















    def move_shape(self, axis, distance):
        """Move shape in active viewer"""
        try:
            if self.active_viewer == "top":
                # Initialize if not exists
                if not hasattr(self, 'model_pos_left'):
                    self.model_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                if not hasattr(self, 'current_pos_left'):
                    self.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                if not hasattr(self, 'orig_pos_left'):
                    self.orig_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}

                self.model_pos_left[axis] += distance
                # Update current position values immediately
                self.current_pos_left[axis] = self.orig_pos_left[axis] + self.model_pos_left[axis]

                # Apply actual translation to the VTK actor using transform matrix
                if self.vtk_renderer_left.step_actor:
                    # Get or create user transform
                    transform = self.vtk_renderer_left.step_actor.GetUserTransform()
                    if not transform:
                        import vtk
                        transform = vtk.vtkTransform()
                        self.vtk_renderer_left.step_actor.SetUserTransform(transform)

                    # Apply translation
                    if axis == 'x':
                        transform.Translate(distance, 0, 0)
                    elif axis == 'y':
                        transform.Translate(0, distance, 0)
                    elif axis == 'z':
                        transform.Translate(0, 0, distance)

                    print(f"🔧 Applied {distance}mm translation along {axis.upper()}-axis to transform matrix")

                    # Update bounding box to follow translation
                    if hasattr(self, 'bbox_visible_left') and self.bbox_visible_left:
                        self.vtk_renderer_left.update_bounding_box()
                        print(f"🔧 Updated bounding box for TOP viewer translation")

                    self.vtk_renderer_left.render_window.Render()

                # Update the display immediately
                self.update_transform_display()
                # Also update VTK text overlays
                self.update_vtk_text_overlays()

            else:
                # Initialize if not exists
                if not hasattr(self, 'model_pos_right'):
                    self.model_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                if not hasattr(self, 'current_pos_right'):
                    self.current_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                if not hasattr(self, 'orig_pos_right'):
                    self.orig_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}

                self.model_pos_right[axis] += distance
                # Update current position values immediately
                self.current_pos_right[axis] = self.orig_pos_right[axis] + self.model_pos_right[axis]

                # Apply actual translation to the VTK actor using transform matrix
                if self.vtk_renderer_right.step_actor:
                    # Get or create user transform
                    transform = self.vtk_renderer_right.step_actor.GetUserTransform()
                    if not transform:
                        import vtk
                        transform = vtk.vtkTransform()
                        self.vtk_renderer_right.step_actor.SetUserTransform(transform)

                    # Apply translation
                    if axis == 'x':
                        transform.Translate(distance, 0, 0)
                    elif axis == 'y':
                        transform.Translate(0, distance, 0)
                    elif axis == 'z':
                        transform.Translate(0, 0, distance)

                    print(f"🔧 Applied {distance}mm translation along {axis.upper()}-axis to transform matrix")

                    self.vtk_renderer_right.render_window.Render()

                # Update the display immediately
                self.update_transform_display()

        except Exception as e:
            print(f"Error in move_shape: {e}")
            self.statusBar().showMessage(f"Move error: {e}")

        # Update transform display if it exists
        try:
            self.update_transform_display()
        except:
            pass

    def setup_vtk_text_overlays(self):
        """Setup VTK text overlays directly on the 3D screens"""
        print("DEBUG: 🔧 Setting up VTK text overlays...")

        try:
            import vtk

            # Setup text overlay for TOP viewer
            if hasattr(self, 'vtk_renderer_left') and self.vtk_renderer_left and hasattr(self.vtk_renderer_left, 'renderer'):
                renderer = self.vtk_renderer_left.renderer

                # Create text actor for position and rotation display (no cursor)
                self.combined_text_actor_left = vtk.vtkTextActor()
                self.combined_text_actor_left.SetInput("ANGLE: 0.0° AXIS: [0.00 0.00 1.00] POS: X=0.000mm Y=0.000mm Z=0.000mm")
                self.combined_text_actor_left.GetTextProperty().SetFontSize(18)  # Good readable size
                self.combined_text_actor_left.GetTextProperty().SetColor(1.0, 1.0, 0.0)  # Yellow
                self.combined_text_actor_left.GetTextProperty().SetBold(True)
                self.combined_text_actor_left.SetPosition(10, 10)  # Bottom left
                renderer.AddActor2D(self.combined_text_actor_left)

                print("DEBUG: ✅ TOP viewer text overlays created")
            else:
                print("🔥 SETUP DEBUG: TOP viewer renderer not ready")

            # Setup text overlay for BOTTOM viewer
            print(f"🔥 SETUP DEBUG: vtk_renderer_right exists = {hasattr(self, 'vtk_renderer_right')}")
            if hasattr(self, 'vtk_renderer_right'):
                print(f"🔥 SETUP DEBUG: vtk_renderer_right value = {self.vtk_renderer_right}")
                if self.vtk_renderer_right:
                    print(f"🔥 SETUP DEBUG: vtk_renderer_right.renderer exists = {hasattr(self.vtk_renderer_right, 'renderer')}")

            if hasattr(self, 'vtk_renderer_right') and self.vtk_renderer_right and hasattr(self.vtk_renderer_right, 'renderer'):
                renderer = self.vtk_renderer_right.renderer
                print(f"🔥 SETUP DEBUG: Got renderer = {renderer}")

                # Create text actor for position and rotation display (no cursor)
                self.combined_text_actor_right = vtk.vtkTextActor()
                self.combined_text_actor_right.SetInput("ANGLE: 0.0° AXIS: [0.00 0.00 1.00] POS: X=0.000mm Y=0.000mm Z=0.000mm")
                self.combined_text_actor_right.GetTextProperty().SetFontSize(18)  # Good readable size
                self.combined_text_actor_right.GetTextProperty().SetColor(1.0, 1.0, 0.0)  # Yellow
                self.combined_text_actor_right.GetTextProperty().SetBold(True)
                self.combined_text_actor_right.SetPosition(10, 10)  # Bottom left
                renderer.AddActor2D(self.combined_text_actor_right)

                print("DEBUG: ✅ BOTTOM viewer text overlays created")
            else:
                print("🔥 SETUP DEBUG: BOTTOM viewer renderer not available - text actor NOT created")
                # Retry after a longer delay if renderers aren't ready
                if not hasattr(self, '_text_overlay_retry_count'):
                    self._text_overlay_retry_count = 0
                if self._text_overlay_retry_count < 5:  # Max 5 retries
                    self._text_overlay_retry_count += 1
                    print(f"🔥 SETUP DEBUG: Retrying text overlay setup in 500ms (attempt {self._text_overlay_retry_count})")
                    QTimer.singleShot(500, self.setup_vtk_text_overlays)

        except Exception as e:
            print(f"DEBUG: Error setting up VTK text overlays: {e}")
            import traceback
            traceback.print_exc()

    def update_vtk_text_overlays(self):
        """Update the VTK text overlays with current values"""

        # DEBUG: Check what variables exist
        debug_msg = f"🔥 VTK UPDATE DEBUG: active_viewer={getattr(self, 'active_viewer', 'NONE')}\n"
        debug_msg += f"  LEFT vars: pos={hasattr(self, 'current_pos_left')}, angle={hasattr(self, 'current_angle_left')}, axis={hasattr(self, 'current_axis_left')}\n"
        debug_msg += f"  RIGHT vars: pos={hasattr(self, 'current_pos_right')}, angle={hasattr(self, 'current_angle_right')}, axis={hasattr(self, 'current_axis_right')}\n"
        if hasattr(self, 'current_axis_left'):
            debug_msg += f"  current_axis_left = {self.current_axis_left}\n"
        if hasattr(self, 'current_axis_right'):
            debug_msg += f"  current_axis_right = {self.current_axis_right}\n"

        with open("debug_rotation.txt", "a", encoding="utf-8") as f:
            f.write(debug_msg)

        try:
            # Update TOP viewer text - show CURRENT values when STEP file is loaded in TOP viewer
            if hasattr(self, 'combined_text_actor_left'):
                # CRITICAL FIX: If STEP file is loaded in TOP viewer, show CURRENT values there
                # Initialize missing variables to prevent display issues
                if not hasattr(self, 'current_pos_left'):
                    self.current_pos_left = getattr(self, 'orig_pos_left', {'x': 0.0, 'y': 0.0, 'z': 0.0}).copy()
                if not hasattr(self, 'current_angle_left'):
                    self.current_angle_left = getattr(self, 'orig_angle_left', 0.0)
                if not hasattr(self, 'current_axis_left'):
                    self.current_axis_left = getattr(self, 'orig_axis_left', {'x': 0.0, 'y': 0.0, 'z': 1.0}).copy()

                # CRITICAL FIX: Show BUTTON rotation values, not mouse rotation values
                if hasattr(self, 'current_pos_left') and hasattr(self, 'model_rot_left'):
                    # Calculate axis and angle from BUTTON rotations (model_rot_left)
                    import math
                    rot_mag = math.sqrt(self.model_rot_left['x']**2 + self.model_rot_left['y']**2 + self.model_rot_left['z']**2)
                    if rot_mag > 0.001:
                        button_axis = {
                            'x': self.model_rot_left['x'] / rot_mag,
                            'y': self.model_rot_left['y'] / rot_mag,
                            'z': self.model_rot_left['z'] / rot_mag
                        }
                        button_angle = rot_mag
                    else:
                        button_axis = {'x': 0.0, 'y': 0.0, 'z': 1.0}
                        button_angle = 0.0

                    # Show BUTTON rotation values in order: ROT, AXIS, ANGLE, POS
                    combined_text = f"ROT: X={self.model_rot_left['x']:.1f}° Y={self.model_rot_left['y']:.1f}° Z={self.model_rot_left['z']:.1f}° AXIS: [{button_axis['x']:.2f} {button_axis['y']:.2f} {button_axis['z']:.2f}] ANGLE: {button_angle:.1f}° POS: X={self.current_pos_left['x']:.3f}mm Y={self.current_pos_left['y']:.3f}mm Z={self.current_pos_left['z']:.3f}mm"
                else:
                    # No STEP file loaded - show zeros
                    combined_text = f"ANGLE: 0.0° AXIS: [0.00 0.00 1.00] POS: X=0.000mm Y=0.000mm Z=0.000mm"
                self.combined_text_actor_left.SetInput(combined_text)

                # Render the update
                if hasattr(self, 'vtk_widget_left'):
                    self.vtk_widget_left.GetRenderWindow().Render()

            # Update BOTTOM viewer text - show CURRENT values (including user modifications, no cursor)
            print(f"🔥 DEBUG: Checking BOTTOM viewer - has_actor={hasattr(self, 'combined_text_actor_right')}")
            with open("debug_display.txt", "a", encoding="utf-8") as f:
                f.write(f"🔥 DEBUG: BOTTOM viewer check - has_actor={hasattr(self, 'combined_text_actor_right')}\n")
            if hasattr(self, 'combined_text_actor_right'):
                # CRITICAL FIX: Initialize missing variables to prevent intermittent resets
                if not hasattr(self, 'current_pos_right'):
                    self.current_pos_right = getattr(self, 'current_pos_left', {'x': 0.0, 'y': 0.0, 'z': 0.0}).copy()
                if not hasattr(self, 'current_angle_right'):
                    self.current_angle_right = getattr(self, 'current_angle_left', 0.0)
                if not hasattr(self, 'current_axis_right'):
                    self.current_axis_right = getattr(self, 'current_axis_left', {'x': 0.0, 'y': 0.0, 'z': 1.0}).copy()

                # CRITICAL FIX: Show SAME values as TOP viewer (model_rot_left)
                if hasattr(self, 'current_pos_right') and hasattr(self, 'model_rot_left'):
                    # Use LEFT viewer rotation values for consistency
                    import math
                    rot_mag = math.sqrt(self.model_rot_left['x']**2 + self.model_rot_left['y']**2 + self.model_rot_left['z']**2)
                    if rot_mag > 0.001:
                        button_axis = {
                            'x': self.model_rot_left['x'] / rot_mag,
                            'y': self.model_rot_left['y'] / rot_mag,
                            'z': self.model_rot_left['z'] / rot_mag
                        }
                        button_angle = rot_mag
                    else:
                        button_axis = {'x': 0.0, 'y': 0.0, 'z': 1.0}
                        button_angle = 0.0

                    # Show SAME rotation values as TOP viewer
                    combined_text = f"ROT: X={self.model_rot_left['x']:.1f}° Y={self.model_rot_left['y']:.1f}° Z={self.model_rot_left['z']:.1f}° AXIS: [{button_axis['x']:.2f} {button_axis['y']:.2f} {button_axis['z']:.2f}] ANGLE: {button_angle:.1f}° POS: X={self.current_pos_right['x']:.3f}mm Y={self.current_pos_right['y']:.3f}mm Z={self.current_pos_right['z']:.3f}mm"
                    print(f"🔥 BOTTOM DISPLAY: Setting text to: {combined_text}")
                    with open("debug_display.txt", "a", encoding="utf-8") as f:
                        f.write(f"🔥 BOTTOM DISPLAY: {combined_text}\n")
                else:
                    # No data - show zeros
                    combined_text = f"ANGLE: 0.0° AXIS: [0.00 0.00 1.00] POS: X=0.000mm Y=0.000mm Z=0.000mm"
                    print(f"🔥 BOTTOM DISPLAY: Missing data - showing defaults: {combined_text}")
                    with open("debug_display.txt", "a", encoding="utf-8") as f:
                        f.write(f"🔥 BOTTOM DISPLAY: Missing data - {combined_text}\n")
                self.combined_text_actor_right.SetInput(combined_text)

                # Render the update
                if hasattr(self, 'vtk_widget_right'):
                    self.vtk_widget_right.GetRenderWindow().Render()

        except Exception as e:
            pass  # Silently handle errors

    def get_step_file_coordinates(self):
        """Get coordinates from the STEP file geometry (bounding box center)"""
        try:
            # Get the active viewer's step actor
            if self.active_viewer == "top" and hasattr(self, 'vtk_renderer_left') and hasattr(self.vtk_renderer_left, 'step_actor') and self.vtk_renderer_left.step_actor:
                actor = self.vtk_renderer_left.step_actor
            elif self.active_viewer == "bottom" and hasattr(self, 'vtk_renderer_right') and hasattr(self.vtk_renderer_right, 'step_actor') and self.vtk_renderer_right.step_actor:
                actor = self.vtk_renderer_right.step_actor
            else:
                return None  # No STEP file loaded

            # Get the bounding box of the STEP geometry
            bounds = actor.GetBounds()  # [xmin, xmax, ymin, ymax, zmin, zmax]

            # Calculate center coordinates
            center_x = (bounds[0] + bounds[1]) / 2.0
            center_y = (bounds[2] + bounds[3]) / 2.0
            center_z = (bounds[4] + bounds[5]) / 2.0

            return {'x': center_x, 'y': center_y, 'z': center_z}

        except Exception as e:
            return None  # Error getting coordinates

    def get_step_file_coordinates_for_viewer(self, viewer):
        """Get STEP file coordinates - both viewers should show IDENTICAL values for same file"""
        try:
            # Check if STEP file is loaded in the requested viewer
            if viewer == "top":
                if not (hasattr(self, 'step_loader_left') and self.step_loader_left.current_polydata):
                    return None
                # Use the STEP file's geometry center (should be same for identical files)
                if hasattr(self, 'orig_pos_left'):
                    return self.orig_pos_left
            elif viewer == "bottom":
                if not (hasattr(self, 'step_loader_right') and self.step_loader_right.current_polydata):
                    return None
                # Use the STEP file's geometry center (should be same for identical files)
                if hasattr(self, 'orig_pos_right'):
                    return self.orig_pos_right

            return None  # No STEP file loaded in this viewer

        except Exception as e:
            print(f"🔍 DEBUG: Error getting coordinates for {viewer}: {e}")
            return None  # Error getting coordinates

    def setup_mouse_tracking(self):
        """Setup SAFE mouse tracking for both cursor position and rotation numbers"""
        print("DEBUG: 🔧 Setting up SAFE mouse tracking...")

        try:
            # Setup for TOP viewer with error handling
            if hasattr(self, 'vtk_widget_left') and self.vtk_widget_left:
                try:
                    # Enable mouse tracking
                    self.vtk_widget_left.setMouseTracking(True)

                    # Get the interactor and add event observers SAFELY
                    interactor = self.vtk_widget_left.GetRenderWindow().GetInteractor()
                    if interactor:
                        # DON'T remove all observers - this breaks mouse rotation!
                        # The VTK interactor style needs its observers to work
                        # Just add our observers without removing existing ones

                        # Add mouse move observer for cursor position
                        interactor.AddObserver('MouseMoveEvent', self.on_mouse_move_safe)
                        # Add interaction observer for rotation numbers
                        interactor.AddObserver('EndInteractionEvent', self.on_interaction_end_safe)
                        print("DEBUG: ✅ TOP viewer SAFE mouse tracking setup complete")

                except Exception as e:
                    print(f"DEBUG: TOP viewer mouse tracking error (continuing): {e}")

            # Setup for BOTTOM viewer with error handling
            if hasattr(self, 'vtk_widget_right') and self.vtk_widget_right:
                try:
                    self.vtk_widget_right.setMouseTracking(True)
                    interactor = self.vtk_widget_right.GetRenderWindow().GetInteractor()
                    if interactor:
                        # DON'T remove all observers - this breaks mouse rotation!
                        # The VTK interactor style needs its observers to work
                        # Just add our observers without removing existing ones

                        interactor.AddObserver('MouseMoveEvent', self.on_mouse_move_safe)
                        interactor.AddObserver('EndInteractionEvent', self.on_interaction_end_safe)
                        print("DEBUG: ✅ BOTTOM viewer SAFE mouse tracking setup complete")
                except Exception as e:
                    print(f"DEBUG: BOTTOM viewer mouse tracking error (continuing): {e}")

        except Exception as e:
            print(f"DEBUG: Error setting up mouse tracking (continuing): {e}")

    def on_mouse_move_safe(self, obj, event):
        """SAFE handle mouse move - update cursor position only"""
        try:
            interactor = obj
            pos = interactor.GetEventPosition()
            self.update_cursor_from_mouse(pos)
            self.update_vtk_text_overlays()
        except Exception as e:
            pass  # Silently handle all errors to prevent crashes

    def on_mouse_move(self, obj, event):
        """Legacy handler - redirects to safe version"""
        self.on_mouse_move_safe(obj, event)

    def on_interaction_end_safe(self, obj, event):
        """SAFE handle end of interaction - update rotation numbers on VTK overlay"""
        try:
            # Update rotation numbers when interaction ends
            self.update_rotation_numbers()
            # Update VTK text overlays
            self.update_vtk_text_overlays()
        except Exception as e:
            pass  # Silently handle all errors to prevent crashes

    def setup_rotation_monitor(self):
        """Setup a timer to periodically check for rotation changes - DISABLED"""
        # DISABLED: This timer interferes with button rotation updates
        # self.rotation_timer = QTimer()
        # self.rotation_timer.timeout.connect(self.check_rotation_changes)
        # self.rotation_timer.start(500)  # Check every 500ms
        print("DEBUG: Rotation monitor timer DISABLED to prevent AXIS interference")

        # Store previous orientations for comparison
        self.prev_left_orientation = [0, 0, 0]
        self.prev_right_orientation = [0, 0, 0]

    def check_rotation_changes(self):
        """Check if VTK actors have rotated and update display"""
        try:
            # Check LEFT viewer
            if hasattr(self, 'vtk_widget_left') and hasattr(self, 'current_rot_left'):
                renderer = self.vtk_widget_left.GetRenderWindow().GetRenderers().GetFirstRenderer()
                if renderer:
                    actors = renderer.GetActors()
                    actors.InitTraversal()
                    actor = actors.GetNextActor()
                    if actor:
                        orientation = actor.GetOrientation()
                        # Check if orientation changed significantly
                        if (abs(orientation[0] - self.prev_left_orientation[0]) > 1.0 or
                            abs(orientation[1] - self.prev_left_orientation[1]) > 1.0 or
                            abs(orientation[2] - self.prev_left_orientation[2]) > 1.0):

                            # Update ANGLE and AXIS values that the display uses
                            if hasattr(self, 'current_angle_left'):
                                import math
                                self.current_angle_left = math.sqrt(orientation[0]**2 + orientation[1]**2 + orientation[2]**2)

                            # Update AXIS based on rotation orientation
                            if hasattr(self, 'current_axis_left'):
                                import math
                                # Normalize the rotation vector to get the axis
                                rot_mag = math.sqrt(orientation[0]**2 + orientation[1]**2 + orientation[2]**2)
                                if rot_mag > 0.001:
                                    self.current_axis_left = {
                                        'x': orientation[0] / rot_mag,
                                        'y': orientation[1] / rot_mag,
                                        'z': orientation[2] / rot_mag
                                    }
                                else:
                                    self.current_axis_left = {'x': 0.0, 'y': 0.0, 'z': 1.0}

                            self.prev_left_orientation = list(orientation)

                            # Update display
                            self.update_vtk_text_overlays()

            # Check RIGHT viewer
            if hasattr(self, 'vtk_widget_right') and hasattr(self, 'current_rot_right'):
                renderer = self.vtk_widget_right.GetRenderWindow().GetRenderers().GetFirstRenderer()
                if renderer:
                    actors = renderer.GetActors()
                    actors.InitTraversal()
                    actor = actors.GetNextActor()
                    if actor:
                        orientation = actor.GetOrientation()
                        # Check if orientation changed significantly
                        if (abs(orientation[0] - self.prev_right_orientation[0]) > 1.0 or
                            abs(orientation[1] - self.prev_right_orientation[1]) > 1.0 or
                            abs(orientation[2] - self.prev_right_orientation[2]) > 1.0):

                            # Update ANGLE and AXIS values that the display uses
                            if hasattr(self, 'current_angle_right'):
                                import math
                                self.current_angle_right = math.sqrt(orientation[0]**2 + orientation[1]**2 + orientation[2]**2)

                            # Update AXIS based on rotation orientation
                            if hasattr(self, 'current_axis_right'):
                                import math
                                # Normalize the rotation vector to get the axis
                                rot_mag = math.sqrt(orientation[0]**2 + orientation[1]**2 + orientation[2]**2)
                                if rot_mag > 0.001:
                                    self.current_axis_right = {
                                        'x': orientation[0] / rot_mag,
                                        'y': orientation[1] / rot_mag,
                                        'z': orientation[2] / rot_mag
                                    }
                                else:
                                    self.current_axis_right = {'x': 0.0, 'y': 0.0, 'z': 1.0}

                            self.prev_right_orientation = list(orientation)

                            # Update display
                            self.update_vtk_text_overlays()

        except Exception as e:
            pass  # Silently handle errors

    def update_rotation_from_vtk_actors(self):
        """Directly read rotation values from VTK actors and update display"""
        print("🔥🔥🔥 HARDCORE DEBUG: update_rotation_from_vtk_actors CALLED!")
        try:
            # Update LEFT viewer rotation
            print("🔥 Checking LEFT viewer...")
            if hasattr(self, 'vtk_widget_left'):
                print("🔥 vtk_widget_left EXISTS")
                if hasattr(self, 'current_rot_left'):
                    print("🔥 current_rot_left EXISTS")
                    renderer = self.vtk_widget_left.GetRenderWindow().GetRenderers().GetFirstRenderer()
                    if renderer:
                        print("🔥 LEFT renderer found")
                        actors = renderer.GetActors()
                        actors.InitTraversal()
                        actor = actors.GetNextActor()
                        if actor:
                            orientation = actor.GetOrientation()
                            print(f"🔥🔥🔥 LEFT ACTOR ORIENTATION: {orientation}")
                            old_rot = self.current_rot_left.copy()
                            self.current_rot_left = {
                                'x': orientation[0],
                                'y': orientation[1],
                                'z': orientation[2]
                            }
                            print(f"🔥 LEFT rotation CHANGED from {old_rot} to {self.current_rot_left}")
                        else:
                            print("🔥 NO LEFT ACTOR FOUND!")
                    else:
                        print("🔥 NO LEFT RENDERER FOUND!")
                else:
                    print("🔥 current_rot_left DOES NOT EXIST!")
            else:
                print("🔥 vtk_widget_left DOES NOT EXIST!")

            # Update RIGHT viewer rotation
            print("🔥 Checking RIGHT viewer...")
            if hasattr(self, 'vtk_widget_right'):
                print("🔥 vtk_widget_right EXISTS")
                if hasattr(self, 'current_rot_right'):
                    print("🔥 current_rot_right EXISTS")
                    renderer = self.vtk_widget_right.GetRenderWindow().GetRenderers().GetFirstRenderer()
                    if renderer:
                        print("🔥 RIGHT renderer found")
                        actors = renderer.GetActors()
                        actors.InitTraversal()
                        actor = actors.GetNextActor()
                        if actor:
                            orientation = actor.GetOrientation()
                            print(f"🔥🔥🔥 RIGHT ACTOR ORIENTATION: {orientation}")
                            old_rot = self.current_rot_right.copy()
                            self.current_rot_right = {
                                'x': orientation[0],
                                'y': orientation[1],
                                'z': orientation[2]
                            }
                            print(f"🔥 RIGHT rotation CHANGED from {old_rot} to {self.current_rot_right}")
                        else:
                            print("🔥 NO RIGHT ACTOR FOUND!")
                    else:
                        print("🔥 NO RIGHT RENDERER FOUND!")
                else:
                    print("🔥 current_rot_right DOES NOT EXIST!")
            else:
                print("🔥 vtk_widget_right DOES NOT EXIST!")
        except Exception as e:
            print(f"🔥🔥🔥 HARDCORE ERROR in update_rotation_from_vtk_actors: {e}")
            import traceback
            traceback.print_exc()

    def on_interaction_end(self, obj, event):
        """Legacy handler - redirects to safe version"""
        self.on_interaction_end_safe(obj, event)

    def check_rotation_changes(self):
        """Timer-based rotation tracking for mouse rotations"""
        try:
            if not hasattr(self, 'vtk_renderer_left') or not hasattr(self.vtk_renderer_left, 'step_actor') or not self.vtk_renderer_left.step_actor:
                return

            # Get current VTK actor orientation
            current_orientation = self.vtk_renderer_left.step_actor.GetOrientation()

            # Initialize if first time
            if not hasattr(self, 'last_orientation_check'):
                self.last_orientation_check = current_orientation
                return

            # Calculate change in orientation
            orientation_change = sum(abs(current_orientation[i] - self.last_orientation_check[i]) for i in range(3))

            # Only update if significant change (> 1 degree total change)
            # AND not immediately after a button rotation
            import time
            recent_button_rotation = hasattr(self, 'button_rotation_time') and (time.time() - self.button_rotation_time) < 0.5

            if orientation_change > 1.0 and not recent_button_rotation:
                print(f"🔧 Mouse rotation detected: change = {orientation_change:.1f}°")

                # Update rotation values from VTK actor
                self.current_rot_left = {
                    'x': current_orientation[0],
                    'y': current_orientation[1],
                    'z': current_orientation[2]
                }

                # Update display
                self.update_vtk_text_overlays()

            # Update last known orientation
            self.last_orientation_check = current_orientation

        except Exception as e:
            pass  # Silently handle errors

    def update_cursor_from_mouse(self, mouse_pos):
        """Update cursor position from mouse coordinates"""
        try:
            # Determine which widget the mouse is over (not just active viewer)
            widget = None
            is_top_viewer = False

            if hasattr(self, 'vtk_widget_left') and self.vtk_widget_left.underMouse():
                widget = self.vtk_widget_left
                is_top_viewer = True
            elif hasattr(self, 'vtk_widget_right') and self.vtk_widget_right.underMouse():
                widget = self.vtk_widget_right
                is_top_viewer = False
            else:
                return

            # Get widget size
            widget_size = widget.GetSize()
            if widget_size[0] <= 0 or widget_size[1] <= 0:
                return

            # Convert to normalized coordinates
            norm_x = mouse_pos[0] / widget_size[0]
            norm_y = mouse_pos[1] / widget_size[1]

            # Calculate 3D position (simple approximation)
            import math
            center_dist = math.sqrt((norm_x - 0.5)**2 + (norm_y - 0.5)**2)

            # Update the correct cursor based on which viewer
            cursor_data = {
                'x': (norm_x - 0.5) * 15.0,  # Scale to model width
                'y': (0.5 - norm_y) * 12.0,  # Flip Y and scale
                'z': 3.0 + center_dist * 3.0  # Vary Z with distance from center
            }

            if is_top_viewer:
                self.cursor_pos_left = cursor_data
            else:
                self.cursor_pos_right = cursor_data

            # Cursor labels removed - now using VTK text overlays

        except Exception as e:
            pass  # Silently handle errors

    def update_rotation_numbers(self):
        """Update rotation numbers from current camera/actor state"""
        try:
            # Only update if STEP file is loaded
            if (self.active_viewer == "top" and
                hasattr(self, 'vtk_renderer_left') and
                hasattr(self.vtk_renderer_left, 'step_actor') and
                self.vtk_renderer_left.step_actor):

                # Get current states
                camera = self.vtk_renderer_left.renderer.GetActiveCamera()
                actor = self.vtk_renderer_left.step_actor

                camera_pos = camera.GetPosition()
                actor_rot = actor.GetOrientation()

                # Calculate rotation display values from camera position
                import math
                distance = math.sqrt(camera_pos[0]**2 + camera_pos[1]**2 + camera_pos[2]**2)

                if distance > 0:
                    # Calculate spherical coordinates for display
                    elevation = math.degrees(math.asin(max(-1, min(1, camera_pos[2] / distance))))
                    azimuth = math.degrees(math.atan2(camera_pos[1], camera_pos[0]))

                    # ANGLE stays the same as original file - don't change it!
                    # Only update AXIS based on rotation
                    if hasattr(self, 'current_axis_left'):
                        # Calculate axis from rotation orientation
                        self.current_axis_left = {
                            'x': elevation / 100.0,  # Normalize for display
                            'y': azimuth / 100.0,    # Normalize for display
                            'z': 1.0                 # Keep Z component
                        }

                # Rotation labels removed - now using VTK text overlays

        except Exception as e:
            pass  # Silently handle errors



    def update_cursor_position(self):
        """Update cursor position based on mouse location over VTK widgets"""
        import math
        try:
            # Get the currently active VTK widget
            if self.active_viewer == "top" and hasattr(self, 'vtk_widget_left'):
                widget = self.vtk_widget_left
            elif self.active_viewer == "bottom" and hasattr(self, 'vtk_widget_right'):
                widget = self.vtk_widget_right
            else:
                return

            # Check if mouse is over the widget and widget exists
            if widget and widget.underMouse():
                # Get global mouse position
                from PyQt5.QtGui import QCursor
                global_pos = QCursor.pos()

                # Convert to widget coordinates
                widget_pos = widget.mapFromGlobal(global_pos)
                x = widget_pos.x()
                y = widget_pos.y()

                # Get widget size
                widget_size = widget.size()
                if widget_size.width() > 0 and widget_size.height() > 0:
                    # Normalize mouse position (0-1)
                    norm_x = max(0, min(1, x / widget_size.width()))
                    norm_y = max(0, min(1, y / widget_size.height()))

                    # Convert to approximate 3D coordinates
                    # Scale based on model bounds (approximately 12.7mm x 9.4mm)
                    # Z varies based on distance from center (simple depth approximation)
                    center_dist = math.sqrt((norm_x - 0.5)**2 + (norm_y - 0.5)**2)
                    z_offset = center_dist * 3.0  # Vary Z based on distance from center

                    self.cursor_pos = {
                        'x': (norm_x - 0.5) * 15.0,  # Scale to model width
                        'y': (0.5 - norm_y) * 12.0,  # Flip Y and scale to model height
                        'z': 3.0 + z_offset  # Z varies with cursor position
                    }

                    # Update cursor position display
                    if hasattr(self, 'lbl_cursor_x'):
                        self.lbl_cursor_x.setText(f"X: {self.cursor_pos['x']:.3f}mm")
                        self.lbl_cursor_y.setText(f"Y: {self.cursor_pos['y']:.3f}mm")
                        self.lbl_cursor_z.setText(f"Z: {self.cursor_pos['z']:.3f}mm")

        except Exception as e:
            # Silently handle errors
            pass

    def update_combined_rotation(self, viewer):
        """Update current rotation to show only model rotation (not camera)"""
        if viewer == "top":
            # Use model_rot_left for display (button rotations), not current_rot_left (mouse rotations)
            display_rot_left = self.model_rot_left.copy()
        else:
            # Use model_rot_right for display (button rotations), not current_rot_right (mouse rotations)
            display_rot_right = self.model_rot_right.copy()

    def change_model_color(self, color_name):
        """Change model color in active viewer"""
        self.statusBar().showMessage(f"{self.active_viewer.title()}: Color changed to {color_name}")

    def show_help(self):
        """Show help dialog"""
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.information(self, "Help", "STEP Viewer Help:\n\n1. Load STEP files\n2. Use rotation buttons\n3. Use movement buttons\n4. Save transformed files")

    def show_help(self):
        """Show help dialog"""
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.information(self, "Help", "STEP Viewer Help:\n\n1. Load STEP files\n2. Use rotation buttons\n3. Use movement buttons\n4. Save transformed files")

    def show_help(self):
        """Show help dialog"""
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.information(self, "Help", "STEP Viewer Help:\n\n1. Load STEP files\n2. Use rotation buttons\n3. Use movement buttons\n4. Save transformed files")

    def show_help(self):
        """Show help dialog"""
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.information(self, "Help", "STEP Viewer Help:\n\n1. Load STEP files\n2. Use rotation buttons\n3. Use movement buttons\n4. Save transformed files")

    def show_help(self):
        """Show help dialog"""
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.information(self, "Help", "STEP Viewer Help:\n\n1. Load STEP files\n2. Use rotation buttons\n3. Use movement buttons\n4. Save transformed files")

    def toggle_bbox_overlay(self):
        """Toggle bounding box in active viewer"""
        if self.active_viewer == "top":
            self.bbox_visible_left = not self.bbox_visible_left
            if self.vtk_renderer_left:
                self.vtk_renderer_left.toggle_bounding_box(self.bbox_visible_left)
            status = "shown" if self.bbox_visible_left else "hidden"
            print(f"TOP bounding box {status}")
        else:
            self.bbox_visible_right = not self.bbox_visible_right
            if self.vtk_renderer_right:
                self.vtk_renderer_right.toggle_bounding_box(self.bbox_visible_right)
            status = "shown" if self.bbox_visible_right else "hidden"
            print(f"BOTTOM bounding box {status}")

        self.statusBar().showMessage(f"{self.active_viewer.title()}: Bounding box {status}")

    def force_view_update(self):
        """Update from Current View - FIXED to read actual VTK state"""
        print("🔧 DEBUG: force_view_update() called!")
        print(f"🔧 DEBUG: active_viewer = {self.active_viewer}")
        try:
            if self.active_viewer == "top":
                renderer = self.vtk_renderer_left
                viewer_name = "TOP"
            else:
                renderer = self.vtk_renderer_right
                viewer_name = "BOTTOM"

            if not renderer.step_actor:
                self.statusBar().showMessage("No model loaded to update from")
                return

            # Get current actor position and orientation
            current_pos = renderer.step_actor.GetPosition()
            current_orientation = renderer.step_actor.GetOrientation()

            print(f"🔧 {viewer_name} current VTK position: {current_pos}")
            print(f"🔧 {viewer_name} current VTK orientation: {current_orientation}")

            # Update our tracking variables with actual VTK values
            if self.active_viewer == "top":
                self.current_pos_left = {
                    'x': current_pos[0],
                    'y': current_pos[1],
                    'z': current_pos[2]
                }
                # DISABLED: This was overwriting button rotation values with VTK orientation!
                # self.current_rot_left = {
                #     'x': current_orientation[0],
                #     'y': current_orientation[1],
                #     'z': current_orientation[2]
                # }
                print(f"🔧 DISABLED: Not overwriting button rotation values with VTK orientation")
                print(f"🔧 Updated TOP tracking: pos={self.current_pos_left}, rot={self.current_rot_left}")
            else:
                self.current_pos_right = {
                    'x': current_pos[0],
                    'y': current_pos[1],
                    'z': current_pos[2]
                }
                self.current_rot_right = {
                    'x': current_orientation[0],
                    'y': current_orientation[1],
                    'z': current_orientation[2]
                }
                print(f"🔧 Updated BOTTOM tracking: pos={self.current_pos_right}, rot={self.current_rot_right}")

            # Update the display
            self.update_transform_display()

            # Update VTK text overlays
            self.update_rotation_numbers()
            self.update_vtk_text_overlays()

            self.statusBar().showMessage(f"{viewer_name}: Updated from current VTK view state")
            print(f"✅ {viewer_name} numbers updated from current VTK view")

        except Exception as e:
            print(f"❌ Error updating from current view: {e}")
            self.statusBar().showMessage(f"Error updating from view: {e}")

    def update_transform_display(self):
        """Update the transform display labels for both viewers"""
        # Update TOP viewer labels with 3 decimal precision
        if hasattr(self, 'lbl_orig_pos_x') and hasattr(self, 'orig_pos_left'):
            self.lbl_orig_pos_x.setText(f"X: {self.orig_pos_left['x']:.3f}mm")
            self.lbl_orig_pos_y.setText(f"Y: {self.orig_pos_left['y']:.3f}mm")
            self.lbl_orig_pos_z.setText(f"Z: {self.orig_pos_left['z']:.3f}mm")

        if hasattr(self, 'lbl_orig_angle') and hasattr(self, 'orig_angle_left'):
            self.lbl_orig_angle.setText(f"{self.orig_angle_left:.1f}°")

        if hasattr(self, 'lbl_orig_axis_x') and hasattr(self, 'orig_axis_left'):
            self.lbl_orig_axis_x.setText(f"X: {self.orig_axis_left['x']:.2f}")
            self.lbl_orig_axis_y.setText(f"Y: {self.orig_axis_left['y']:.2f}")
            self.lbl_orig_axis_z.setText(f"Z: {self.orig_axis_left['z']:.2f}")

        if hasattr(self, 'lbl_curr_pos_x'):
            self.lbl_curr_pos_x.setText(f"X: {self.current_pos_left['x']:.3f}mm")
            self.lbl_curr_pos_y.setText(f"Y: {self.current_pos_left['y']:.3f}mm")
            self.lbl_curr_pos_z.setText(f"Z: {self.current_pos_left['z']:.3f}mm")
            # CRITICAL FIX: Use model_rot_left for button rotation display
            self.lbl_curr_rot_x.setText(f"X: {self.model_rot_left['x']:.3f}°")
            self.lbl_curr_rot_y.setText(f"Y: {self.model_rot_left['y']:.3f}°")
            self.lbl_curr_rot_z.setText(f"Z: {self.model_rot_left['z']:.3f}°")
            print(f"DEBUG: Updated GUI labels - Button ROT: X={self.model_rot_left['x']:.3f}°, Y={self.model_rot_left['y']:.3f}°, Z={self.model_rot_left['z']:.3f}°")

        # Update BOTTOM viewer labels with 3 decimal precision and proper units
        if hasattr(self, 'lbl_orig_pos_x_bottom') and hasattr(self, 'orig_pos_right'):
            self.lbl_orig_pos_x_bottom.setText(f"X: {self.orig_pos_right['x']:.3f}mm")
            self.lbl_orig_pos_y_bottom.setText(f"Y: {self.orig_pos_right['y']:.3f}mm")
            self.lbl_orig_pos_z_bottom.setText(f"Z: {self.orig_pos_right['z']:.3f}mm")

        if hasattr(self, 'lbl_orig_angle_bottom') and hasattr(self, 'orig_angle_right'):
            self.lbl_orig_angle_bottom.setText(f"{self.orig_angle_right:.1f}°")

        if hasattr(self, 'lbl_orig_axis_x_bottom') and hasattr(self, 'orig_axis_right'):
            self.lbl_orig_axis_x_bottom.setText(f"X: {self.orig_axis_right['x']:.2f}")
            self.lbl_orig_axis_y_bottom.setText(f"Y: {self.orig_axis_right['y']:.2f}")
            self.lbl_orig_axis_z_bottom.setText(f"Z: {self.orig_axis_right['z']:.2f}")

        if hasattr(self, 'lbl_curr_pos_x_bottom'):
            self.lbl_curr_pos_x_bottom.setText(f"X: {self.current_pos_right['x']:.3f}mm")
            self.lbl_curr_pos_y_bottom.setText(f"Y: {self.current_pos_right['y']:.3f}mm")
            self.lbl_curr_pos_z_bottom.setText(f"Z: {self.current_pos_right['z']:.3f}mm")
            self.lbl_curr_rot_x_bottom.setText(f"X: {self.current_rot_right['x']:.3f}°")
            self.lbl_curr_rot_y_bottom.setText(f"Y: {self.current_rot_right['y']:.3f}°")
            self.lbl_curr_rot_z_bottom.setText(f"Z: {self.current_rot_right['z']:.3f}°")

    def track_mouse_rotation(self):
        """Track VTK actor rotation changes from mouse dragging - BOTH viewers"""
        try:
            # Track rotation for BOTH viewers
            # TOP VIEWER
            if (hasattr(self, 'vtk_renderer_left') and
                hasattr(self.vtk_renderer_left, 'step_actor') and
                self.vtk_renderer_left.step_actor):

                # Get current VTK actor orientation (this changes when you drag the mouse)
                actor = self.vtk_renderer_left.step_actor
                current_orientation = actor.GetOrientation()  # [X, Y, Z] in degrees

                # Initialize previous orientation if not exists
                if not hasattr(self, 'prev_orientation'):
                    self.prev_orientation = [0.0, 0.0, 0.0]

                # Check if orientation changed (mouse was dragged)
                orientation_changed = (
                    abs(current_orientation[0] - self.prev_orientation[0]) > 0.1 or
                    abs(current_orientation[1] - self.prev_orientation[1]) > 0.1 or
                    abs(current_orientation[2] - self.prev_orientation[2]) > 0.1
                )

                if orientation_changed:  # Mouse rotation detected
                    print(f"🔥 DEBUG: Mouse rotation detected! X={current_orientation[0]:.1f}°, Y={current_orientation[1]:.1f}°, Z={current_orientation[2]:.1f}°")

                    # Update our rotation tracking for mouse rotations
                    if viewer == "top":
                        self.current_rot_left = {
                            'x': current_orientation[0],
                            'y': current_orientation[1],
                            'z': current_orientation[2]
                        }
                        print(f"🔥 DEBUG: Updated TOP rotation: {self.current_rot_left}")
                    else:
                        self.current_rot_right = {
                            'x': current_orientation[0],
                            'y': current_orientation[1],
                            'z': current_orientation[2]
                        }
                        print(f"🔥 DEBUG: Updated BOTTOM rotation: {self.current_rot_right}")

                    # Store current as previous for next check
                    self.prev_orientation = list(current_orientation)
                else:
                    print(f"🔥 DEBUG: No orientation change detected")

            # BOTTOM VIEWER
            if (hasattr(self, 'vtk_renderer_right') and
                hasattr(self.vtk_renderer_right, 'step_actor') and
                self.vtk_renderer_right.step_actor):

                # Get current VTK actor orientation (this changes when you drag the mouse)
                actor = self.vtk_renderer_right.step_actor
                current_orientation_right = actor.GetOrientation()  # [X, Y, Z] in degrees

                # Initialize previous orientation if not exists
                if not hasattr(self, 'prev_orientation_right'):
                    self.prev_orientation_right = [0.0, 0.0, 0.0]

                # Check if orientation changed (mouse rotation)
                if (abs(current_orientation_right[0] - self.prev_orientation_right[0]) > 0.1 or
                    abs(current_orientation_right[1] - self.prev_orientation_right[1]) > 0.1 or
                    abs(current_orientation_right[2] - self.prev_orientation_right[2]) > 0.1):

                    # Update the current rotation values for BOTTOM viewer
                    self.current_rot_right['x'] = current_orientation_right[0]
                    self.current_rot_right['y'] = current_orientation_right[1]
                    self.current_rot_right['z'] = current_orientation_right[2]

                    # Update GUI labels
                    self.update_transform_display()

                    # Store current as previous for next check
                    self.prev_orientation_right = list(current_orientation_right)

        except Exception as e:
            print(f"DEBUG: Error in track_mouse_rotation: {e}")

    def update_camera_display(self):
        """Update position/rotation display when camera moves (mouse rotation)"""
        # Debug: Print every few calls to see if timer is working
        if not hasattr(self, 'timer_count'):
            self.timer_count = 0
        self.timer_count += 1
        if self.timer_count % 10 == 0:  # Every 5 seconds (10 * 500ms)
            print(f"DEBUG: Camera update timer working, call #{self.timer_count}")

        try:
            # Only update if a STEP file is loaded
            if not hasattr(self, 'vtk_renderer_left') or not hasattr(self.vtk_renderer_left, 'step_actor') or not self.vtk_renderer_left.step_actor:
                return  # No STEP file loaded yet

            # Get current camera position for active viewer
            if self.active_viewer == "top" and hasattr(self, 'vtk_renderer_left'):
                camera = self.vtk_renderer_left.renderer.GetActiveCamera()
                current_pos = camera.GetPosition()
                current_focal = camera.GetFocalPoint()

                # Get the actual model bounds from the displayed geometry
                if hasattr(self.vtk_renderer_left, 'step_actor') and self.vtk_renderer_left.step_actor:
                    bounds = self.vtk_renderer_left.step_actor.GetBounds()
                    # Calculate actual displayed model center
                    model_center = [
                        (bounds[0] + bounds[1]) / 2,  # X center
                        (bounds[2] + bounds[3]) / 2,  # Y center
                        (bounds[4] + bounds[5]) / 2   # Z center
                    ]
                else:
                    # Fallback to camera focal point
                    model_center = current_focal

                # Update position display (actual displayed model position)
                self.current_pos_left = {
                    'x': model_center[0],
                    'y': model_center[1],
                    'z': model_center[2]
                }

                # Get actual VTK actor rotation (this is what changes when you drag the mouse)
                if hasattr(self.vtk_renderer_left, 'step_actor') and self.vtk_renderer_left.step_actor:
                    # Get the actor's current orientation
                    actor = self.vtk_renderer_left.step_actor
                    orientation = actor.GetOrientation()  # Returns [X, Y, Z] rotation in degrees

                    print(f"🔥 DEBUG: VTK Actor orientation: X={orientation[0]:.1f}°, Y={orientation[1]:.1f}°, Z={orientation[2]:.1f}°")

                    # Update rotation values from VTK actor orientation (for mouse rotations)
                    self.current_rot_left = {
                        'x': orientation[0],
                        'y': orientation[1],
                        'z': orientation[2]
                    }
                    print(f"🔧 Updated rotation values from VTK actor orientation")
                else:
                    print("🔥 DEBUG: No VTK actor found for rotation tracking")

        except Exception as e:
            print(f"DEBUG: Error in update_camera_display: {e}")

def main():
    print("DEBUG: STARTING STEP VIEWER...")
    app = QApplication(sys.argv)
    app.setApplicationName("TDK STEP Viewer Dual")
    app.setApplicationVersion("3.0")
    print("DEBUG: QApplication created")

    viewer = StepViewerTDK()
    print("DEBUG: StepViewerTDK created")
    viewer.show()
    print("DEBUG: GUI shown, starting event loop...")

    # Start rotation tracking timer - DISABLED to prevent interference with button rotations
    # from PyQt5.QtCore import QTimer
    # rotation_timer = QTimer()
    # rotation_timer.timeout.connect(viewer.check_rotation_changes)
    # rotation_timer.start(100)  # Check every 100ms
    print("DEBUG: Rotation tracking timer DISABLED to prevent AXIS reset")

    sys.exit(app.exec_())

if __name__ == "__main__":
    main()