#!/usr/bin/env python3

print("DEBUGGING BOTTOM VIEWER TEXT DISPLAY")
print("=" * 50)

# Test if the main program creates the bottom text actors properly
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.getcwd())

try:
    # Import the main application
    from step_viewer_tdk_modular import StepViewerApp
    from PyQt5.QtWidgets import QApplication
    
    # Create QApplication
    app = QApplication(sys.argv)
    
    # Create the main window
    print("Creating StepViewerApp...")
    main_window = StepViewerApp()
    
    # Check if bottom text actors exist
    print("\nChecking BOTTOM viewer text actors...")
    
    if hasattr(main_window, 'cursor_text_actor_right'):
        print("✅ cursor_text_actor_right exists")
        actor = main_window.cursor_text_actor_right
        print(f"   Visibility: {actor.GetVisibility()}")
        print(f"   Input: {actor.GetInput()}")
        print(f"   Font size: {actor.GetTextProperty().GetFontSize()}")
        print(f"   Color: {actor.GetTextProperty().GetColor()}")
        position = actor.GetPosition()
        print(f"   Position: ({position[0]}, {position[1]})")
    else:
        print("❌ cursor_text_actor_right does NOT exist")
    
    if hasattr(main_window, 'combined_text_actor_right'):
        print("✅ combined_text_actor_right exists")
        actor = main_window.combined_text_actor_right
        print(f"   Visibility: {actor.GetVisibility()}")
        print(f"   Input: {actor.GetInput()}")
        print(f"   Font size: {actor.GetTextProperty().GetFontSize()}")
        print(f"   Color: {actor.GetTextProperty().GetColor()}")
        position = actor.GetPosition()
        print(f"   Position: ({position[0]}, {position[1]})")
    else:
        print("❌ combined_text_actor_right does NOT exist")
    
    # Check if bottom renderer exists
    print("\nChecking BOTTOM renderer...")
    if hasattr(main_window, 'vtk_renderer_right'):
        print("✅ vtk_renderer_right exists")
        if hasattr(main_window.vtk_renderer_right, 'renderer'):
            renderer = main_window.vtk_renderer_right.renderer
            print(f"   Renderer exists: {renderer is not None}")
            if renderer:
                actors = renderer.GetActors2D()
                print(f"   Number of 2D actors: {actors.GetNumberOfItems()}")
                
                # List all 2D actors
                actors.InitTraversal()
                for i in range(actors.GetNumberOfItems()):
                    actor = actors.GetNextItem()
                    if actor:
                        print(f"   Actor {i}: {type(actor).__name__}")
                        if hasattr(actor, 'GetInput'):
                            print(f"     Input: {actor.GetInput()}")
                        if hasattr(actor, 'GetVisibility'):
                            print(f"     Visibility: {actor.GetVisibility()}")
        else:
            print("❌ vtk_renderer_right.renderer does NOT exist")
    else:
        print("❌ vtk_renderer_right does NOT exist")
    
    # Check if text overlay setup was called
    print("\nChecking text overlay setup...")
    if hasattr(main_window, 'setup_text_overlays'):
        print("✅ setup_text_overlays method exists")
    else:
        print("❌ setup_text_overlays method does NOT exist")
    
    print("\nDone - closing application")
    app.quit()

except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()

print("\nBOTTOM TEXT DEBUG COMPLETE")
