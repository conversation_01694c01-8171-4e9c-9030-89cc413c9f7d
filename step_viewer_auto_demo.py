#!/usr/bin/env python3
"""
AUTO DEMO - Automatically loads files and shows overlay
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer_tdk_modular import StepViewerTDK

class AutoDemo:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = StepViewerTDK()
        self.viewer.show()
        # Timer for automatic demo
        self.timer = QTimer()
        self.timer.timeout.connect(self.run_demo)
        self.step = 0
        print("🎯 AUTO DEMO - Will automatically load files and show overlay")

    def run_demo(self):
        """Run the demo sequence"""
        try:
            if self.step == 0:
                print("🎯 Step 1: Loading TOP file...")
                top_file = "SOIC16P127_1270X940X610L89X51.STEP"
                if os.path.exists(top_file):
                    print(f"🔧 DEBUG: Setting active viewer to 'top'")
                    self.viewer.active_viewer = "top"
                    self.viewer.update_viewer_highlights()
                    print(f"🔧 DEBUG: Loading {top_file} into TOP viewer")
                    success = self.viewer.load_step_file_direct(top_file)
                    print(f"✅ TOP file load result: {success}")
                    # Debug: Check what was actually loaded
                    if hasattr(self.viewer, 'step_loader_left') and self.viewer.step_loader_left.current_polydata:
                        bounds = self.viewer.step_loader_left.current_polydata.GetBounds()
                        points = self.viewer.step_loader_left.current_polydata.GetNumberOfPoints()
                        print(f"🔴 TOP model bounds: X({bounds[0]:.2f} to {bounds[1]:.2f}), Y({bounds[2]:.2f} to {bounds[3]:.2f}), Z({bounds[4]:.2f} to {bounds[5]:.2f})")
                        print(f"🔴 TOP model points: {points}")
                else:
                    print(f"❌ TOP file not found: {top_file}")
                self.step += 1

            elif self.step == 1:
                print("🎯 Step 2: Loading BOTTOM file...")
                bottom_file = "test.step"
                if os.path.exists(bottom_file):
                    print(f"🔧 DEBUG: Setting active viewer to 'bottom'")
                    self.viewer.active_viewer = "bottom"
                    self.viewer.update_viewer_highlights()
                    print(f"🔧 DEBUG: Loading {bottom_file} into BOTTOM viewer")
                    success = self.viewer.load_step_file_direct(bottom_file)
                    print(f"✅ BOTTOM file load result: {success}")
                    # Debug: Check what was actually loaded
                    if hasattr(self.viewer, 'step_loader_right') and self.viewer.step_loader_right.current_polydata:
                        bounds = self.viewer.step_loader_right.current_polydata.GetBounds()
                        points = self.viewer.step_loader_right.current_polydata.GetNumberOfPoints()
                        print(f"🔵 BOTTOM model bounds: X({bounds[0]:.2f} to {bounds[1]:.2f}), Y({bounds[2]:.2f} to {bounds[3]:.2f}), Z({bounds[4]:.2f} to {bounds[5]:.2f})")
                        print(f"🔵 BOTTOM model points: {points}")
                else:
                    print(f"❌ BOTTOM file not found: {bottom_file}")
                self.step += 1

            elif self.step == 2:
                print("🎯 Step 3: Enabling overlay mode (first time)...")
                self.viewer.toggle_viewer_overlay()
                print("✅ Overlay enabled - should show BOTH parts")
                self.step += 1

            elif self.step == 3:
                print("🎯 Step 4: Disabling overlay mode...")
                self.viewer.toggle_viewer_overlay()
                print("✅ Overlay disabled - back to dual view")
                self.step += 1

            elif self.step == 4:
                print("🎯 Step 5: Re-enabling overlay mode (second time)...")
                self.viewer.toggle_viewer_overlay()
                print("✅ Overlay re-enabled - should STILL show BOTH parts")
                print("🔧 Check if you see:")
                print("   - RED: 16-pin SOIC from TOP")
                print("   - GRAY: AMPHENOL connector from BOTTOM")
                print("   - If you only see 16-pin parts, the bug is reproduced!")
                self.step += 1

            elif self.step == 3:
                print("🎯 Step 4: Triggering overlay...")
                self.viewer.toggle_viewer_overlay()
                print("✅ Overlay triggered - You should see both models overlaid!")
                self.step += 1

            else:
                print("✅ OVERLAY TOGGLE TEST COMPLETE!")
                print("🔧 Results:")
                print("   - First overlay enable: Should show BOTH parts")
                print("   - Overlay disable: Back to dual view")
                print("   - Second overlay enable: Should STILL show BOTH parts")
                print("❌ If second overlay only shows 16-pin parts, bug confirmed!")

                # DEBUG: Count total actors in overlay
                if hasattr(self.viewer, 'overlay_renderer') and self.viewer.overlay_renderer:
                    total_actors = self.viewer.overlay_renderer.GetActors().GetNumberOfItems()
                    print(f"🔧 DEBUG: Total actors in overlay renderer: {total_actors}")

                    # List all actors
                    actors = self.viewer.overlay_renderer.GetActors()
                    actors.InitTraversal()
                    for i in range(total_actors):
                        actor = actors.GetNextActor()
                        if actor:
                            bounds = actor.GetBounds()
                            color = actor.GetProperty().GetColor()
                            print(f"🔧 DEBUG: Overlay actor {i}: bounds={bounds}, color={color}, addr={hex(id(actor))}")
                else:
                    print("🔧 DEBUG: No overlay renderer found")
                self.timer.stop()

        except Exception as e:
            print(f"❌ Error in demo step {self.step}: {e}")
            import traceback
            traceback.print_exc()
            self.timer.stop()
    
    def run(self):
        """Start the demo"""
        # Start the demo sequence after 2 seconds
        self.timer.start(2000)
        return self.app.exec_()

if __name__ == "__main__":
    demo = AutoDemo()
    sys.exit(demo.run())
