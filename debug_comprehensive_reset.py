#!/usr/bin/env python3
"""
Comprehensive Reset Debug - Find the EXACT reason why reset doesn't work visually
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular import StepViewerTDK

def get_all_actors_in_renderer(renderer):
    """Get ALL actors in the renderer, not just step_actors"""
    all_actors = []
    
    if hasattr(renderer, 'renderer') and renderer.renderer:
        actor_collection = renderer.renderer.GetActors()
        actor_collection.InitTraversal()
        
        actor = actor_collection.GetNextActor()
        while actor:
            all_actors.append(actor)
            actor = actor_collection.GetNextActor()
    
    return all_actors

def print_all_actors_state(viewer, label=""):
    """Print state of ALL actors in the renderer"""
    print(f"\n🔍 ALL ACTORS STATE - {label}")
    print("=" * 80)
    
    renderer = viewer.vtk_renderer_left
    
    # Get ALL actors in the renderer
    all_actors = get_all_actors_in_renderer(renderer)
    print(f"📊 TOTAL ACTORS IN RENDERER: {len(all_actors)}")
    
    for i, actor in enumerate(all_actors):
        pos = actor.GetPosition()
        orient = actor.GetOrientation()
        visible = actor.GetVisibility()
        bounds = actor.GetBounds()
        
        print(f"   🎭 Actor {i}:")
        print(f"     Type: {type(actor)}")
        print(f"     Position: {pos}")
        print(f"     Orientation: {orient}")
        print(f"     Visible: {visible}")
        print(f"     Bounds: {bounds}")
        print(f"     Memory Address: {hex(id(actor))}")
        
        # Check if this is one of our known actors
        is_step_actor = False
        is_multi_actor = False
        is_bbox_actor = False
        
        if hasattr(renderer, 'step_actor') and renderer.step_actor == actor:
            is_step_actor = True
            print(f"     *** THIS IS THE SINGLE STEP_ACTOR ***")
            
        if hasattr(renderer, 'step_actors') and renderer.step_actors:
            for j, multi_actor in enumerate(renderer.step_actors):
                if multi_actor == actor:
                    is_multi_actor = True
                    print(f"     *** THIS IS MULTI_ACTOR {j} ***")
                    
        if hasattr(renderer, 'bounding_box_actor') and renderer.bounding_box_actor == actor:
            is_bbox_actor = True
            print(f"     *** THIS IS THE BOUNDING BOX ACTOR ***")
            
        if not (is_step_actor or is_multi_actor or is_bbox_actor):
            print(f"     *** UNKNOWN ACTOR TYPE ***")
    
    # Also check our known actor references
    print(f"\n📋 KNOWN ACTOR REFERENCES:")
    if hasattr(renderer, 'step_actors') and renderer.step_actors:
        print(f"   step_actors: {len(renderer.step_actors)} actors")
        for i, actor in enumerate(renderer.step_actors):
            print(f"     Multi-actor {i}: {hex(id(actor))} - Pos={actor.GetPosition()}, Orient={actor.GetOrientation()}")
    else:
        print(f"   step_actors: None or empty")
        
    if hasattr(renderer, 'step_actor') and renderer.step_actor:
        print(f"   step_actor: {hex(id(renderer.step_actor))} - Pos={renderer.step_actor.GetPosition()}, Orient={renderer.step_actor.GetOrientation()}")
    else:
        print(f"   step_actor: None")
        
    if hasattr(renderer, 'bounding_box_actor') and renderer.bounding_box_actor:
        print(f"   bounding_box_actor: {hex(id(renderer.bounding_box_actor))} - Pos={renderer.bounding_box_actor.GetPosition()}")
    else:
        print(f"   bounding_box_actor: None")

def test_individual_actor_transformations(viewer):
    """Test transformations on each individual actor to see which one is visible"""
    print(f"\n🧪 TESTING INDIVIDUAL ACTOR TRANSFORMATIONS")
    print("=" * 80)
    
    renderer = viewer.vtk_renderer_left
    all_actors = get_all_actors_in_renderer(renderer)
    
    for i, actor in enumerate(all_actors):
        if not actor.GetVisibility():
            print(f"🔧 Skipping Actor {i} - not visible")
            continue
            
        print(f"\n🔧 Testing Actor {i} - {type(actor)}")
        
        # Get original state
        orig_pos = actor.GetPosition()
        orig_orient = actor.GetOrientation()
        print(f"   Original: Pos={orig_pos}, Orient={orig_orient}")
        
        # Apply a large, obvious transformation
        print(f"   Applying LARGE transformation: Move +50mm X, Rotate +90° Z")
        actor.AddPosition(50, 0, 0)  # Move 50mm in X
        actor.RotateWXYZ(90, 0, 0, 1)  # Rotate 90° around Z
        actor.Modified()
        
        # Force render
        renderer.render_window.Render()
        viewer.vtk_widget_left.update()
        viewer.update()
        
        # Check new state
        new_pos = actor.GetPosition()
        new_orient = actor.GetOrientation()
        print(f"   After transform: Pos={new_pos}, Orient={new_orient}")
        
        # Ask user if they can see the change
        print(f"👁️ CAN YOU SEE THE MODEL MOVE/ROTATE? Actor {i} transformed for 3 seconds...")
        time.sleep(3)
        
        # Reset this actor
        print(f"   Resetting Actor {i} back to original...")
        actor.SetPosition(orig_pos)
        actor.SetOrientation(orig_orient)
        actor.Modified()
        
        # Force render
        renderer.render_window.Render()
        viewer.vtk_widget_left.update()
        viewer.update()
        
        print(f"👁️ DID THE MODEL MOVE BACK? Actor {i} reset for 2 seconds...")
        time.sleep(2)

def comprehensive_reset_debug():
    """Comprehensive debug to find exactly why reset doesn't work"""
    
    print("🔧 COMPREHENSIVE RESET DEBUG")
    print("=" * 80)
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    # Process events to ensure GUI is ready
    app.processEvents()
    time.sleep(1)
    
    # Find a STEP file to test with
    test_files = [
        "AMPHENOL_U77-A1118-200T.STEP",
        "test.step",
        "sample.stp"
    ]
    
    test_file = None
    for filename in test_files:
        if os.path.exists(filename):
            test_file = filename
            break
    
    if not test_file:
        print("❌ No STEP file found for testing")
        return False
    
    print(f"📁 Using test file: {test_file}")
    
    # Step 1: Load STEP file
    print(f"\n📋 STEP 1: LOADING {test_file}...")
    success = viewer.load_step_file_direct(test_file)
    app.processEvents()
    time.sleep(2)
    
    if not success:
        print("❌ Failed to load STEP file")
        return False
    
    print_all_actors_state(viewer, "AFTER LOADING")
    
    # Step 2: Test individual actor transformations
    test_individual_actor_transformations(viewer)
    
    # Step 3: Test GUI transformations
    print(f"\n📋 STEP 3: TESTING GUI TRANSFORMATIONS...")
    viewer.active_viewer = "top"
    
    print_all_actors_state(viewer, "BEFORE GUI ROTATION")
    
    print("🔧 Applying GUI rotation Z+45°...")
    viewer.rotate_shape("z", 45)
    app.processEvents()
    time.sleep(1)
    
    print_all_actors_state(viewer, "AFTER GUI ROTATION")
    
    print("👁️ Can you see the GUI rotation? (3 seconds)")
    time.sleep(3)
    
    # Step 4: Test GUI reset
    print(f"\n📋 STEP 4: TESTING GUI RESET...")
    
    print("🔧 Applying GUI reset...")
    viewer.reset_to_original()
    app.processEvents()
    time.sleep(1)
    
    print_all_actors_state(viewer, "AFTER GUI RESET")
    
    print("👁️ Can you see the GUI reset? (3 seconds)")
    time.sleep(3)
    
    # Step 5: Final analysis
    print(f"\n📋 STEP 5: FINAL ANALYSIS")
    print("=" * 80)
    
    renderer = viewer.vtk_renderer_left
    all_actors = get_all_actors_in_renderer(renderer)
    
    print(f"🔍 SUMMARY:")
    print(f"   Total actors in renderer: {len(all_actors)}")
    print(f"   step_actors count: {len(renderer.step_actors) if hasattr(renderer, 'step_actors') and renderer.step_actors else 0}")
    print(f"   step_actor exists: {hasattr(renderer, 'step_actor') and bool(renderer.step_actor)}")
    print(f"   bounding_box_actor exists: {hasattr(renderer, 'bounding_box_actor') and bool(renderer.bounding_box_actor)}")
    
    print(f"\n🎯 CONCLUSION:")
    print("Based on the individual actor tests above:")
    print("- The actor that caused visible movement is the one being displayed")
    print("- If NO actor caused visible movement, there may be a rendering issue")
    print("- If multiple actors caused movement, they may be overlapping")
    
    # Keep window open
    print(f"\n👁️ Window will stay open for final inspection...")
    QTimer.singleShot(10000, app.quit)
    app.exec_()
    
    return True

if __name__ == "__main__":
    comprehensive_reset_debug()
