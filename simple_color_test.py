#!/usr/bin/env python3

print("SIMPLE COLOR-TO-COLOR TEST")

# Get STEP file colors (2 dark, 17 light = 19 total)
dark_color = (63, 63, 63)
light_color = (192, 192, 192)

# Create simple pattern: 2 dark, 17 light, repeat
step_pattern = [dark_color, dark_color] + [light_color] * 17
print(f"STEP pattern: {len(step_pattern)} colors")
print(f"Pattern: {step_pattern}")

# Apply this pattern to 1760 display items
display_colors = []
for i in range(1760):
    pattern_index = i % len(step_pattern)
    display_colors.append(step_pattern[pattern_index])

print(f"Display: {len(display_colors)} items")

# Count colors
dark_count = display_colors.count(dark_color)
light_count = display_colors.count(light_color)
print(f"Dark items: {dark_count}")
print(f"Light items: {light_count}")

# Verify first few items
print("\nFirst 25 items:")
for i in range(25):
    print(f"Item {i}: {display_colors[i]}")

print("\nSIMPLE COLOR TEST COMPLETE")
