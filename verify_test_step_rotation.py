#!/usr/bin/env python3
"""
COMPREHENSIVE VERIFICATION TEST for test.step rotation persistence
This test demonstrates the complete workflow:
1. Load test.step
2. Apply specific rotations using buttons
3. Save the rotated file
4. Load the saved file
5. Verify the rotation is preserved and displayed correctly
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer_tdk_modular import StepViewerTDK

class TestStepRotationVerification:
    def __init__(self):
        self.app = QApplication([])
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        # Use a clear, easy-to-verify rotation
        self.test_rotation = {'x': 90.0, 'y': 0.0, 'z': 0.0}  # Simple 90° X rotation
        self.original_filename = "test.step"
        self.saved_filename = "test_rotated_verification.step"
        
        print("🧪 TEST.STEP ROTATION PERSISTENCE VERIFICATION")
        print("=" * 60)
        print(f"📋 Test Plan:")
        print(f"   1. Load {self.original_filename}")
        print(f"   2. Apply rotation: {self.test_rotation}")
        print(f"   3. Save as {self.saved_filename}")
        print(f"   4. Clear viewer and load saved file")
        print(f"   5. Verify rotation is preserved and displayed")
        print("=" * 60)
        
        # Start the test sequence
        QTimer.singleShot(1000, self.step1_load_original)
        
    def step1_load_original(self):
        """Step 1: Load the original test.step file"""
        print(f"\n🔄 STEP 1: Loading original {self.original_filename}...")
        
        if not os.path.exists(self.original_filename):
            print(f"❌ Test file not found: {self.original_filename}")
            print("   Please ensure test.step exists in the current directory")
            self.app.quit()
            return
            
        # Set active viewer to top
        self.viewer.active_viewer = "top"
        self.viewer.update_viewer_highlights()
        
        # Load the file
        success = self.viewer.load_step_file_direct(self.original_filename)
        
        if not success:
            print(f"❌ Failed to load {self.original_filename}")
            self.app.quit()
            return
            
        print(f"✅ Original file loaded: {self.original_filename}")
        
        # Check initial rotation values
        if hasattr(self.viewer, 'model_rot_left'):
            initial_rotation = self.viewer.model_rot_left
            print(f"📊 Initial model_rot_left: {initial_rotation}")
            
            # Verify it starts at zero
            if all(abs(v) < 0.1 for v in initial_rotation.values()):
                print("✅ Initial rotation is zero (as expected)")
            else:
                print(f"⚠️  Initial rotation is not zero: {initial_rotation}")
        else:
            print("❌ model_rot_left not found")
            
        # Check current_rot_left too
        if hasattr(self.viewer, 'current_rot_left'):
            current_rotation = self.viewer.current_rot_left
            print(f"📊 Initial current_rot_left: {current_rotation}")
        else:
            print("❌ current_rot_left not found")
            
        QTimer.singleShot(2000, self.step2_apply_rotation)
        
    def step2_apply_rotation(self):
        """Step 2: Apply a specific rotation using button method"""
        print(f"\n🔄 STEP 2: Applying rotation {self.test_rotation}...")
        
        # Apply the X rotation (90 degrees for clear visual difference)
        print(f"   Applying X rotation: {self.test_rotation['x']}°")
        self.viewer.rotate_shape('x', self.test_rotation['x'])
        
        # Verify the rotation was applied
        if hasattr(self.viewer, 'model_rot_left'):
            applied_rotation = self.viewer.model_rot_left
            print(f"📊 Applied model_rot_left: {applied_rotation}")
            
            # Check if X rotation matches
            x_match = abs(applied_rotation['x'] - self.test_rotation['x']) < 0.1
            y_match = abs(applied_rotation['y'] - self.test_rotation['y']) < 0.1
            z_match = abs(applied_rotation['z'] - self.test_rotation['z']) < 0.1
            
            if x_match and y_match and z_match:
                print("✅ Rotation applied correctly to model_rot_left")
            else:
                print("❌ Rotation not applied correctly to model_rot_left")
                print(f"   Expected: {self.test_rotation}")
                print(f"   Got: {applied_rotation}")
        else:
            print("❌ model_rot_left not found after rotation")
            
        # Also check current_rot_left
        if hasattr(self.viewer, 'current_rot_left'):
            current_rotation = self.viewer.current_rot_left
            print(f"📊 Applied current_rot_left: {current_rotation}")
        
        QTimer.singleShot(2000, self.step3_save_file)
        
    def step3_save_file(self):
        """Step 3: Save the rotated file"""
        print(f"\n🔄 STEP 3: Saving rotated file as {self.saved_filename}...")
        
        try:
            # Get the current values that will be used for saving
            if self.viewer.active_viewer == "top":
                loader = self.viewer.step_loader_left
                current_rot = self.viewer.model_rot_left if hasattr(self.viewer, 'model_rot_left') else {'x': 0, 'y': 0, 'z': 0}
                current_pos = self.viewer.current_pos_left if hasattr(self.viewer, 'current_pos_left') else {'x': 0, 'y': 0, 'z': 0}
                orig_rot = {'x': 0, 'y': 0, 'z': 0}  # Button rotations start from zero
                orig_pos = self.viewer.orig_pos_left if hasattr(self.viewer, 'orig_pos_left') else {'x': 0, 'y': 0, 'z': 0}
            
            print(f"📊 Saving with rotation values:")
            print(f"   current_rot (model_rot_left): {current_rot}")
            print(f"   orig_rot: {orig_rot}")
            print(f"   Delta will be: {{'x': {current_rot['x'] - orig_rot['x']}, 'y': {current_rot['y'] - orig_rot['y']}, 'z': {current_rot['z'] - orig_rot['z']}}}")
            
            # Use the internal save method directly
            success = self.viewer._save_step_with_transformations(
                self.saved_filename, loader, current_pos, current_rot, orig_pos, orig_rot
            )
            
            if success and os.path.exists(self.saved_filename):
                file_size = os.path.getsize(self.saved_filename)
                print(f"✅ File saved successfully: {file_size:,} bytes")
                
                # Check if it's a valid STEP file
                with open(self.saved_filename, 'r') as f:
                    first_line = f.readline().strip()
                    
                if first_line.startswith('ISO-10303'):
                    print("✅ Valid STEP file format")
                else:
                    print(f"❌ Invalid STEP file format, first line: {first_line}")
                    
            else:
                print("❌ File save failed")
                self.app.quit()
                return
                
        except Exception as e:
            print(f"❌ Save failed with exception: {e}")
            import traceback
            traceback.print_exc()
            self.app.quit()
            return
            
        QTimer.singleShot(2000, self.step4_clear_and_load_saved)
        
    def step4_clear_and_load_saved(self):
        """Step 4: Clear viewer and load the saved file"""
        print(f"\n🔄 STEP 4: Clearing viewer and loading saved file...")
        
        # Clear the current view
        self.viewer.clear_view()
        print("✅ Viewer cleared")
        
        # Check model_rot_left after clear (should still have the rotation values due to our fix)
        if hasattr(self.viewer, 'model_rot_left'):
            after_clear_rotation = self.viewer.model_rot_left
            print(f"📊 model_rot_left after clear: {after_clear_rotation}")
            
        # Load the saved file
        success = self.viewer.load_step_file_direct(self.saved_filename)
        
        if not success:
            print(f"❌ Failed to load saved file: {self.saved_filename}")
            self.app.quit()
            return
            
        print(f"✅ Saved file loaded: {self.saved_filename}")
        
        QTimer.singleShot(2000, self.step5_verify_rotation)
        
    def step5_verify_rotation(self):
        """Step 5: Verify the rotation was preserved and is displayed correctly"""
        print(f"\n🔄 STEP 5: Verifying rotation preservation and display...")
        
        # Check all rotation tracking variables
        rotation_data = {}
        
        if hasattr(self.viewer, 'model_rot_left'):
            rotation_data['model_rot_left'] = self.viewer.model_rot_left
            print(f"📊 Loaded model_rot_left: {rotation_data['model_rot_left']}")
        else:
            rotation_data['model_rot_left'] = {'x': 0, 'y': 0, 'z': 0}
            print("❌ model_rot_left not found after loading")
            
        if hasattr(self.viewer, 'current_rot_left'):
            rotation_data['current_rot_left'] = self.viewer.current_rot_left
            print(f"📊 Loaded current_rot_left: {rotation_data['current_rot_left']}")
        else:
            rotation_data['current_rot_left'] = {'x': 0, 'y': 0, 'z': 0}
            print("❌ current_rot_left not found after loading")
            
        if hasattr(self.viewer, 'orig_rot_left'):
            rotation_data['orig_rot_left'] = self.viewer.orig_rot_left
            print(f"📊 Loaded orig_rot_left: {rotation_data['orig_rot_left']}")
        else:
            rotation_data['orig_rot_left'] = {'x': 0, 'y': 0, 'z': 0}
            print("❌ orig_rot_left not found after loading")
        
        # The key test: verify rotation preservation
        print(f"\n🔍 VERIFICATION ANALYSIS:")
        print(f"   Expected rotation: {self.test_rotation}")
        
        rotation_preserved = False
        preservation_source = None
        
        # Check each rotation source
        for source_name, rot_values in rotation_data.items():
            x_match = abs(rot_values['x'] - self.test_rotation['x']) < 1.0  # 1 degree tolerance
            y_match = abs(rot_values['y'] - self.test_rotation['y']) < 1.0
            z_match = abs(rot_values['z'] - self.test_rotation['z']) < 1.0
            
            print(f"   {source_name}: X={rot_values['x']:.1f}° Y={rot_values['y']:.1f}° Z={rot_values['z']:.1f}°", end="")
            
            if x_match and y_match and z_match:
                print(" ✅ MATCHES!")
                rotation_preserved = True
                preservation_source = source_name
            else:
                print(" ❌ No match")
                
        print(f"\n" + "=" * 60)
        if rotation_preserved:
            print(f"🎉 SUCCESS: Rotation persistence is WORKING!")
            print(f"✅ Rotation preserved in: {preservation_source}")
            print(f"✅ Expected: X={self.test_rotation['x']:.1f}° Y={self.test_rotation['y']:.1f}° Z={self.test_rotation['z']:.1f}°")
            print(f"✅ Got: X={rotation_data[preservation_source]['x']:.1f}° Y={rotation_data[preservation_source]['y']:.1f}° Z={rotation_data[preservation_source]['z']:.1f}°")
            
            # Additional verification: check if the model is visually rotated
            print(f"\n🎯 VISUAL VERIFICATION:")
            print(f"   The model should now appear rotated 90° around the X-axis")
            print(f"   This means it should look different from the original orientation")
            
        else:
            print(f"❌ FAILED: Rotation persistence is BROKEN!")
            print(f"   Expected: {self.test_rotation}")
            for source_name, rot_values in rotation_data.items():
                print(f"   {source_name}: {rot_values}")
                
        print("=" * 60)
        
        # Clean up test file
        if os.path.exists(self.saved_filename):
            os.remove(self.saved_filename)
            print(f"🧹 Cleaned up test file: {self.saved_filename}")
            
        # Keep window open for user to see the results
        print(f"\n⏰ Keeping window open for 10 seconds to view results...")
        print(f"   You should see the model rotated 90° around X-axis if working correctly")
        QTimer.singleShot(10000, self.app.quit)

def main():
    test = TestStepRotationVerification()
    test.app.exec_()

if __name__ == "__main__":
    main()
