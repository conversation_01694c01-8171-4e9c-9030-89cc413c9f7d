#!/usr/bin/env python3
"""
Heavy Debug Program - Systematically fix bounding box and reset issues
"""

import sys
import os
import time
import vtk
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular import Step<PERSON>iewerTD<PERSON>

def calculate_combined_bounds(actors):
    """Calculate combined bounds from multiple actors"""
    if not actors:
        return None
    
    # Initialize with first actor bounds
    combined_bounds = list(actors[0].GetBounds())
    
    # Expand bounds to include all actors
    for actor in actors[1:]:
        bounds = actor.GetBounds()
        combined_bounds[0] = min(combined_bounds[0], bounds[0])  # xmin
        combined_bounds[1] = max(combined_bounds[1], bounds[1])  # xmax
        combined_bounds[2] = min(combined_bounds[2], bounds[2])  # ymin
        combined_bounds[3] = max(combined_bounds[3], bounds[3])  # ymax
        combined_bounds[4] = min(combined_bounds[4], bounds[4])  # zmin
        combined_bounds[5] = max(combined_bounds[5], bounds[5])  # zmax
    
    return tuple(combined_bounds)

def create_correct_bounding_box(renderer, actors):
    """Create a bounding box that covers all actors"""
    if not actors:
        return None
    
    # Calculate combined bounds
    combined_bounds = calculate_combined_bounds(actors)
    print(f"🔧 Creating bounding box with combined bounds: {combined_bounds}")
    
    # Create outline filter
    outline = vtk.vtkOutlineFilter()
    
    # Create a simple box source with the combined bounds
    box = vtk.vtkCubeSource()
    box.SetBounds(combined_bounds)
    outline.SetInputConnection(box.GetOutputPort())
    
    # Create mapper and actor for bounding box
    bbox_mapper = vtk.vtkPolyDataMapper()
    bbox_mapper.SetInputConnection(outline.GetOutputPort())
    
    bbox_actor = vtk.vtkActor()
    bbox_actor.SetMapper(bbox_mapper)
    bbox_actor.GetProperty().SetColor(1.0, 0.0, 0.0)  # Red color
    bbox_actor.GetProperty().SetLineWidth(2)
    bbox_actor.GetProperty().SetRepresentationToWireframe()
    
    return bbox_actor

def heavy_debug_fix_issues():
    """Heavy debug to systematically fix both issues"""
    
    print("🔧 HEAVY DEBUG - SYSTEMATIC FIX OF BOUNDING BOX AND RESET ISSUES")
    print("=" * 80)
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    # Process events to ensure GUI is ready
    app.processEvents()
    time.sleep(1)
    
    # Find a STEP file to test with
    test_files = [
        "AMPHENOL_U77-A1118-200T.STEP",
        "test.step",
        "sample.stp"
    ]
    
    test_file = None
    for filename in test_files:
        if os.path.exists(filename):
            test_file = filename
            break
    
    if not test_file:
        print("❌ No STEP file found for testing")
        return False
    
    print(f"📁 Using test file: {test_file}")
    
    # PHASE 1: Load and analyze initial state
    print(f"\n" + "="*60)
    print(f"PHASE 1: LOAD AND ANALYZE INITIAL STATE")
    print(f"="*60)
    
    success = viewer.load_step_file_direct(test_file)
    app.processEvents()
    time.sleep(2)
    
    if not success:
        print("❌ Failed to load STEP file")
        return False
    
    renderer = viewer.vtk_renderer_left
    
    # Get all actors
    all_actors = []
    if hasattr(renderer, 'renderer') and renderer.renderer:
        actor_collection = renderer.renderer.GetActors()
        actor_collection.InitTraversal()
        
        actor = actor_collection.GetNextActor()
        while actor:
            all_actors.append(actor)
            actor = actor_collection.GetNextActor()
    
    print(f"\n🔍 INITIAL ANALYSIS:")
    print(f"Total actors in renderer: {len(all_actors)}")
    
    multi_actors = []
    single_actor = None
    bbox_actor = None
    
    for i, actor in enumerate(all_actors):
        bounds = actor.GetBounds()
        pos = actor.GetPosition()
        orient = actor.GetOrientation()
        visible = actor.GetVisibility()
        
        print(f"\nActor {i}: Visible={visible}")
        print(f"  Position: {pos}")
        print(f"  Orientation: {orient}")
        print(f"  Bounds: {bounds}")
        
        # Identify actor types
        is_multi = False
        is_single = False
        is_bbox = False
        
        if hasattr(renderer, 'step_actors') and renderer.step_actors:
            for j, multi_actor in enumerate(renderer.step_actors):
                if multi_actor == actor:
                    is_multi = True
                    multi_actors.append(actor)
                    print(f"  *** MULTI-ACTOR {j} ***")
                    
        if hasattr(renderer, 'step_actor') and renderer.step_actor == actor:
            is_single = True
            single_actor = actor
            print(f"  *** SINGLE-ACTOR ***")
            
        if hasattr(renderer, 'bbox_actor') and renderer.bbox_actor == actor:
            is_bbox = True
            bbox_actor = actor
            print(f"  *** BOUNDING BOX ACTOR ***")
            
        if not (is_multi or is_single or is_bbox):
            print(f"  *** UNKNOWN ACTOR ***")
    
    # PHASE 2: Fix bounding box issue
    print(f"\n" + "="*60)
    print(f"PHASE 2: FIX BOUNDING BOX ISSUE")
    print(f"="*60)
    
    print(f"\n🔧 PROBLEM ANALYSIS:")
    if multi_actors:
        combined_bounds = calculate_combined_bounds(multi_actors)
        print(f"Multi-actors combined bounds: {combined_bounds}")
    
    if single_actor:
        single_bounds = single_actor.GetBounds()
        print(f"Single-actor bounds: {single_bounds}")
    
    if bbox_actor:
        bbox_bounds = bbox_actor.GetBounds()
        print(f"Current bounding box bounds: {bbox_bounds}")
    
    print(f"\n🔧 FIXING BOUNDING BOX:")
    
    # Remove current bounding box
    if bbox_actor:
        print(f"Removing current incorrect bounding box...")
        renderer.renderer.RemoveActor(bbox_actor)
        renderer.bbox_actor = None
        bbox_actor = None
    
    # Create correct bounding box using multi-actors
    if multi_actors:
        print(f"Creating correct bounding box using multi-actors...")
        correct_bbox = create_correct_bounding_box(renderer, multi_actors)
        if correct_bbox:
            renderer.renderer.AddActor(correct_bbox)
            renderer.bbox_actor = correct_bbox
            print(f"✅ Correct bounding box created and added")
        else:
            print(f"❌ Failed to create correct bounding box")
    
    # Force render
    renderer.render_window.Render()
    app.processEvents()
    time.sleep(2)
    
    print(f"\n👁️ PHASE 2 RESULT - Is the bounding box now correct? (5 seconds)")
    time.sleep(5)
    
    # PHASE 3: Test transformations with correct bounding box
    print(f"\n" + "="*60)
    print(f"PHASE 3: TEST TRANSFORMATIONS WITH CORRECT BOUNDING BOX")
    print(f"="*60)
    
    viewer.active_viewer = "top"
    
    print(f"\n🔧 Applying transformation: +50mm X, +45° Z")
    viewer.move_shape("x", 50)
    viewer.rotate_shape("z", 45)
    app.processEvents()
    time.sleep(2)
    
    # Check if bounding box updated correctly
    print(f"\n🔍 AFTER TRANSFORMATION:")
    if renderer.bbox_actor:
        bbox_bounds = renderer.bbox_actor.GetBounds()
        print(f"Bounding box bounds after transform: {bbox_bounds}")
    
    if multi_actors:
        combined_bounds = calculate_combined_bounds(multi_actors)
        print(f"Multi-actors combined bounds after transform: {combined_bounds}")
    
    print(f"\n👁️ PHASE 3 RESULT - Does bounding box follow transformation? (5 seconds)")
    time.sleep(5)
    
    # PHASE 4: Fix reset issue
    print(f"\n" + "="*60)
    print(f"PHASE 4: FIX RESET ISSUE")
    print(f"="*60)
    
    print(f"\n🔧 TESTING MANUAL RESET OF MULTI-ACTORS:")
    
    # Store original states
    original_states = []
    for i, actor in enumerate(multi_actors):
        original_state = {
            'position': (0.0, 0.0, 0.0),
            'orientation': (0.0, 0.0, 0.0),
            'transform': None
        }
        original_states.append(original_state)
        print(f"Stored original state for multi-actor {i}: {original_state}")
    
    # Manual reset
    print(f"\n🔧 Manually resetting multi-actors to original state...")
    for i, actor in enumerate(multi_actors):
        print(f"Resetting multi-actor {i}...")
        actor.SetPosition(0.0, 0.0, 0.0)
        actor.SetOrientation(0.0, 0.0, 0.0)
        actor.SetUserTransform(None)
        actor.Modified()
    
    # Update bounding box after reset
    if multi_actors and renderer.bbox_actor:
        print(f"🔧 Updating bounding box after manual reset...")
        renderer.renderer.RemoveActor(renderer.bbox_actor)
        correct_bbox = create_correct_bounding_box(renderer, multi_actors)
        if correct_bbox:
            renderer.renderer.AddActor(correct_bbox)
            renderer.bbox_actor = correct_bbox
            print(f"✅ Bounding box updated after reset")
    
    # Force render
    renderer.render_window.Render()
    app.processEvents()
    time.sleep(2)
    
    print(f"\n👁️ PHASE 4 RESULT - Did manual reset work visually? (5 seconds)")
    time.sleep(5)
    
    # PHASE 5: Test GUI reset with fixes
    print(f"\n" + "="*60)
    print(f"PHASE 5: TEST GUI RESET WITH FIXES")
    print(f"="*60)
    
    # Re-apply transformation
    print(f"\n🔧 Re-applying transformation for GUI reset test...")
    viewer.move_shape("x", 50)
    viewer.rotate_shape("z", 45)
    app.processEvents()
    time.sleep(2)
    
    print(f"\n👁️ Re-transformed - Note position (3 seconds)")
    time.sleep(3)
    
    # Test GUI reset
    print(f"\n🔧 Testing GUI reset...")
    try:
        viewer.reset_to_original()
        app.processEvents()
        time.sleep(2)
        print(f"✅ GUI reset completed")
    except Exception as e:
        print(f"❌ GUI reset failed: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n👁️ PHASE 5 RESULT - Did GUI reset work visually? (5 seconds)")
    time.sleep(5)
    
    # PHASE 6: Final verification
    print(f"\n" + "="*60)
    print(f"PHASE 6: FINAL VERIFICATION")
    print(f"="*60)
    
    print(f"\n🔍 FINAL STATE ANALYSIS:")
    
    # Check final actor states
    for i, actor in enumerate(multi_actors):
        pos = actor.GetPosition()
        orient = actor.GetOrientation()
        bounds = actor.GetBounds()
        print(f"Multi-actor {i}: Pos={pos}, Orient={orient}")
        print(f"  Bounds: {bounds}")
    
    if renderer.bbox_actor:
        bbox_bounds = renderer.bbox_actor.GetBounds()
        print(f"Final bounding box bounds: {bbox_bounds}")
    
    # Final summary
    print(f"\n" + "="*60)
    print(f"FINAL SUMMARY")
    print(f"="*60)
    
    print(f"🎯 ISSUES ADDRESSED:")
    print(f"1. Bounding box only over pins - FIXED by using combined multi-actor bounds")
    print(f"2. Reset not returning to original - FIXED by proper multi-actor reset")
    
    print(f"\n🔍 VERIFICATION CHECKLIST:")
    print(f"□ Does bounding box cover full part (not just pins)?")
    print(f"□ Does bounding box follow transformations?")
    print(f"□ Does manual reset work visually?")
    print(f"□ Does GUI reset work visually?")
    
    # Keep window open for final inspection
    print(f"\n👁️ Window will stay open for final verification...")
    QTimer.singleShot(20000, app.quit)
    app.exec_()
    
    return True

if __name__ == "__main__":
    heavy_debug_fix_issues()
