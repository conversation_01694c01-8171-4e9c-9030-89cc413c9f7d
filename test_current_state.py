#!/usr/bin/env python3
"""
Test Current State - See what's broken
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular import Step<PERSON>iewerTDK

def test_current_state():
    """Test what's currently broken"""
    
    print("🔧 TEST CURRENT STATE")
    print("=" * 40)
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    # Process events to ensure GUI is ready
    app.processEvents()
    time.sleep(1)
    
    # Find a STEP file to test with
    test_files = [
        "AMPHENOL_U77-A1118-200T.STEP",
        "test.step",
        "sample.stp"
    ]
    
    test_file = None
    for filename in test_files:
        if os.path.exists(filename):
            test_file = filename
            break
    
    if not test_file:
        print("❌ No STEP file found for testing")
        return False
    
    print(f"📁 Using test file: {test_file}")
    
    # Test 1: Load model
    print(f"\n📋 TEST 1: LOADING MODEL...")
    success = viewer.load_step_file_direct(test_file)
    app.processEvents()
    time.sleep(3)
    
    if not success:
        print("❌ Model loading failed")
        return False
    
    print("✅ Model loaded")
    print("👁️ Check if model and bounding box look correct (10 seconds)")
    time.sleep(10)
    
    # Test 2: Apply transformation
    print(f"\n📋 TEST 2: APPLYING TRANSFORMATION...")
    viewer.active_viewer = "top"
    
    print("🔧 Moving +50mm X")
    viewer.move_shape("x", 50)
    app.processEvents()
    time.sleep(2)
    
    print("🔧 Rotating +45° Z")
    viewer.rotate_shape("z", 45)
    app.processEvents()
    time.sleep(2)
    
    print("👁️ Check if transformation works (5 seconds)")
    time.sleep(5)
    
    # Test 3: Reset
    print(f"\n📋 TEST 3: TESTING RESET...")
    
    print("🔧 Calling reset...")
    viewer.reset_to_original()
    app.processEvents()
    time.sleep(3)
    
    print("👁️ Check if reset works (10 seconds)")
    time.sleep(10)
    
    print(f"\n🎯 RESULTS:")
    print(f"1. Does model load correctly?")
    print(f"2. Does bounding box cover full model?")
    print(f"3. Do transformations work?")
    print(f"4. Does reset work?")
    
    # Keep window open
    print(f"\n👁️ Window will stay open for inspection...")
    QTimer.singleShot(15000, app.quit)
    app.exec_()
    
    return True

if __name__ == "__main__":
    test_current_state()
