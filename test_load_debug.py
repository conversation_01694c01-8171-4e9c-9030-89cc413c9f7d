#!/usr/bin/env python3
"""
Test loading test.step to see debug messages about STEP file value extraction
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer_tdk_modular import StepViewerTDK

class LoadDebugTest:
    def __init__(self):
        self.app = QApplication([])
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        print("🧪 LOAD DEBUG TEST: Loading test.step to see extracted values")
        print("=" * 60)
        
        # Start the test
        QTimer.singleShot(1000, self.load_test_step)
        
    def load_test_step(self):
        """Load test.step and capture debug output"""
        print("\n🔄 Loading test.step into TOP window...")
        
        if not os.path.exists("test.step"):
            print("❌ test.step not found!")
            self.app.quit()
            return
            
        # Set active viewer to top
        self.viewer.active_viewer = "top"
        self.viewer.update_viewer_highlights()
        
        # Load the file
        success = self.viewer.load_step_file_direct("test.step")
        
        if success:
            print("✅ test.step loaded successfully")
            
            # Check what values were extracted
            if hasattr(self.viewer, 'orig_pos_left'):
                print(f"📊 Extracted orig_pos_left: {self.viewer.orig_pos_left}")
            else:
                print("❌ orig_pos_left not found")
                
            if hasattr(self.viewer, 'orig_rot_left'):
                print(f"📊 Extracted orig_rot_left: {self.viewer.orig_rot_left}")
            else:
                print("❌ orig_rot_left not found")
                
            if hasattr(self.viewer, 'current_pos_left'):
                print(f"📊 Current current_pos_left: {self.viewer.current_pos_left}")
            else:
                print("❌ current_pos_left not found")
                
            if hasattr(self.viewer, 'current_rot_left'):
                print(f"📊 Current current_rot_left: {self.viewer.current_rot_left}")
            else:
                print("❌ current_rot_left not found")
                
        else:
            print("❌ Failed to load test.step")
            
        # Keep window open for a few seconds
        QTimer.singleShot(5000, self.app.quit)

def main():
    test = LoadDebugTest()
    test.app.exec_()

if __name__ == "__main__":
    main()
