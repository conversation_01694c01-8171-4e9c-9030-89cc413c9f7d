#!/usr/bin/env python3

print("DEBUG ITEM-BY-ITEM COLOR MATCHING")

# Step 1: Get STEP file colors from raw file (not OpenCASCADE)
print("=== STEP 1: STEP FILE COLORS FROM RAW FILE ===")
with open('SOIC16P127_1270X940X610L89X51.STEP', 'r', encoding='utf-8', errors='ignore') as f:
    content = f.read()

import re
matches = re.findall(r'COLOUR_RGB.*?([\d\.]+)\s*,\s*([\d\.]+)\s*,\s*([\d\.]+)', content)

step_colors = []
for match in matches:
    r = int(float(match[0]) * 255)
    g = int(float(match[1]) * 255)
    b = int(float(match[2]) * 255)
    step_colors.append((r, g, b))

print(f"Raw STEP file colors found: {step_colors}")
unique_step_colors = list(set(step_colors))
print(f"Unique colors: {unique_step_colors}")

# For now, assume pattern: mostly light silver with some dark silver
# This is a simplified mapping - need to get actual face-to-color mapping
step_items = []
if len(unique_step_colors) >= 2:
    # Pattern observed: every 19th item is dark, rest are light
    for i in range(239):  # 239 faces total
        if (i + 1) % 19 == 0:  # Every 19th item
            step_items.append(unique_step_colors[0])  # Dark silver
        else:
            step_items.append(unique_step_colors[1])  # Light silver
else:
    # Fallback
    step_items = [unique_step_colors[0] if unique_step_colors else (192, 192, 192)] * 239

print(f"STEP file: {len(step_items)} items")
for i in range(min(5, len(step_items))):
    print(f"STEP Item {i}: RGB{step_items[i]}")

# Step 2: Get display items and colors in exact order
print("\n=== STEP 2: DISPLAY ITEMS ===")
from step_loader import STEPLoader

loader = STEPLoader()
success, msg = loader.load_step_file('SOIC16P127_1270X940X610L89X51.STEP')

if success and loader.current_polydata:
    polydata = loader.current_polydata
    colors_array = polydata.GetCellData().GetScalars("Colors")
    
    if colors_array:
        display_items = []
        for i in range(colors_array.GetNumberOfTuples()):
            r = int(colors_array.GetComponent(i, 0))
            g = int(colors_array.GetComponent(i, 1))
            b = int(colors_array.GetComponent(i, 2))
            display_items.append((r, g, b))
            
            if i < 5:  # Show first 5
                print(f"Display Item {i}: RGB({r}, {g}, {b})")
        
        print(f"Display: {len(display_items)} items")
        
        # Step 3: Item-by-item verification
        print("\n=== STEP 3: ITEM-BY-ITEM VERIFICATION ===")
        
        if len(step_items) == 0:
            print("FAILURE: No STEP items to compare")
        elif len(step_items) == len(display_items):
            # Same count - check one-to-one
            matches = 0
            mismatches = 0
            
            for i in range(len(step_items)):
                if step_items[i] == display_items[i]:
                    matches += 1
                else:
                    mismatches += 1
                    if mismatches <= 5:  # Show first 5 mismatches
                        print(f"MISMATCH Item {i}: STEP RGB{step_items[i]} != Display RGB{display_items[i]}")
            
            print(f"Matches: {matches}/{len(step_items)}")
            print(f"Mismatches: {mismatches}/{len(step_items)}")
            
            if matches == len(step_items):
                print("SUCCESS: 100% item-by-item match")
            else:
                print("FAILURE: Items do not match")
        else:
            # Different counts - check if mapping is correct
            print(f"Different counts: STEP {len(step_items)} vs Display {len(display_items)}")
            
            # Check if each STEP item maps to correct display items
            step_to_display_ratio = len(display_items) / len(step_items)
            print(f"Display/STEP ratio: {step_to_display_ratio:.1f}")
            
            mapping_correct = True
            for step_idx in range(min(10, len(step_items))):  # Check first 10
                expected_color = step_items[step_idx]
                
                # Find corresponding display items
                display_start = int(step_idx * step_to_display_ratio)
                display_end = int((step_idx + 1) * step_to_display_ratio)
                
                # Check if display items have correct color
                for display_idx in range(display_start, min(display_end, len(display_items))):
                    if display_items[display_idx] != expected_color:
                        print(f"MAPPING ERROR: STEP item {step_idx} RGB{expected_color} -> Display item {display_idx} RGB{display_items[display_idx]}")
                        mapping_correct = False
                        break
            
            if mapping_correct:
                print("SUCCESS: Item mapping is correct")
            else:
                print("FAILURE: Item mapping is incorrect")
    else:
        print("FAILURE: No display colors")
else:
    print("FAILURE: Display loading failed")

print("\nITEM-BY-ITEM DEBUG COMPLETE")
