#!/usr/bin/env python3
"""
Debug rotation loading to see exactly what happens when loading a rotated STEP file
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from step_viewer_tdk_modular_fixed import StepViewerTDK

def debug_rotation_loading():
    """Debug the rotation loading process"""
    print("🔍 Debugging rotation loading process...")
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    
    # Step 1: Load original file and apply rotation
    print("\n📂 Step 1: Loading original test.step...")
    viewer.active_viewer = "top"
    success = viewer.load_step_file_direct('test.step')
    
    if success:
        print("✅ Original file loaded")
        print(f"📊 TOP orig_rot_left: {viewer.orig_rot_left}")
        print(f"📊 TOP current_rot_left: {viewer.current_rot_left}")
        
        # Apply 45° X rotation
        print("\n🔄 Step 2: Applying 45° X rotation...")
        viewer.rotate_shape('x', 45)
        print(f"📊 After rotation - TOP current_rot_left: {viewer.current_rot_left}")
        
        # Save the rotated file
        print("\n💾 Step 3: Saving rotated file...")
        import tempfile
        temp_file = tempfile.mktemp(suffix='.step')
        viewer.save_step_file_option1_direct(temp_file)
        print(f"💾 Saved to: {temp_file}")
        
        # Step 4: Load the saved file in BOTTOM viewer
        print("\n📂 Step 4: Loading saved file in BOTTOM viewer...")
        viewer.active_viewer = "bottom"
        
        # Add debug prints to track what happens during loading
        print("🔍 BEFORE loading - BOTTOM values:")
        if hasattr(viewer, 'orig_rot_right'):
            print(f"📊 BOTTOM orig_rot_right: {viewer.orig_rot_right}")
        if hasattr(viewer, 'current_rot_right'):
            print(f"📊 BOTTOM current_rot_right: {viewer.current_rot_right}")
        
        success2 = viewer.load_step_file_direct(temp_file)
        
        if success2:
            print("✅ Rotated file loaded")
            print("🔍 AFTER loading - BOTTOM values:")
            print(f"📊 BOTTOM orig_rot_right: {viewer.orig_rot_right}")
            print(f"📊 BOTTOM current_rot_right: {viewer.current_rot_right}")
            
            # Check if the values are correct
            expected_x = 45.0
            actual_x = viewer.current_rot_right['x']
            
            print(f"\n🎯 RESULT:")
            print(f"Expected X rotation: {expected_x}°")
            print(f"Actual X rotation: {actual_x}°")
            
            if abs(actual_x - expected_x) < 1.0:
                print("✅ SUCCESS: Rotation values are correct!")
            else:
                print("❌ FAILED: Rotation values are incorrect")
                print("🔍 Let's check what the STEP file actually contains...")
                
                # Test the extraction method directly
                result = viewer._analyze_step_coordinate_system_from_vectors([1.0, 0.0, 0.0], [0.0, -0.707106781187, 0.707106781187])
                print(f"📊 Direct extraction test: {result}")
                
        else:
            print("❌ Failed to load rotated file")
            
        # Clean up
        try:
            os.remove(temp_file)
            print(f"🧹 Cleaned up: {temp_file}")
        except:
            pass
            
    else:
        print("❌ Failed to load original file")
    
    # Close after 5 seconds
    QTimer.singleShot(5000, app.quit)
    
    print("\n🎯 Debug completed. GUI will close in 5 seconds...")
    viewer.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    debug_rotation_loading()
