#!/usr/bin/env python3
"""
Comprehensive STEP File Diagnostics Tool
Analyzes geometry, colors, cells, and actor creation in detail
"""

import sys
import os
import vtk
from step_loader import <PERSON><PERSON><PERSON>oader

def analyze_polydata_geometry(polydata, name=""):
    """Analyze polydata geometry in detail"""
    print(f"\n{'='*60}")
    print(f"📊 POLYDATA GEOMETRY ANALYSIS: {name}")
    print(f"{'='*60}")
    
    # Basic info
    num_points = polydata.GetNumberOfPoints()
    num_cells = polydata.GetNumberOfCells()
    bounds = polydata.GetBounds()
    
    print(f"🔢 Points: {num_points}")
    print(f"🔢 Cells: {num_cells}")
    print(f"📏 Bounds: X({bounds[0]:.3f} to {bounds[1]:.3f}), Y({bounds[2]:.3f} to {bounds[3]:.3f}), Z({bounds[4]:.3f} to {bounds[5]:.3f})")
    
    # Analyze cell types
    cell_types = {}
    for i in range(num_cells):
        cell = polydata.GetCell(i)
        cell_type = cell.GetCellType()
        cell_type_name = cell.GetClassName()
        if cell_type_name not in cell_types:
            cell_types[cell_type_name] = 0
        cell_types[cell_type_name] += 1
    
    print(f"🔺 Cell Types:")
    for cell_type, count in cell_types.items():
        print(f"   {cell_type}: {count} cells")
    
    # Analyze point distribution
    points = polydata.GetPoints()
    x_coords = []
    y_coords = []
    z_coords = []
    
    for i in range(num_points):
        point = points.GetPoint(i)
        x_coords.append(point[0])
        y_coords.append(point[1])
        z_coords.append(point[2])
    
    print(f"📍 Point Distribution:")
    print(f"   X: min={min(x_coords):.3f}, max={max(x_coords):.3f}, range={max(x_coords)-min(x_coords):.3f}")
    print(f"   Y: min={min(y_coords):.3f}, max={max(y_coords):.3f}, range={max(y_coords)-min(y_coords):.3f}")
    print(f"   Z: min={min(z_coords):.3f}, max={max(z_coords):.3f}, range={max(z_coords)-min(z_coords):.3f}")

def analyze_color_data(polydata, name=""):
    """Analyze color data in detail"""
    print(f"\n{'='*60}")
    print(f"🎨 COLOR DATA ANALYSIS: {name}")
    print(f"{'='*60}")
    
    # Check for color data
    cell_data = polydata.GetCellData()
    color_array = cell_data.GetScalars("Colors")
    
    if not color_array:
        print("❌ No color data found")
        return None
    
    num_tuples = color_array.GetNumberOfTuples()
    num_components = color_array.GetNumberOfComponents()
    
    print(f"🎨 Color Array Info:")
    print(f"   Tuples: {num_tuples}")
    print(f"   Components: {num_components}")
    print(f"   Data Type: {color_array.GetDataTypeAsString()}")
    
    # Analyze color distribution
    color_counts = {}
    color_to_cells = {}  # color -> list of cell IDs
    
    for i in range(num_tuples):
        if num_components >= 3:
            r = int(color_array.GetComponent(i, 0))
            g = int(color_array.GetComponent(i, 1))
            b = int(color_array.GetComponent(i, 2))
            color = (r, g, b)
        else:
            color = (int(color_array.GetValue(i)),)
        
        if color not in color_counts:
            color_counts[color] = 0
            color_to_cells[color] = []
        
        color_counts[color] += 1
        color_to_cells[color].append(i)
    
    print(f"🌈 Color Distribution:")
    for color, count in sorted(color_counts.items()):
        percentage = (count / num_tuples) * 100
        print(f"   RGB{color}: {count} cells ({percentage:.1f}%)")
    
    return color_to_cells

def analyze_cells_by_color(polydata, color_to_cells, name=""):
    """Analyze geometry of cells grouped by color"""
    print(f"\n{'='*60}")
    print(f"🔍 CELLS BY COLOR ANALYSIS: {name}")
    print(f"{'='*60}")
    
    if not color_to_cells:
        print("❌ No color data to analyze")
        return
    
    points = polydata.GetPoints()
    
    for color, cell_ids in color_to_cells.items():
        print(f"\n🎨 Color RGB{color} - {len(cell_ids)} cells:")
        
        # Collect all points used by cells of this color
        used_points = set()
        cell_bounds = [float('inf'), float('-inf'), float('inf'), float('-inf'), float('inf'), float('-inf')]
        
        for cell_id in cell_ids:
            cell = polydata.GetCell(cell_id)
            
            # Update bounds based on cell points
            for i in range(cell.GetNumberOfPoints()):
                point_id = cell.GetPointId(i)
                used_points.add(point_id)
                point = points.GetPoint(point_id)
                
                # Update bounds
                cell_bounds[0] = min(cell_bounds[0], point[0])  # min X
                cell_bounds[1] = max(cell_bounds[1], point[0])  # max X
                cell_bounds[2] = min(cell_bounds[2], point[1])  # min Y
                cell_bounds[3] = max(cell_bounds[3], point[1])  # max Y
                cell_bounds[4] = min(cell_bounds[4], point[2])  # min Z
                cell_bounds[5] = max(cell_bounds[5], point[2])  # max Z
        
        print(f"   📍 Points used: {len(used_points)}")
        print(f"   📏 Bounds: X({cell_bounds[0]:.3f} to {cell_bounds[1]:.3f}), Y({cell_bounds[2]:.3f} to {cell_bounds[3]:.3f}), Z({cell_bounds[4]:.3f} to {cell_bounds[5]:.3f})")
        
        # Calculate dimensions
        x_range = cell_bounds[1] - cell_bounds[0]
        y_range = cell_bounds[3] - cell_bounds[2]
        z_range = cell_bounds[5] - cell_bounds[4]
        print(f"   📐 Dimensions: X={x_range:.3f}, Y={y_range:.3f}, Z={z_range:.3f}")
        
        # Sample some cells to see their point coordinates
        print(f"   🔍 Sample cells (first 3):")
        for i, cell_id in enumerate(cell_ids[:3]):
            cell = polydata.GetCell(cell_id)
            print(f"      Cell {cell_id}: {cell.GetNumberOfPoints()} points")
            for j in range(min(3, cell.GetNumberOfPoints())):  # Show first 3 points
                point_id = cell.GetPointId(j)
                point = points.GetPoint(point_id)
                print(f"         Point {point_id}: ({point[0]:.3f}, {point[1]:.3f}, {point[2]:.3f})")

def simulate_vtk_actor_creation(polydata, color_to_cells, name=""):
    """Simulate VTK actor creation like the real code does"""
    print(f"\n{'='*60}")
    print(f"🎭 VTK ACTOR SIMULATION: {name}")
    print(f"{'='*60}")
    
    if not color_to_cells:
        print("❌ No color data - would create single actor")
        return
    
    print(f"🎭 Simulating creation of {len(color_to_cells)} actors...")
    
    for i, (color, cell_ids) in enumerate(color_to_cells.items()):
        print(f"\n🎨 Actor {i} - Color RGB{color}:")
        
        # Create new polydata like the real code
        color_polydata = vtk.vtkPolyData()
        color_polydata.SetPoints(polydata.GetPoints())  # Same points as original code
        
        # Create new cell array with only cells for this color
        color_cells = vtk.vtkCellArray()
        for cell_id in cell_ids:
            cell = polydata.GetCell(cell_id)
            color_cells.InsertNextCell(cell)
        
        color_polydata.SetPolys(color_cells)
        
        # Create mapper and actor
        mapper = vtk.vtkPolyDataMapper()
        mapper.SetInputData(color_polydata)
        mapper.SetScalarVisibility(False)
        
        actor = vtk.vtkActor()
        actor.SetMapper(mapper)
        
        # Get bounds
        actor_bounds = actor.GetBounds()
        
        print(f"   📏 Actor bounds: X({actor_bounds[0]:.3f} to {actor_bounds[1]:.3f}), Y({actor_bounds[2]:.3f} to {actor_bounds[3]:.3f}), Z({actor_bounds[4]:.3f} to {actor_bounds[5]:.3f})")
        print(f"   🔢 Polydata points: {color_polydata.GetNumberOfPoints()}")
        print(f"   🔢 Polydata cells: {color_polydata.GetNumberOfCells()}")
        
        # Check if bounds match expected
        if (abs(actor_bounds[0] + 4.7) < 0.1 and abs(actor_bounds[1] - 4.7) < 0.1 and 
            abs(actor_bounds[2] + 4.7) < 0.1 and abs(actor_bounds[3] - 4.7) < 0.1):
            print(f"   ⚠️  WARNING: This actor has the problematic bounds (-4.7, 4.7)!")

def comprehensive_step_analysis(filename):
    """Perform comprehensive analysis of a STEP file"""
    print(f"🔬 COMPREHENSIVE STEP FILE ANALYSIS")
    print(f"📁 File: {filename}")
    print(f"{'='*80}")
    
    if not os.path.exists(filename):
        print(f"❌ File not found: {filename}")
        return
    
    # Load STEP file
    print(f"📂 Loading STEP file...")
    loader = STEPLoader()
    result = loader.load_step_file(filename)
    
    if len(result) == 3:
        polydata, success, message = result
    else:
        success, message = result
        polydata = loader.current_polydata
    
    if not success or not polydata:
        print(f"❌ Failed to load STEP file: {message}")
        return
    
    print(f"✅ STEP file loaded successfully: {message}")
    
    # Analyze original polydata
    analyze_polydata_geometry(polydata, "ORIGINAL POLYDATA")
    
    # Analyze color data
    color_to_cells = analyze_color_data(polydata, "ORIGINAL POLYDATA")
    
    # Analyze cells by color
    analyze_cells_by_color(polydata, color_to_cells, "ORIGINAL POLYDATA")
    
    # Simulate VTK actor creation
    simulate_vtk_actor_creation(polydata, color_to_cells, "ORIGINAL POLYDATA")
    
    print(f"\n{'='*80}")
    print(f"🏁 ANALYSIS COMPLETE")
    print(f"{'='*80}")

if __name__ == "__main__":
    print("🔬 COMPREHENSIVE STEP DIAGNOSTICS TOOL")
    print("=" * 80)
    
    # Analyze both files
    print("\n" + "="*40 + " 16-PIN SOIC " + "="*40)
    comprehensive_step_analysis("SOIC16P127_1270X940X610L89X51.STEP")
    
    print("\n" + "="*40 + " 8-PIN SOIC " + "="*40)
    comprehensive_step_analysis("test.step")
