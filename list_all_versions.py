#!/usr/bin/env python3
"""
LIST ALL NUMBERED VERSIONS
Show all 40+ versions and help identify which ones to test
"""

import os
import re
from datetime import datetime

def list_all_versions():
    """List all numbered versions with details"""
    print("📁 ALL NUMBERED VERSIONS IN CURRENT DIRECTORY")
    print("=" * 60)
    
    # Find all files with version numbers
    version_files = []
    
    for file in os.listdir('.'):
        if file.endswith('.py'):
            # Look for patterns like _rev1, _rev13, _working, etc.
            if '_rev' in file or '_working' in file or file.endswith('_backup.py'):
                try:
                    stat = os.stat(file)
                    size = stat.st_size
                    mtime = datetime.fromtimestamp(stat.st_mtime)
                    version_files.append((file, size, mtime))
                except:
                    version_files.append((file, 0, None))
    
    # Sort by modification time (newest first)
    version_files.sort(key=lambda x: x[2] if x[2] else datetime.min, reverse=True)
    
    print(f"Found {len(version_files)} numbered versions:\n")
    
    for i, (file, size, mtime) in enumerate(version_files, 1):
        time_str = mtime.strftime("%Y-%m-%d %H:%M") if mtime else "Unknown"
        size_kb = size // 1024 if size > 0 else 0
        
        # Extract version info
        version_info = ""
        if '_rev' in file:
            match = re.search(r'_rev(\d+)', file)
            if match:
                version_info = f"Rev {match.group(1)}"
        elif '_working' in file:
            version_info = "Working"
        elif '_backup' in file:
            version_info = "Backup"
        
        print(f"{i:2d}. {file:<40} {version_info:<10} {size_kb:4d}KB {time_str}")
    
    return version_files

def suggest_test_versions(version_files):
    """Suggest which versions to test based on names and dates"""
    print("\n🎯 SUGGESTED VERSIONS TO TEST:")
    print("=" * 40)
    
    # Look for key versions
    suggestions = []
    
    for file, size, mtime in version_files:
        if '_working' in file:
            suggestions.append((file, "Working version - likely stable"))
        elif '_rev1' in file and file.endswith('_rev1.py'):
            suggestions.append((file, "Rev 1 - early version"))
        elif '_backup' in file:
            suggestions.append((file, "Backup - might be stable"))
    
    # Also suggest recent versions
    if len(version_files) > 0:
        recent = version_files[0]  # Most recent
        if recent[0] not in [s[0] for s in suggestions]:
            suggestions.append((recent[0], "Most recent version"))
    
    if len(version_files) > 5:
        middle = version_files[len(version_files)//2]  # Middle version
        if middle[0] not in [s[0] for s in suggestions]:
            suggestions.append((middle[0], "Middle version - might be stable"))
    
    for i, (file, reason) in enumerate(suggestions, 1):
        print(f"{i}. {file}")
        print(f"   → {reason}")
        print()

def create_test_script():
    """Create a script to test specific versions"""
    print("🔧 CREATING VERSION TEST SCRIPT...")
    
    test_script = '''#!/usr/bin/env python3
"""
TEST SPECIFIC VERSION
Quick test of rotation and colors for a specific version
"""

import sys
import os
import shutil

def test_version(version_file):
    """Test a specific version file"""
    if not os.path.exists(version_file):
        print(f"❌ {version_file} not found")
        return False
    
    print(f"🧪 TESTING: {version_file}")
    print("-" * 40)
    
    # Backup current version
    if os.path.exists("step_viewer_tdk_modular.py"):
        shutil.copy("step_viewer_tdk_modular.py", "step_viewer_tdk_modular_current_backup.py")
        print("✅ Backed up current version")
    
    # Copy test version to main name
    shutil.copy(version_file, "step_viewer_tdk_modular.py")
    print(f"✅ Copied {version_file} to main program")
    
    print("\\n🎯 NOW TEST MANUALLY:")
    print("1. Run: python step_viewer_tdk_modular.py")
    print("2. Load debug_auto_saved.step")
    print("3. Test X+15° button multiple times")
    print("4. Check if rotation increments: 15° → 30° → 45°")
    print("5. Check if colors display correctly")
    
    return True

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python test_version_script.py <version_file>")
        print("Example: python test_version_script.py step_viewer_tdk_modular_working.py")
        sys.exit(1)
    
    version_file = sys.argv[1]
    test_version(version_file)
'''
    
    with open('test_version_script.py', 'w') as f:
        f.write(test_script)
    
    print("✅ Created test_version_script.py")
    print("\nUsage: python test_version_script.py <version_file>")

def main():
    print("🗂️  VERSION MANAGER - LIST ALL 40+ VERSIONS")
    print("=" * 60)
    
    version_files = list_all_versions()
    suggest_test_versions(version_files)
    create_test_script()
    
    print("\n📋 NEXT STEPS:")
    print("1. Pick a version from the suggestions above")
    print("2. Run: python test_version_script.py <version_file>")
    print("3. Test rotation and colors manually")
    print("4. If it works, we can apply our fixes to that version")

if __name__ == "__main__":
    main()
