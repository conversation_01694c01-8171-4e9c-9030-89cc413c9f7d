@echo off
:: Enable delayed expansion for variables at the beginning
setlocal enabledelayedexpansion

echo ========================================
echo 3D Viewer Program Backup Script
echo ========================================

set DEST_DIR=e:\python\viewer\save
set REV_FILE=%DEST_DIR%\revision.txt

:: Create destination directory if it doesn't exist
if not exist "%DEST_DIR%" (
    echo Creating save directory: %DEST_DIR%
    mkdir "%DEST_DIR%"
)

:: Check if rev.txt exists and read/increment revision number
if exist "%REV_FILE%" (
    :: Read current revision number from file
    set /p CURRENT_REV=<"%REV_FILE%"
    echo Current revision read from file: !CURRENT_REV!
    :: Increment revision number (handle empty case)
    if "!CURRENT_REV!"=="" (
        set NEXT_REV=1
    ) else (
        set /a NEXT_REV=!CURRENT_REV!+1
    )
    echo Calculated next revision: !NEXT_REV!
) else (
    :: First time - start with revision 1
    set NEXT_REV=1
    echo First time - starting with revision 1
)

:: Save new revision number to file
echo !NEXT_REV! > "%REV_FILE%"

echo.
echo Copying files with _REV!NEXT_REV! suffix:
echo ----------------------------------------

:: Copy the 6 modular files with revision numbers
copy "step_viewer_tdk_modular.py" "%DEST_DIR%\step_viewer_tdk_modular_REV!NEXT_REV!.py"
if !errorlevel! equ 0 (echo   ✓ step_viewer_tdk_modular_REV!NEXT_REV!.py) else (echo   ✗ Failed: step_viewer_tdk_modular.py)

copy "vtk_renderer.py" "%DEST_DIR%\vtk_renderer_REV!NEXT_REV!.py"
if !errorlevel! equ 0 (echo   ✓ vtk_renderer_REV!NEXT_REV!.py) else (echo   ✗ Failed: vtk_renderer.py)

copy "step_loader.py" "%DEST_DIR%\step_loader_REV!NEXT_REV!.py"
if !errorlevel! equ 0 (echo   ✓ step_loader_REV!NEXT_REV!.py) else (echo   ✗ Failed: step_loader.py)

copy "gui_components.py" "%DEST_DIR%\gui_components_REV!NEXT_REV!.py"
if !errorlevel! equ 0 (echo   ✓ gui_components_REV!NEXT_REV!.py) else (echo   ✗ Failed: gui_components.py)

copy "simple_step_modifier.py" "%DEST_DIR%\simple_step_modifier_REV!NEXT_REV!.py"
if !errorlevel! equ 0 (echo   ✓ simple_step_modifier_REV!NEXT_REV!.py) else (echo   ✗ Failed: simple_step_modifier.py)

copy "requirements.txt" "%DEST_DIR%\requirements_REV!NEXT_REV!.txt"
if !errorlevel! equ 0 (echo   ✓ requirements_REV!NEXT_REV!.txt) else (echo   ✗ Failed: requirements.txt)

echo.
echo ========================================
echo Backup completed! 
echo Files saved to: %DEST_DIR%
echo Next revision will be: !NEXT_REV!
echo ========================================

echo.
echo Files created with REV!NEXT_REV!:
dir "%DEST_DIR%\*_REV!NEXT_REV!*" /b 2>nul

pause
