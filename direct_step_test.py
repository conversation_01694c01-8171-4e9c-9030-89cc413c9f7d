#!/usr/bin/env python3

import sys
import os

# Force output to appear
sys.stdout.flush()

print("DIRECT STEP TEST STARTING", flush=True)

# Check if file exists
step_file = 'SOIC16P127_1270X940X610L89X51.STEP'
if os.path.exists(step_file):
    print(f"File exists: {step_file}", flush=True)
else:
    print(f"File NOT found: {step_file}", flush=True)
    sys.exit(1)

try:
    print("Importing step_loader...", flush=True)
    from step_loader import STEPLoader
    print("step_loader imported", flush=True)
    
    print("Creating loader...", flush=True)
    loader = STEPLoader()
    print("Loader created", flush=True)
    
    print("Loading STEP file...", flush=True)
    success, message = loader.load_step_file(step_file)
    print(f"Result: success={success}, message={message}", flush=True)
    
    if success:
        print("STEP file loaded successfully!", flush=True)
        if hasattr(loader, 'current_polydata') and loader.current_polydata:
            print(f"Polydata: {loader.current_polydata.GetNumberOfCells()} cells", flush=True)
        else:
            print("No polydata available", flush=True)
    else:
        print(f"STEP loading failed: {message}", flush=True)
        
except Exception as e:
    print(f"ERROR: {e}", flush=True)
    import traceback
    traceback.print_exc()

print("DIRECT STEP TEST COMPLETED", flush=True)
