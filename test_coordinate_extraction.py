#!/usr/bin/env python3
"""
Test the new coordinate system extraction vs the old hardcoded values
"""

import os
import re
import math

def extract_step_coordinate_system_new(filename):
    """New dynamic extraction method"""
    try:
        if not filename or not os.path.exists(filename):
            print(f"STEP file not found: {filename}")
            return None, None
            
        with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Find the main AXIS2_PLACEMENT_3D (usually #11)
        axis_match = re.search(r'#11 = AXIS2_PLACEMENT_3D\(\'\',(#\d+),(#\d+),(#\d+)\);', content)
        if not axis_match:
            print(f"Could not find main AXIS2_PLACEMENT_3D in {filename}")
            return None, None
            
        origin_ref = axis_match.group(1)
        z_dir_ref = axis_match.group(2)
        x_dir_ref = axis_match.group(3)
        
        print(f"Found AXIS2_PLACEMENT_3D: origin={origin_ref}, z_dir={z_dir_ref}, x_dir={x_dir_ref}")
        
        # Extract origin point
        origin_pattern = f'{origin_ref} = CARTESIAN_POINT\\(\'\'\\,\\(([^)]+)\\)\\);'
        origin_match = re.search(origin_pattern, content)
        if origin_match:
            coords = [float(x.strip()) for x in origin_match.group(1).split(',')]
            orig_pos = {'x': coords[0], 'y': coords[1], 'z': coords[2]}
        else:
            orig_pos = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            
        # Extract Z direction
        z_dir_pattern = f'{z_dir_ref} = DIRECTION\\(\'\'\\,\\(([^)]+)\\)\\);'
        z_dir_match = re.search(z_dir_pattern, content)
        if z_dir_match:
            z_direction = [float(x.strip()) for x in z_dir_match.group(1).split(',')]
        else:
            z_direction = [0, 0, 1]
            
        # Extract X direction  
        x_dir_pattern = f'{x_dir_ref} = DIRECTION\\(\'\'\\,\\(([^)]+)\\)\\);'
        x_dir_match = re.search(x_dir_pattern, content)
        if x_dir_match:
            x_direction = [float(x.strip()) for x in x_dir_match.group(1).split(',')]
        else:
            x_direction = [1, 0, 0]
        
        # Calculate rotation from direction vectors
        if abs(z_direction[2] - 1.0) < 0.001 and abs(x_direction[0] - 1.0) < 0.001:
            # Standard orientation
            orig_rot = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        else:
            # Calculate Euler angles from direction vectors
            z_rot = math.degrees(math.atan2(x_direction[1], x_direction[0]))
            x_rot = math.degrees(math.atan2(z_direction[1], z_direction[2]))
            y_rot = math.degrees(math.atan2(-z_direction[0], math.sqrt(z_direction[1]**2 + z_direction[2]**2)))
            
            orig_rot = {'x': x_rot, 'y': y_rot, 'z': z_rot}
        
        print(f"NEW METHOD EXTRACTED:")
        print(f"  Origin: {orig_pos}")
        print(f"  Z Direction: {z_direction}")
        print(f"  X Direction: {x_direction}")
        print(f"  Calculated Rotation: {orig_rot}")
        
        return orig_pos, orig_rot
        
    except Exception as e:
        print(f"Error extracting STEP coordinate system: {e}")
        return None, None

def get_old_hardcoded_values():
    """Old hardcoded values from the program"""
    orig_pos = {'x': -4.190000000000000, 'y': -3.667300000000000, 'z': 0.491400000000000}
    orig_rot = {'x': 0.0, 'y': 0.0, 'z': 9.0}
    
    print(f"OLD HARDCODED VALUES:")
    print(f"  Position: {orig_pos}")
    print(f"  Rotation: {orig_rot}")
    
    return orig_pos, orig_rot

def compare_methods(filename):
    """Compare old vs new methods"""
    print(f"\n{'='*60}")
    print(f"COMPARING EXTRACTION METHODS FOR: {filename}")
    print(f"{'='*60}")
    
    # Test new method
    print(f"\n1. NEW DYNAMIC METHOD:")
    new_pos, new_rot = extract_step_coordinate_system_new(filename)
    
    # Show old method
    print(f"\n2. OLD HARDCODED METHOD:")
    old_pos, old_rot = get_old_hardcoded_values()
    
    # Compare
    print(f"\n3. COMPARISON:")
    if new_pos and old_pos:
        pos_diff_x = abs(new_pos['x'] - old_pos['x'])
        pos_diff_y = abs(new_pos['y'] - old_pos['y'])
        pos_diff_z = abs(new_pos['z'] - old_pos['z'])
        
        print(f"  Position differences:")
        print(f"    X: {pos_diff_x:.6f} ({new_pos['x']:.6f} vs {old_pos['x']:.6f})")
        print(f"    Y: {pos_diff_y:.6f} ({new_pos['y']:.6f} vs {old_pos['y']:.6f})")
        print(f"    Z: {pos_diff_z:.6f} ({new_pos['z']:.6f} vs {old_pos['z']:.6f})")
        
        if pos_diff_x < 0.001 and pos_diff_y < 0.001 and pos_diff_z < 0.001:
            print(f"  ✅ Positions match!")
        else:
            print(f"  ❌ Positions differ significantly!")
    
    if new_rot and old_rot:
        rot_diff_x = abs(new_rot['x'] - old_rot['x'])
        rot_diff_y = abs(new_rot['y'] - old_rot['y'])
        rot_diff_z = abs(new_rot['z'] - old_rot['z'])
        
        print(f"  Rotation differences:")
        print(f"    X: {rot_diff_x:.2f}° ({new_rot['x']:.2f}° vs {old_rot['x']:.2f}°)")
        print(f"    Y: {rot_diff_y:.2f}° ({new_rot['y']:.2f}° vs {old_rot['y']:.2f}°)")
        print(f"    Z: {rot_diff_z:.2f}° ({new_rot['z']:.2f}° vs {old_rot['z']:.2f}°)")
        
        if rot_diff_x < 1.0 and rot_diff_y < 1.0 and rot_diff_z < 1.0:
            print(f"  ✅ Rotations match!")
        else:
            print(f"  ❌ Rotations differ significantly!")

if __name__ == "__main__":
    # Test with available STEP files
    test_files = ["test.step", "test1.step", "SOIC16P127_1270X940X610L89X51.STEP"]
    
    for filename in test_files:
        if os.path.exists(filename):
            compare_methods(filename)
        else:
            print(f"\n❌ File not found: {filename}")
    
    print(f"\n{'='*60}")
    print(f"SUMMARY: This shows why the GUI values were wrong!")
    print(f"The program was using hardcoded values instead of reading the actual STEP file.")
    print(f"{'='*60}")
