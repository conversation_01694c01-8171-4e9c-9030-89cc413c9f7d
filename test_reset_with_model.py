#!/usr/bin/env python3
"""
Test reset functionality with actual STEP file loaded
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication, QPushButton
from step_viewer_tdk_modular import StepViewerTDK

def find_step_files():
    """Find available STEP files in current directory"""
    step_files = []
    for file in os.listdir('.'):
        if file.lower().endswith(('.step', '.stp')):
            step_files.append(file)
    return step_files

def find_reset_button(widget):
    """Find the reset button in the GUI"""
    for child in widget.findChildren(QPushButton):
        if "reset" in child.text().lower():
            return child
    return None

def get_actor_info(viewer):
    """Get information about VTK actors"""
    if viewer.active_viewer == "top":
        renderer = viewer.vtk_renderer_left
    else:
        renderer = viewer.vtk_renderer_right
        
    info = {
        'has_step_actors': hasattr(renderer, 'step_actors') and renderer.step_actors,
        'has_step_actor': hasattr(renderer, 'step_actor') and renderer.step_actor,
        'actor_count': 0,
        'positions': [],
        'orientations': []
    }
    
    if info['has_step_actors']:
        info['actor_count'] = len(renderer.step_actors)
        for actor in renderer.step_actors:
            info['positions'].append(actor.GetPosition())
            info['orientations'].append(actor.GetOrientation())
    elif info['has_step_actor']:
        info['actor_count'] = 1
        info['positions'].append(renderer.step_actor.GetPosition())
        info['orientations'].append(renderer.step_actor.GetOrientation())
        
    return info

def test_reset_with_model():
    """Test reset functionality with loaded model"""
    print("🔧 TESTING RESET WITH LOADED MODEL")
    print("=" * 60)
    
    # Find STEP files
    step_files = find_step_files()
    if not step_files:
        print("❌ No STEP files found in current directory!")
        print("   Please place a STEP file in the current directory to test.")
        return False
        
    test_file = step_files[0]
    print(f"📁 Using test file: {test_file}")
    
    # Create application and viewer
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.show()
    
    # Process events to ensure GUI is ready
    app.processEvents()
    time.sleep(0.5)
    
    # Find reset button
    reset_button = find_reset_button(viewer)
    if not reset_button:
        print("❌ Reset button not found!")
        app.quit()
        return False
        
    print(f"✅ Found reset button: '{reset_button.text()}'")
    
    try:
        # Step 1: Load STEP file
        print(f"\n📋 STEP 1: LOADING {test_file}...")
        success = viewer.load_step_file_direct(test_file)
        app.processEvents()
        time.sleep(2)  # Give time for loading

        if not success:
            print("❌ Failed to load STEP file")
            app.quit()
            return False

        # Check if model loaded
        actor_info = get_actor_info(viewer)
        if actor_info['actor_count'] == 0:
            print("❌ No actors found after loading - file may not have loaded properly")
            app.quit()
            return False
            
        print(f"✅ Model loaded successfully!")
        print(f"   Actor count: {actor_info['actor_count']}")
        print(f"   Has step_actors: {actor_info['has_step_actors']}")
        print(f"   Has step_actor: {actor_info['has_step_actor']}")
        
        # Step 2: Show initial state
        print(f"\n📋 STEP 2: INITIAL STATE...")
        print(f"   Display Position: {viewer.current_pos_left}")
        print(f"   Display Rotation: {viewer.current_rot_left}")
        if actor_info['positions']:
            print(f"   VTK Actor Position: {actor_info['positions'][0]}")
            print(f"   VTK Actor Orientation: {actor_info['orientations'][0]}")
        
        # Step 3: Apply transformations
        print(f"\n📋 STEP 3: APPLYING TRANSFORMATIONS...")
        print("   Rotating 45° around Z-axis...")
        viewer.rotate_shape('z', 45)
        app.processEvents()
        time.sleep(0.5)
        
        print("   Moving 10mm along X-axis...")
        viewer.move_shape('x', 10)
        app.processEvents()
        time.sleep(0.5)
        
        # Check state after transforms
        actor_info_after = get_actor_info(viewer)
        print(f"   After transforms:")
        print(f"   Display Position: {viewer.current_pos_left}")
        print(f"   Display Rotation: {viewer.current_rot_left}")
        if actor_info_after['positions']:
            print(f"   VTK Actor Position: {actor_info_after['positions'][0]}")
            print(f"   VTK Actor Orientation: {actor_info_after['orientations'][0]}")
        
        # Step 4: Test reset button
        print(f"\n📋 STEP 4: TESTING RESET BUTTON...")
        print("🔧 Clicking reset button...")
        reset_button.click()
        app.processEvents()
        time.sleep(1)
        
        # Check state after reset
        actor_info_reset = get_actor_info(viewer)
        print(f"   After reset:")
        print(f"   Display Position: {viewer.current_pos_left}")
        print(f"   Display Rotation: {viewer.current_rot_left}")
        if actor_info_reset['positions']:
            print(f"   VTK Actor Position: {actor_info_reset['positions'][0]}")
            print(f"   VTK Actor Orientation: {actor_info_reset['orientations'][0]}")
        
        # Step 5: Analyze results
        print(f"\n📋 STEP 5: ANALYSIS...")
        
        # Check display values reset
        pos_reset = all(abs(v) < 0.01 for v in viewer.current_pos_left.values())
        rot_reset = all(abs(v) < 0.01 for v in viewer.current_rot_left.values())
        
        # Check VTK actor values reset
        actor_pos_reset = True
        actor_rot_reset = True
        
        if actor_info_reset['positions']:
            pos = actor_info_reset['positions'][0]
            orient = actor_info_reset['orientations'][0]
            actor_pos_reset = all(abs(v) < 0.01 for v in pos)
            actor_rot_reset = all(abs(v) < 0.01 for v in orient)
        
        print(f"📊 RESULTS:")
        print(f"   Display position reset: {'✅ YES' if pos_reset else '❌ NO'}")
        print(f"   Display rotation reset: {'✅ YES' if rot_reset else '❌ NO'}")
        print(f"   VTK actor position reset: {'✅ YES' if actor_pos_reset else '❌ NO'}")
        print(f"   VTK actor rotation reset: {'✅ YES' if actor_rot_reset else '❌ NO'}")
        
        success = pos_reset and rot_reset and actor_pos_reset and actor_rot_reset
        print(f"\n🎯 OVERALL RESULT: {'✅ SUCCESS' if success else '❌ FAILED'}")
        
        if not success:
            print("\n🔧 ISSUES FOUND:")
            if not pos_reset:
                print("   - Display position not reset to 0,0,0")
            if not rot_reset:
                print("   - Display rotation not reset to 0,0,0")
            if not actor_pos_reset:
                print("   - VTK actor position not reset")
            if not actor_rot_reset:
                print("   - VTK actor rotation not reset")
        
        app.quit()
        return success
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        app.quit()
        return False

if __name__ == "__main__":
    success = test_reset_with_model()
    print(f"\n{'='*60}")
    print(f"FINAL RESULT: {'✅ TEST PASSED' if success else '❌ TEST FAILED'}")
    print(f"{'='*60}")
    sys.exit(0 if success else 1)
