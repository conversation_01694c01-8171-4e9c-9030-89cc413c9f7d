#!/usr/bin/env python3
"""
Test the fixed integration - STEPLoader should now use STEPTransformer
"""

import sys
import os
import vtk

# Add current directory to path for imports
sys.path.append('.')

from step_loader import <PERSON><PERSON><PERSON>oader

def test_fixed_step_loader_integration():
    """Test the fixed STEPLoader integration with STEPTransformer"""
    print("🧪 TESTING FIXED STEP LOADER INTEGRATION")
    print("=" * 60)
    
    # Load the file using STEPLoader
    loader = STEPLoader()
    if not loader.load_step_file("test.step"):
        print("❌ Failed to load test.step")
        return False
    
    print("✅ Loaded test.step with STEPLoader")
    
    # Test save with 45-degree Z rotation
    print(f"\n📝 Testing save with 45° Z rotation transformation...")
    
    # Create a transformation matrix (45° Z rotation)
    transform = vtk.vtkTransform()
    transform.RotateZ(45.0)
    matrix = transform.GetMatrix()
    
    print(f"   Created VTK transformation matrix for 45° Z rotation")
    
    # Try the save method (should now use STEPTransformer)
    output_file = "test_fixed_integration.step"
    result = loader.save_step_file(output_file, matrix)
    
    print(f"   Save result: {result}")
    
    # Check what file was created
    if os.path.exists(output_file):
        size = os.path.getsize(output_file)
        print(f"   ✅ STEP file created: {output_file} ({size} bytes)")
        
        # Analyze the result
        analyze_step_file(output_file)
        return True
    else:
        print(f"   ❌ STEP file not created: {output_file}")
        
        # Check if STL was created instead
        stl_file = output_file.replace('.step', '.stl')
        if os.path.exists(stl_file):
            size = os.path.getsize(stl_file)
            print(f"   ❌ STL file created instead: {stl_file} ({size} bytes)")
            print(f"   💡 Integration still not working - falling back to STL")
        
        return False

def analyze_step_file(filename):
    """Analyze the STEP file to verify transformation"""
    print(f"\n🔍 ANALYZING: {filename}")
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find coordinate points
        import re
        coord_pattern = r'CARTESIAN_POINT\s*\(\s*\'[^\']*\'\s*,\s*\(\s*([-\d.E+-]+)\s*,\s*([-\d.E+-]+)\s*,\s*([-\d.E+-]+)\s*\)'
        coords = re.findall(coord_pattern, content)
        
        print(f"   File size: {len(content)} chars, {len(coords)} coordinate points")
        print(f"   First 3 coordinates:")
        
        # Original coordinates for comparison
        original_coords = [
            (0.000000, 0.000000, 0.000000),
            (-1.109997, -1.612300, 0.491400),
            (-0.889997, -1.612300, 0.491400)
        ]
        
        for i, (x, y, z) in enumerate(coords[:3]):
            new_x, new_y, new_z = float(x), float(y), float(z)
            orig_x, orig_y, orig_z = original_coords[i]
            
            print(f"     Point {i+1}: ({new_x:10.6f}, {new_y:10.6f}, {new_z:10.6f})")
            
            if i == 0:  # Origin should stay at origin for pure rotation
                if abs(new_x) < 0.001 and abs(new_y) < 0.001 and abs(new_z) < 0.001:
                    print(f"       ✅ Origin correctly unchanged")
                else:
                    print(f"       ❌ Origin moved unexpectedly")
            else:
                # Check if it's rotated (distance preserved)
                orig_dist = (orig_x**2 + orig_y**2 + orig_z**2)**0.5
                new_dist = (new_x**2 + new_y**2 + new_z**2)**0.5
                
                if abs(orig_dist - new_dist) < 0.01:
                    print(f"       ✅ Distance preserved: {orig_dist:.4f} → {new_dist:.4f}")
                    
                    # Check if coordinates changed (indicating rotation)
                    coord_diff = abs(new_x - orig_x) + abs(new_y - orig_y) + abs(new_z - orig_z)
                    if coord_diff > 0.1:
                        print(f"       ✅ Coordinates changed (rotation applied)")
                    else:
                        print(f"       ❌ Coordinates unchanged (no rotation)")
                else:
                    print(f"       ❌ Distance changed: {orig_dist:.4f} → {new_dist:.4f}")
                    
    except Exception as e:
        print(f"❌ Error analyzing file: {e}")

def compare_with_expected():
    """Compare with the expected STEPTransformer result"""
    print(f"\n📊 COMPARING WITH EXPECTED RESULT")
    print("=" * 50)
    
    # Expected coordinates from STEPTransformer (from previous tests)
    expected_coords = [
        (0.000000, 0.000000, 0.000000),
        (0.355182, -1.924955, 0.491400),
        (0.510745, -1.769391, 0.491400)
    ]
    
    # Get actual coordinates from the integration test
    actual_file = "test_fixed_integration.step"
    if not os.path.exists(actual_file):
        print("❌ Integration test file not found")
        return
    
    try:
        with open(actual_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        import re
        coord_pattern = r'CARTESIAN_POINT\s*\(\s*\'[^\']*\'\s*,\s*\(\s*([-\d.E+-]+)\s*,\s*([-\d.E+-]+)\s*,\s*([-\d.E+-]+)\s*\)'
        coords = re.findall(coord_pattern, content)
        
        print("   Comparing first 3 coordinates:")
        for i, ((exp_x, exp_y, exp_z), (act_x, act_y, act_z)) in enumerate(zip(expected_coords, coords[:3])):
            act_x, act_y, act_z = float(act_x), float(act_y), float(act_z)
            
            print(f"     Point {i+1}:")
            print(f"       Expected: ({exp_x:10.6f}, {exp_y:10.6f}, {exp_z:10.6f})")
            print(f"       Actual:   ({act_x:10.6f}, {act_y:10.6f}, {act_z:10.6f})")
            
            # Check if they match
            diff_x = abs(act_x - exp_x)
            diff_y = abs(act_y - exp_y)
            diff_z = abs(act_z - exp_z)
            
            if diff_x < 0.001 and diff_y < 0.001 and diff_z < 0.001:
                print(f"       ✅ PERFECT MATCH!")
            else:
                print(f"       ❌ Difference: ({diff_x:.6f}, {diff_y:.6f}, {diff_z:.6f})")
                
    except Exception as e:
        print(f"❌ Error comparing files: {e}")

def main():
    """Main test function"""
    print("🔧 FIXED INTEGRATION TESTING")
    print("=" * 70)
    
    # Test the fixed integration
    success = test_fixed_step_loader_integration()
    
    if success:
        # Compare with expected results
        compare_with_expected()
    
    print("\n" + "=" * 70)
    print("🎯 FIXED INTEGRATION TESTING COMPLETE")
    
    if success:
        print("✅ SUCCESS: STEPLoader now creates proper STEP files with transformations!")
        print("✅ The main program should now work correctly!")
    else:
        print("❌ FAILED: Integration still not working correctly")
        print("💡 Check the console output above for debugging information")

if __name__ == "__main__":
    main()
