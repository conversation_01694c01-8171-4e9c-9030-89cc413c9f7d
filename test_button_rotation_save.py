#!/usr/bin/env python3
"""
Test button rotation save workflow:
1. Load STEP file
2. Apply button rotation (not mouse)
3. Save file
4. Load saved file
5. Check if rotation is preserved correctly
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from step_viewer_tdk_modular import StepViewerTDK
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
import tempfile

def test_button_rotation_workflow():
    """Test the complete button rotation save/load workflow"""
    
    print("🚀 Testing button rotation save/load workflow...")
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    # Test file
    test_file = "test.step"
    if not os.path.exists(test_file):
        print(f"❌ Test file {test_file} not found")
        return
    
    # Create temporary save file
    temp_save_file = tempfile.mktemp(suffix='.step')
    
    def step1_load_original():
        """Step 1: Load original STEP file"""
        print(f"\n📁 Step 1: Loading original file {test_file}...")
        viewer.set_active_viewer("bottom")
        viewer.step_loader_right.load_step_file(test_file)
        
        # Check initial rotation
        initial_rot = viewer._extract_rotation_from_vtk_actor("bottom")
        print(f"   Initial rotation: {initial_rot}")
        print(f"   Initial current_rot_right: {getattr(viewer, 'current_rot_right', 'NOT SET')}")
        
        QTimer.singleShot(2000, step2_apply_button_rotation)
    
    def step2_apply_button_rotation():
        """Step 2: Apply button rotation (X=45 degrees)"""
        print(f"\n🔘 Step 2: Applying button rotation (X=45°)...")
        
        # Apply button rotation using the rotate_shape method
        if hasattr(viewer, 'rotate_shape'):
            print("   Using rotate_shape method...")
            viewer.rotate_shape('x', 45.0)
        else:
            # Manually set the rotation values like buttons would
            print("   Manually setting rotation values...")
            viewer.current_rot_right = {'x': 45.0, 'y': 0.0, 'z': 0.0}
            
            # Update the display
            if hasattr(viewer, 'update_transform_display'):
                viewer.update_transform_display()
        
        # Check captured rotation
        captured_rot = viewer._extract_rotation_from_vtk_actor("bottom")
        print(f"   Captured rotation: {captured_rot}")
        
        # Check display value
        if hasattr(viewer, 'current_rot_right'):
            display_rot = viewer.current_rot_right
            print(f"   Display rotation: {display_rot}")
        
        QTimer.singleShot(2000, step3_save_file)
    
    def step3_save_file():
        """Step 3: Save the file with button rotation"""
        print(f"\n💾 Step 3: Saving file to {temp_save_file}...")
        
        # Manually call the save process to avoid dialog
        try:
            # Get the loader and rotation data
            loader = viewer.step_loader_right
            current_rot = viewer._extract_rotation_from_vtk_actor("bottom")
            current_pos = getattr(viewer, 'current_pos_right', {'x': 0, 'y': 0, 'z': 0})
            orig_rot = getattr(viewer, 'orig_rot_right', {'x': 0, 'y': 0, 'z': 0})
            orig_pos = getattr(viewer, 'orig_pos_right', {'x': 0, 'y': 0, 'z': 0})
            
            print(f"   Save parameters:")
            print(f"      Current rotation: {current_rot}")
            print(f"      Current position: {current_pos}")
            print(f"      Original rotation: {orig_rot}")
            print(f"      Original position: {orig_pos}")
            
            # Call the internal save method directly
            success = viewer._save_step_with_transformations(
                temp_save_file, loader, current_pos, current_rot, orig_pos, orig_rot
            )
            
            if success:
                print(f"   ✅ File saved successfully")
                file_size = os.path.getsize(temp_save_file) if os.path.exists(temp_save_file) else 0
                print(f"   📁 File size: {file_size} bytes")
            else:
                print(f"   ❌ Save failed")
        
        except Exception as e:
            print(f"   ❌ Save error: {e}")
            import traceback
            traceback.print_exc()
        
        QTimer.singleShot(2000, step4_load_saved_file)
    
    def step4_load_saved_file():
        """Step 4: Load the saved file"""
        print(f"\n📂 Step 4: Loading saved file {temp_save_file}...")
        
        if not os.path.exists(temp_save_file):
            print(f"   ❌ Saved file does not exist!")
            QTimer.singleShot(1000, cleanup_and_exit)
            return
        
        # Reset rotation tracking before loading
        viewer.current_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        viewer.orig_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        
        # Load the saved file
        viewer.step_loader_right.load_step_file(temp_save_file)
        
        # Check the rotation after loading
        loaded_rot = viewer._extract_rotation_from_vtk_actor("bottom")
        print(f"   Loaded file rotation: {loaded_rot}")
        
        # Check display value
        if hasattr(viewer, 'current_rot_right'):
            display_rot = viewer.current_rot_right
            print(f"   Display rotation after load: {display_rot}")
        
        QTimer.singleShot(2000, step5_analyze_results)
    
    def step5_analyze_results():
        """Step 5: Analyze the results"""
        print(f"\n📊 Step 5: Analysis...")
        
        # Check if the saved file contains the rotation
        if os.path.exists(temp_save_file):
            with open(temp_save_file, 'r') as f:
                content = f.read()
            
            # Look for coordinate systems
            import re
            axis_matches = re.findall(r'AXIS2_PLACEMENT_3D\([^)]+\)', content)
            print(f"   Found {len(axis_matches)} coordinate systems in saved file")
            
            # Look for transformed points
            point_matches = re.findall(r'CARTESIAN_POINT\([^)]+\)', content)
            print(f"   Found {len(point_matches)} CARTESIAN_POINT entries")
            
            # Show first few coordinate systems
            for i, match in enumerate(axis_matches[:3]):
                print(f"   Coord system {i+1}: {match}")
        
        print(f"\n🎯 CONCLUSION:")
        print(f"   - Button rotation was applied: ✅")
        print(f"   - File was saved: {'✅' if os.path.exists(temp_save_file) else '❌'}")
        print(f"   - File contains transformations: {'✅' if len(axis_matches) > 0 else '❌'}")
        
        QTimer.singleShot(1000, cleanup_and_exit)
    
    def cleanup_and_exit():
        """Cleanup and exit"""
        print(f"\n🧹 Cleaning up...")
        if os.path.exists(temp_save_file):
            os.remove(temp_save_file)
            print(f"   Removed temp file: {temp_save_file}")
        
        app.quit()
    
    # Start the test sequence
    QTimer.singleShot(3000, step1_load_original)
    
    # Run the application
    app.exec_()

if __name__ == "__main__":
    test_button_rotation_workflow()
