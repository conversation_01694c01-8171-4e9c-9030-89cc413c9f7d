#!/usr/bin/env python3
"""
Debug STEP loader from start to finish
"""

import sys
import os

print("=== DEBUGGING STEP LOADER FROM START TO FINISH ===")

# Step 1: Check if STEP file exists
step_file = 'SOIC16P127_1270X940X610L89X51.STEP'
print(f"Step 1: Checking STEP file: {step_file}")
if os.path.exists(step_file):
    print(f"✓ STEP file exists, size: {os.path.getsize(step_file)} bytes")
else:
    print(f"✗ STEP file NOT found")
    sys.exit(1)

# Step 2: Test step_loader import
print("Step 2: Testing step_loader import...")
try:
    from step_loader import STEPLoader
    print("✓ STEPLoader imported successfully")
except Exception as e:
    print(f"✗ STEPLoader import failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

# Step 3: Create STEPLoader instance
print("Step 3: Creating STEPLoader instance...")
try:
    loader = STEPLoader()
    print("✓ STEPLoader instance created")
except Exception as e:
    print(f"✗ STEPLoader creation failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

# Step 4: Test STEP file loading
print("Step 4: Testing STEP file loading...")
try:
    success, message = loader.load_step_file(step_file)
    print(f"Load result: success={success}, message='{message}'")
    
    if success:
        print("✓ STEP file loaded successfully")
        
        # Step 5: Check polydata
        if hasattr(loader, 'current_polydata') and loader.current_polydata:
            polydata = loader.current_polydata
            print(f"✓ Polydata available: {polydata.GetNumberOfCells()} cells, {polydata.GetNumberOfPoints()} points")
            
            # Step 6: Check colors
            colors = polydata.GetCellData().GetScalars("Colors")
            if colors:
                print(f"✓ Colors available: {colors.GetNumberOfTuples()} tuples")
                
                # Show first few colors
                for i in range(min(3, colors.GetNumberOfTuples())):
                    r = int(colors.GetComponent(i, 0))
                    g = int(colors.GetComponent(i, 1))
                    b = int(colors.GetComponent(i, 2))
                    print(f"  Color {i}: RGB({r}, {g}, {b})")
                
                print("✓ STEP LOADER IS WORKING CORRECTLY")
            else:
                print("✗ No colors found in polydata")
        else:
            print("✗ No polydata available")
    else:
        print(f"✗ STEP file loading failed: {message}")
        
except Exception as e:
    print(f"✗ STEP loading error: {e}")
    import traceback
    traceback.print_exc()

print("=== DEBUG COMPLETED ===")
