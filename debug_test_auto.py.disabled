#!/usr/bin/env python3
"""
Automated debug test to trigger all the issues and capture debug output
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer_tdk_modular import StepViewerTDK

class AutoDebugTest:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        # Set up timer to automatically test functionality
        self.timer = QTimer()
        self.timer.timeout.connect(self.run_tests)
        self.timer.setSingleShot(True)
        self.timer.start(2000)  # Wait 2 seconds for GUI to load
        
        print("🔧 AUTO DEBUG: Starting automated debug test...")
        
    def run_tests(self):
        """Run automated tests to trigger debug output"""
        try:
            print("🔧 AUTO DEBUG: Running automated tests...")
            
            # Test 1: Load STEP file in top viewer
            step_file = "E:/Python/3d-models/SOIC16P127_1270X940X610L89X51.STEP"
            if os.path.exists(step_file):
                print("🔧 AUTO DEBUG: Loading STEP file in TOP viewer...")
                self.viewer.active_viewer = "top"
                self.viewer.load_step_file_direct(step_file)
                print("🔧 AUTO DEBUG: TOP file loaded")
                
                # Test 2: Load same file in bottom viewer
                print("🔧 AUTO DEBUG: Loading STEP file in BOTTOM viewer...")
                self.viewer.active_viewer = "bottom"
                self.viewer.load_step_file_direct(step_file)
                print("🔧 AUTO DEBUG: BOTTOM file loaded")
                
                # Test 3: Check GUI labels
                print("🔧 AUTO DEBUG: Checking GUI labels...")
                self.check_gui_labels()
                
                # Test 4: Test overlay
                print("🔧 AUTO DEBUG: Testing overlay...")
                self.viewer.toggle_viewer_overlay()
                
                # Test 5: Check text overlays
                print("🔧 AUTO DEBUG: Checking text overlays...")
                self.check_text_overlays()
                
            else:
                print(f"🔧 AUTO DEBUG: STEP file not found: {step_file}")
                
        except Exception as e:
            print(f"❌ AUTO DEBUG: Error in automated test: {e}")
            import traceback
            traceback.print_exc()
            
        # Exit after tests
        QTimer.singleShot(5000, self.app.quit)
        
    def check_gui_labels(self):
        """Check what the GUI labels are showing"""
        print("🔧 AUTO DEBUG: === GUI LABEL CHECK ===")
        
        # Check TOP viewer labels
        if hasattr(self.viewer, 'lbl_orig_rot_x'):
            print(f"🔧 AUTO DEBUG: TOP orig_rot_x label = '{self.viewer.lbl_orig_rot_x.text()}'")
            print(f"🔧 AUTO DEBUG: TOP orig_rot_y label = '{self.viewer.lbl_orig_rot_y.text()}'")
            print(f"🔧 AUTO DEBUG: TOP orig_rot_z label = '{self.viewer.lbl_orig_rot_z.text()}'")
        else:
            print("🔧 AUTO DEBUG: TOP orig_rot labels NOT FOUND")
            
        # Check BOTTOM viewer labels
        if hasattr(self.viewer, 'lbl_orig_rot_x_bottom'):
            print(f"🔧 AUTO DEBUG: BOTTOM orig_rot_x label = '{self.viewer.lbl_orig_rot_x_bottom.text()}'")
            print(f"🔧 AUTO DEBUG: BOTTOM orig_rot_y label = '{self.viewer.lbl_orig_rot_y_bottom.text()}'")
            print(f"🔧 AUTO DEBUG: BOTTOM orig_rot_z label = '{self.viewer.lbl_orig_rot_z_bottom.text()}'")
        else:
            print("🔧 AUTO DEBUG: BOTTOM orig_rot labels NOT FOUND")
            
    def check_text_overlays(self):
        """Check text overlay status"""
        print("🔧 AUTO DEBUG: === TEXT OVERLAY CHECK ===")
        
        # Check TOP viewer text actors
        if hasattr(self.viewer, 'cursor_text_actor_left'):
            text = self.viewer.cursor_text_actor_left.GetInput()
            visibility = self.viewer.cursor_text_actor_left.GetVisibility()
            position = self.viewer.cursor_text_actor_left.GetPosition()
            print(f"🔧 AUTO DEBUG: cursor_text_actor_left text = '{text}'")
            print(f"🔧 AUTO DEBUG: cursor_text_actor_left visibility = {visibility}")
            print(f"🔧 AUTO DEBUG: cursor_text_actor_left position = {position}")
        else:
            print("🔧 AUTO DEBUG: cursor_text_actor_left NOT FOUND")
            
        if hasattr(self.viewer, 'combined_text_actor_left'):
            text = self.viewer.combined_text_actor_left.GetInput()
            visibility = self.viewer.combined_text_actor_left.GetVisibility()
            position = self.viewer.combined_text_actor_left.GetPosition()
            print(f"🔧 AUTO DEBUG: combined_text_actor_left text = '{text}'")
            print(f"🔧 AUTO DEBUG: combined_text_actor_left visibility = {visibility}")
            print(f"🔧 AUTO DEBUG: combined_text_actor_left position = {position}")
        else:
            print("🔧 AUTO DEBUG: combined_text_actor_left NOT FOUND")

def main():
    test = AutoDebugTest()
    sys.exit(test.app.exec_())

if __name__ == "__main__":
    main()
