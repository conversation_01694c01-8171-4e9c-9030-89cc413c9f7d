#!/usr/bin/env python3
"""
Deep Debug Tool: Find and Eliminate the Red Box
Traces the creation of the problematic actor with bounds (-4.7, 4.7)
"""

import sys
import os
import vtk
from step_loader import STEPLoader
from vtk_renderer import VT<PERSON><PERSON><PERSON><PERSON>

def trace_actor_creation():
    """Trace the creation of actors step by step"""
    print("🔍 DEEP DEBUG: Tracing Actor Creation")
    print("=" * 80)
    
    # Load the 16-pin SOIC file
    filename = "SOIC16P127_1270X940X610L89X51.STEP"
    print(f"📂 Loading: {filename}")
    
    loader = STEPLoader()
    result = loader.load_step_file(filename)
    
    if len(result) == 3:
        polydata, success, message = result
    else:
        success, message = result
        polydata = loader.current_polydata
    
    if not success or not polydata:
        print(f"❌ Failed to load: {message}")
        return
    
    print(f"✅ Loaded: {message}")
    print(f"📊 Original polydata: {polydata.GetNumberOfPoints()} points, {polydata.GetNumberOfCells()} cells")
    print(f"📏 Original bounds: {polydata.GetBounds()}")
    
    # Create VTK renderer manually without GUI
    print(f"\n🎭 Creating VTK Renderer (headless)...")
    import vtk
    vtk_renderer = vtk.vtkRenderer()

    # Simulate the multi-color actor creation directly
    print(f"\n🔍 Simulating multi-color actor creation...")
    
    # Get color data
    cell_colors = polydata.GetCellData().GetScalars("Colors")
    if not cell_colors:
        print("❌ No color data found")
        return

    def debug_create_multi_color_actors(polydata, cell_colors):
        print(f"\n🔍 DEEP DEBUG: _create_multi_color_actors called")
        print(f"   Input polydata: {polydata.GetNumberOfPoints()} points, {polydata.GetNumberOfCells()} cells")
        print(f"   Input bounds: {polydata.GetBounds()}")
        print(f"   Cell colors: {cell_colors.GetNumberOfTuples()} tuples")
        
        # Analyze color groups
        color_groups = {}
        for cell_id in range(cell_colors.GetNumberOfTuples()):
            r = int(cell_colors.GetComponent(cell_id, 0))
            g = int(cell_colors.GetComponent(cell_id, 1))
            b = int(cell_colors.GetComponent(cell_id, 2))
            color_key = (r, g, b)
            
            if color_key not in color_groups:
                color_groups[color_key] = []
            color_groups[color_key].append(cell_id)
        
        print(f"🎨 Color groups found: {len(color_groups)}")
        for color, cell_list in color_groups.items():
            print(f"   RGB{color}: {len(cell_list)} cells")
        
        # Create actors list
        step_actors = []

        # Manually create actors with deep debugging
        for i, (color_rgb, cell_ids) in enumerate(color_groups.items()):
            print(f"\n🎭 Creating actor {i} for color RGB{color_rgb} ({len(cell_ids)} cells)...")
            
            # Create polydata exactly like the original code
            color_polydata = vtk.vtkPolyData()
            color_polydata.SetPoints(polydata.GetPoints())  # Same points
            
            print(f"   📊 Color polydata points: {color_polydata.GetNumberOfPoints()}")
            
            # Create cell array
            color_cells = vtk.vtkCellArray()
            for cell_id in cell_ids:
                cell = polydata.GetCell(cell_id)
                color_cells.InsertNextCell(cell)
            
            color_polydata.SetPolys(color_cells)
            print(f"   📊 Color polydata cells: {color_polydata.GetNumberOfCells()}")
            
            # Create mapper
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputData(color_polydata)
            mapper.SetScalarVisibility(False)
            
            # Create actor
            actor = vtk.vtkActor()
            actor.SetMapper(mapper)
            
            # Set color
            r, g, b = color_rgb
            actor.GetProperty().SetColor(r/255.0, g/255.0, b/255.0)
            actor.GetProperty().SetAmbient(0.3)
            actor.GetProperty().SetDiffuse(0.7)
            
            # Get bounds BEFORE adding to renderer
            actor_bounds = actor.GetBounds()
            print(f"   📏 Actor bounds: {actor_bounds}")
            
            # Check if this is the problematic actor
            x_min, x_max = actor_bounds[0], actor_bounds[1]
            y_min, y_max = actor_bounds[2], actor_bounds[3]
            z_min, z_max = actor_bounds[4], actor_bounds[5]
            
            if (abs(x_min + 4.7) < 0.1 and abs(x_max - 4.7) < 0.1 and 
                abs(y_min + 4.7) < 0.1 and abs(y_max - 4.7) < 0.1):
                print(f"   ⚠️  WARNING: This is the PROBLEMATIC ACTOR!")
                print(f"   🔍 Analyzing why this actor has wrong bounds...")
                
                # Analyze the cells assigned to this color
                print(f"   📊 Analyzing {len(cell_ids)} cells for this color...")
                
                # Get actual bounds from the cells
                points = polydata.GetPoints()
                actual_bounds = [float('inf'), float('-inf'), float('inf'), float('-inf'), float('inf'), float('-inf')]
                
                for cell_id in cell_ids[:10]:  # Check first 10 cells
                    cell = polydata.GetCell(cell_id)
                    print(f"      Cell {cell_id}: {cell.GetNumberOfPoints()} points")
                    
                    for j in range(cell.GetNumberOfPoints()):
                        point_id = cell.GetPointId(j)
                        point = points.GetPoint(point_id)
                        print(f"         Point {point_id}: ({point[0]:.3f}, {point[1]:.3f}, {point[2]:.3f})")
                        
                        # Update actual bounds
                        actual_bounds[0] = min(actual_bounds[0], point[0])
                        actual_bounds[1] = max(actual_bounds[1], point[0])
                        actual_bounds[2] = min(actual_bounds[2], point[1])
                        actual_bounds[3] = max(actual_bounds[3], point[1])
                        actual_bounds[4] = min(actual_bounds[4], point[2])
                        actual_bounds[5] = max(actual_bounds[5], point[2])
                
                print(f"   📏 Actual bounds from cells: {actual_bounds}")
                print(f"   📏 VTK actor bounds: {actor_bounds}")
                print(f"   ❓ Why are they different?")
                
                # The issue is that VTK is calculating bounds from ALL points in the polydata,
                # not just the points used by the cells!
                print(f"   💡 ROOT CAUSE: VTK calculates bounds from ALL {color_polydata.GetNumberOfPoints()} points")
                print(f"   💡 But only {len(cell_ids)} cells use a subset of those points")
                print(f"   💡 The unused points extend the bounding box incorrectly")
                
                print(f"   🚫 SOLUTION: DO NOT CREATE THIS ACTOR!")
                continue
            
            # Add to renderer and store
            vtk_renderer.AddActor(actor)
            step_actors.append(actor)
            print(f"   ✅ Actor {i} created and added successfully")

        print(f"\n✅ Created {len(step_actors)} actors total")
        return step_actors

    # Call the debug function
    actors = debug_create_multi_color_actors(polydata, cell_colors)

    print(f"\n📊 Final result:")
    print(f"   Total actors created: {len(actors) if actors else 0}")
    if actors:
        for i, actor in enumerate(actors):
            bounds = actor.GetBounds()
            color = actor.GetProperty().GetColor()
            print(f"   Actor {i}: bounds={bounds}, color={color}")

            # Check if this is the problematic actor
            x_min, x_max = bounds[0], bounds[1]
            y_min, y_max = bounds[2], bounds[3]
            if (abs(x_min + 4.7) < 0.1 and abs(x_max - 4.7) < 0.1 and
                abs(y_min + 4.7) < 0.1 and abs(y_max - 4.7) < 0.1):
                print(f"   🚫 FOUND THE PROBLEMATIC ACTOR: Actor {i}")
                print(f"   🔥 THIS ACTOR SHOULD BE DELETED FROM THE CODE!")

if __name__ == "__main__":
    trace_actor_creation()
