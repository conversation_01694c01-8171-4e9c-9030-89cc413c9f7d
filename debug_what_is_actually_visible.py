#!/usr/bin/env python3
"""
Debug What Is Actually Visible - Find out what you're really seeing
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular import Step<PERSON>iewerTD<PERSON>

def debug_what_is_actually_visible():
    """Debug to find out what's actually being displayed"""
    
    print("🔧 DEBUG WHAT IS ACTUALLY VISIBLE")
    print("=" * 60)
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    # Process events to ensure GUI is ready
    app.processEvents()
    time.sleep(1)
    
    # Find a STEP file to test with
    test_files = [
        "AMPHENOL_U77-A1118-200T.STEP",
        "test.step",
        "sample.stp"
    ]
    
    test_file = None
    for filename in test_files:
        if os.path.exists(filename):
            test_file = filename
            break
    
    if not test_file:
        print("❌ No STEP file found for testing")
        return False
    
    print(f"📁 Using test file: {test_file}")
    
    # Step 1: Load STEP file
    print(f"\n📋 STEP 1: LOADING {test_file}...")
    success = viewer.load_step_file_direct(test_file)
    app.processEvents()
    time.sleep(2)
    
    if not success:
        print("❌ Failed to load STEP file")
        return False
    
    renderer = viewer.vtk_renderer_left
    
    # Get all actors
    all_actors = []
    if hasattr(renderer, 'renderer') and renderer.renderer:
        actor_collection = renderer.renderer.GetActors()
        actor_collection.InitTraversal()
        
        actor = actor_collection.GetNextActor()
        while actor:
            all_actors.append(actor)
            actor = actor_collection.GetNextActor()
    
    print(f"\n🔍 INITIAL STATE:")
    print(f"Total actors: {len(all_actors)}")
    
    multi_actors = []
    bbox_actor = None
    
    for i, actor in enumerate(all_actors):
        visible = actor.GetVisibility()
        bounds = actor.GetBounds()
        
        print(f"Actor {i}: Visible={visible}, Bounds={bounds}")
        
        # Identify actor types
        if hasattr(renderer, 'step_actors') and renderer.step_actors:
            for j, multi_actor in enumerate(renderer.step_actors):
                if multi_actor == actor:
                    multi_actors.append(actor)
                    print(f"  *** MULTI-ACTOR {j} ***")
                    
        if hasattr(renderer, 'bbox_actor') and renderer.bbox_actor == actor:
            bbox_actor = actor
            print(f"  *** BOUNDING BOX ACTOR ***")
    
    print(f"\n👁️ INITIAL MODEL - Note what you see (3 seconds)")
    time.sleep(3)
    
    # Step 2: Test visibility by hiding each actor individually
    print(f"\n📋 STEP 2: TESTING WHAT'S ACTUALLY VISIBLE...")
    
    print(f"\n🔧 TEST 1: Hide all multi-actors, keep bounding box")
    for actor in multi_actors:
        actor.SetVisibility(False)
    renderer.render_window.Render()
    app.processEvents()
    time.sleep(1)
    
    print(f"👁️ Multi-actors hidden - What do you see now? (5 seconds)")
    time.sleep(5)
    
    # Show multi-actors again
    for actor in multi_actors:
        actor.SetVisibility(True)
    renderer.render_window.Render()
    app.processEvents()
    time.sleep(1)
    
    print(f"\n🔧 TEST 2: Hide bounding box, keep multi-actors")
    if bbox_actor:
        bbox_actor.SetVisibility(False)
    renderer.render_window.Render()
    app.processEvents()
    time.sleep(1)
    
    print(f"👁️ Bounding box hidden - What do you see now? (5 seconds)")
    time.sleep(5)
    
    # Show bounding box again
    if bbox_actor:
        bbox_actor.SetVisibility(True)
    renderer.render_window.Render()
    app.processEvents()
    time.sleep(1)
    
    # Step 3: Test transformations on what's actually visible
    print(f"\n📋 STEP 3: TESTING TRANSFORMATIONS ON VISIBLE ACTORS...")
    
    viewer.active_viewer = "top"
    
    print(f"\n🔧 TEST 3A: Transform multi-actors only")
    print(f"Applying +50mm X to multi-actors...")
    for actor in multi_actors:
        actor.AddPosition(50, 0, 0)
        actor.Modified()
    
    renderer.render_window.Render()
    app.processEvents()
    time.sleep(1)
    
    print(f"👁️ Multi-actors moved +50mm X - Do you see movement? (5 seconds)")
    time.sleep(5)
    
    # Reset multi-actors
    for actor in multi_actors:
        actor.SetPosition(0, 0, 0)
        actor.Modified()
    renderer.render_window.Render()
    app.processEvents()
    time.sleep(1)
    
    print(f"\n🔧 TEST 3B: Transform bounding box only")
    if bbox_actor:
        print(f"Applying +50mm X to bounding box...")
        bbox_actor.AddPosition(50, 0, 0)
        bbox_actor.Modified()
        
        renderer.render_window.Render()
        app.processEvents()
        time.sleep(1)
        
        print(f"👁️ Bounding box moved +50mm X - Do you see movement? (5 seconds)")
        time.sleep(5)
        
        # Reset bounding box
        bbox_actor.SetPosition(0, 0, 0)
        bbox_actor.Modified()
        renderer.render_window.Render()
        app.processEvents()
        time.sleep(1)
    
    # Step 4: Test GUI transformations
    print(f"\n📋 STEP 4: TESTING GUI TRANSFORMATIONS...")
    
    print(f"\n🔧 TEST 4: GUI transformation")
    print(f"Calling viewer.move_shape('x', 50)...")
    viewer.move_shape("x", 50)
    app.processEvents()
    time.sleep(2)
    
    print(f"👁️ After GUI move - Do you see movement? (5 seconds)")
    time.sleep(5)
    
    # Check what actually moved
    print(f"\n🔍 AFTER GUI MOVE - What actually changed:")
    for i, actor in enumerate(multi_actors):
        pos = actor.GetPosition()
        print(f"Multi-actor {i}: Position={pos}")
    
    if bbox_actor:
        pos = bbox_actor.GetPosition()
        bounds = bbox_actor.GetBounds()
        print(f"Bounding box: Position={pos}, Bounds={bounds}")
    
    # Step 5: Test GUI reset
    print(f"\n📋 STEP 5: TESTING GUI RESET...")
    
    print(f"\n🔧 TEST 5: GUI reset")
    print(f"Calling viewer.reset_to_original()...")
    viewer.reset_to_original()
    app.processEvents()
    time.sleep(2)
    
    print(f"👁️ After GUI reset - Do you see it return? (5 seconds)")
    time.sleep(5)
    
    # Check what actually reset
    print(f"\n🔍 AFTER GUI RESET - What actually changed:")
    for i, actor in enumerate(multi_actors):
        pos = actor.GetPosition()
        print(f"Multi-actor {i}: Position={pos}")
    
    if bbox_actor:
        pos = bbox_actor.GetPosition()
        bounds = bbox_actor.GetBounds()
        print(f"Bounding box: Position={pos}, Bounds={bounds}")
    
    # Step 6: Final analysis
    print(f"\n📋 STEP 6: FINAL ANALYSIS")
    print("=" * 60)
    
    print(f"🎯 KEY QUESTIONS:")
    print(f"1. When multi-actors were hidden, what disappeared?")
    print(f"2. When bounding box was hidden, what disappeared?")
    print(f"3. When multi-actors moved manually, did you see movement?")
    print(f"4. When bounding box moved manually, did you see movement?")
    print(f"5. When GUI moved, did you see movement?")
    print(f"6. When GUI reset, did you see movement?")
    
    print(f"\n🔍 DIAGNOSIS:")
    print(f"- If only bounding box movement was visible: You're seeing the bounding box, not the model")
    print(f"- If only multi-actor movement was visible: You're seeing the model, not the bounding box")
    print(f"- If GUI doesn't cause visible movement: GUI is transforming the wrong actor")
    print(f"- If manual movement works but GUI doesn't: GUI logic issue")
    
    # Keep window open
    print(f"\n👁️ Window will stay open for final inspection...")
    QTimer.singleShot(15000, app.quit)
    app.exec_()
    
    return True

if __name__ == "__main__":
    debug_what_is_actually_visible()
