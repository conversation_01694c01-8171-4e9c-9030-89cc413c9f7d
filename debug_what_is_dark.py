#!/usr/bin/env python3
"""
Debug what geometry actually gets dark silver color in STEP file
"""

print("=== DEBUGGING WHAT GETS DARK SILVER COLOR ===")

with open('SOIC16P127_1270X940X610L89X51.STEP', 'r', encoding='utf-8', errors='ignore') as f:
    content = f.read()

lines = content.split('\n')

# Find the dark silver color IDs
dark_silver_ids = []
light_silver_ids = []

for line in lines:
    if 'COLOUR_RGB' in line and line.startswith('#'):
        if '0.25098039215686' in line:  # Dark silver value
            color_id = line.split('=')[0].strip()
            dark_silver_ids.append(color_id)
            print(f"Dark silver color: {color_id}")
        elif '0.75294117647059' in line:  # Light silver value
            color_id = line.split('=')[0].strip()
            light_silver_ids.append(color_id)

print(f"\nFound {len(dark_silver_ids)} dark silver colors: {dark_silver_ids}")
print(f"Found {len(light_silver_ids)} light silver colors: {light_silver_ids}")

# Find what FILL_AREA_STYLE_COLOUR entries reference the dark colors
dark_fill_styles = []
for line in lines:
    if 'FILL_AREA_STYLE_COLOUR' in line:
        for dark_id in dark_silver_ids:
            if dark_id in line:
                fill_id = line.split('=')[0].strip()
                dark_fill_styles.append(fill_id)
                print(f"Dark fill style: {line.strip()}")

print(f"\nFound {len(dark_fill_styles)} dark fill styles: {dark_fill_styles}")

# Find what SURFACE_STYLE entries reference the dark fill styles
dark_surface_styles = []
for line in lines:
    if 'SURFACE_STYLE' in line:
        for fill_id in dark_fill_styles:
            if fill_id in line:
                surface_id = line.split('=')[0].strip()
                dark_surface_styles.append(surface_id)
                print(f"Dark surface style: {line.strip()}")

print(f"\nFound {len(dark_surface_styles)} dark surface styles: {dark_surface_styles}")

# Find what geometry references these dark surface styles
print(f"\nLooking for geometry that uses dark surface styles...")
dark_geometry = []
for line in lines:
    for surface_id in dark_surface_styles:
        if surface_id in line and line.startswith('#') and line != surface_id + ' =':
            dark_geometry.append(line.strip())
            print(f"Dark geometry: {line.strip()}")

print(f"\nFound {len(dark_geometry)} geometry entries with dark colors")

# Look for clues about what these geometry entries represent
print(f"\nAnalyzing dark geometry for clues...")
for geom in dark_geometry:
    if 'PIN' in geom.upper():
        print(f"PIN found: {geom}")
    elif 'LEAD' in geom.upper():
        print(f"LEAD found: {geom}")
    elif 'BODY' in geom.upper():
        print(f"BODY found: {geom}")

print("\n=== ANALYSIS COMPLETE ===")
print("This shows what geometry actually gets dark silver color in the STEP file")
