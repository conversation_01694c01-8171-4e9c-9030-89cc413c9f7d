DEBUGGING COLOR PARSING STEP BY STEP
==================================================
Step 1: STEP file read - 542926 characters
Step 2: Using regex pattern: COLOUR_RGB\s*\(\s*[^,]*,\s*([\d\.]+)\s*,\s*([\d\.]+)\s*,\s*([\d\.]+)\s*\)
Step 3: Found 19 regex matches
Step 3b: Simple pattern found 19 matches
Using original pattern
Match 0: (0.753, 0.753, 0.753) -> RGB(192, 192, 192)
Match 1: (0.753, 0.753, 0.753) -> RGB(192, 192, 192)
Match 2: (0.753, 0.753, 0.753) -> RGB(192, 192, 192)
Match 3: (0.753, 0.753, 0.753) -> RGB(192, 192, 192)
Match 4: (0.753, 0.753, 0.753) -> RGB(192, 192, 192)
Match 5: (0.753, 0.753, 0.753) -> RGB(192, 192, 192)
... and 13 more matches
Step 5: 1 unique colors found:
  Color 0: RGB(192, 192, 192) - appears 6 times
Step 6: step_loader would use these colors: [(192, 192, 192)]
SUCCESS: Colors found - step_loader will use STEP file colors
==================================================
COLOR PARSING DEBUG COMPLETE
