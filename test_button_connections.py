#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Button Connections - Verify buttons are connected and working
"""

import sys
import os

def test_button_connections():
    """Test if buttons are properly connected in the main program"""
    
    # Read the main program
    with open("step_viewer_tdk_modular.py", "r", encoding="utf-8") as f:
        content = f.read()
    
    print("🔍 CHECKING BUTTON CONNECTIONS...")
    
    # Check if gui_components is imported
    if "from gui_components import" in content or "import gui_components" in content:
        print("✅ gui_components is imported")
    else:
        print("❌ gui_components NOT imported")
    
    # Check if rotate_shape method exists
    if "def rotate_shape(self, axis, degrees):" in content:
        print("✅ rotate_shape method exists")
    else:
        print("❌ rotate_shape method NOT found")
    
    # Check if move_shape method exists  
    if "def move_shape(self, axis, distance):" in content:
        print("✅ move_shape method exists")
    else:
        print("❌ move_shape method NOT found")
    
    print("\\n🔍 CHECKING GUI COMPONENTS FILE...")
    
    # Check gui_components.py
    try:
        with open("gui_components.py", "r", encoding="utf-8") as f:
            gui_content = f.read()
        
        # Check button connections
        if "parent.rotate_shape('x'" in gui_content:
            print("✅ X rotation buttons connected")
        else:
            print("❌ X rotation buttons NOT connected")
            
        if "parent.rotate_shape('y'" in gui_content:
            print("✅ Y rotation buttons connected")
        else:
            print("❌ Y rotation buttons NOT connected")
            
        if "parent.rotate_shape('z'" in gui_content:
            print("✅ Z rotation buttons connected")
        else:
            print("❌ Z rotation buttons NOT connected")
            
        if "parent.move_shape('x'" in gui_content:
            print("✅ X movement buttons connected")
        else:
            print("❌ X movement buttons NOT connected")
            
        # Count button connections
        rotate_connections = gui_content.count("parent.rotate_shape")
        move_connections = gui_content.count("parent.move_shape")
        
        print(f"\\n📊 BUTTON CONNECTION SUMMARY:")
        print(f"   Rotation button connections: {rotate_connections}")
        print(f"   Movement button connections: {move_connections}")
        
    except FileNotFoundError:
        print("❌ gui_components.py NOT found")
    
    print("\\n🔍 CHECKING MAIN PROGRAM SETUP...")
    
    # Check if GUI components are actually called
    if "create_gui_components" in content:
        print("✅ create_gui_components called")
    else:
        print("❌ create_gui_components NOT called")
    
    # Check active viewer initialization
    if "self.active_viewer" in content:
        print("✅ active_viewer variable exists")
        # Find initial value
        lines = content.split('\\n')
        for line in lines:
            if "self.active_viewer" in line and "=" in line:
                print(f"   Initial value: {line.strip()}")
                break
    else:
        print("❌ active_viewer variable NOT found")

def create_minimal_test():
    """Create a minimal test program to verify button functionality"""
    
    test_code = '''#!/usr/bin/env python3
import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel

class MinimalButtonTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Minimal Button Test")
        self.active_viewer = "top"
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        self.status_label = QLabel("Ready - Click buttons to test")
        layout.addWidget(self.status_label)
        
        # Test rotation buttons
        btn_x_plus = QPushButton("X+ Rotation")
        btn_x_plus.clicked.connect(lambda: self.rotate_shape('x', 15))
        layout.addWidget(btn_x_plus)
        
        btn_y_plus = QPushButton("Y+ Rotation") 
        btn_y_plus.clicked.connect(lambda: self.rotate_shape('y', 15))
        layout.addWidget(btn_y_plus)
        
        btn_z_plus = QPushButton("Z+ Rotation")
        btn_z_plus.clicked.connect(lambda: self.rotate_shape('z', 15))
        layout.addWidget(btn_z_plus)
        
        # Test movement buttons
        btn_move_x = QPushButton("Move X+")
        btn_move_x.clicked.connect(lambda: self.move_shape('x', 1.0))
        layout.addWidget(btn_move_x)
        
    def rotate_shape(self, axis, degrees):
        """Test rotate_shape method"""
        message = f"rotate_shape called: axis={axis}, degrees={degrees}, active_viewer={self.active_viewer}"
        print(message)
        self.status_label.setText(message)
        
    def move_shape(self, axis, distance):
        """Test move_shape method"""
        message = f"move_shape called: axis={axis}, distance={distance}, active_viewer={self.active_viewer}"
        print(message)
        self.status_label.setText(message)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MinimalButtonTest()
    window.show()
    sys.exit(app.exec_())
'''
    
    with open("minimal_button_test.py", "w", encoding="utf-8") as f:
        f.write(test_code)
    
    print("\\n✅ Created minimal_button_test.py")
    print("   Run: python minimal_button_test.py")

if __name__ == "__main__":
    test_button_connections()
    create_minimal_test()
    
    print("\\n🎯 NEXT STEPS:")
    print("1. Run: python minimal_button_test.py (to verify basic button functionality)")
    print("2. Check if buttons work in minimal test")
    print("3. If minimal test works, the issue is in the main program setup")
    print("4. If minimal test fails, the issue is with PyQt5 installation")
