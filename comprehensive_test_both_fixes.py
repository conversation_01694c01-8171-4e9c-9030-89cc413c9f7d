#!/usr/bin/env python3
"""
COMPREHENSIVE TEST - VERIFY BOTH FIXES WORK
Test both rotation increment and STEP file colors before committing
"""

import sys
import os
os.environ['QT_QPA_PLATFORM'] = 'offscreen'

from PyQt5.QtWidgets import QApplication
app = QApplication([])

import step_viewer_tdk_modular

def test_rotation_increment():
    """Test that rotation buttons increment correctly"""
    print("🔄 TESTING ROTATION INCREMENT FIX")
    print("-" * 40)
    
    # Create main window
    main_window = step_viewer_tdk_modular.StepViewerTDK()
    
    # Initialize rotation tracking
    main_window.model_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
    main_window.active_viewer = 'top'
    
    print(f"Initial rotation: {main_window.model_rot_left}")
    
    # Test 5 button clicks
    expected_values = [15.0, 30.0, 45.0, 60.0, 75.0]
    
    for i, expected in enumerate(expected_values):
        print(f"\nClick #{i+1}: X+15°")
        main_window.rotate_shape('x', 15.0)
        actual = main_window.model_rot_left['x']
        print(f"  Expected: {expected:.1f}°, Actual: {actual:.1f}°")
        
        if abs(actual - expected) < 0.1:
            print(f"  ✅ CORRECT")
        else:
            print(f"  ❌ WRONG - Should be {expected:.1f}°")
            return False
    
    print(f"\nFinal rotation: {main_window.model_rot_left}")
    print("✅ ROTATION INCREMENT FIX: WORKING")
    return True

def test_step_file_colors():
    """Test that STEP file colors are loaded and applied correctly"""
    print("\n🎨 TESTING STEP FILE COLORS FIX")
    print("-" * 40)
    
    # Create main window
    main_window = step_viewer_tdk_modular.StepViewerTDK()
    main_window.active_viewer = 'top'
    
    # Test loading STEP file
    debug_file = "debug_auto_saved.step"
    if not os.path.exists(debug_file):
        print(f"❌ {debug_file} not found")
        return False
    
    print(f"Loading: {debug_file}")
    
    try:
        # Load STEP file
        success, message = main_window.step_loader_left.load_step_file(debug_file)
        
        if not success:
            print(f"❌ Load failed: {message}")
            return False
        
        polydata = main_window.step_loader_left.current_polydata
        if not polydata:
            print("❌ No polydata loaded")
            return False
        
        print(f"✅ Loaded: {polydata.GetNumberOfCells()} cells")
        
        # Check for colors
        cell_colors = polydata.GetCellData().GetScalars()
        if not cell_colors:
            print("❌ No colors found in polydata")
            return False
        
        num_colors = cell_colors.GetNumberOfTuples()
        color_range = cell_colors.GetRange()
        
        print(f"✅ Colors found: {num_colors} colors")
        print(f"✅ Color range: {color_range}")
        
        # Check specific colors
        print("First 3 colors:")
        for i in range(min(3, num_colors)):
            color = cell_colors.GetTuple3(i)
            print(f"  Color {i}: RGB({color[0]:.0f}, {color[1]:.0f}, {color[2]:.0f})")
        
        # Test VTK display
        main_window.vtk_renderer_left.clear_view()
        main_window.vtk_renderer_left.display_polydata(polydata)
        
        # Check if actor has colors
        if hasattr(main_window.vtk_renderer_left, 'step_actor') and main_window.vtk_renderer_left.step_actor:
            mapper = main_window.vtk_renderer_left.step_actor.GetMapper()
            if mapper.GetScalarVisibility():
                print("✅ VTK mapper has scalar visibility enabled")
                print("✅ STEP FILE COLORS FIX: WORKING")
                return True
            else:
                print("❌ VTK mapper scalar visibility disabled")
                return False
        else:
            print("❌ No VTK actor created")
            return False
            
    except Exception as e:
        print(f"❌ Error testing colors: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run comprehensive tests"""
    print("🧪 COMPREHENSIVE TEST - BOTH FIXES")
    print("=" * 50)
    
    # Test 1: Rotation increment
    rotation_ok = test_rotation_increment()
    
    # Test 2: STEP file colors  
    colors_ok = test_step_file_colors()
    
    # Summary
    print("\n📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    print(f"Rotation Increment Fix: {'✅ WORKING' if rotation_ok else '❌ BROKEN'}")
    print(f"STEP File Colors Fix:   {'✅ WORKING' if colors_ok else '❌ BROKEN'}")
    
    if rotation_ok and colors_ok:
        print("\n🎉 BOTH FIXES ARE WORKING!")
        print("✅ Safe to commit these changes")
        print("\nThe fixes are:")
        print("1. Rotation: += instead of = in rotate_shape method")
        print("2. Colors: Proper scalar setup in VTK pipeline")
    elif rotation_ok:
        print("\n⚠️  ROTATION FIX WORKS, COLORS NEED MORE WORK")
        print("✅ Rotation fix is ready to commit")
        print("❌ Color fix needs debugging")
    elif colors_ok:
        print("\n⚠️  COLORS FIX WORKS, ROTATION NEEDS MORE WORK") 
        print("✅ Color fix is ready to commit")
        print("❌ Rotation fix needs debugging")
    else:
        print("\n❌ BOTH FIXES NEED MORE WORK")
        print("Neither fix is ready to commit")
    
    app.quit()
    return rotation_ok and colors_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
