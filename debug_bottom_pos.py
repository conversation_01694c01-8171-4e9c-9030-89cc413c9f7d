#!/usr/bin/env python3
"""
Debug why bottom viewer isn't showing POS values
"""

import re

def extract_pos_from_step_file(filename):
    """Extract position from STEP file #8041 entity"""
    try:
        with open(filename, 'r') as f:
            content = f.read()
        
        # Look for #8041 entity
        point_match = re.search(r'#8041\s*=\s*CARTESIAN_POINT\s*\([^;]+\)\s*;', content)
        if point_match:
            point_line = point_match.group(0)
            print(f"Found #8041 line: {point_line}")
            
            # Extract coordinates
            coords_match = re.search(r'\(\s*[^,]+,\s*\(\s*([^,]+),\s*([^,]+),\s*([^)]+)\s*\)', point_line)
            if coords_match:
                x = float(coords_match.group(1))
                y = float(coords_match.group(2))
                z = float(coords_match.group(3))
                print(f"Extracted coordinates: X={x:.3f}, Y={y:.3f}, Z={z:.3f}")
                return {'x': x, 'y': y, 'z': z}
            else:
                print("Failed to extract coordinates from line")
        else:
            print("No #8041 entity found in file")
        
        return None
    except Exception as e:
        print(f"Error reading file: {e}")
        return None

def main():
    print("=" * 60)
    print("DEBUG: Why bottom viewer isn't showing POS values")
    print("=" * 60)
    
    # Test with the file that was loaded
    test_file = "test.step"
    print(f"Testing with file: {test_file}")
    
    coords = extract_pos_from_step_file(test_file)
    if coords:
        print(f"✅ SUCCESS: Found coordinates {coords}")
        print(f"   This should show as: POS: X={coords['x']:.3f}mm Y={coords['y']:.3f}mm Z={coords['z']:.3f}mm")
    else:
        print(f"❌ FAILED: No coordinates found")
        print(f"   This explains why bottom viewer shows 'ROT: ...' without POS")
    
    # Also test the modified file
    modified_file = "complete_pos_rot_test.step"
    print(f"\nTesting with modified file: {modified_file}")
    
    coords2 = extract_pos_from_step_file(modified_file)
    if coords2:
        print(f"✅ SUCCESS: Found coordinates {coords2}")
        print(f"   This should show as: POS: X={coords2['x']:.3f}mm Y={coords2['y']:.3f}mm Z={coords2['z']:.3f}mm")
    else:
        print(f"❌ FAILED: No coordinates found in modified file")

if __name__ == "__main__":
    main()
