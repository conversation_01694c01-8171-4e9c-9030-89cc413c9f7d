#!/usr/bin/env python3
"""
Analyze STEP file to see what's actually in it using the same method as our code
"""

import sys
import os

def analyze_step_file(filename):
    print(f"📁 Analyzing STEP file: {filename}")

    if not os.path.exists(filename):
        print(f"❌ File not found: {filename}")
        return

    try:
        # Use the same STEP loader as our code
        from step_loader import STEPLoader

        loader = STEPLoader()
        result = loader.load_step_file(filename)

        if len(result) == 3:
            polydata, success, message = result
        else:
            success, message = result
            polydata = loader.current_polydata

        print(f"🔧 STEP Loader Result: {success}")
        print(f"🔧 Message: {message}")

        if success and polydata:
            print(f"🔧 PolyData Type: {polydata.GetClassName()}")
            print(f"🔧 Number of cells: {polydata.GetNumberOfCells()}")
            print(f"🔧 Number of points: {polydata.GetNumberOfPoints()}")
            print(f"🔧 Bounds: {polydata.GetBounds()}")

            # Check if there are multiple parts by examining the data
            if hasattr(polydata, 'GetNumberOfBlocks'):
                num_blocks = polydata.GetNumberOfBlocks()
                print(f"🔧 Number of blocks: {num_blocks}")

                for i in range(num_blocks):
                    block = polydata.GetBlock(i)
                    if block:
                        print(f"   Block {i}: {block.GetClassName()}")
                        print(f"      Cells: {block.GetNumberOfCells()}")
                        print(f"      Bounds: {block.GetBounds()}")
            else:
                print("🔧 Single polydata object (no blocks)")

            # Now simulate what our VTK renderer does
            print("\n🔧 Simulating VTK renderer behavior...")

            import vtk

            # Create mapper and actor like our code does
            mapper = vtk.vtkDataSetMapper()
            mapper.SetInputData(polydata)

            actor = vtk.vtkActor()
            actor.SetMapper(mapper)

            print(f"🔧 Single actor bounds: {actor.GetBounds()}")

            # Check if the polydata has multiple parts that would create multiple actors
            if polydata.GetNumberOfCells() > 0:
                print(f"🔧 This STEP file creates a single polydata with {polydata.GetNumberOfCells()} cells")
                print("🔧 This should result in ONE actor, not multiple actors")
            else:
                print("🔧 No cells found in polydata")

        else:
            print("❌ Failed to load STEP file")

    except Exception as e:
        print(f"❌ Error analyzing file: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("=== ANALYZING 16-PIN SOIC ===")
    analyze_step_file("SOIC16P127_1270X940X610L89X51.STEP")

    print("\n=== ANALYZING TEST.STEP ===")
    analyze_step_file("test.step")
