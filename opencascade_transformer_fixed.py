#!/usr/bin/env python3
"""
Fixed OpenCASCADE transformation system with correct import paths
"""

import os
import math

class OpenCASCADETransformer:
    """Fixed OpenCASCADE transformation system"""
    
    def __init__(self):
        self.available = self._test_imports()
    
    def _test_imports(self):
        """Test if all required OpenCASCADE modules are available"""
        try:
            # Test correct import paths
            from OCC.Core.gp import gp_Trsf, gp_Pnt, gp_Vec, gp_Ax1, gp_Dir
            from OCC.Core.BRepBuilderAPI import BRepBuilderAPI_Transform
            from OCC.Core.STEPControl import STEP<PERSON><PERSON>rol_Reader, STEPControl_Writer
            from OCC.Core.STEPCAFControl import STEPCAFControl_Reader, STEPCAFControl_Writer
            
            print("✅ All OpenCASCADE modules imported successfully")
            return True
            
        except Exception as e:
            print(f"❌ OpenCASCADE import failed: {e}")
            return False
    
    def transform_step_file(self, input_file, output_file, rotation_dict, position_dict=None):
        """
        Transform a STEP file with rotation and position changes
        
        Args:
            input_file: Path to input STEP file
            output_file: Path to output STEP file  
            rotation_dict: {'x': degrees, 'y': degrees, 'z': degrees}
            position_dict: {'x': units, 'y': units, 'z': units} (optional)
        
        Returns:
            bool: True if successful, False otherwise
        """
        if not self.available:
            print("❌ OpenCASCADE not available for transformation")
            return False
            
        try:
            # Import with correct paths
            from OCC.Core.gp import gp_Trsf, gp_Pnt, gp_Vec, gp_Ax1, gp_Dir
            from OCC.Core.BRepBuilderAPI import BRepBuilderAPI_Transform
            from OCC.Core.STEPControl import STEPControl_Reader, STEPControl_Writer
            from OCC.Core.STEPCAFControl import STEPCAFControl_Reader
            from OCC.Core.IFSelect import IFSelect_ReturnStatus
            
            print(f"🔧 Transforming STEP file: {input_file} -> {output_file}")
            print(f"   Rotation: {rotation_dict}")
            if position_dict:
                print(f"   Position: {position_dict}")
            
            # Read the STEP file
            reader = STEPControl_Reader()
            status = reader.ReadFile(input_file)
            
            if status != IFSelect_ReturnStatus.IFSelect_RetDone:
                print(f"❌ Failed to read STEP file: {input_file}")
                return False
                
            print("✅ STEP file read successfully")
            
            # Transfer shapes
            reader.TransferRoots()
            shape = reader.OneShape()
            
            if shape.IsNull():
                print("❌ No shapes found in STEP file")
                return False
                
            print("✅ Shapes extracted from STEP file")
            
            # Create transformation matrix
            transform = gp_Trsf()
            
            # Apply rotations (convert degrees to radians)
            if rotation_dict.get('x', 0) != 0:
                x_rad = math.radians(rotation_dict['x'])
                x_axis = gp_Ax1(gp_Pnt(0, 0, 0), gp_Dir(1, 0, 0))
                x_transform = gp_Trsf()
                x_transform.SetRotation(x_axis, x_rad)
                transform.Multiply(x_transform)
                print(f"✅ Applied X rotation: {rotation_dict['x']}°")
            
            if rotation_dict.get('y', 0) != 0:
                y_rad = math.radians(rotation_dict['y'])
                y_axis = gp_Ax1(gp_Pnt(0, 0, 0), gp_Dir(0, 1, 0))
                y_transform = gp_Trsf()
                y_transform.SetRotation(y_axis, y_rad)
                transform.Multiply(y_transform)
                print(f"✅ Applied Y rotation: {rotation_dict['y']}°")
            
            if rotation_dict.get('z', 0) != 0:
                z_rad = math.radians(rotation_dict['z'])
                z_axis = gp_Ax1(gp_Pnt(0, 0, 0), gp_Dir(0, 0, 1))
                z_transform = gp_Trsf()
                z_transform.SetRotation(z_axis, z_rad)
                transform.Multiply(z_transform)
                print(f"✅ Applied Z rotation: {rotation_dict['z']}°")
            
            # Apply translation if provided
            if position_dict:
                translation = gp_Vec(
                    position_dict.get('x', 0),
                    position_dict.get('y', 0), 
                    position_dict.get('z', 0)
                )
                translate_transform = gp_Trsf()
                translate_transform.SetTranslation(translation)
                transform.Multiply(translate_transform)
                print(f"✅ Applied translation: {position_dict}")
            
            # Apply transformation to shape
            transformer = BRepBuilderAPI_Transform(shape, transform)
            transformed_shape = transformer.Shape()
            
            print("✅ Transformation applied to geometry")
            
            # Write the transformed shape to new STEP file
            writer = STEPControl_Writer()
            writer.Transfer(transformed_shape, 1)  # 1 = Interface_Static.InterfaceStatic_WriteMode_Vertex
            
            write_status = writer.Write(output_file)
            
            if write_status != IFSelect_ReturnStatus.IFSelect_RetDone:
                print(f"❌ Failed to write STEP file: {output_file}")
                return False
            
            print(f"✅ Transformed STEP file saved: {output_file}")
            
            # Verify file was created
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                print(f"✅ File verification: {file_size} bytes")
                return True
            else:
                print("❌ Output file was not created")
                return False
                
        except Exception as e:
            print(f"❌ Transformation failed: {e}")
            import traceback
            traceback.print_exc()
            return False

def test_transformer():
    """Test the fixed transformer"""
    print("🧪 TESTING Fixed OpenCASCADE Transformer")
    print("=" * 50)
    
    transformer = OpenCASCADETransformer()
    
    if not transformer.available:
        print("❌ Transformer not available - OpenCASCADE issues")
        return False
    
    # Test with a sample file if available
    test_files = ['test.step', 'SOIC16P127_1270X940X610L89X51.STEP']
    input_file = None
    
    for test_file in test_files:
        if os.path.exists(test_file):
            input_file = test_file
            break
    
    if not input_file:
        print("❌ No test STEP file found")
        return False
    
    print(f"📁 Using test file: {input_file}")
    
    # Test transformation
    output_file = "test_transformed_fixed.step"
    rotation = {'x': 15.0, 'y': 30.0, 'z': 45.0}
    
    success = transformer.transform_step_file(input_file, output_file, rotation)
    
    if success:
        print("🎉 SUCCESS: OpenCASCADE transformation is working!")
        return True
    else:
        print("❌ FAILURE: Transformation failed")
        return False

if __name__ == "__main__":
    test_transformer()
