#!/usr/bin/env python3
"""
Debug ALL actors in the overlay renderer to find the source of 16-pin contamination
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from step_viewer_tdk_modular import StepViewerTDK

def debug_all_overlay_actors(viewer):
    """Debug every single actor in the overlay renderer"""
    print(f"\n🔍 COMPLETE OVERLAY RENDERER ANALYSIS:")
    
    if not hasattr(viewer, 'overlay_renderer') or not viewer.overlay_renderer:
        print("   ❌ No overlay renderer found")
        return
    
    # Get ALL actors from overlay renderer
    actors = viewer.overlay_renderer.GetActors()
    actors.InitTraversal()
    
    actor_count = 0
    print(f"   📊 Analyzing ALL actors in overlay renderer...")
    
    while True:
        actor = actors.GetNextActor()
        if not actor:
            break
        
        bounds = actor.GetBounds()
        color = actor.GetProperty().GetColor()
        opacity = actor.GetProperty().GetOpacity()
        visibility = actor.GetVisibility()
        
        # Calculate dimensions
        x_range = bounds[1] - bounds[0]
        y_range = bounds[3] - bounds[2]
        z_range = bounds[5] - bounds[4]
        
        print(f"   🎭 Overlay Actor {actor_count}:")
        print(f"      Bounds: {bounds}")
        print(f"      Dimensions: {x_range:.3f} x {y_range:.3f} x {z_range:.3f}")
        print(f"      Color: {color}")
        print(f"      Opacity: {opacity}")
        print(f"      Visibility: {visibility}")
        
        # Identify what this actor might be
        if x_range > 8.0 or y_range > 8.0:
            print(f"      ⚠️  LARGE ACTOR - This looks like 16-pin geometry!")
            print(f"      🔍 This is likely the source of the 16-pin pins in overlay!")
        elif x_range < 4.0 and y_range < 6.0:
            print(f"      ✅ Small actor - likely 8-pin geometry")
        else:
            print(f"      ❓ Medium actor - unknown geometry")
        
        actor_count += 1
    
    print(f"   📊 Total overlay actors: {actor_count}")
    
    # Also check the overlay actor lists
    if hasattr(viewer, 'overlay_bottom_actors'):
        print(f"   📋 overlay_bottom_actors count: {len(viewer.overlay_bottom_actors)}")
    if hasattr(viewer, 'overlay_top_actors'):
        print(f"   📋 overlay_top_actors count: {len(viewer.overlay_top_actors)}")

def main():
    """Run complete overlay actor debugging"""
    print("🔬 DEBUGGING ALL OVERLAY ACTORS")
    print("=" * 60)
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.show()
    
    # Load files
    print("\n🔴 Loading 16-pin into TOP viewer...")
    viewer.active_viewer = "top"
    viewer.update_viewer_highlights()
    
    if os.path.exists("SOIC16P127_1270X940X610L89X51.STEP"):
        success1 = viewer.load_step_file_direct("SOIC16P127_1270X940X610L89X51.STEP")
        print(f"   16-pin load result: {success1}")
    else:
        print("   ❌ 16-pin STEP file not found")
        return 1
    
    print("\n🔵 Loading 8-pin into BOTTOM viewer...")
    viewer.active_viewer = "bottom"
    viewer.update_viewer_highlights()
    
    if os.path.exists("test.step"):
        success2 = viewer.load_step_file_direct("test.step")
        print(f"   8-pin load result: {success2}")
    else:
        print("   ❌ 8-pin STEP file not found")
        return 1
    
    # Trigger overlay
    print("\n🎯 Creating overlay...")
    viewer.toggle_viewer_overlay()
    
    # Debug all overlay actors
    debug_all_overlay_actors(viewer)
    
    print("\n" + "=" * 60)
    print("🎯 ANALYSIS COMPLETE")
    print("Look for actors with large dimensions (>8.0) - these are the 16-pin contamination!")
    
    # Keep window open
    print("\n⏱️  Window will stay open - close it when done examining")
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
