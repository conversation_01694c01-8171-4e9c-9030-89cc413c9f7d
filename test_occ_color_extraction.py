#!/usr/bin/env python3

print("TESTING OPENCASCADE COLOR EXTRACTION")

try:
    from OCC.Core.STEPControl import STEP<PERSON><PERSON>rol_Reader
    from OCC.Core.IFSelect import IFSelect_RetDone
    print("Basic imports OK")
    
    reader = STEPControl_Reader()
    status = reader.ReadFile('SOIC16P127_1270X940X610L89X51.STEP')
    
    if status == IFSelect_RetDone:
        print("STEP file read OK")
        reader.TransferRoots()
        shape = reader.OneShape()
        print("Shape extracted OK")
        
        # Try color extraction
        from OCC.Core.XCAFDoc import XCAFDoc_ColorTool
        from OCC.Core.XCAFApp import XCAFApp_Application
        from OCC.Core.TDocStd import TDocStd_Document
        from OCC.Core.STEPCAFControl import STEPCAFControl_Reader
        
        print("Color tools imported OK")
        
        app = XCAFApp_Application.GetApplication()
        doc = TDocStd_Document("MDTV-XCAF")
        
        caf_reader = STEPCAFControl_Reader()
        caf_reader.SetColorMode(True)
        caf_reader.SetNameMode(True)
        
        caf_status = caf_reader.ReadFile('SOIC16P127_1270X940X610L89X51.STEP')
        if caf_status == IFSelect_RetDone:
            print("CAF STEP file read OK")
            caf_reader.Transfer(doc)
            
            color_tool = XCAFDoc_ColorTool.Set(doc.Main())

            # Actually test color extraction
            from OCC.Core.TopExp import TopExp_Explorer
            from OCC.Core.TopAbs import TopAbs_FACE
            from OCC.Core.Quantity import Quantity_Color

            # Get the shape from the original reader
            reader.TransferRoots()
            shape_label = reader.OneShape()
            explorer = TopExp_Explorer(shape_label, TopAbs_FACE)

            colors_found = []
            face_count = 0

            while explorer.More():
                face = explorer.Current()
                color = Quantity_Color()

                if color_tool.GetColor(face, color):
                    r = int(color.Red() * 255)
                    g = int(color.Green() * 255)
                    b = int(color.Blue() * 255)
                    colors_found.append((r, g, b))
                    print(f"Face {face_count}: RGB({r}, {g}, {b})")

                face_count += 1
                explorer.Next()

            print(f"ACTUAL TEST: Found {len(colors_found)} colored faces out of {face_count} total faces")

            unique_colors = list(set(colors_found))
            print(f"Unique colors: {unique_colors}")

            if len(unique_colors) > 0:
                print("COLOR EXTRACTION ACTUALLY WORKING")
            else:
                print("COLOR EXTRACTION FAILED - NO COLORS FOUND")
        else:
            print("CAF read failed")
    else:
        print("STEP read failed")
        
except Exception as e:
    print(f"ERROR: {e}")

print("TEST COMPLETE")
