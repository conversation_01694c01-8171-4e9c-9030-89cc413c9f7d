#!/usr/bin/env python3
"""
Debug program to identify the coordinate system mismatch issue
This will show exactly why the TOP and BOTTOM viewers show different models
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from step_viewer_tdk_modular_fixed import Step<PERSON>iewerTD<PERSON>

def debug_coordinate_system_mismatch():
    """Debug the coordinate system extraction mismatch"""
    print("🔍 DEBUGGING COORDINATE SYSTEM MISMATCH")
    print("=" * 60)

    # Create QApplication first
    app = QApplication([])

    # Create viewer instance
    viewer = StepViewerTDK()
    
    # Test files
    original_file = "auto_demo_rotated.step"  # Original file loaded in TOP
    saved_file = "test_rotation_save_verification.step"  # Saved file loaded in BOTTOM
    
    if not os.path.exists(original_file):
        print(f"❌ Original file not found: {original_file}")
        return
        
    if not os.path.exists(saved_file):
        print(f"❌ Saved file not found: {saved_file}")
        return
    
    print(f"📂 Testing coordinate system extraction...")
    print()
    
    # Extract coordinate system from original file
    print(f"🔵 ORIGINAL FILE: {original_file}")
    orig_pos_1, orig_rot_1 = viewer._extract_step_coordinate_system(original_file)
    print(f"   Position: {orig_pos_1}")
    print(f"   Rotation: {orig_rot_1}")
    print()
    
    # Extract coordinate system from saved file  
    print(f"🟢 SAVED FILE: {saved_file}")
    orig_pos_2, orig_rot_2 = viewer._extract_step_coordinate_system(saved_file)
    print(f"   Position: {orig_pos_2}")
    print(f"   Rotation: {orig_rot_2}")
    print()
    
    # Compare the results
    print("🔍 COMPARISON:")
    print("-" * 40)
    
    if orig_pos_1 and orig_pos_2:
        pos_diff_x = abs(orig_pos_1['x'] - orig_pos_2['x'])
        pos_diff_y = abs(orig_pos_1['y'] - orig_pos_2['y']) 
        pos_diff_z = abs(orig_pos_1['z'] - orig_pos_2['z'])
        
        print(f"Position differences:")
        print(f"   ΔX: {pos_diff_x:.6f}")
        print(f"   ΔY: {pos_diff_y:.6f}")
        print(f"   ΔZ: {pos_diff_z:.6f}")
        
        if pos_diff_x < 0.001 and pos_diff_y < 0.001 and pos_diff_z < 0.001:
            print("   ✅ Positions match")
        else:
            print("   ❌ Positions differ significantly")
    
    if orig_rot_1 and orig_rot_2:
        rot_diff_x = abs(orig_rot_1['x'] - orig_rot_2['x'])
        rot_diff_y = abs(orig_rot_1['y'] - orig_rot_2['y'])
        rot_diff_z = abs(orig_rot_1['z'] - orig_rot_2['z'])
        
        print(f"Rotation differences:")
        print(f"   ΔX: {rot_diff_x:.3f}°")
        print(f"   ΔY: {rot_diff_y:.3f}°") 
        print(f"   ΔZ: {rot_diff_z:.3f}°")
        
        if rot_diff_x < 1.0 and rot_diff_y < 1.0 and rot_diff_z < 1.0:
            print("   ✅ Rotations match (within 1°)")
        else:
            print("   ❌ Rotations differ significantly")
            print()
            print("🚨 ROOT CAUSE IDENTIFIED:")
            print("   The saved STEP file has different coordinate system data")
            print("   than the original file. This means:")
            print("   1. TOP viewer loads original file → shows original orientation")
            print("   2. BOTTOM viewer loads saved file → shows transformed orientation")
            print("   3. The models appear different because they ARE different!")
            print()
            print("💡 SOLUTION NEEDED:")
            print("   The BOTTOM viewer should apply the INVERSE transformation")
            print("   to display the model in the same orientation as the TOP viewer.")
    
    print()
    print("🔍 DETAILED STEP FILE ANALYSIS:")
    print("-" * 40)
    
    # Show raw STEP file coordinate system data
    print(f"📄 Raw coordinate system data from {original_file}:")
    show_step_coordinate_data(original_file)
    print()
    
    print(f"📄 Raw coordinate system data from {saved_file}:")
    show_step_coordinate_data(saved_file)

def show_step_coordinate_data(filename):
    """Show raw coordinate system data from STEP file"""
    try:
        with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        import re
        
        # Find AXIS2_PLACEMENT_3D entries
        axis_matches = re.findall(r'(#\d+) = AXIS2_PLACEMENT_3D\(\'\',(#\d+),(#\d+),(#\d+)\);', content)
        
        for i, (axis_id, origin_ref, z_dir_ref, x_dir_ref) in enumerate(axis_matches[:3]):  # Show first 3
            print(f"   AXIS2_PLACEMENT_3D {axis_id}:")
            
            # Get origin
            origin_pattern = f'{origin_ref} = CARTESIAN_POINT\\(\'\'\\,\\(([^)]+)\\)\\);'
            origin_match = re.search(origin_pattern, content)
            if origin_match:
                coords = origin_match.group(1)
                print(f"     Origin: {coords}")
            
            # Get Z direction
            z_dir_pattern = f'{z_dir_ref} = DIRECTION\\(\'\'\\,\\(([^)]+)\\)\\);'
            z_dir_match = re.search(z_dir_pattern, content)
            if z_dir_match:
                z_dir = z_dir_match.group(1)
                print(f"     Z Direction: {z_dir}")
            
            # Get X direction
            x_dir_pattern = f'{x_dir_ref} = DIRECTION\\(\'\'\\,\\(([^)]+)\\)\\);'
            x_dir_match = re.search(x_dir_pattern, content)
            if x_dir_match:
                x_dir = x_dir_match.group(1)
                print(f"     X Direction: {x_dir}")
            
            if i < len(axis_matches) - 1:
                print()
                
    except Exception as e:
        print(f"   Error reading file: {e}")

if __name__ == "__main__":
    debug_coordinate_system_mismatch()
