#!/usr/bin/env python3
"""
QUICK FIX: Hide all text at startup
"""

# Find and replace ALL SetVisibility(1) with SetVisibility(0) in text creation
import re

def fix_startup_text():
    with open('step_viewer_tdk_modular.py', 'r') as f:
        content = f.read()
    
    # Find all text actor creations and force them to be hidden
    patterns = [
        (r'(self\.combined_text_actor_\w+\.SetVisibility\()\d+(\))', r'\g<1>0\g<2>'),
        (r'(self\.cursor_text_actor_\w+\.SetVisibility\()\d+(\))', r'\g<1>0\g<2>'),
    ]
    
    for pattern, replacement in patterns:
        content = re.sub(pattern, replacement, content)
    
    # Also add explicit hiding after text actor creation
    lines = content.split('\n')
    new_lines = []
    
    for line in lines:
        new_lines.append(line)
        if 'renderer.AddActor2D(self.combined_text_actor' in line:
            indent = len(line) - len(line.lstrip())
            new_lines.append(' ' * indent + line.split('(')[1].split(')')[0] + '.SetVisibility(0)  # FORCE HIDE')
        elif 'renderer.AddActor2D(self.cursor_text_actor' in line:
            indent = len(line) - len(line.lstrip())
            new_lines.append(' ' * indent + line.split('(')[1].split(')')[0] + '.SetVisibility(0)  # FORCE HIDE')
    
    with open('step_viewer_tdk_modular.py', 'w') as f:
        f.write('\n'.join(new_lines))
    
    print("✅ QUICK FIX APPLIED: All text actors forced to be hidden at startup")

if __name__ == "__main__":
    fix_startup_text()
