File size: 32173 bytes
Checking bytes 9352 to 9452
Position 9399: byte 0xf0 (240)
Context (raw): b'a):\n        print(f"\xf0\x9f\x94\x8d VTK DEBUG: disp'
Position 9400: byte 0x9f (159)
Context (raw): b'):\n        print(f"\xf0\x9f\x94\x8d VTK DEBUG: displ'
Position 9401: byte 0x94 (148)
Context (raw): b':\n        print(f"\xf0\x9f\x94\x8d VTK DEBUG: displa'
Position 9402: byte 0x8d (141)
Context (raw): b'\n        print(f"\xf0\x9f\x94\x8d VTK DEBUG: display'
