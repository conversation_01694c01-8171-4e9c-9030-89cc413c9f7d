Checking OCC.Core modules:
OK BRepMesh.py exists
OK from OCC.Core import BRep<PERSON>esh - WORKS
   Contains: ['<PERSON>ep<PERSON>esh_BaseMeshAlgo', 'BRepMesh_BoundaryParamsRangeSplitter', 'BRepMesh_Circle', '<PERSON>ep<PERSON><PERSON>_CircleInspector', '<PERSON><PERSON><PERSON><PERSON>_CircleInspector_IsEqual']...
OK TopExp.py exists
OK from OCC.Core import TopExp - WORKS
   Contains: ['ClassNotWrappedError', 'IntEnum', 'MethodNotWrappedError', 'OCC', 'Proxy']...
OK TopAbs.py exists
OK from OCC.Core import TopAbs - WORKS
   Contains: ['ClassNotWrappedError', 'IntEnum', 'MethodNotWrappedError', 'OCC', 'Proxy']...
OK BRep.py exists
OK from OCC.Core import BRep - WORKS
   Contains: ['BRep_Builder', 'BRep_Curve3D', 'BRep_CurveOn2Surfaces', 'BRep_CurveOnClosedSurface', 'BRep_CurveOnSurface']...
OK TopLoc.py exists
OK from OCC.Core import TopLoc - WORKS
   Contains: ['ClassNotWrappedError', 'Handle_TopLoc_Datum3D_Create', 'Handle_TopLoc_Datum3D_DownCast', 'Handle_TopLoc_Datum3D_IsNull', 'Handle_TopLoc_SListNodeOfItemLocation_Create']...
OK STEPControl.py exists
OK from OCC.Core import STEPControl - WORKS
   Contains: ['ClassNotWrappedError', 'Handle_STEPControl_ActorRead_Create', 'Handle_STEPControl_ActorRead_DownCast', 'Handle_STEPControl_ActorRead_IsNull', 'Handle_STEPControl_ActorWrite_Create']...
OK IFSelect.py exists
OK from OCC.Core import IFSelect - WORKS
   Contains: ['ClassNotWrappedError', 'Handle_IFSelect_Act_Create', 'Handle_IFSelect_Act_DownCast', 'Handle_IFSelect_Act_IsNull', 'Handle_IFSelect_Activator_Create']...

Testing specific class imports:
OK BRepMesh_IncrementalMesh: from OCC.Core.BRepMesh import BRepMesh_IncrementalMesh
OK TopExp_Explorer: from OCC.Core.TopExp import TopExp_Explorer
OK TopAbs_FACE: from OCC.Core.TopAbs import TopAbs_FACE
OK BRep_Tool: from OCC.Core.BRep import BRep_Tool
OK TopLoc_Location: from OCC.Core.TopLoc import TopLoc_Location
OK STEPControl_Reader: from OCC.Core.STEPControl import STEPControl_Reader
OK IFSelect_RetDone: from OCC.Core.IFSelect import IFSelect_RetDone
