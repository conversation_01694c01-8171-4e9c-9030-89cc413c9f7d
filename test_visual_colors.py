#!/usr/bin/env python3

print("TESTING VISUAL COLORS - CAPTURE WHAT IS ACTUALLY DISPLAYED")

import vtk
from step_loader import STEPLoader

# Load STEP file
loader = STEPLoader()
success, msg = loader.load_step_file('SOIC16P127_1270X940X610L89X51.STEP')

if success and loader.current_polydata:
    polydata = loader.current_polydata
    
    # Create VTK renderer to see what is actually displayed
    renderer = vtk.vtkRenderer()
    render_window = vtk.vtkRenderWindow()
    render_window.AddRenderer(renderer)
    render_window.SetOffScreenRendering(1)  # No window, just capture
    render_window.SetSize(800, 600)
    
    # Create mapper and actor
    mapper = vtk.vtkPolyDataMapper()
    mapper.SetInputData(polydata)
    
    actor = vtk.vtkActor()
    actor.SetMapper(mapper)
    
    # Add to renderer
    renderer.AddActor(actor)
    renderer.SetBackground(0.1, 0.1, 0.1)  # Dark background
    
    # Position camera
    renderer.ResetCamera()
    camera = renderer.GetActiveCamera()
    camera.Zoom(1.5)
    
    # Render
    render_window.Render()
    
    # Capture image
    window_to_image = vtk.vtkWindowToImageFilter()
    window_to_image.SetInput(render_window)
    window_to_image.Update()
    
    # Save image
    writer = vtk.vtkPNGWriter()
    writer.SetFileName("my_step_display.png")
    writer.SetInputConnection(window_to_image.GetOutputPort())
    writer.Write()
    
    print("Image saved as my_step_display.png")
    
    # Analyze colors in the rendered image
    image_data = window_to_image.GetOutput()
    dims = image_data.GetDimensions()
    
    # Sample colors from the rendered image
    color_samples = []
    for y in range(100, dims[1]-100, 50):  # Sample middle area
        for x in range(100, dims[0]-100, 50):
            pixel = image_data.GetScalarComponentAsFloat(x, y, 0, 0)  # R
            pixel_g = image_data.GetScalarComponentAsFloat(x, y, 0, 1)  # G  
            pixel_b = image_data.GetScalarComponentAsFloat(x, y, 0, 2)  # B
            
            if pixel > 50:  # Skip background pixels
                color_samples.append((int(pixel), int(pixel_g), int(pixel_b)))
    
    # Count unique colors in rendered image
    unique_colors = list(set(color_samples))
    print(f"Colors found in rendered image: {len(unique_colors)}")
    for color in unique_colors[:5]:  # Show first 5
        print(f"  Rendered color: RGB{color}")
    
    print("VISUAL COLOR TEST COMPLETE")
    print("Check my_step_display.png to see what is actually displayed")
    
else:
    print("STEP loading failed - cannot test visual colors")
