#!/usr/bin/env python3
"""
Test Visual Reset Fix - Focus on rendering and visual updates
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular import StepViewerTDK

def force_complete_render_update(viewer):
    """Force complete rendering update of all components"""
    print("🔄 FORCING COMPLETE RENDER UPDATE...")
    
    renderer = viewer.vtk_renderer_left
    
    # 1. Update VTK renderer
    if hasattr(renderer, 'renderer') and renderer.renderer:
        print("  ✅ Updating VTK renderer...")
        renderer.renderer.Modified()
        renderer.renderer.ResetCamera()
        
    # 2. Update render window
    if hasattr(renderer, 'render_window') and renderer.render_window:
        print("  ✅ Updating render window...")
        renderer.render_window.Modified()
        renderer.render_window.Render()
        
    # 3. Update VTK widget
    if hasattr(renderer, 'vtk_widget') and renderer.vtk_widget:
        print("  ✅ Updating VTK widget...")
        renderer.vtk_widget.GetRenderWindow().Modified()
        renderer.vtk_widget.GetRenderWindow().Render()
        renderer.vtk_widget.update()
        
    # 4. Update all actors
    if hasattr(renderer, 'step_actors') and renderer.step_actors:
        print(f"  ✅ Updating {len(renderer.step_actors)} multi-actors...")
        for i, actor in enumerate(renderer.step_actors):
            actor.Modified()
            print(f"    Actor {i}: Pos={actor.GetPosition()}, Orient={actor.GetOrientation()}")
            
    if hasattr(renderer, 'step_actor') and renderer.step_actor:
        print("  ✅ Updating single actor...")
        renderer.step_actor.Modified()
        print(f"    Single Actor: Pos={renderer.step_actor.GetPosition()}, Orient={renderer.step_actor.GetOrientation()}")
    
    # 5. Update bounding box
    if hasattr(renderer, 'bounding_box_actor') and renderer.bounding_box_actor:
        print("  ✅ Updating bounding box...")
        renderer.bounding_box_actor.Modified()
        
    # 6. Force Qt widget update
    viewer.vtk_widget_left.update()
    viewer.update()
    
    print("🔄 Complete render update finished!")

def test_visual_reset_with_forced_updates():
    """Test reset with forced rendering updates"""
    
    print("🔧 VISUAL RESET TEST WITH FORCED UPDATES")
    print("=" * 60)
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    # Process events to ensure GUI is ready
    app.processEvents()
    time.sleep(1)
    
    # Find a STEP file to test with
    test_files = [
        "AMPHENOL_U77-A1118-200T.STEP",
        "test.step",
        "sample.stp"
    ]
    
    test_file = None
    for filename in test_files:
        if os.path.exists(filename):
            test_file = filename
            break
    
    if not test_file:
        print("❌ No STEP file found for testing")
        return False
    
    print(f"📁 Using test file: {test_file}")
    
    # Step 1: Load STEP file
    print(f"\n📋 STEP 1: LOADING {test_file}...")
    success = viewer.load_step_file_direct(test_file)
    app.processEvents()
    time.sleep(2)
    
    if not success:
        print("❌ Failed to load STEP file")
        return False
    
    force_complete_render_update(viewer)
    print("✅ Model loaded and rendered")
    
    # Step 2: Apply transformations
    print(f"\n📋 STEP 2: APPLYING TRANSFORMATIONS...")
    
    # Apply rotation
    viewer.active_viewer = "top"
    viewer.rotate_shape("z", 45)
    app.processEvents()
    time.sleep(1)
    
    # Apply movement
    viewer.move_shape("x", 10)
    app.processEvents()
    time.sleep(1)
    
    force_complete_render_update(viewer)
    print("✅ Transformations applied and rendered")
    
    # Step 3: Test reset with forced updates
    print(f"\n📋 STEP 3: TESTING RESET WITH FORCED UPDATES...")
    
    # Call reset function
    print("🔧 Calling reset_to_original()...")
    viewer.reset_to_original()
    app.processEvents()
    time.sleep(1)
    
    # Force complete render update after reset
    force_complete_render_update(viewer)
    print("✅ Reset completed with forced rendering")
    
    # Step 4: Additional render fixes
    print(f"\n📋 STEP 4: ADDITIONAL RENDER FIXES...")
    
    # Try calling fit_view again
    renderer = viewer.vtk_renderer_left
    if hasattr(renderer, 'fit_view'):
        print("🔧 Calling fit_view()...")
        renderer.fit_view()
        
    # Force camera reset
    if hasattr(renderer, 'renderer') and renderer.renderer:
        print("🔧 Resetting camera...")
        renderer.renderer.ResetCamera()
        renderer.renderer.GetRenderWindow().Render()
    
    # Update bounding box specifically
    if hasattr(viewer, 'update_bounding_box'):
        print("🔧 Updating bounding box...")
        viewer.update_bounding_box()
    
    force_complete_render_update(viewer)
    
    # Step 5: Final verification
    print(f"\n📋 STEP 5: FINAL VERIFICATION...")
    
    # Check actor states
    if hasattr(renderer, 'step_actors') and renderer.step_actors:
        for i, actor in enumerate(renderer.step_actors):
            pos = actor.GetPosition()
            orient = actor.GetOrientation()
            print(f"  Multi-Actor {i}: Pos={pos}, Orient={orient}")
            
    if hasattr(renderer, 'step_actor') and renderer.step_actor:
        pos = renderer.step_actor.GetPosition()
        orient = renderer.step_actor.GetOrientation()
        print(f"  Single-Actor: Pos={pos}, Orient={orient}")
    
    # Check display values
    print(f"  Display Position: {viewer.current_pos_left}")
    print(f"  Display Rotation: {viewer.current_rot_left}")
    
    print(f"\n🎯 VISUAL INSPECTION TIME!")
    print("👁️ Window will stay open for 20 seconds...")
    print("🔍 Check if the model and bounding box have visually reset!")
    print("📍 The model should be back to its original position and orientation")
    
    # Keep window open for manual inspection
    QTimer.singleShot(20000, app.quit)  # Close after 20 seconds
    
    app.exec_()
    return True

if __name__ == "__main__":
    test_visual_reset_with_forced_updates()
