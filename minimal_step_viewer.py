#!/usr/bin/env python3
"""
Minimal STEP viewer that actually runs and displays something
"""

import sys
import vtk
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import QTimer

try:
    from vtkmodules.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor
except ImportError:
    from vtk.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor

class MinimalSTEPViewer(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Minimal STEP Viewer - RUNNING")
        self.setGeometry(100, 100, 800, 600)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Create VTK widget
        self.vtk_widget = QVTKRenderWindowInteractor(central_widget)
        layout.addWidget(self.vtk_widget)
        
        # Create renderer
        self.renderer = vtk.vtkRenderer()
        self.renderer.SetBackground(0.1, 0.1, 0.1)  # Dark background
        
        # Get render window
        self.render_window = self.vtk_widget.GetRenderWindow()
        self.render_window.AddRenderer(self.renderer)
        
        # Create a simple cube to show something is working
        self.create_test_cube()
        
        # Initialize VTK
        self.vtk_widget.Initialize()
        self.vtk_widget.Start()
        
        print("MINIMAL STEP VIEWER IS RUNNING AND DISPLAYING")
        print("Window should be visible with a colored cube")
        
    def create_test_cube(self):
        """Create a test cube with colors to verify display works"""
        # Create cube
        cube_source = vtk.vtkCubeSource()
        cube_source.SetXLength(2.0)
        cube_source.SetYLength(1.0) 
        cube_source.SetZLength(0.5)
        
        # Create mapper
        mapper = vtk.vtkPolyDataMapper()
        mapper.SetInputConnection(cube_source.GetOutputPort())
        
        # Create actor
        actor = vtk.vtkActor()
        actor.SetMapper(mapper)
        
        # Set colors - light silver like STEP file should have
        actor.GetProperty().SetColor(0.75, 0.75, 0.75)  # Light silver
        
        # Add to renderer
        self.renderer.AddActor(actor)
        
        # Set camera
        self.renderer.ResetCamera()
        
        print("Test cube created with light silver color")

def main():
    print("Starting Minimal STEP Viewer...")
    
    app = QApplication(sys.argv)
    
    viewer = MinimalSTEPViewer()
    viewer.show()
    
    print("Viewer window created and shown")
    print("Application running - window should be visible")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
