#!/usr/bin/env python3
"""
Real workflow test - Load, rotate, save, then load the saved file
"""

import sys
import os
sys.path.append('.')

from step_viewer_tdk_modular import StepViewerTDK
from PyQt5.QtWidgets import QApplication
import time

def test_real_workflow():
    """Test the complete workflow: load -> rotate -> save -> load saved file"""
    print("🚀 REAL WORKFLOW TEST")
    print("=" * 60)
    
    app = QApplication([])
    viewer = StepViewerTDK()
    viewer.show()
    
    try:
        # STEP 1: Load original test.step
        print("\n📂 STEP 1: Loading original test.step...")
        viewer.active_viewer = "top"
        success = viewer.load_step_file_direct('test.step')
        
        if not success:
            print("❌ Failed to load original test.step")
            return
        
        print("✅ Original file loaded successfully")
        print(f"📊 Original rotation: {viewer.current_rot_left}")
        
        # STEP 2: Apply a rotation (let's use a nice visible rotation)
        rotation_to_apply = {"x": 45.0, "y": 30.0, "z": 15.0}
        print(f"\n🔄 STEP 2: Applying rotation {rotation_to_apply}...")
        
        viewer._apply_3d_rotation_matrix("left", rotation_to_apply)
        print("✅ Rotation applied to model")
        print(f"📊 Current rotation state: {viewer.current_rot_left}")
        
        # STEP 3: Save the rotated model
        save_filename = "test_workflow_rotated.step"
        print(f"\n💾 STEP 3: Saving rotated model as {save_filename}...")
        
        # Use the proper save method
        loader = viewer.step_loader_left
        delta_pos = {"x": 0.0, "y": 0.0, "z": 0.0}
        delta_rot = rotation_to_apply
        orig_pos = {"x": 0.0, "y": 0.0, "z": 0.0}
        orig_rot = {"x": 0.0, "y": 0.0, "z": 0.0}  # Original test.step has no rotation
        
        success = viewer._save_step_text_transform(
            save_filename, loader, delta_pos, delta_rot, orig_pos, orig_rot
        )
        
        if not success:
            print("❌ Failed to save rotated file")
            return
        
        print(f"✅ Rotated file saved successfully: {save_filename}")
        
        # STEP 4: Load the saved rotated file in the bottom viewer
        print(f"\n📂 STEP 4: Loading saved rotated file in bottom viewer...")
        viewer.active_viewer = "bottom"
        success = viewer.load_step_file_direct(save_filename)
        
        if not success:
            print("❌ Failed to load saved rotated file")
            return
        
        print("✅ Saved rotated file loaded successfully")
        print(f"📊 Loaded rotation: {viewer.current_rot_right}")
        
        # STEP 5: Compare the results
        print(f"\n🔍 STEP 5: Comparing results...")
        expected_rot = rotation_to_apply
        loaded_rot = viewer.current_rot_right
        
        print(f"📊 Expected rotation: X={expected_rot['x']:.1f}°, Y={expected_rot['y']:.1f}°, Z={expected_rot['z']:.1f}°")
        print(f"📊 Loaded rotation:   X={loaded_rot['x']:.1f}°, Y={loaded_rot['y']:.1f}°, Z={loaded_rot['z']:.1f}°")
        
        # Calculate differences
        x_diff = abs(loaded_rot['x'] - expected_rot['x'])
        y_diff = abs(loaded_rot['y'] - expected_rot['y'])
        z_diff = abs(loaded_rot['z'] - expected_rot['z'])
        
        print(f"📊 Differences:       X={x_diff:.3f}°, Y={y_diff:.3f}°, Z={z_diff:.3f}°")
        
        # Check if the rotation was preserved accurately
        tolerance = 0.1  # Allow small floating point differences
        if x_diff < tolerance and y_diff < tolerance and z_diff < tolerance:
            print(f"\n🎉 SUCCESS! Rotation preserved perfectly!")
            print(f"   ✅ The saved rotated file loads with the correct orientation")
            print(f"   ✅ You can see both models side by side:")
            print(f"      - TOP viewer: Original + applied rotation")
            print(f"      - BOTTOM viewer: Saved rotated file (should look identical)")
        else:
            print(f"\n❌ FAILED! Rotation not preserved accurately")
            print(f"   Differences exceed tolerance of {tolerance}°")
        
        # STEP 6: Visual comparison info
        print(f"\n👀 STEP 6: Visual comparison...")
        print(f"   🔍 Look at both viewers:")
        print(f"   📺 TOP viewer shows: Original test.step with {rotation_to_apply} rotation applied")
        print(f"   📺 BOTTOM viewer shows: Loaded {save_filename} file")
        print(f"   ✅ Both should look identical if the workflow is working correctly")
        
        # Keep the window open for visual inspection
        print(f"\n⏳ Keeping window open for 10 seconds for visual inspection...")
        time.sleep(10)
        
    except Exception as e:
        print(f"❌ Error during workflow test: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n🎯 WORKFLOW TEST COMPLETE")
    app.quit()

if __name__ == "__main__":
    test_real_workflow()
