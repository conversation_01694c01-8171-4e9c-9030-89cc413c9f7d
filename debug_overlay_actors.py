#!/usr/bin/env python3
"""
Debug exactly what actors are being copied to the overlay
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from step_viewer_tdk_modular import StepViewerTDK

def debug_overlay_actors():
    print("🔧 DEBUGGING OVERLAY ACTORS IN DETAIL")

    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    
    # Load TOP file (16-pin SOIC)
    print("\n=== LOADING TOP FILE ===")
    viewer.active_viewer = "top"
    viewer.load_step_file_direct("SOIC16P127_1270X940X610L89X51.STEP")
    
    # Debug TOP actors
    print(f"\n🔧 TOP RENDERER ACTORS:")
    if hasattr(viewer.vtk_renderer_left, 'step_actors') and viewer.vtk_renderer_left.step_actors:
        print(f"   Multi-actors count: {len(viewer.vtk_renderer_left.step_actors)}")
        for i, actor in enumerate(viewer.vtk_renderer_left.step_actors):
            bounds = actor.GetBounds()
            color = actor.GetProperty().GetColor()
            print(f"   Actor {i}: bounds={bounds}, color={color}")
    
    if hasattr(viewer.vtk_renderer_left, 'step_actor') and viewer.vtk_renderer_left.step_actor:
        bounds = viewer.vtk_renderer_left.step_actor.GetBounds()
        color = viewer.vtk_renderer_left.step_actor.GetProperty().GetColor()
        visible = viewer.vtk_renderer_left.step_actor.GetVisibility()
        print(f"   Single-actor: bounds={bounds}, color={color}, visible={visible}")
    
    # Load BOTTOM file (8-pin SOIC)
    print("\n=== LOADING BOTTOM FILE ===")
    viewer.active_viewer = "bottom"
    viewer.load_step_file_direct("test.step")
    
    # Debug BOTTOM actors
    print(f"\n🔧 BOTTOM RENDERER ACTORS:")
    if hasattr(viewer.vtk_renderer_right, 'step_actors') and viewer.vtk_renderer_right.step_actors:
        print(f"   Multi-actors count: {len(viewer.vtk_renderer_right.step_actors)}")
        for i, actor in enumerate(viewer.vtk_renderer_right.step_actors):
            bounds = actor.GetBounds()
            color = actor.GetProperty().GetColor()
            print(f"   Actor {i}: bounds={bounds}, color={color}")
    
    if hasattr(viewer.vtk_renderer_right, 'step_actor') and viewer.vtk_renderer_right.step_actor:
        bounds = viewer.vtk_renderer_right.step_actor.GetBounds()
        color = viewer.vtk_renderer_right.step_actor.GetProperty().GetColor()
        visible = viewer.vtk_renderer_right.step_actor.GetVisibility()
        print(f"   Single-actor: bounds={bounds}, color={color}, visible={visible}")
    
    # Enable overlay and debug what gets copied
    print("\n=== ENABLING OVERLAY ===")
    viewer.toggle_overlay()
    
    # Debug overlay actors
    print(f"\n🔧 OVERLAY RENDERER ACTORS:")
    if hasattr(viewer, 'overlay_renderer'):
        actors = viewer.overlay_renderer.GetActors()
        actors.InitTraversal()
        actor_count = 0
        while True:
            actor = actors.GetNextActor()
            if not actor:
                break
            bounds = actor.GetBounds()
            color = actor.GetProperty().GetColor()
            opacity = actor.GetProperty().GetOpacity()
            print(f"   Overlay Actor {actor_count}: bounds={bounds}, color={color}, opacity={opacity}")
            actor_count += 1
        print(f"   Total overlay actors: {actor_count}")
    
    print("\n🔧 ANALYSIS COMPLETE")
    app.quit()

if __name__ == "__main__":
    debug_overlay_actors()
