#!/usr/bin/env python3
"""
Show EXACT POS and ROT values as requested:
1. Original POS/ROT in STEP file
2. What POS/ROT gets written after rotation
3. What POS/ROT gets read back from saved file
4. Verification they match
"""

import re
import numpy as np
from simple_step_modifier import SimpleSTEPModifier
import vtk
import math

def extract_pos_rot_from_step(filename):
    """Extract POS and ROT values from STEP file root coordinate system"""
    print(f"🔍 EXTRACTING POS/ROT FROM: {filename}")
    
    with open(filename, 'r') as f:
        content = f.read()
    
    # Extract position from #12 (root CARTESIAN_POINT)
    point_match = re.search(r'#12\s*=\s*CARTESIAN_POINT\s*\([^;]+\)\s*;', content)
    position = [0.0, 0.0, 0.0]
    if point_match:
        point_line = point_match.group(0)
        coords_match = re.search(r'\(\s*[^,]+,\s*\(\s*([^,]+),\s*([^,]+),\s*([^)]+)\s*\)', point_line)
        if coords_match:
            position = [float(coords_match.group(1)), float(coords_match.group(2)), float(coords_match.group(3))]
    
    # Extract Z direction from #13
    z_dir_match = re.search(r'#13\s*=\s*DIRECTION\s*\([^;]+\)\s*;', content)
    z_direction = [0.0, 0.0, 1.0]
    if z_dir_match:
        z_line = z_dir_match.group(0)
        z_coords_match = re.search(r'\(\s*[^,]+,\s*\(\s*([^,]+),\s*([^,]+),\s*([^)]+)\s*\)', z_line)
        if z_coords_match:
            z_direction = [float(z_coords_match.group(1)), float(z_coords_match.group(2)), float(z_coords_match.group(3))]
    
    # Extract X direction from #14
    x_dir_match = re.search(r'#14\s*=\s*DIRECTION\s*\([^;]+\)\s*;', content)
    x_direction = [1.0, 0.0, 0.0]
    if x_dir_match:
        x_line = x_dir_match.group(0)
        x_coords_match = re.search(r'\(\s*[^,]+,\s*\(\s*([^,]+),\s*([^,]+),\s*([^)]+)\s*\)', x_line)
        if x_coords_match:
            x_direction = [float(x_coords_match.group(1)), float(x_coords_match.group(2)), float(x_coords_match.group(3))]
    
    # Convert direction vectors to Euler angles (rotation)
    # This is the same conversion the GUI should be doing
    x_vec = np.array(x_direction)
    z_vec = np.array(z_direction)
    
    # Calculate Y vector as cross product of Z x X
    y_vec = np.cross(z_vec, x_vec)
    
    # Create rotation matrix from direction vectors
    rotation_matrix = np.array([
        [x_vec[0], y_vec[0], z_vec[0]],
        [x_vec[1], y_vec[1], z_vec[1]],
        [x_vec[2], y_vec[2], z_vec[2]]
    ])
    
    # Convert rotation matrix to Euler angles (XYZ order)
    # This matches the GUI's expected rotation format
    def matrix_to_euler_xyz(R):
        """Convert rotation matrix to Euler angles (XYZ order) in degrees"""
        sy = math.sqrt(R[0,0] * R[0,0] + R[1,0] * R[1,0])
        
        singular = sy < 1e-6
        
        if not singular:
            x = math.atan2(R[2,1], R[2,2])
            y = math.atan2(-R[2,0], sy)
            z = math.atan2(R[1,0], R[0,0])
        else:
            x = math.atan2(-R[1,2], R[1,1])
            y = math.atan2(-R[2,0], sy)
            z = 0
        
        return [math.degrees(x), math.degrees(y), math.degrees(z)]
    
    rotation = matrix_to_euler_xyz(rotation_matrix)
    
    return {
        'position': position,
        'rotation': rotation,
        'raw_vectors': {
            'x_direction': x_direction,
            'z_direction': z_direction
        }
    }

def show_step_file_lines(filename, title):
    """Show the exact lines from STEP file for root coordinate system"""
    print(f"\n📄 {title}: {filename}")
    print("-" * 60)
    
    with open(filename, 'r') as f:
        content = f.read()
    
    lines = content.split('\n')
    
    for i, line in enumerate(lines):
        if '#12 = CARTESIAN_POINT' in line:
            print(f"   Line {i+1}: {line}")
        elif '#13 = DIRECTION' in line:
            print(f"   Line {i+1}: {line}")
        elif '#14 = DIRECTION' in line:
            print(f"   Line {i+1}: {line}")

def main():
    print("=" * 80)
    print("🔧 COMPLETE POS/ROT VALUES TEST")
    print("=" * 80)
    
    original_file = "test.step"
    test_file = "pos_rot_test.step"
    
    # STEP 1: Show original POS/ROT
    print("📋 STEP 1: ORIGINAL POS/ROT VALUES")
    print("-" * 60)
    
    original_data = extract_pos_rot_from_step(original_file)
    
    print(f"📊 ORIGINAL POS/ROT:")
    print(f"   POS: X={original_data['position'][0]:.3f}mm Y={original_data['position'][1]:.3f}mm Z={original_data['position'][2]:.3f}mm")
    print(f"   ROT: X={original_data['rotation'][0]:.3f}° Y={original_data['rotation'][1]:.3f}° Z={original_data['rotation'][2]:.3f}°")
    
    show_step_file_lines(original_file, "ORIGINAL STEP FILE LINES")
    
    # STEP 2: Apply 45° Z rotation and save
    print(f"\n📋 STEP 2: APPLYING 45° Z ROTATION")
    print("-" * 60)
    
    modifier = SimpleSTEPModifier()
    modifier.load_step_file(original_file)
    
    # Create 45° Z rotation matrix
    transform = vtk.vtkTransform()
    transform.RotateZ(45.0)
    matrix = transform.GetMatrix()
    
    print(f"🔧 Input transformation: 45° Z rotation")
    print(f"🔧 Expected result: ROT Z should become 45°")
    
    # Transform geometry
    modifier.transform_geometry_coordinates(matrix)
    
    # Set coordinate system (45° Z rotation)
    modifier.modify_placement(0.0, 0.0, 0.0, 0.0, 0.0, 45.0)
    
    # Save file
    modifier.save_step_file(test_file)
    
    # STEP 3: Show what was written
    print(f"\n📋 STEP 3: WHAT WAS WRITTEN TO SAVED FILE")
    print("-" * 60)
    
    saved_data = extract_pos_rot_from_step(test_file)
    
    print(f"📊 SAVED POS/ROT:")
    print(f"   POS: X={saved_data['position'][0]:.3f}mm Y={saved_data['position'][1]:.3f}mm Z={saved_data['position'][2]:.3f}mm")
    print(f"   ROT: X={saved_data['rotation'][0]:.3f}° Y={saved_data['rotation'][1]:.3f}° Z={saved_data['rotation'][2]:.3f}°")
    
    show_step_file_lines(test_file, "SAVED STEP FILE LINES")
    
    # STEP 4: Read back verification
    print(f"\n📋 STEP 4: READING BACK FROM SAVED FILE")
    print("-" * 60)
    
    readback_data = extract_pos_rot_from_step(test_file)
    
    print(f"📊 READ BACK POS/ROT:")
    print(f"   POS: X={readback_data['position'][0]:.3f}mm Y={readback_data['position'][1]:.3f}mm Z={readback_data['position'][2]:.3f}mm")
    print(f"   ROT: X={readback_data['rotation'][0]:.3f}° Y={readback_data['rotation'][1]:.3f}° Z={readback_data['rotation'][2]:.3f}°")
    
    # STEP 5: Verification
    print(f"\n📋 STEP 5: VERIFICATION - DO POS/ROT VALUES MATCH?")
    print("-" * 60)
    
    print(f"📊 COMPARISON:")
    print(f"   ORIGINAL POS: X={original_data['position'][0]:7.3f}mm Y={original_data['position'][1]:7.3f}mm Z={original_data['position'][2]:7.3f}mm")
    print(f"   SAVED    POS: X={saved_data['position'][0]:7.3f}mm Y={saved_data['position'][1]:7.3f}mm Z={saved_data['position'][2]:7.3f}mm")
    print(f"   READ BACK POS: X={readback_data['position'][0]:7.3f}mm Y={readback_data['position'][1]:7.3f}mm Z={readback_data['position'][2]:7.3f}mm")
    print()
    print(f"   ORIGINAL ROT: X={original_data['rotation'][0]:7.3f}° Y={original_data['rotation'][1]:7.3f}° Z={original_data['rotation'][2]:7.3f}°")
    print(f"   SAVED    ROT: X={saved_data['rotation'][0]:7.3f}° Y={saved_data['rotation'][1]:7.3f}° Z={saved_data['rotation'][2]:7.3f}°")
    print(f"   READ BACK ROT: X={readback_data['rotation'][0]:7.3f}° Y={readback_data['rotation'][1]:7.3f}° Z={readback_data['rotation'][2]:7.3f}°")
    
    # Expected values
    expected_pos = [0.0, 0.0, 0.0]
    expected_rot = [0.0, 0.0, 45.0]  # 45° Z rotation
    
    print(f"\n📊 EXPECTED VALUES FOR 45° Z ROTATION:")
    print(f"   EXPECTED POS: X={expected_pos[0]:7.3f}mm Y={expected_pos[1]:7.3f}mm Z={expected_pos[2]:7.3f}mm")
    print(f"   EXPECTED ROT: X={expected_rot[0]:7.3f}° Y={expected_rot[1]:7.3f}° Z={expected_rot[2]:7.3f}°")
    
    # Check matches
    pos_match = np.allclose(readback_data['position'], expected_pos, atol=1e-3)
    rot_match = np.allclose(readback_data['rotation'], expected_rot, atol=1.0)  # 1 degree tolerance
    
    print(f"\n🔍 MATCH VERIFICATION:")
    print(f"   Position matches expected: {'✅ YES' if pos_match else '❌ NO'}")
    print(f"   Rotation matches expected: {'✅ YES' if rot_match else '❌ NO'}")
    
    # Check if saved and read back match
    pos_consistent = np.allclose(saved_data['position'], readback_data['position'], atol=1e-6)
    rot_consistent = np.allclose(saved_data['rotation'], readback_data['rotation'], atol=1e-3)
    
    print(f"   Saved/ReadBack consistent: {'✅ YES' if (pos_consistent and rot_consistent) else '❌ NO'}")
    
    if pos_match and rot_match and pos_consistent and rot_consistent:
        print(f"\n🎉 PERFECT SUCCESS: All POS/ROT values work correctly!")
        print(f"   ✅ Original POS/ROT were read correctly")
        print(f"   ✅ 45° Z rotation was applied and written correctly")
        print(f"   ✅ Saved POS/ROT were read back correctly")
        print(f"   ✅ All values match expected 45° Z rotation")
        print(f"   ✅ The coordinate transformation system is working!")
    else:
        print(f"\n❌ ISSUES DETECTED:")
        if not pos_match:
            print(f"   ❌ Position doesn't match expected values")
        if not rot_match:
            print(f"   ❌ Rotation doesn't match expected values")
        if not pos_consistent:
            print(f"   ❌ Position not consistent between save/read")
        if not rot_consistent:
            print(f"   ❌ Rotation not consistent between save/read")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
