#!/usr/bin/env python3
"""
Debug VTK color display to see why colors aren't showing correctly
"""

import sys
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer_tdk_modular import StepViewerTDK

class VTKColorDebugger:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        print("=== VTK COLOR DISPLAY DEBUG ===")
        
        # Start the test after a short delay
        QTimer.singleShot(2000, self.debug_vtk_colors)
        
    def debug_vtk_colors(self):
        print("=== DEBUGGING VTK COLOR DISPLAY ===")
        
        # Load model
        filename = "E:/Python/3d-models/SOIC16P127_1270X940X610L89X51.STEP"
        print(f"Loading: {filename}")
        
        success, message = self.viewer.step_loader_left.load_step_file(filename)
        if not success:
            print(f"Failed to load: {message}")
            self.app.quit()
            return
            
        print(f"Load success: {success}")
        
        # Display in VTK renderer
        polydata = self.viewer.step_loader_left.current_polydata
        if not polydata:
            print("No polydata available")
            self.app.quit()
            return
            
        print(f"Polydata: {polydata.GetNumberOfCells()} cells, {polydata.GetNumberOfPoints()} points")
        
        # Check color data
        cell_data = polydata.GetCellData()
        if cell_data.GetScalars():
            colors = cell_data.GetScalars()
            print(f"Cell colors: {colors.GetNumberOfTuples()} tuples, {colors.GetNumberOfComponents()} components")
            print(f"Color data type: {colors.GetDataType()}")
            print(f"Color range: {colors.GetRange()}")
            
            # Sample some colors
            print("Sample colors:")
            for i in range(min(10, colors.GetNumberOfTuples())):
                color = colors.GetTuple3(i)
                print(f"  Cell {i}: RGB{color}")
        else:
            print("No cell color data found")
            
        # Display the polydata
        self.viewer.vtk_renderer_left.display_polydata(polydata)
        
        # Check VTK mapper settings
        mapper = self.viewer.vtk_renderer_left.step_actor.GetMapper()
        print(f"Mapper scalar visibility: {mapper.GetScalarVisibility()}")
        print(f"Mapper scalar mode: {mapper.GetScalarMode()}")
        print(f"Mapper color mode: {mapper.GetColorMode()}")
        
        # Check actor properties
        actor_prop = self.viewer.vtk_renderer_left.step_actor.GetProperty()
        print(f"Actor color: {actor_prop.GetColor()}")
        print(f"Actor ambient: {actor_prop.GetAmbient()}")
        print(f"Actor diffuse: {actor_prop.GetDiffuse()}")
        
        print("=== VTK COLOR DEBUG COMPLETE ===")
        print("Check the visual display to see if colors are showing correctly")
        
        # Keep the program running for visual inspection
        QTimer.singleShot(10000, self.app.quit)  # Quit after 10 seconds
        
    def run(self):
        return self.app.exec_()

if __name__ == "__main__":
    debugger = VTKColorDebugger()
    sys.exit(debugger.run())
