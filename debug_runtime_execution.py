#!/usr/bin/env python3
"""
DEBUG RUNTIME EXECUTION - Test the actual running program
This will connect to the running main program and test if the methods
are actually being called and working during runtime.
"""

import sys
import os
import time
import subprocess
import psutil

def find_running_step_viewer():
    """Find if step_viewer_tdk_modular.py is currently running"""
    print("🔍 SEARCHING FOR RUNNING STEP VIEWER PROCESS")
    print("=" * 50)
    
    running_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['cmdline']:
                cmdline = ' '.join(proc.info['cmdline'])
                if 'step_viewer_tdk_modular.py' in cmdline:
                    running_processes.append({
                        'pid': proc.info['pid'],
                        'cmdline': cmdline
                    })
                    print(f"✅ Found process PID {proc.info['pid']}: {cmdline}")
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass
    
    if running_processes:
        print(f"✅ Found {len(running_processes)} running step viewer process(es)")
        return running_processes
    else:
        print("❌ No running step viewer processes found")
        return []

def test_timer_setup():
    """Test if the rotation timer is actually set up"""
    print("\n⏰ TESTING TIMER SETUP IN RUNNING PROGRAM")
    print("=" * 50)
    
    # Create a test script that checks if the timer exists
    test_script = '''
import sys
import os
sys.path.insert(0, ".")

# Try to access the running QApplication
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

app = QApplication.instance()
if app:
    print("✅ QApplication instance found")
    
    # Look for QTimer objects
    timers = []
    for obj in app.findChildren(QTimer):
        if obj.interval() > 0:
            timers.append(f"Timer interval: {obj.interval()}ms, active: {obj.isActive()}")
    
    if timers:
        print(f"✅ Found {len(timers)} active timers:")
        for timer in timers:
            print(f"   {timer}")
    else:
        print("❌ No active timers found")
else:
    print("❌ No QApplication instance found")
'''
    
    try:
        # Write and run the test script
        with open('test_timer_setup.py', 'w') as f:
            f.write(test_script)
        
        result = subprocess.run([sys.executable, 'test_timer_setup.py'], 
                              capture_output=True, text=True, timeout=10)
        
        print("Timer test output:")
        print(result.stdout)
        if result.stderr:
            print("Timer test errors:")
            print(result.stderr)
            
        # Clean up
        if os.path.exists('test_timer_setup.py'):
            os.remove('test_timer_setup.py')
            
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ Timer test error: {e}")
        return False

def test_method_calls_during_runtime():
    """Test if methods are actually being called during runtime"""
    print("\n📞 TESTING METHOD CALLS DURING RUNTIME")
    print("=" * 50)
    
    # Create a monitoring script
    monitor_script = '''
import sys
import os
import time
sys.path.insert(0, ".")

# Monkey patch the main program methods to add debug output
try:
    import step_viewer_tdk_modular
    
    # Store original methods
    original_update_vtk_text_overlays = step_viewer_tdk_modular.StepViewerTDK.update_vtk_text_overlays
    original_update_rotation_from_mouse = step_viewer_tdk_modular.StepViewerTDK.update_rotation_from_mouse
    original_rotate_shape = step_viewer_tdk_modular.StepViewerTDK.rotate_shape
    
    def debug_update_vtk_text_overlays(self):
        print(f"🔥 DEBUG: update_vtk_text_overlays called at {time.strftime('%H:%M:%S')}")
        return original_update_vtk_text_overlays(self)
    
    def debug_update_rotation_from_mouse(self):
        print(f"🔥 DEBUG: update_rotation_from_mouse called at {time.strftime('%H:%M:%S')}")
        return original_update_rotation_from_mouse(self)
    
    def debug_rotate_shape(self, axis, degrees):
        print(f"🔥 DEBUG: rotate_shape called with {axis}, {degrees}° at {time.strftime('%H:%M:%S')}")
        return original_rotate_shape(self, axis, degrees)
    
    # Monkey patch the methods
    step_viewer_tdk_modular.StepViewerTDK.update_vtk_text_overlays = debug_update_vtk_text_overlays
    step_viewer_tdk_modular.StepViewerTDK.update_rotation_from_mouse = debug_update_rotation_from_mouse
    step_viewer_tdk_modular.StepViewerTDK.rotate_shape = debug_rotate_shape
    
    print("✅ Method monitoring installed")
    print("Now run the main program and watch for method calls...")
    
    # Keep the script running for a while
    time.sleep(30)
    
except Exception as e:
    print(f"❌ Method monitoring error: {e}")
'''
    
    try:
        # Write the monitoring script
        with open('monitor_methods.py', 'w') as f:
            f.write(monitor_script)
        
        print("✅ Method monitoring script created")
        print("📋 To use: Run 'python monitor_methods.py' in another terminal")
        print("   Then interact with the main program to see if methods are called")
        
        return True
        
    except Exception as e:
        print(f"❌ Method monitoring setup error: {e}")
        return False

def test_variable_initialization():
    """Test if the required variables are initialized"""
    print("\n🔧 TESTING VARIABLE INITIALIZATION")
    print("=" * 50)
    
    # Create a script to check variable initialization
    init_test_script = '''
import sys
sys.path.insert(0, ".")

try:
    import step_viewer_tdk_modular
    
    # Create a test instance (without showing UI)
    from PyQt5.QtWidgets import QApplication
    import os
    os.environ['QT_QPA_PLATFORM'] = 'offscreen'  # Prevent window from showing
    
    app = QApplication([])
    
    # Create instance
    viewer = step_viewer_tdk_modular.StepViewerTDK()
    
    # Check if required variables exist
    required_vars = [
        'model_rot_left', 'model_rot_right',
        'current_rot_left', 'current_rot_right', 
        'current_axis_left', 'current_axis_right',
        'current_angle_left', 'current_angle_right'
    ]
    
    print("Variable initialization check:")
    for var in required_vars:
        if hasattr(viewer, var):
            value = getattr(viewer, var)
            print(f"✅ {var}: {value}")
        else:
            print(f"❌ {var}: NOT INITIALIZED")
    
    # Check if timer exists
    if hasattr(viewer, 'rotation_timer'):
        timer = viewer.rotation_timer
        print(f"✅ rotation_timer: interval={timer.interval()}ms, active={timer.isActive()}")
    else:
        print("❌ rotation_timer: NOT INITIALIZED")
    
    app.quit()
    
except Exception as e:
    print(f"❌ Variable initialization test error: {e}")
'''
    
    try:
        # Write and run the test script
        with open('test_variable_init.py', 'w') as f:
            f.write(init_test_script)
        
        result = subprocess.run([sys.executable, 'test_variable_init.py'], 
                              capture_output=True, text=True, timeout=15)
        
        print("Variable initialization test output:")
        print(result.stdout)
        if result.stderr:
            print("Variable initialization test errors:")
            print(result.stderr)
            
        # Clean up
        if os.path.exists('test_variable_init.py'):
            os.remove('test_variable_init.py')
            
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ Variable initialization test error: {e}")
        return False

def main():
    """Run all runtime debug tests"""
    print("RUNTIME EXECUTION DEBUGGING")
    print("=" * 60)
    
    tests = [
        ("Find Running Process", find_running_step_viewer),
        ("Timer Setup", test_timer_setup),
        ("Method Call Monitoring", test_method_calls_during_runtime),
        ("Variable Initialization", test_variable_initialization)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("RUNTIME DEBUG RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        if isinstance(result, list):  # For find_running_process
            status = "✅ FOUND" if result else "❌ NOT FOUND"
            print(f"{status}: {test_name}")
            if result:
                passed += 1
        else:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status}: {test_name}")
            if result:
                passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed < len(results):
        print("\n⚠️ RUNTIME ISSUES FOUND:")
        print("The methods exist but may not be executing properly at runtime")
        print("Check variable initialization and timer setup")

if __name__ == "__main__":
    main()
