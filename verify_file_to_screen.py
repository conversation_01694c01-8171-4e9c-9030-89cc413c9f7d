#!/usr/bin/env python3

print("VERIFY STEP FILE COLORS MATCH SCREEN COLORS")

# 1. Get colors from STEP file
print("=== STEP FILE COLORS ===")
with open('SOIC16P127_1270X940X610L89X51.STEP', 'r', encoding='utf-8', errors='ignore') as f:
    content = f.read()

import re
colour_pattern = r'COLOUR_RGB.*?([\d\.]+)\s*,\s*([\d\.]+)\s*,\s*([\d\.]+)'
matches = re.findall(colour_pattern, content)

file_colors = []
for match in matches:
    r = int(float(match[0]) * 255)
    g = int(float(match[1]) * 255)
    b = int(float(match[2]) * 255)
    file_colors.append((r, g, b))

unique_file_colors = list(set(file_colors))
print("Colors in STEP file:")
for color in unique_file_colors:
    count = file_colors.count(color)
    print(f"  RGB{color}: {count} entries")

# 2. Get colors that will go to screen
print("\n=== SCREEN COLORS ===")
from step_loader import STEPLoader

loader = STEPLoader()
success, msg = loader.load_step_file('SOIC16P127_1270X940X610L89X51.STEP')

if success and loader.current_polydata:
    polydata = loader.current_polydata
    colors_array = polydata.GetCellData().GetScalars("Colors")
    
    if colors_array:
        screen_colors = []
        for i in range(colors_array.GetNumberOfTuples()):
            r = int(colors_array.GetComponent(i, 0))
            g = int(colors_array.GetComponent(i, 1))
            b = int(colors_array.GetComponent(i, 2))
            screen_colors.append((r, g, b))
        
        unique_screen_colors = list(set(screen_colors))
        print("Colors going to screen:")
        for color in unique_screen_colors:
            count = screen_colors.count(color)
            print(f"  RGB{color}: {count} cells")
        
        # 3. Verify quantities match
        print("\n=== VERIFICATION ===")

        # Check if same colors exist
        colors_match = set(unique_file_colors) == set(unique_screen_colors)

        # Check if quantities are proportional
        file_total = len(file_colors)
        screen_total = len(screen_colors)

        print(f"STEP file total: {file_total} color entries")
        print(f"Screen total: {screen_total} color entries")

        quantities_match = True
        for color in unique_file_colors:
            file_count = file_colors.count(color)
            screen_count = screen_colors.count(color)
            file_ratio = file_count / file_total
            screen_ratio = screen_count / screen_total

            print(f"RGB{color}:")
            print(f"  File: {file_count}/{file_total} ({file_ratio:.1%})")
            print(f"  Screen: {screen_count}/{screen_total} ({screen_ratio:.1%})")

            # Allow some tolerance for triangulation differences
            if abs(file_ratio - screen_ratio) > 0.1:  # 10% tolerance
                quantities_match = False
                print(f"  RATIO MISMATCH!")

        if colors_match and quantities_match:
            print("\nSUCCESS: Colors and quantities match")
        else:
            print("\nFAILURE: Colors or quantities do not match")
            if not colors_match:
                print("Color types don't match")
            if not quantities_match:
                print("Color quantities/ratios don't match")
    else:
        print("FAILURE: No colors going to screen")
else:
    print("FAILURE: Could not load STEP file")

print("\nVERIFICATION COMPLETE")
