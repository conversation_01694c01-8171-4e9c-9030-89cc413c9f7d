@echo off
echo ========================================
echo DIRECT CMD LAUNCHER - BYPASSING POWERSHELL
echo ========================================
echo.
echo Starting main STEP viewer program...
echo Bypassing PowerShell function/alias issues...
echo.

REM Change to the correct directory
cd /d "E:\Python\3d-models"

REM Run the main program directly using full path to Python
echo Running: C:\Users\<USER>\Miniforge3\python.exe step_viewer_tdk_modular.py
echo.
C:\Users\<USER>\Miniforge3\python.exe step_viewer_tdk_modular.py

echo.
echo Program finished. Press any key to close...
pause > nul
