#!/usr/bin/env python3
"""
Debug rotation values calculation
"""

import sys
import os
sys.path.append('.')

def debug_rotation_calculation():
    """Debug the rotation calculation logic"""
    print("🔍 DEBUGGING ROTATION CALCULATION")
    print("=" * 50)
    
    # Simulate the values from our test
    orig_rot = {'x': 90.0, 'y': 0.0, 'z': 0.0}  # From loaded file
    delta_rot = {'x': 0.0, 'y': 45.0, 'z': 0.0}  # Additional rotation
    
    print(f"📊 Input values:")
    print(f"   orig_rot: {orig_rot}")
    print(f"   delta_rot: {delta_rot}")
    
    # Calculate new values (same logic as in _save_step_text_transform)
    new_rot_x = orig_rot['x'] + delta_rot['x']
    new_rot_y = orig_rot['y'] + delta_rot['y']
    new_rot_z = orig_rot['z'] + delta_rot['z']
    
    print(f"\n🧮 Calculated new values:")
    print(f"   new_rot_x = {orig_rot['x']} + {delta_rot['x']} = {new_rot_x}")
    print(f"   new_rot_y = {orig_rot['y']} + {delta_rot['y']} = {new_rot_y}")
    print(f"   new_rot_z = {orig_rot['z']} + {delta_rot['z']} = {new_rot_z}")
    
    # Create the rotation values dict (same as in the method)
    current_rotation_values = {
        'x': new_rot_x,
        'y': new_rot_y,
        'z': new_rot_z
    }
    
    print(f"\n📦 current_rotation_values dict:")
    print(f"   {current_rotation_values}")
    
    # Test the SimpleSTEPModifier directly
    print(f"\n🧪 Testing SimpleSTEPModifier directly...")
    
    try:
        from simple_step_modifier import SimpleSTEPModifier
        
        modifier = SimpleSTEPModifier()
        
        # Load a test file
        if modifier.load_step_file('test_rotation_1.step'):
            print(f"✅ Loaded test_rotation_1.step")
            
            # Test the save with our rotation values
            print(f"🔧 Calling save_step_file with rotation_values: {current_rotation_values}")
            
            # Save to a debug file
            debug_filename = "debug_rotation_values.step"
            success = modifier.save_step_file(debug_filename, current_rotation_values)
            
            if success:
                print(f"✅ Saved debug file: {debug_filename}")
                
                # Check what was actually saved
                with open(debug_filename, 'r') as f:
                    content = f.read()
                
                # Find the rotation comment
                lines = content.split('\n')
                for line in lines:
                    if 'ROTATION_VALUES:' in line:
                        print(f"📄 Found rotation comment: {line.strip()}")
                        break
                else:
                    print(f"❌ No rotation comment found in saved file")
                    
            else:
                print(f"❌ Failed to save debug file")
        else:
            print(f"❌ Failed to load test_rotation_1.step")
            
    except Exception as e:
        print(f"❌ Error testing SimpleSTEPModifier: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_rotation_calculation()
