#!/usr/bin/env python3
"""
Find the correct import paths for OpenCASCADE geometry modules
"""

import os
import sys

print("🔍 FINDING OpenCASCADE Geometry Modules")
print("=" * 50)

# Check what's actually available in OCC.Core
try:
    import OCC.Core
    occ_path = os.path.dirname(OCC.Core.__file__)
    print(f"📁 OCC.Core path: {occ_path}")
    
    # List all files in the Core directory
    core_files = os.listdir(occ_path)
    print(f"📁 Found {len(core_files)} files in Core directory")
    
    # Look for geometry-related modules
    geometry_modules = [f for f in core_files if 'gp' in f.lower() or 'geom' in f.lower()]
    print(f"🔍 Geometry-related files: {len(geometry_modules)}")
    for mod in sorted(geometry_modules)[:10]:  # Show first 10
        print(f"   - {mod}")
    
    # Look for transformation-related modules  
    transform_modules = [f for f in core_files if 'trsf' in f.lower() or 'transform' in f.lower()]
    print(f"🔍 Transform-related files: {len(transform_modules)}")
    for mod in sorted(transform_modules):
        print(f"   - {mod}")
        
    # Look for BRep modules
    brep_modules = [f for f in core_files if 'brep' in f.lower()]
    print(f"🔍 BRep-related files: {len(brep_modules)}")
    for mod in sorted(brep_modules)[:5]:  # Show first 5
        print(f"   - {mod}")
        
except Exception as e:
    print(f"❌ Error exploring OCC.Core: {e}")

print()

# Try different import patterns for geometry
geometry_imports = [
    "from OCC.Core.gp import gp_Trsf",
    "from OCC.Core import gp",
    "from OCC.Core._gp import gp_Trsf", 
    "from OCC.gp import gp_Trsf",
    "import OCC.Core.gp as gp"
]

print("🧪 Testing geometry import patterns:")
for import_stmt in geometry_imports:
    try:
        exec(import_stmt)
        print(f"✅ {import_stmt}: SUCCESS")
        
        # If successful, try to create a transformation
        try:
            if 'gp_Trsf' in locals():
                transform = gp_Trsf()
                print(f"   ✅ Can create gp_Trsf object")
            elif 'gp' in locals():
                transform = gp.gp_Trsf()
                print(f"   ✅ Can create gp.gp_Trsf object")
        except Exception as e:
            print(f"   ❌ Cannot create transform object: {e}")
            
    except Exception as e:
        print(f"❌ {import_stmt}: FAILED - {e}")

print()

# Try BRep transform imports
brep_imports = [
    "from OCC.Core.BRepBuilderAPI import BRepBuilderAPI_Transform",
    "from OCC.Core import BRepBuilderAPI_Transform",
    "from OCC.Core._BRepBuilderAPI import BRepBuilderAPI_Transform",
    "import OCC.Core.BRepBuilderAPI as BRepAPI"
]

print("🧪 Testing BRep transform import patterns:")
for import_stmt in brep_imports:
    try:
        exec(import_stmt)
        print(f"✅ {import_stmt}: SUCCESS")
    except Exception as e:
        print(f"❌ {import_stmt}: FAILED - {e}")

print()
print("🔍 Investigation complete!")
