#!/usr/bin/env python3
"""
Debug script to investigate rotation display issues
"""

import sys
import os
sys.path.append('.')

from step_viewer_tdk_modular import StepViewerTDK
from PyQt5.QtWidgets import QApplication
import time

def debug_rotation_display():
    """Debug the rotation display issue"""
    print("🔍 DEBUGGING ROTATION DISPLAY ISSUE")
    print("=" * 50)
    
    app = QApplication([])
    viewer = StepViewerTDK()
    viewer.show()
    
    try:
        # Load original file in top viewer
        print("\n📂 Loading original test.step in TOP viewer...")
        viewer.active_viewer = "top"
        success = viewer.load_step_file_direct('test.step')
        
        if success:
            print("✅ Original file loaded successfully")
            
            # Get original data
            if hasattr(viewer, 'current_rot_left'):
                orig_rot = viewer.current_rot_left
                print(f"📊 Original rotation: {orig_rot}")
            
            if hasattr(viewer.step_loader_left, 'current_polydata'):
                orig_bounds = viewer.step_loader_left.current_polydata.GetBounds()
                print(f"📊 Original bounds: {orig_bounds}")
                print(f"📊 Original cells: {viewer.step_loader_left.current_polydata.GetNumberOfCells()}")
        
        # Load rotated file in bottom viewer
        print("\n📂 Loading rotated simple_rotation_test.step in BOTTOM viewer...")
        viewer.active_viewer = "bottom"
        success = viewer.load_step_file_direct('simple_rotation_test.step')
        
        if success:
            print("✅ Rotated file loaded successfully")
            
            # Get rotated data
            if hasattr(viewer, 'current_rot_right'):
                loaded_rot = viewer.current_rot_right
                print(f"📊 Loaded rotation: {loaded_rot}")
            
            if hasattr(viewer.step_loader_right, 'current_polydata'):
                loaded_bounds = viewer.step_loader_right.current_polydata.GetBounds()
                print(f"📊 Loaded bounds: {loaded_bounds}")
                print(f"📊 Loaded cells: {viewer.step_loader_right.current_polydata.GetNumberOfCells()}")
                
            # Compare bounds
            if 'orig_bounds' in locals() and 'loaded_bounds' in locals():
                print(f"\n🔍 BOUNDS COMPARISON:")
                print(f"   Original: X({orig_bounds[0]:.3f} to {orig_bounds[1]:.3f}), Y({orig_bounds[2]:.3f} to {orig_bounds[3]:.3f}), Z({orig_bounds[4]:.3f} to {orig_bounds[5]:.3f})")
                print(f"   Loaded:   X({loaded_bounds[0]:.3f} to {loaded_bounds[1]:.3f}), Y({loaded_bounds[2]:.3f} to {loaded_bounds[3]:.3f}), Z({loaded_bounds[4]:.3f} to {loaded_bounds[5]:.3f})")
                
                # Check if bounds are significantly different (indicating geometry transformation)
                x_diff = abs((loaded_bounds[1] - loaded_bounds[0]) - (orig_bounds[1] - orig_bounds[0]))
                y_diff = abs((loaded_bounds[3] - loaded_bounds[2]) - (orig_bounds[3] - orig_bounds[2]))
                z_diff = abs((loaded_bounds[5] - loaded_bounds[4]) - (orig_bounds[5] - orig_bounds[4]))
                
                print(f"\n🔍 DIMENSION CHANGES:")
                print(f"   X dimension change: {x_diff:.3f}")
                print(f"   Y dimension change: {y_diff:.3f}")
                print(f"   Z dimension change: {z_diff:.3f}")
                
                if x_diff > 0.1 or y_diff > 0.1 or z_diff > 0.1:
                    print("✅ Geometry appears to be transformed (bounds changed significantly)")
                else:
                    print("❌ Geometry may not be transformed (bounds similar)")
        
        # Check if the issue is with display vs data
        print(f"\n🔍 CHECKING DISPLAY ACTORS:")
        
        # Check top viewer actors
        if hasattr(viewer.vtk_renderer_left, 'step_actors') and viewer.vtk_renderer_left.step_actors:
            print(f"📊 TOP viewer has {len(viewer.vtk_renderer_left.step_actors)} multi-actors")
        elif hasattr(viewer.vtk_renderer_left, 'step_actor') and viewer.vtk_renderer_left.step_actor:
            print(f"📊 TOP viewer has single actor")
        
        # Check bottom viewer actors
        if hasattr(viewer.vtk_renderer_right, 'step_actors') and viewer.vtk_renderer_right.step_actors:
            print(f"📊 BOTTOM viewer has {len(viewer.vtk_renderer_right.step_actors)} multi-actors")
            
            # Check if actors have transforms applied
            for i, actor in enumerate(viewer.vtk_renderer_right.step_actors):
                transform = actor.GetUserTransform()
                if transform:
                    matrix = transform.GetMatrix()
                    print(f"📊 Actor {i} has transform matrix applied")
                else:
                    print(f"📊 Actor {i} has NO transform matrix")
                    
        elif hasattr(viewer.vtk_renderer_right, 'step_actor') and viewer.vtk_renderer_right.step_actor:
            print(f"📊 BOTTOM viewer has single actor")
            transform = viewer.vtk_renderer_right.step_actor.GetUserTransform()
            if transform:
                print(f"📊 Single actor has transform matrix applied")
            else:
                print(f"📊 Single actor has NO transform matrix")
        
    except Exception as e:
        print(f"❌ Error during debug: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n🎯 DEBUG COMPLETE")
    
    # Keep window open briefly
    time.sleep(2)
    app.quit()

if __name__ == "__main__":
    debug_rotation_display()
