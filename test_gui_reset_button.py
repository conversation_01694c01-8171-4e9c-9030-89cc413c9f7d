#!/usr/bin/env python3
"""
Test GUI Reset Button - Find what method the reset button actually calls
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication, QPushButton
from PyQt5.QtCore import QTimer

# Import the main program
from step_viewer_tdk_modular import StepViewerTDK

class GUIResetTester:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        self.reset_button = None
        
    def find_reset_button(self, widget):
        """Recursively find the reset button in the GUI"""
        if isinstance(widget, QPushButton):
            if "reset" in widget.text().lower():
                print(f"Found reset button: '{widget.text()}'")
                return widget
        
        # Check children
        for child in widget.findChildren(QPushButton):
            if "reset" in child.text().lower():
                print(f"Found reset button: '{child.text()}'")
                return child
        
        return None
        
    def run_test(self):
        """Test the actual GUI reset button"""
        print("=" * 60)
        print("🔧 GUI RESET BUTTON TEST")
        print("=" * 60)
        
        # Step 1: Create viewer
        print("\n📋 STEP 1: CREATING VIEWER...")
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        # Step 2: Find reset button
        print("\n📋 STEP 2: FINDING RESET BUTTON...")
        self.reset_button = self.find_reset_button(self.viewer)
        
        if not self.reset_button:
            print("❌ Reset button not found!")
            return False
            
        print(f"✅ Found reset button: '{self.reset_button.text()}'")
        
        # Step 3: Load test file
        print("\n📋 STEP 3: LOADING TEST STEP FILE...")
        test_file = "test.step"
        if not os.path.exists(test_file):
            print(f"❌ Test file {test_file} not found!")
            return False
            
        # Load file programmatically
        success = self.viewer.step_loader_left.load_step_file(test_file)
        if success:
            print(f"✅ Loaded {test_file} successfully")
            # Update viewer
            self.viewer.vtk_renderer_left.display_polydata(self.viewer.step_loader_left.current_polydata)
            self.viewer.extract_step_transformation_data("top")
            self.viewer.update_transform_display()
        else:
            print(f"❌ Failed to load {test_file}")
            return False
            
        # Step 4: Show initial values
        print("\n📋 STEP 4: INITIAL VALUES...")
        self.show_current_values("INITIAL")
        
        # Step 5: Apply rotation
        print("\n📋 STEP 5: APPLYING ROTATION (45° Z-axis)...")
        self.viewer.rotate_shape('z', 45)
        time.sleep(0.5)  # Let the rotation complete
        self.show_current_values("AFTER ROTATION")
        
        # Step 6: Test GUI reset button
        print("\n📋 STEP 6: TESTING GUI RESET BUTTON...")
        print("🔧 Clicking the actual GUI reset button...")
        print("🔧 Watch for debug output to see which method is called...")
        
        # Click the reset button
        self.reset_button.click()
        time.sleep(0.5)  # Let the reset complete
        
        self.show_current_values("AFTER GUI RESET")
        
        # Step 7: Check what happened
        print("\n📋 STEP 7: ANALYSIS...")
        return self.analyze_reset_result()
        
    def show_current_values(self, stage):
        """Show current position and rotation values"""
        print(f"   {stage} VALUES:")
        print(f"   Position: X={self.viewer.current_pos_left['x']:.3f}, Y={self.viewer.current_pos_left['y']:.3f}, Z={self.viewer.current_pos_left['z']:.3f}")
        print(f"   Rotation: X={self.viewer.current_rot_left['x']:.3f}°, Y={self.viewer.current_rot_left['y']:.3f}°, Z={self.viewer.current_rot_left['z']:.3f}°")
        
    def analyze_reset_result(self):
        """Analyze what the GUI reset button actually did"""
        pos_reset = (abs(self.viewer.current_pos_left['x']) < 0.001 and 
                    abs(self.viewer.current_pos_left['y']) < 0.001 and 
                    abs(self.viewer.current_pos_left['z']) < 0.001)
                    
        rot_reset = (abs(self.viewer.current_rot_left['x']) < 0.001 and 
                    abs(self.viewer.current_rot_left['y']) < 0.001 and 
                    abs(self.viewer.current_rot_left['z']) < 0.001)
        
        print(f"   Numbers reset: {'✅ YES' if pos_reset and rot_reset else '❌ NO'}")
        
        # Check if VTK actors were reset (visual reset)
        visual_reset = False
        if hasattr(self.viewer.vtk_renderer_left, 'step_actors') and self.viewer.vtk_renderer_left.step_actors:
            # Check if actors have identity transform
            import vtk
            for actor in self.viewer.vtk_renderer_left.step_actors:
                transform = actor.GetUserTransform()
                if transform:
                    matrix = transform.GetMatrix()
                    # Check if it's identity matrix (simplified check)
                    visual_reset = (abs(matrix.GetElement(0,3)) < 0.001 and 
                                  abs(matrix.GetElement(1,3)) < 0.001 and 
                                  abs(matrix.GetElement(2,3)) < 0.001)
                    break
                else:
                    # No transform means identity (reset)
                    visual_reset = True
                    break
        elif hasattr(self.viewer.vtk_renderer_left, 'step_actor') and self.viewer.vtk_renderer_left.step_actor:
            transform = self.viewer.vtk_renderer_left.step_actor.GetUserTransform()
            if transform:
                matrix = transform.GetMatrix()
                visual_reset = (abs(matrix.GetElement(0,3)) < 0.001 and 
                              abs(matrix.GetElement(1,3)) < 0.001 and 
                              abs(matrix.GetElement(2,3)) < 0.001)
            else:
                # No transform means identity (reset)
                visual_reset = True
        
        print(f"   Model reset: {'✅ YES' if visual_reset else '❌ NO'}")
        
        # Determine what happened
        if pos_reset and rot_reset and visual_reset:
            print(f"\n🎯 RESULT: ✅ GUI RESET BUTTON WORKS CORRECTLY")
            print("   Both numbers and model were reset properly")
            return True
        elif pos_reset and rot_reset and not visual_reset:
            print(f"\n🎯 RESULT: ⚠️ GUI RESET BUTTON ONLY RESETS NUMBERS")
            print("   Numbers reset but model did not reset visually")
            print("   This confirms the GUI button calls a different method than the fixed one")
            return False
        else:
            print(f"\n🎯 RESULT: ❌ GUI RESET BUTTON DOESN'T WORK")
            print("   Neither numbers nor model were reset")
            return False

def main():
    """Run the GUI reset button test"""
    tester = GUIResetTester()
    success = tester.run_test()
    
    print(f"\n{'='*60}")
    if success:
        print("✅ GUI RESET BUTTON WORKS CORRECTLY!")
    else:
        print("❌ GUI RESET BUTTON HAS ISSUES - CHECK DEBUG OUTPUT ABOVE")
        print("   Look for debug messages to see which method was called")
    print(f"{'='*60}")
    
    # Keep the app running briefly to see any delayed debug output
    QTimer.singleShot(2000, tester.app.quit)  # Quit after 2 seconds
    tester.app.exec_()
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
