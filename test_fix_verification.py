#!/usr/bin/env python3
"""
Test to verify the giant black box fix is working
"""

import sys
import os
import vtk
from step_loader import STEPLoader
from vtk_renderer import VT<PERSON><PERSON><PERSON><PERSON>

def test_16pin_soic_actors():
    """Test that 16-pin SOIC creates correct actors without giant box"""
    print("🔬 Testing 16-pin SOIC actor creation...")
    
    # Load the 16-pin SOIC
    loader = STEPLoader()
    result = loader.load_step_file("SOIC16P127_1270X940X610L89X51.STEP")
    
    if len(result) == 3:
        polydata, success, message = result
    else:
        success, message = result
        polydata = loader.current_polydata
    
    if not success or not polydata:
        print(f"❌ Failed to load 16-pin SOIC: {message}")
        return False
    
    print(f"✅ Loaded 16-pin SOIC: {message}")
    
    # Create VTK renderer and test actor creation
    renderer = VTKRenderer()
    
    # Simulate the display_polydata process
    cell_colors = polydata.GetCellData().GetScalars("Colors")
    
    if not cell_colors or cell_colors.GetNumberOfTuples() == 0:
        print("❌ No colors found in polydata")
        return False
    
    # Group cells by color (like the real code does)
    color_groups = {}
    for cell_id in range(cell_colors.GetNumberOfTuples()):
        r = int(cell_colors.GetComponent(cell_id, 0))
        g = int(cell_colors.GetComponent(cell_id, 1))
        b = int(cell_colors.GetComponent(cell_id, 2))
        color_key = (r, g, b)
        
        if color_key not in color_groups:
            color_groups[color_key] = []
        color_groups[color_key].append(cell_id)
    
    print(f"🎨 Found {len(color_groups)} color groups:")
    
    actors_created = 0
    actors_skipped = 0
    
    for i, (color_rgb, cell_ids) in enumerate(color_groups.items()):
        print(f"   Color {i}: RGB{color_rgb} - {len(cell_ids)} cells")
        
        # Create actor like the real code
        color_polydata = vtk.vtkPolyData()
        color_polydata.SetPoints(polydata.GetPoints())
        
        color_cells = vtk.vtkCellArray()
        for cell_id in cell_ids:
            cell = polydata.GetCell(cell_id)
            color_cells.InsertNextCell(cell)
        
        color_polydata.SetPolys(color_cells)
        
        mapper = vtk.vtkPolyDataMapper()
        mapper.SetInputData(color_polydata)
        mapper.SetScalarVisibility(False)
        
        actor = vtk.vtkActor()
        actor.SetMapper(mapper)
        
        r, g, b = color_rgb
        actor.GetProperty().SetColor(r/255.0, g/255.0, b/255.0)
        
        # Get bounds and check for problematic dimensions
        bounds = actor.GetBounds()
        x_min, x_max, y_min, y_max, z_min, z_max = bounds
        x_range = x_max - x_min
        y_range = y_max - y_min
        
        print(f"      Bounds: {bounds}")
        print(f"      Dimensions: {x_range:.3f} x {y_range:.3f}")
        
        # Apply the same fix logic as vtk_renderer.py
        is_problematic = (x_range > 10.0 or y_range > 10.0)
        
        if is_problematic:
            print(f"      ⚠️  WOULD BE SKIPPED: Giant dimensions detected!")
            actors_skipped += 1
        else:
            print(f"      ✅ WOULD BE KEPT: Normal dimensions")
            actors_created += 1
    
    print(f"\n📊 RESULTS:")
    print(f"   Actors that would be created: {actors_created}")
    print(f"   Actors that would be skipped: {actors_skipped}")
    
    # Expected: 1 actor created (light gray body), 1 actor skipped (dark giant pins)
    if actors_created == 1 and actors_skipped == 1:
        print(f"✅ SUCCESS: Fix is working correctly!")
        print(f"   - 1 actor created (should be the light gray body)")
        print(f"   - 1 actor skipped (should be the problematic dark pins)")
        return True
    else:
        print(f"❌ FAILURE: Unexpected actor counts!")
        return False

def main():
    """Run the verification test"""
    print("🧪 VERIFYING GIANT BLACK BOX FIX")
    print("=" * 50)
    
    success = test_16pin_soic_actors()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 FIX VERIFICATION: PASSED")
        print("The giant black box issue should now be resolved!")
    else:
        print("💥 FIX VERIFICATION: FAILED")
        print("The fix may not be working as expected.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
