#!/usr/bin/env python3

print("SIMPLE SHAPE COLOR MATCHING")

from step_loader import STEPLoader

# Load the file
loader = STEPLoader()
success, msg = loader.load_step_file('SOIC16P127_1270X940X610L89X51.STEP')

if success and loader.current_polydata:
    polydata = loader.current_polydata
    colors_array = polydata.GetCellData().GetScalars("Colors")
    
    if colors_array:
        # Get display colors
        display_colors = []
        for i in range(colors_array.GetNumberOfTuples()):
            r = int(colors_array.GetComponent(i, 0))
            g = int(colors_array.GetComponent(i, 1))
            b = int(colors_array.GetComponent(i, 2))
            display_colors.append((r, g, b))
        
        # STEP shape colors (known from analysis)
        dark_color = (63, 63, 63)
        light_color = (192, 192, 192)
        dark_faces = [18, 37, 56, 75, 94, 113, 132, 151, 170, 189, 208, 227]
        
        step_shape_colors = []
        for face_idx in range(239):  # 239 STEP shapes
            if face_idx in dark_faces:
                step_shape_colors.append(dark_color)
            else:
                step_shape_colors.append(light_color)
        
        print(f"STEP shapes: {len(step_shape_colors)}")
        print(f"Display items: {len(display_colors)}")
        
        # Simple check: do the colors match the expected pattern?
        dark_count = display_colors.count(dark_color)
        light_count = display_colors.count(light_color)
        
        expected_dark = step_shape_colors.count(dark_color)
        expected_light = step_shape_colors.count(light_color)
        
        print(f"Display: {dark_count} dark, {light_count} light")
        print(f"Expected: {expected_dark} dark shapes, {expected_light} light shapes")
        
        # Check if shape 18 is correct
        triangles_per_shape = len(display_colors) / len(step_shape_colors)
        shape_18_start = int(18 * triangles_per_shape)
        shape_18_end = int(19 * triangles_per_shape)
        
        print(f"Shape 18 triangles: {shape_18_start} to {shape_18_end}")
        print(f"Shape 18 should be: {step_shape_colors[18]}")
        
        for i in range(shape_18_start, min(shape_18_end, len(display_colors))):
            actual_color = display_colors[i]
            print(f"Triangle {i}: {actual_color}")
            
        if all(display_colors[i] == step_shape_colors[18] for i in range(shape_18_start, min(shape_18_end, len(display_colors)))):
            print("Shape 18 matches!")
        else:
            print("Shape 18 does NOT match!")
    else:
        print("No colors")
else:
    print("Failed to load")

print("SIMPLE SHAPE MATCHING COMPLETE")
