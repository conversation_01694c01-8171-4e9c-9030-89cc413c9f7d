#!/usr/bin/env python3
"""
Test the save/load rotation persistence fix
This test verifies that:
1. Load a file
2. Apply rotation using buttons
3. Save the rotated file
4. Load the saved file
5. Verify the rotation is preserved correctly
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer_tdk_modular import StepViewerTDK

class SaveLoadRotationTest:
    def __init__(self):
        self.app = QApplication([])
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        self.test_rotation = {'x': 45.0, 'y': 30.0, 'z': 15.0}
        self.original_filename = "SOIC16P127_1270X940X610L89X51.STEP"
        self.saved_filename = "test_rotation_persistence.step"
        
        print("🧪 SAVE/LOAD ROTATION PERSISTENCE TEST")
        print("=" * 50)
        
        # Start the test sequence
        QTimer.singleShot(1000, self.step1_load_original)
        
    def step1_load_original(self):
        """Step 1: Load the original file"""
        print("\n🔄 STEP 1: Loading original file...")
        
        if not os.path.exists(self.original_filename):
            print(f"❌ Test file not found: {self.original_filename}")
            self.app.quit()
            return
            
        # Set active viewer to top
        self.viewer.active_viewer = "top"
        self.viewer.update_viewer_highlights()
        
        # Load the file
        success = self.viewer.load_step_file_direct(self.original_filename)
        
        if not success:
            print("❌ Failed to load original file")
            self.app.quit()
            return
            
        print(f"✅ Original file loaded: {self.original_filename}")
        
        # Check initial model_rot_left values
        if hasattr(self.viewer, 'model_rot_left'):
            print(f"📊 Initial model_rot_left: {self.viewer.model_rot_left}")
        else:
            print("❌ model_rot_left not found")
            
        QTimer.singleShot(1000, self.step2_apply_rotations)
        
    def step2_apply_rotations(self):
        """Step 2: Apply specific rotations using buttons"""
        print(f"\n🔄 STEP 2: Applying rotations {self.test_rotation}...")
        
        # Apply rotations using the button methods
        self.viewer.rotate_shape('x', self.test_rotation['x'])
        self.viewer.rotate_shape('y', self.test_rotation['y'])
        self.viewer.rotate_shape('z', self.test_rotation['z'])
        
        # Verify the rotations were applied to model_rot_left
        if hasattr(self.viewer, 'model_rot_left'):
            applied_rotation = self.viewer.model_rot_left
            print(f"📊 Applied model_rot_left: {applied_rotation}")
            
            # Check if rotations match what we applied
            x_match = abs(applied_rotation['x'] - self.test_rotation['x']) < 0.1
            y_match = abs(applied_rotation['y'] - self.test_rotation['y']) < 0.1
            z_match = abs(applied_rotation['z'] - self.test_rotation['z']) < 0.1
            
            if x_match and y_match and z_match:
                print("✅ Rotations applied correctly to model_rot_left")
            else:
                print("❌ Rotations not applied correctly to model_rot_left")
                print(f"   Expected: {self.test_rotation}")
                print(f"   Got: {applied_rotation}")
        else:
            print("❌ model_rot_left not found after rotation")
            
        QTimer.singleShot(1000, self.step3_save_file)
        
    def step3_save_file(self):
        """Step 3: Save the rotated file"""
        print(f"\n🔄 STEP 3: Saving rotated file as {self.saved_filename}...")
        
        try:
            # Use the save_step_file_option1 method (Option 1)
            # First we need to simulate the file dialog by setting the filename directly
            # We'll call the internal save method directly

            # Get the current values that would be used for saving
            if self.viewer.active_viewer == "top":
                loader = self.viewer.step_loader_left
                current_rot = self.viewer.model_rot_left if hasattr(self.viewer, 'model_rot_left') else {'x': 0, 'y': 0, 'z': 0}
                current_pos = self.viewer.current_pos_left if hasattr(self.viewer, 'current_pos_left') else {'x': 0, 'y': 0, 'z': 0}
                orig_rot = {'x': 0, 'y': 0, 'z': 0}  # Button rotations start from zero
                orig_pos = self.viewer.orig_pos_left if hasattr(self.viewer, 'orig_pos_left') else {'x': 0, 'y': 0, 'z': 0}
            else:
                loader = self.viewer.step_loader_right
                current_rot = self.viewer.model_rot_right if hasattr(self.viewer, 'model_rot_right') else {'x': 0, 'y': 0, 'z': 0}
                current_pos = self.viewer.current_pos_right if hasattr(self.viewer, 'current_pos_right') else {'x': 0, 'y': 0, 'z': 0}
                orig_rot = {'x': 0, 'y': 0, 'z': 0}  # Button rotations start from zero
                orig_pos = self.viewer.orig_pos_right if hasattr(self.viewer, 'orig_pos_right') else {'x': 0, 'y': 0, 'z': 0}

            print(f"📊 Saving with current_rot (model_rot_left): {current_rot}")
            print(f"📊 Saving with orig_rot: {orig_rot}")

            # Use the internal save method directly
            success = self.viewer._save_step_with_transformations(self.saved_filename, loader, current_pos, current_rot, orig_pos, orig_rot)
            
            if success and os.path.exists(self.saved_filename):
                file_size = os.path.getsize(self.saved_filename)
                print(f"✅ File saved successfully: {file_size} bytes")
                
                # Check if it's a valid STEP file
                with open(self.saved_filename, 'r') as f:
                    first_line = f.readline().strip()
                    
                if first_line.startswith('ISO-10303'):
                    print("✅ Valid STEP file format")
                else:
                    print(f"❌ Invalid STEP file format, first line: {first_line}")
                    
            else:
                print("❌ File save failed")
                self.app.quit()
                return
                
        except Exception as e:
            print(f"❌ Save failed with exception: {e}")
            self.app.quit()
            return
            
        QTimer.singleShot(1000, self.step4_clear_and_load_saved)
        
    def step4_clear_and_load_saved(self):
        """Step 4: Clear viewer and load the saved file"""
        print(f"\n🔄 STEP 4: Clearing viewer and loading saved file...")
        
        # Clear the current view
        self.viewer.clear_view()
        
        # Reset model_rot_left to verify it gets set correctly from the saved file
        if hasattr(self.viewer, 'model_rot_left'):
            print(f"📊 model_rot_left after clear: {self.viewer.model_rot_left}")
            
        # Load the saved file
        success = self.viewer.load_step_file_direct(self.saved_filename)
        
        if not success:
            print("❌ Failed to load saved file")
            self.app.quit()
            return
            
        print(f"✅ Saved file loaded: {self.saved_filename}")
        
        QTimer.singleShot(1000, self.step5_verify_rotation)
        
    def step5_verify_rotation(self):
        """Step 5: Verify the rotation was preserved"""
        print(f"\n🔄 STEP 5: Verifying rotation preservation...")
        
        # Check what rotation values are now present
        if hasattr(self.viewer, 'model_rot_left'):
            loaded_rotation = self.viewer.model_rot_left
            print(f"📊 Loaded model_rot_left: {loaded_rotation}")
        else:
            loaded_rotation = {'x': 0, 'y': 0, 'z': 0}
            print("❌ model_rot_left not found after loading")
            
        if hasattr(self.viewer, 'current_rot_left'):
            current_rotation = self.viewer.current_rot_left
            print(f"📊 Loaded current_rot_left: {current_rotation}")
        else:
            current_rotation = {'x': 0, 'y': 0, 'z': 0}
            print("❌ current_rot_left not found after loading")
            
        # The key test: does the loaded file show the rotation we applied?
        # This should be visible in either model_rot_left or current_rot_left
        rotation_preserved = False
        
        # Check if the rotation is preserved in any of the rotation tracking variables
        for rot_name, rot_values in [("model_rot_left", loaded_rotation), ("current_rot_left", current_rotation)]:
            x_match = abs(rot_values['x'] - self.test_rotation['x']) < 1.0  # Allow 1 degree tolerance
            y_match = abs(rot_values['y'] - self.test_rotation['y']) < 1.0
            z_match = abs(rot_values['z'] - self.test_rotation['z']) < 1.0
            
            if x_match and y_match and z_match:
                print(f"✅ Rotation preserved in {rot_name}!")
                print(f"   Expected: {self.test_rotation}")
                print(f"   Got: {rot_values}")
                rotation_preserved = True
                break
                
        if not rotation_preserved:
            print("❌ ROTATION NOT PRESERVED!")
            print(f"   Expected: {self.test_rotation}")
            print(f"   model_rot_left: {loaded_rotation}")
            print(f"   current_rot_left: {current_rotation}")
            
        print("\n" + "=" * 50)
        if rotation_preserved:
            print("🎉 SUCCESS: Save/Load rotation persistence is WORKING!")
        else:
            print("❌ FAILED: Save/Load rotation persistence is BROKEN!")
        print("=" * 50)
        
        # Clean up test file
        if os.path.exists(self.saved_filename):
            os.remove(self.saved_filename)
            print(f"🧹 Cleaned up test file: {self.saved_filename}")
            
        # Keep window open for a few seconds to see results
        QTimer.singleShot(3000, self.app.quit)

def main():
    test = SaveLoadRotationTest()
    test.app.exec_()

if __name__ == "__main__":
    main()
