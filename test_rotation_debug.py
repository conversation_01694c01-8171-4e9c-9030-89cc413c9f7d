#!/usr/bin/env python3
"""
Automated test to debug why mouse rotation doesn't update text display
"""

import sys
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer, QPoint
from PyQt5.QtGui import QMouseEvent
from PyQt5.Qt import Qt
from step_viewer_tdk_modular import StepViewerTDK

class RotationTester:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = StepViewerTDK()
        self.test_step = 0
        
        # Timer for automated testing
        self.timer = QTimer()
        self.timer.timeout.connect(self.run_test_step)
        
    def start_test(self):
        print("=== AUTOMATED ROTATION TEST STARTING ===")
        self.viewer.show()
        
        # Start the test sequence after a short delay
        QTimer.singleShot(2000, self.begin_test_sequence)
        
        return self.app.exec_()
    
    def begin_test_sequence(self):
        print("=== BEGINNING TEST SEQUENCE ===")
        self.timer.start(1000)  # Run test steps every 1 second
    
    def run_test_step(self):
        if self.test_step == 0:
            print("STEP 1: Checking if model is loaded...")
            if hasattr(self.viewer, 'step_loader_left') and self.viewer.step_loader_left.current_polydata:
                print("✅ Model is loaded in TOP viewer")
            else:
                print("❌ No model loaded - loading default model...")
                # Auto-load the debug model
                filename = "E:/Python/3d-models/SOIC16P127_1270X940X610L89X51.STEP"
                self.viewer.step_loader_left.load_step_file(filename)
                self.viewer.vtk_renderer_left.display_polydata(self.viewer.step_loader_left.current_polydata)
                self.viewer.extract_step_transformation_data("top")
                self.viewer.setup_text_overlay_for_viewer("top")
                print("✅ Model loaded automatically")

        elif self.test_step == 1:
            print("STEP 2: Getting initial text overlay content...")
            if hasattr(self.viewer, 'combined_text_actor_left'):
                initial_text = self.viewer.combined_text_actor_left.GetInput()
                print(f"INITIAL text overlay: '{initial_text}'")
            else:
                print("❌ Text overlay missing")

        elif self.test_step == 2:
            print("STEP 3: Simulating mouse drag rotation...")
            # Get the VTK widget for TOP viewer
            vtk_widget = None
            if hasattr(self.viewer, 'vtk_widget_left'):
                vtk_widget = self.viewer.vtk_widget_left
                print("✅ Found VTK widget for TOP viewer")
            else:
                print("❌ VTK widget not found")

            if vtk_widget:
                # Simulate mouse drag to rotate
                print("Simulating mouse press at (100, 100)...")
                press_event = QMouseEvent(QMouseEvent.MouseButtonPress, QPoint(100, 100), Qt.LeftButton, Qt.LeftButton, Qt.NoModifier)
                vtk_widget.mousePressEvent(press_event)

                print("Simulating mouse move to (150, 150) - this should rotate...")
                move_event = QMouseEvent(QMouseEvent.MouseMove, QPoint(150, 150), Qt.LeftButton, Qt.LeftButton, Qt.NoModifier)
                vtk_widget.mouseMoveEvent(move_event)

                print("Simulating mouse release...")
                release_event = QMouseEvent(QMouseEvent.MouseButtonRelease, QPoint(150, 150), Qt.LeftButton, Qt.LeftButton, Qt.NoModifier)
                vtk_widget.mouseReleaseEvent(release_event)

                print("✅ Mouse rotation simulation complete")

        elif self.test_step == 3:
            print("STEP 4: Checking if text overlay updated after mouse rotation...")
            if hasattr(self.viewer, 'combined_text_actor_left'):
                after_text = self.viewer.combined_text_actor_left.GetInput()
                print(f"AFTER ROTATION text overlay: '{after_text}'")

                # Check if text changed
                if hasattr(self, 'initial_text') and after_text != self.initial_text:
                    print("✅ TEXT CHANGED - Mouse rotation is updating text!")
                else:
                    print("❌ TEXT DID NOT CHANGE - Mouse rotation is NOT updating text")

        elif self.test_step == 4:
            print("STEP 5: Manually calling update_text_overlays() to see if it works...")
            self.viewer.update_text_overlays()

            if hasattr(self.viewer, 'combined_text_actor_left'):
                manual_text = self.viewer.combined_text_actor_left.GetInput()
                print(f"AFTER MANUAL UPDATE: '{manual_text}'")

            print("=== TEST COMPLETE ===")
            self.timer.stop()
            QTimer.singleShot(2000, self.app.quit)

        self.test_step += 1

if __name__ == "__main__":
    tester = RotationTester()
    sys.exit(tester.start_test())
