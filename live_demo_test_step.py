#!/usr/bin/env python3
"""
LIVE DEMO: test.step rotation persistence workflow
This demonstrates the complete workflow:
1. Load test.step into TOP window
2. Rotate it 45 degrees using rotation buttons
3. Save it using the green save button (Option 1)
4. Load the saved file into BOTTOM window
5. Compare TOP (rotated) vs BOTTOM (should show same rotation)
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication, QFileDialog
from PyQt5.QtCore import QTimer
from step_viewer_tdk_modular import StepViewerTDK

class LiveDemoTestStep:
    def __init__(self):
        self.app = QApplication([])
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        # Demo parameters
        self.rotation_angle = 45.0  # 45 degree rotation
        self.original_filename = "test.step"
        self.saved_filename = "test_rotated_45deg.step"
        
        print("🎬 LIVE DEMO: test.step Rotation Persistence Workflow")
        print("=" * 60)
        print("📋 Demo Steps:")
        print("   1. Load test.step into TOP window")
        print("   2. Rotate it 45° around X-axis using rotation buttons")
        print("   3. Save using green save button (Option 1)")
        print("   4. Load saved file into BOTTOM window")
        print("   5. Compare TOP vs BOTTOM (should match)")
        print("=" * 60)
        
        # Start the demo
        QTimer.singleShot(1000, self.step1_load_into_top)
        
    def step1_load_into_top(self):
        """Step 1: Load test.step into TOP window"""
        print(f"\n🔄 STEP 1: Loading {self.original_filename} into TOP window...")
        
        if not os.path.exists(self.original_filename):
            print(f"❌ File not found: {self.original_filename}")
            print("   Please ensure test.step exists in the current directory")
            self.app.quit()
            return
            
        # Set active viewer to TOP
        self.viewer.active_viewer = "top"
        self.viewer.update_viewer_highlights()
        print("✅ TOP window selected as active viewer")
        
        # Load the file
        success = self.viewer.load_step_file_direct(self.original_filename)
        
        if not success:
            print(f"❌ Failed to load {self.original_filename}")
            self.app.quit()
            return
            
        print(f"✅ {self.original_filename} loaded into TOP window")
        
        # Show initial rotation state
        if hasattr(self.viewer, 'model_rot_left'):
            initial_rotation = self.viewer.model_rot_left
            print(f"📊 TOP initial rotation: {initial_rotation}")
        
        print("🎯 You should now see the original test.step model in the TOP window")
        
        QTimer.singleShot(3000, self.step2_rotate_model)
        
    def step2_rotate_model(self):
        """Step 2: Rotate the model 45 degrees using rotation buttons"""
        print(f"\n🔄 STEP 2: Rotating model {self.rotation_angle}° around X-axis...")
        
        # Apply rotation using the button method (simulating clicking the X+15° button 3 times)
        print(f"   Applying X rotation: {self.rotation_angle}°")
        self.viewer.rotate_shape('x', self.rotation_angle)
        
        # Verify rotation was applied
        if hasattr(self.viewer, 'model_rot_left'):
            rotated_values = self.viewer.model_rot_left
            print(f"📊 TOP after rotation: {rotated_values}")
            
            if abs(rotated_values['x'] - self.rotation_angle) < 0.1:
                print("✅ Rotation applied successfully")
            else:
                print(f"❌ Rotation not applied correctly")
                print(f"   Expected X={self.rotation_angle}°, got X={rotated_values['x']}°")
        
        print("🎯 You should now see the model rotated 45° in the TOP window")
        print("   The model should look visibly different from the original orientation")
        
        QTimer.singleShot(3000, self.step3_save_with_green_button)
        
    def step3_save_with_green_button(self):
        """Step 3: Save using the green save button (Option 1)"""
        print(f"\n🔄 STEP 3: Saving rotated model using green save button...")
        
        # We'll simulate the green button save by calling the save method directly
        # with a predetermined filename to avoid the file dialog
        print(f"   Saving as: {self.saved_filename}")
        
        try:
            # Get the values that would be used by the green save button
            if self.viewer.active_viewer == "top":
                loader = self.viewer.step_loader_left
                current_rot = self.viewer.model_rot_left if hasattr(self.viewer, 'model_rot_left') else {'x': 0, 'y': 0, 'z': 0}
                current_pos = self.viewer.current_pos_left if hasattr(self.viewer, 'current_pos_left') else {'x': 0, 'y': 0, 'z': 0}
                orig_rot = {'x': 0, 'y': 0, 'z': 0}  # Button rotations start from zero
                orig_pos = self.viewer.orig_pos_left if hasattr(self.viewer, 'orig_pos_left') else {'x': 0, 'y': 0, 'z': 0}
            
            print(f"📊 Save parameters:")
            print(f"   current_rot (from model_rot_left): {current_rot}")
            print(f"   orig_rot: {orig_rot}")
            print(f"   Delta rotation: {{'x': {current_rot['x'] - orig_rot['x']}, 'y': {current_rot['y'] - orig_rot['y']}, 'z': {current_rot['z'] - orig_rot['z']}}}")
            
            # Use the same save method as the green button
            success = self.viewer._save_step_with_transformations(
                self.saved_filename, loader, current_pos, current_rot, orig_pos, orig_rot
            )
            
            if success and os.path.exists(self.saved_filename):
                file_size = os.path.getsize(self.saved_filename)
                print(f"✅ File saved successfully: {file_size:,} bytes")
                print(f"✅ Saved as: {self.saved_filename}")
            else:
                print("❌ Save failed")
                self.app.quit()
                return
                
        except Exception as e:
            print(f"❌ Save failed with exception: {e}")
            self.app.quit()
            return
            
        print("🎯 The rotated model has been saved with the 45° rotation embedded")
        
        QTimer.singleShot(3000, self.step4_load_into_bottom)
        
    def step4_load_into_bottom(self):
        """Step 4: Load the saved file into BOTTOM window"""
        print(f"\n🔄 STEP 4: Loading saved file into BOTTOM window...")
        
        # Switch to BOTTOM viewer
        self.viewer.active_viewer = "bottom"
        self.viewer.update_viewer_highlights()
        print("✅ BOTTOM window selected as active viewer")
        
        # Load the saved file
        success = self.viewer.load_step_file_direct(self.saved_filename)
        
        if not success:
            print(f"❌ Failed to load saved file: {self.saved_filename}")
            self.app.quit()
            return
            
        print(f"✅ {self.saved_filename} loaded into BOTTOM window")
        
        # Show the rotation state of the loaded file
        if hasattr(self.viewer, 'model_rot_right'):
            bottom_rotation = self.viewer.model_rot_right
            print(f"📊 BOTTOM loaded rotation: {bottom_rotation}")
        
        if hasattr(self.viewer, 'current_rot_right'):
            bottom_current = self.viewer.current_rot_right
            print(f"📊 BOTTOM current rotation: {bottom_current}")
            
        if hasattr(self.viewer, 'orig_rot_right'):
            bottom_orig = self.viewer.orig_rot_right
            print(f"📊 BOTTOM orig rotation: {bottom_orig}")
        
        print("🎯 You should now see:")
        print("   TOP window: Original rotated model (45° rotation)")
        print("   BOTTOM window: Loaded saved model (should also show 45° rotation)")
        
        QTimer.singleShot(3000, self.step5_compare_results)
        
    def step5_compare_results(self):
        """Step 5: Compare TOP vs BOTTOM to verify rotation persistence"""
        print(f"\n🔄 STEP 5: Comparing TOP vs BOTTOM windows...")
        
        # Get rotation values from both viewers
        top_rotation = None
        bottom_rotation = None
        
        if hasattr(self.viewer, 'model_rot_left'):
            top_rotation = self.viewer.model_rot_left
            print(f"📊 TOP model_rot_left: {top_rotation}")
        
        if hasattr(self.viewer, 'model_rot_right'):
            bottom_rotation = self.viewer.model_rot_right
            print(f"📊 BOTTOM model_rot_right: {bottom_rotation}")
        
        # Also check current_rot values
        if hasattr(self.viewer, 'current_rot_left'):
            top_current = self.viewer.current_rot_left
            print(f"📊 TOP current_rot_left: {top_current}")
            
        if hasattr(self.viewer, 'current_rot_right'):
            bottom_current = self.viewer.current_rot_right
            print(f"📊 BOTTOM current_rot_right: {bottom_current}")
            
        # Check orig_rot values (these show what was loaded from the file)
        if hasattr(self.viewer, 'orig_rot_left'):
            top_orig = self.viewer.orig_rot_left
            print(f"📊 TOP orig_rot_left: {top_orig}")
            
        if hasattr(self.viewer, 'orig_rot_right'):
            bottom_orig = self.viewer.orig_rot_right
            print(f"📊 BOTTOM orig_rot_right: {bottom_orig}")
        
        print(f"\n🔍 ANALYSIS:")
        print(f"   Expected rotation: X={self.rotation_angle}°, Y=0°, Z=0°")
        
        # The key test: does the BOTTOM window show the rotation that was saved?
        rotation_preserved = False
        
        # Check if BOTTOM orig_rot_right shows the saved rotation
        if hasattr(self.viewer, 'orig_rot_right'):
            bottom_orig = self.viewer.orig_rot_right
            x_match = abs(bottom_orig['x'] - self.rotation_angle) < 1.0
            y_match = abs(bottom_orig['y'] - 0.0) < 1.0
            z_match = abs(bottom_orig['z'] - 0.0) < 1.0
            
            if x_match and y_match and z_match:
                rotation_preserved = True
                print(f"✅ BOTTOM window shows correct rotation: {bottom_orig}")
            else:
                print(f"❌ BOTTOM window rotation mismatch: {bottom_orig}")
        
        print(f"\n" + "=" * 60)
        if rotation_preserved:
            print(f"🎉 SUCCESS: Rotation persistence WORKING PERFECTLY!")
            print(f"✅ TOP window: Shows rotated model")
            print(f"✅ BOTTOM window: Shows same rotation from saved file")
            print(f"✅ The save/load workflow preserves rotations correctly")
            
            print(f"\n🎯 VISUAL VERIFICATION:")
            print(f"   Both TOP and BOTTOM windows should show the same rotated model")
            print(f"   The model should appear rotated 45° around the X-axis in both windows")
            print(f"   This proves that the rotation was successfully saved and loaded")
            
        else:
            print(f"❌ FAILED: Rotation persistence not working correctly")
            print(f"   The BOTTOM window should show the same rotation as TOP")
            
        print("=" * 60)
        
        # Clean up
        print(f"\n🧹 Cleaning up...")
        if os.path.exists(self.saved_filename):
            os.remove(self.saved_filename)
            print(f"✅ Removed test file: {self.saved_filename}")
        
        # Keep window open for user to see the results
        print(f"\n⏰ Demo complete! Keeping window open for 15 seconds...")
        print(f"   Take a moment to visually compare the TOP and BOTTOM windows")
        print(f"   Both should show the same 45° rotated model")
        QTimer.singleShot(15000, self.app.quit)

def main():
    demo = LiveDemoTestStep()
    demo.app.exec_()

if __name__ == "__main__":
    main()
