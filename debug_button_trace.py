#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug <PERSON>ton Trace - Inject debug into main program to trace button clicks to display
"""

import sys
import os
import time

def inject_debug_into_main_program():
    """Inject debug code into the main program to trace button issues"""
    
    # Read the main program
    with open("step_viewer_tdk_modular.py", "r", encoding="utf-8") as f:
        content = f.read()
    
    # Create debug version with extensive tracing
    debug_content = content
    
    # 1. Add debug to rotate_shape method start
    old_rotate_start = '''    def rotate_shape(self, axis, degrees):
        """Rotate shape in active viewer"""
        # Write debug to file so we can see it'''
    
    new_rotate_start = '''    def rotate_shape(self, axis, degrees):
        """Rotate shape in active viewer"""
        # INJECTED DEBUG - BUTTON TRACE
        print(f"\\n🔥🔥🔥 BUTTON TRACE: rotate_shape called with axis={axis}, degrees={degrees}")
        print(f"🔥🔥🔥 BUTTON TRACE: active_viewer = {self.active_viewer}")
        with open("debug_button_trace.txt", "a", encoding="utf-8") as f:
            f.write(f"\\n=== BUTTON TRACE START ===\\n")
            f.write(f"rotate_shape called: axis={axis}, degrees={degrees}\\n")
            f.write(f"active_viewer = {self.active_viewer}\\n")
            f.flush()
        # Write debug to file so we can see it'''
    
    debug_content = debug_content.replace(old_rotate_start, new_rotate_start)
    
    # 2. Add debug to move_shape method start
    old_move_start = '''    def move_shape(self, axis, distance):
        """Move shape in active viewer"""
        try:'''
    
    new_move_start = '''    def move_shape(self, axis, distance):
        """Move shape in active viewer"""
        # INJECTED DEBUG - BUTTON TRACE
        print(f"\\n🔥🔥🔥 BUTTON TRACE: move_shape called with axis={axis}, distance={distance}")
        print(f"🔥🔥🔥 BUTTON TRACE: active_viewer = {self.active_viewer}")
        with open("debug_button_trace.txt", "a", encoding="utf-8") as f:
            f.write(f"\\n=== MOVE BUTTON TRACE START ===\\n")
            f.write(f"move_shape called: axis={axis}, distance={distance}\\n")
            f.write(f"active_viewer = {self.active_viewer}\\n")
            f.flush()
        try:'''
    
    debug_content = debug_content.replace(old_move_start, new_move_start)
    
    # 3. Add debug to update_vtk_text_overlays method
    old_update_start = '''    def update_vtk_text_overlays(self):
        """Update the VTK text overlays with current values"""

        # DEBUG: Check what variables exist'''
    
    new_update_start = '''    def update_vtk_text_overlays(self):
        """Update the VTK text overlays with current values"""
        
        # INJECTED DEBUG - DISPLAY TRACE
        print(f"\\n🔥🔥🔥 DISPLAY TRACE: update_vtk_text_overlays called")
        with open("debug_button_trace.txt", "a", encoding="utf-8") as f:
            f.write(f"\\n=== DISPLAY UPDATE TRACE ===\\n")
            f.write(f"update_vtk_text_overlays called\\n")
            f.flush()

        # DEBUG: Check what variables exist'''
    
    debug_content = debug_content.replace(old_update_start, new_update_start)
    
    # 4. Add debug to the actual text setting part
    old_text_set = '''                self.combined_text_actor_right.SetInput(combined_text)

                # Render the update
                if hasattr(self, 'vtk_widget_right'):
                    self.vtk_widget_right.GetRenderWindow().Render()'''
    
    new_text_set = '''                self.combined_text_actor_right.SetInput(combined_text)
                
                # INJECTED DEBUG - TEXT SET TRACE
                print(f"🔥🔥🔥 TEXT SET TRACE: BOTTOM text set to: {combined_text}")
                with open("debug_button_trace.txt", "a", encoding="utf-8") as f:
                    f.write(f"BOTTOM TEXT SET: {combined_text}\\n")
                    f.flush()

                # Render the update
                if hasattr(self, 'vtk_widget_right'):
                    self.vtk_widget_right.GetRenderWindow().Render()
                    print(f"🔥🔥🔥 RENDER TRACE: BOTTOM viewer rendered")
                    with open("debug_button_trace.txt", "a", encoding="utf-8") as f:
                        f.write(f"BOTTOM viewer rendered\\n")
                        f.flush()'''
    
    debug_content = debug_content.replace(old_text_set, new_text_set)
    
    # 5. Add debug to TOP viewer text setting too
    old_top_text = '''                self.combined_text_actor_left.SetInput(combined_text)

                # Render the update
                if hasattr(self, 'vtk_widget_left'):
                    self.vtk_widget_left.GetRenderWindow().Render()'''
    
    new_top_text = '''                self.combined_text_actor_left.SetInput(combined_text)
                
                # INJECTED DEBUG - TEXT SET TRACE
                print(f"🔥🔥🔥 TEXT SET TRACE: TOP text set to: {combined_text}")
                with open("debug_button_trace.txt", "a", encoding="utf-8") as f:
                    f.write(f"TOP TEXT SET: {combined_text}\\n")
                    f.flush()

                # Render the update
                if hasattr(self, 'vtk_widget_left'):
                    self.vtk_widget_left.GetRenderWindow().Render()
                    print(f"🔥🔥🔥 RENDER TRACE: TOP viewer rendered")
                    with open("debug_button_trace.txt", "a", encoding="utf-8") as f:
                        f.write(f"TOP viewer rendered\\n")
                        f.flush()'''
    
    debug_content = debug_content.replace(old_top_text, new_top_text)
    
    # Write debug version
    with open("step_viewer_debug_trace.py", "w", encoding="utf-8") as f:
        f.write(debug_content)
    
    print("✅ Debug version created: step_viewer_debug_trace.py")
    
    # Clear previous debug file
    with open("debug_button_trace.txt", "w", encoding="utf-8") as f:
        f.write("=== DEBUG BUTTON TRACE LOG ===\\n")
    
    print("✅ Debug log file cleared: debug_button_trace.txt")

if __name__ == "__main__":
    inject_debug_into_main_program()
    print("\\n🔥 Debug injection complete!")
    print("\\n📋 Next steps:")
    print("1. Run: python step_viewer_debug_trace.py")
    print("2. Load a STEP file")
    print("3. Click rotation/movement buttons")
    print("4. Check debug_button_trace.txt for trace")
    print("5. Watch console output for real-time debug")
