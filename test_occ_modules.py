#!/usr/bin/env python3
"""
Test what OCC modules are actually available
"""

print("Testing OCC module availability...")

try:
    import OCC
    print(f"✅ OCC base module available: {OCC}")
    print(f"OCC location: {OCC.__file__}")
    
    # Check what's in OCC
    import os
    occ_dir = os.path.dirname(OCC.__file__)
    print(f"OCC directory contents:")
    for item in os.listdir(occ_dir):
        if not item.startswith('__'):
            print(f"  - {item}")
    
except Exception as e:
    print(f"❌ OCC base module failed: {e}")

print("\nTesting specific OCC imports...")

# Test different import patterns
import_tests = [
    "from OCC import STEPControl_Reader",
    "from OCC.STEPControl import STEPControl_Reader", 
    "from OCC.Core import STEPControl_Reader",
    "from OCC.Core.STEPControl_Reader import STEPControl_Reader",
    "import OCC.STEPControl_Reader",
    "import OCC.Core.STEPControl_Reader"
]

for test_import in import_tests:
    try:
        exec(test_import)
        print(f"✅ {test_import}")
    except Exception as e:
        print(f"❌ {test_import} - {e}")

print("\nTesting VTK...")
try:
    import vtk
    print(f"✅ VTK available: {vtk.VTK_VERSION}")
except Exception as e:
    print(f"❌ VTK failed: {e}")
