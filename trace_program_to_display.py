#!/usr/bin/env python3

print("TRACING PROGRAM FROM STEP FILE TO DISPLAY DATA")

# Step 1: Load original STEP file colors
print("=== STEP 1: GET ORIGINAL STEP FILE COLORS ===")
from OCC.Core.STEPControl import STEPControl_Reader
from OCC.Core.IFSelect import IFSelect_RetDone
from OCC.Core.STEPCAFControl import STEPCAFControl_Reader
from OCC.Core.XCAFDoc import XCAFDoc_ColorTool, XCAFDoc_ColorType
from OCC.Core.XCAFApp import XCAFApp_Application
from OCC.Core.TDocStd import TDocStd_Document
from OCC.Core.TopExp import TopExp_Explorer
from OCC.Core.TopAbs import TopAbs_FACE
from OCC.Core.Quantity import Quantity_Color

# Read original STEP file
app = XCAFApp_Application.GetApplication()
doc = TDocStd_Document("MDTV-XCAF")

reader = STEPCAFControl_Reader()
reader.SetColorMode(True)
reader.ReadFile('SOIC16P127_1270X940X610L89X51.STEP')
reader.Transfer(doc)
color_tool = XCAFDoc_ColorTool.Set(doc.Main())

# Get original shape
basic_reader = STEPControl_Reader()
basic_reader.ReadFile('SOIC16P127_1270X940X610L89X51.STEP')
basic_reader.TransferRoots()
original_shape = basic_reader.OneShape()

# Extract original colors
explorer = TopExp_Explorer(original_shape, TopAbs_FACE)
original_colors = []

while explorer.More():
    face = explorer.Current()
    color = Quantity_Color()
    
    try:
        if color_tool.GetColor(face, XCAFDoc_ColorType.XCAFDoc_ColorSurf, color):
            r = int(color.Red() * 255)
            g = int(color.Green() * 255)
            b = int(color.Blue() * 255)
            original_colors.append((r, g, b))
        else:
            original_colors.append((192, 192, 192))  # Default
    except:
        original_colors.append((192, 192, 192))  # Default
    
    explorer.Next()

print(f"Original STEP file has {len(original_colors)} face colors")
original_color_counts = {}
for color in original_colors:
    original_color_counts[color] = original_color_counts.get(color, 0) + 1

print("Original colors:")
for color, count in original_color_counts.items():
    print(f"  RGB{color}: {count} faces")

# Step 2: Follow program flow - load with step_loader
print("\n=== STEP 2: LOAD WITH STEP_LOADER ===")
from step_loader import STEPLoader

loader = STEPLoader()
success, msg = loader.load_step_file('SOIC16P127_1270X940X610L89X51.STEP')

if success and loader.current_polydata:
    polydata = loader.current_polydata
    print(f"step_loader created polydata with {polydata.GetNumberOfCells()} cells")
    
    # Step 3: Get colors that will be displayed
    print("\n=== STEP 3: GET COLORS READY FOR DISPLAY ===")
    colors_array = polydata.GetCellData().GetScalars("Colors")
    
    if colors_array:
        display_colors = []
        for i in range(colors_array.GetNumberOfTuples()):
            r = int(colors_array.GetComponent(i, 0))
            g = int(colors_array.GetComponent(i, 1))
            b = int(colors_array.GetComponent(i, 2))
            display_colors.append((r, g, b))
        
        display_color_counts = {}
        for color in display_colors:
            display_color_counts[color] = display_color_counts.get(color, 0) + 1
        
        print("Colors ready for display:")
        for color, count in display_color_counts.items():
            print(f"  RGB{color}: {count} cells")
        
        # Step 4: VERIFY ORIGINAL vs DISPLAY
        print("\n=== STEP 4: VERIFY ORIGINAL vs DISPLAY ===")
        
        # Check if all original colors are present in display
        all_match = True
        for orig_color in original_color_counts.keys():
            if orig_color not in display_color_counts:
                print(f"MISSING: Original color RGB{orig_color} not in display")
                all_match = False
        
        # Check if display has extra colors not in original
        for display_color in display_color_counts.keys():
            if display_color not in original_color_counts:
                print(f"EXTRA: Display color RGB{display_color} not in original")
                all_match = False
        
        if all_match:
            print("SUCCESS: All original colors present in display data")
        else:
            print("FAILURE: Colors do not match")
        
        print(f"Original unique colors: {len(original_color_counts)}")
        print(f"Display unique colors: {len(display_color_counts)}")
        
    else:
        print("FAILURE: No colors in display data")
else:
    print("FAILURE: step_loader failed")

print("\nPROGRAM TRACE COMPLETE")
