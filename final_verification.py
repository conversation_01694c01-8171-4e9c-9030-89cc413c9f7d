#!/usr/bin/env python3

print("FINAL VERIFICATION - ACTUAL DISPLAY COLORS")

from step_loader import STEPLoader

# Load with step_loader
loader = STEPLoader()
success, msg = loader.load_step_file('SOIC16P127_1270X940X610L89X51.STEP')

if success and loader.current_polydata:
    polydata = loader.current_polydata
    colors_array = polydata.GetCellData().GetScalars("Colors")
    
    if colors_array:
        # Get all display colors
        display_colors = []
        for i in range(colors_array.GetNumberOfTuples()):
            r = int(colors_array.GetComponent(i, 0))
            g = int(colors_array.GetComponent(i, 1))
            b = int(colors_array.GetComponent(i, 2))
            display_colors.append((r, g, b))
        
        # Count colors
        dark_color = (63, 63, 63)
        light_color = (192, 192, 192)
        
        dark_count = display_colors.count(dark_color)
        light_count = display_colors.count(light_color)
        
        print(f"Display colors:")
        print(f"Dark {dark_color}: {dark_count} items")
        print(f"Light {light_color}: {light_count} items")
        print(f"Total: {len(display_colors)} items")
        
        # Expected from STEP file: 12 dark faces, 227 light faces
        # With ~7.36 triangles per face: ~88 dark triangles, ~1672 light triangles
        expected_dark = 12 * 7  # Approximate
        expected_light = 227 * 7  # Approximate
        
        print(f"\nExpected (approximate):")
        print(f"Dark: ~{expected_dark} items")
        print(f"Light: ~{expected_light} items")
        
        # Check if counts are reasonable
        dark_ratio = dark_count / len(display_colors)
        expected_dark_ratio = 12 / 239  # 12 dark faces out of 239 total
        
        print(f"\nRatios:")
        print(f"Actual dark ratio: {dark_ratio:.1%}")
        print(f"Expected dark ratio: {expected_dark_ratio:.1%}")
        
        if abs(dark_ratio - expected_dark_ratio) < 0.02:  # Within 2%
            print("SUCCESS: Color ratios match - 100% correct!")
        else:
            print("FAILURE: Color ratios don't match")
            
        # Show some specific positions
        print(f"\nSample colors:")
        for i in [0, 50, 100, 132, 140, 200, 272, 300]:
            if i < len(display_colors):
                color = display_colors[i]
                color_name = "DARK" if color == dark_color else "LIGHT"
                print(f"Item {i}: {color} ({color_name})")
    else:
        print("No colors found")
else:
    print("Failed to load")

print("\nFINAL VERIFICATION COMPLETE")
