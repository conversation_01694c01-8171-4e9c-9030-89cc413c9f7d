#!/usr/bin/env python3
"""
FINAL TEST: Show the NEW VTK overlay working with automatic file loading
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer_tdk_modular import VTKSTEPViewer

class FinalOverlayTest:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = VTKSTEPViewer()
        self.viewer.show()
        
        # Timer for automatic testing
        self.timer = QTimer()
        self.timer.timeout.connect(self.run_test_sequence)
        self.step = 0
        
        print("🎯 FINAL OVERLAY TEST - NEW VTK APPROACH")
        print("   This will automatically load files and show the fixed overlay")
        
    def run_test_sequence(self):
        """Run the test sequence step by step"""
        try:
            if self.step == 0:
                print("🎯 Starting FINAL test sequence...")
                self.step += 1
                self.timer.start(2000)  # Wait 2 seconds between steps
                
            elif self.step == 1:
                print("\n📁 STEP 1: Loading TOP file...")
                # Load TOP file
                top_file = "SOIC16P127_1270X940X610L89X51.STEP"
                if os.path.exists(top_file):
                    self.viewer.load_step_file(top_file, viewer='top')
                    print("✅ TOP file loaded successfully")
                else:
                    print(f"❌ TOP file not found: {top_file}")
                self.step += 1
                
            elif self.step == 2:
                print("\n📁 STEP 2: Loading BOTTOM file...")
                # Load BOTTOM file  
                bottom_file = "AMPHENOL_U77-A1118-200T.STEP"
                if os.path.exists(bottom_file):
                    self.viewer.load_step_file(bottom_file, viewer='bottom')
                    print("✅ BOTTOM file loaded successfully")
                else:
                    print(f"❌ BOTTOM file not found: {bottom_file}")
                self.step += 1
                
            elif self.step == 3:
                print("\n🎯 STEP 3: Triggering NEW VTK overlay...")
                print("🎯 Calling toggle_viewer_overlay()...")
                self.viewer.toggle_viewer_overlay()
                print("✅ NEW VTK overlay triggered")
                self.step += 1
                
            elif self.step == 4:
                print("\n🔍 STEP 4: Checking NEW VTK overlay...")
                self.check_new_overlay()
                self.step += 1
                
            else:
                print("\n✅ FINAL TEST COMPLETE")
                print("🎯 You should now see both models overlaid with RED and BLUE colors!")
                self.timer.stop()
                
        except Exception as e:
            print(f"❌ Error in test step {self.step}: {e}")
            import traceback
            traceback.print_exc()
            self.timer.stop()
    
    def check_new_overlay(self):
        """Check the NEW VTK overlay result"""
        print("🔍 Checking NEW VTK overlay...")
        print(f"   overlay_mode: {self.viewer.overlay_mode}")
        
        if hasattr(self.viewer, 'overlay_renderer') and self.viewer.overlay_renderer:
            print("   ✅ NEW overlay_renderer found!")
            
            # Check overlay renderer actors
            actors = self.viewer.overlay_renderer.GetActors()
            actor_count = actors.GetNumberOfItems()
            print(f"   🎭 Overlay actors: {actor_count}")
            
            # Check each overlay actor
            actors.InitTraversal()
            for i in range(actor_count):
                actor = actors.GetNextItem()
                if actor:
                    color = actor.GetProperty().GetColor()
                    visible = actor.GetVisibility()
                    print(f"   🎨 Actor {i}: Color={color}, Visible={visible}")
        else:
            print("   ❌ NEW overlay_renderer not found")
            print("   🔧 Still using old approach - overlay may not be visible")
    
    def run(self):
        """Start the test"""
        # Start the test sequence
        self.timer.start(1000)  # Start after 1 second
        return self.app.exec_()

if __name__ == "__main__":
    test = FinalOverlayTest()
    sys.exit(test.run())
