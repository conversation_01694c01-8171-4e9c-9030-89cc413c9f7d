#!/usr/bin/env python3
"""
Test STEP color parsing to see what's actually happening
"""

import os
import re

step_file = 'SOIC16P127_1270X940X610L89X51.STEP'

print("=== TESTING STEP COLOR PARSING ===")

if not os.path.exists(step_file):
    print(f"STEP file not found: {step_file}")
    exit(1)

try:
    with open(step_file, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
    
    print(f"STEP file loaded: {len(content)} characters")
    
    # Test the exact regex pattern from step_loader.py
    colour_pattern = r'COLOUR_RGB\s*\([^,]*,\s*([\d\.]+)\s*,\s*([\d\.]+)\s*,\s*([\d\.]+)\s*\)'
    matches = re.findall(colour_pattern, content)
    
    print(f"Regex matches found: {len(matches)}")
    
    colors_found = []
    for i, match in enumerate(matches):
        try:
            r = int(float(match[0]) * 255)
            g = int(float(match[1]) * 255)
            b = int(float(match[2]) * 255)
            colors_found.append((r, g, b))
            print(f"Color {i}: RGB({r}, {g}, {b}) from values ({match[0]}, {match[1]}, {match[2]})")
        except (ValueError, IndexError) as e:
            print(f"Error parsing color {i}: {e}")
    
    print(f"\nTotal colors parsed: {len(colors_found)}")
    print(f"Colors: {colors_found}")
    
    # Also search for any line containing COLOUR
    print("\n=== SEARCHING FOR ANY COLOR REFERENCES ===")
    lines = content.split('\n')
    color_lines = []
    for i, line in enumerate(lines):
        if 'COLOUR' in line.upper():
            color_lines.append((i+1, line.strip()))
    
    print(f"Found {len(color_lines)} lines with COLOUR:")
    for line_num, line in color_lines[:10]:  # Show first 10
        print(f"Line {line_num}: {line}")
    
    if not colors_found:
        print("\n*** NO COLORS FOUND - THIS IS THE PROBLEM ***")
        print("The STEP file either has no color information or uses a different format")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()

print("=== TEST COMPLETE ===")
