#!/usr/bin/env python3
"""
Automated test to reproduce the overlay contamination issue
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from step_viewer_tdk_modular import StepViewerTDK

def main():
    """Automated overlay contamination test"""
    print("🤖 AUTOMATED OVERLAY CONTAMINATION TEST")
    print("=" * 60)
    
    app = QApplication(sys.argv)
    
    # Create the viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    print("✅ Viewer created and shown")
    
    # Load 16-pin into TOP viewer
    print("\n🔴 Loading 16-pin SOIC into TOP viewer...")
    viewer.active_viewer = "top"
    viewer.update_viewer_highlights()
    
    if os.path.exists("SOIC16P127_1270X940X610L89X51.STEP"):
        success1 = viewer.load_step_file_direct("SOIC16P127_1270X940X610L89X51.STEP")
        print(f"   Result: {success1}")
    else:
        print("   ❌ 16-pin STEP file not found")
        return 1
    
    # Load 8-pin into BOTTOM viewer
    print("\n🔵 Loading 8-pin SOIC into BOTTOM viewer...")
    viewer.active_viewer = "bottom"
    viewer.update_viewer_highlights()
    
    if os.path.exists("test.step"):
        success2 = viewer.load_step_file_direct("test.step")
        print(f"   Result: {success2}")
    else:
        print("   ❌ 8-pin STEP file not found")
        return 1
    
    # Check actor lists before overlay
    print("\n🔍 BEFORE OVERLAY - Actor List Analysis:")
    print(f"   TOP renderer step_actors: {len(viewer.vtk_renderer_left.step_actors) if hasattr(viewer.vtk_renderer_left, 'step_actors') else 'NONE'}")
    print(f"   BOTTOM renderer step_actors: {len(viewer.vtk_renderer_right.step_actors) if hasattr(viewer.vtk_renderer_right, 'step_actors') else 'NONE'}")
    
    if hasattr(viewer.vtk_renderer_left, 'step_actors'):
        print(f"   TOP step_actors list ID: {id(viewer.vtk_renderer_left.step_actors)}")
        for i, actor in enumerate(viewer.vtk_renderer_left.step_actors):
            bounds = actor.GetBounds()
            color = actor.GetProperty().GetColor()
            print(f"      TOP actor {i}: bounds={bounds}, color={color}")
    
    if hasattr(viewer.vtk_renderer_right, 'step_actors'):
        print(f"   BOTTOM step_actors list ID: {id(viewer.vtk_renderer_right.step_actors)}")
        for i, actor in enumerate(viewer.vtk_renderer_right.step_actors):
            bounds = actor.GetBounds()
            color = actor.GetProperty().GetColor()
            print(f"      BOTTOM actor {i}: bounds={bounds}, color={color}")
    
    # Trigger overlay creation
    print("\n🎯 TRIGGERING OVERLAY CREATION...")
    viewer.toggle_viewer_overlay()
    
    print("\n🔍 AFTER OVERLAY - Final Analysis:")
    print("   Check the diagnostic output above for contamination evidence")
    
    # Keep program running so you can see the overlay
    print("\n⏱️  Program running - you can now see the overlay!")
    print("   Close the window when you're done examining it.")

    # Process events to ensure everything is rendered
    app.processEvents()

    # Run the application event loop (keeps window open)
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
