#!/usr/bin/env python3
"""
COMPLETE FLOW DEBUG - Trace EVERY step from STEP file loading to final display
This will debug the ENTIRE pipeline:
1. STEP file loading → color extraction
2. Color application → VTK display
3. Mouse rotation → orientation detection
4. Button rotation → value calculation
5. Value updates → text display
6. Final rendering → what user sees
"""

import sys
import os
import time
import math
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QLabel, QTextEdit
from PyQt5.QtCore import QTimer
import vtk
from vtk.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor

class CompleteFlowDebug(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("COMPLETE FLOW DEBUG - Every Step Traced")
        self.setGeometry(50, 50, 1600, 900)
        
        # State tracking
        self.current_rot = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_axis = {'x': 0.0, 'y': 0.0, 'z': 1.0}
        self.current_angle = 0.0
        self.last_orientation = [0.0, 0.0, 0.0]
        
        # VTK components
        self.vtk_widget = None
        self.renderer = None
        self.step_actor = None
        self.text_actor = None
        
        # Debug tracking
        self.step_count = 0
        
        self.init_ui()
        self.setup_vtk()
        
        # Mouse tracking timer
        self.mouse_timer = QTimer()
        self.mouse_timer.timeout.connect(self.trace_mouse_rotation)
        self.mouse_timer.start(200)
        
    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QHBoxLayout(central_widget)
        
        # Left panel - controls
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_panel.setFixedWidth(500)
        
        # Main test button
        self.load_btn = QPushButton("🔍 TRACE COMPLETE FLOW: Load STEP File")
        self.load_btn.clicked.connect(self.trace_complete_step_loading)
        self.load_btn.setStyleSheet("font-weight: bold; padding: 10px;")
        left_layout.addWidget(self.load_btn)
        
        # Individual test buttons
        left_layout.addWidget(QLabel("INDIVIDUAL TESTS:"))
        
        self.color_btn = QPushButton("1. Test Color Loading Only")
        self.color_btn.clicked.connect(self.trace_color_loading_only)
        left_layout.addWidget(self.color_btn)
        
        self.rotation_btn = QPushButton("2. Test Button Rotation: X+15°")
        self.rotation_btn.clicked.connect(lambda: self.trace_button_rotation('x', 15))
        left_layout.addWidget(self.rotation_btn)
        
        self.mouse_btn = QPushButton("3. Test Mouse Rotation Detection")
        self.mouse_btn.clicked.connect(self.trace_mouse_detection_test)
        left_layout.addWidget(self.mouse_btn)
        
        self.display_btn = QPushButton("4. Test Text Display Update")
        self.display_btn.clicked.connect(self.trace_text_display_update)
        left_layout.addWidget(self.display_btn)
        
        # Current state display
        left_layout.addWidget(QLabel("CURRENT STATE:"))
        self.state_label = QLabel("No model loaded")
        self.state_label.setStyleSheet("background-color: #f0f0f0; padding: 5px; border: 1px solid #ccc;")
        left_layout.addWidget(self.state_label)
        
        # Flow trace log
        left_layout.addWidget(QLabel("COMPLETE FLOW TRACE:"))
        self.trace_log = QTextEdit()
        self.trace_log.setStyleSheet("font-family: monospace; font-size: 9pt;")
        left_layout.addWidget(self.trace_log)
        
        # Clear button
        self.clear_btn = QPushButton("Clear Trace Log")
        self.clear_btn.clicked.connect(self.trace_log.clear)
        left_layout.addWidget(self.clear_btn)
        
        layout.addWidget(left_panel)
        
        # Right panel - VTK viewer
        self.vtk_widget = QVTKRenderWindowInteractor()
        layout.addWidget(self.vtk_widget)
        
    def setup_vtk(self):
        """Setup VTK with complete tracing"""
        self.trace("🔧 STEP 1: VTK SETUP")
        
        self.renderer = vtk.vtkRenderer()
        self.renderer.SetBackground(0.1, 0.1, 0.2)
        self.trace("   ✅ Renderer created with dark blue background")
        
        self.vtk_widget.GetRenderWindow().AddRenderer(self.renderer)
        self.vtk_widget.Initialize()
        self.vtk_widget.Start()
        self.trace("   ✅ VTK widget initialized and started")
        
        # Create text overlay
        self.text_actor = vtk.vtkTextActor()
        self.text_actor.SetInput("Waiting for STEP file...")
        self.text_actor.SetPosition(10, 10)
        
        text_prop = self.text_actor.GetTextProperty()
        text_prop.SetFontSize(14)
        text_prop.SetColor(1.0, 1.0, 1.0)
        
        self.renderer.AddActor2D(self.text_actor)
        self.trace("   ✅ Text overlay created and added")
        
        self.vtk_widget.GetRenderWindow().Render()
        self.trace("   ✅ Initial render complete")
        
    def trace_complete_step_loading(self):
        """Trace the complete STEP file loading process"""
        from PyQt5.QtWidgets import QFileDialog
        
        self.trace("🚀 STARTING COMPLETE STEP FILE LOADING TRACE")
        self.trace("=" * 60)
        
        filename, _ = QFileDialog.getOpenFileName(self, "Load STEP File", "", "STEP Files (*.step *.stp)")
        
        if not filename:
            self.trace("❌ No file selected - trace cancelled")
            return
            
        self.trace(f"📁 STEP 2: FILE SELECTED: {os.path.basename(filename)}")
        
        try:
            # Step 3: Import step_loader
            self.trace("📦 STEP 3: IMPORTING STEP_LOADER")
            from step_loader import STEPLoader
            self.trace("   ✅ step_loader imported successfully")
            
            # Step 4: Create loader instance
            self.trace("🏗️ STEP 4: CREATING LOADER INSTANCE")
            loader = STEPLoader()
            self.trace("   ✅ STEPLoader instance created")
            
            # Step 5: Load the file
            self.trace("📖 STEP 5: LOADING STEP FILE")
            success, message = loader.load_step_file(filename)
            self.trace(f"   Result: success={success}, message='{message}'")
            
            if not success:
                self.trace(f"❌ STEP 5 FAILED: {message}")
                return
                
            # Step 6: Check polydata
            self.trace("🔍 STEP 6: CHECKING POLYDATA")
            if not loader.current_polydata:
                self.trace("❌ STEP 6 FAILED: No polydata created")
                return
                
            polydata = loader.current_polydata
            self.trace(f"   ✅ Polydata created: {polydata.GetNumberOfCells()} cells, {polydata.GetNumberOfPoints()} points")
            
            # Step 7: Check colors in polydata
            self.trace("🎨 STEP 7: CHECKING COLORS IN POLYDATA")
            cell_colors = polydata.GetCellData().GetScalars()
            point_colors = polydata.GetPointData().GetScalars()
            
            if cell_colors:
                self.trace(f"   ✅ Found cell colors: {cell_colors.GetNumberOfTuples()} tuples")
                # Sample first few colors
                for i in range(min(3, cell_colors.GetNumberOfTuples())):
                    color = cell_colors.GetTuple3(i)
                    self.trace(f"      Cell {i}: RGB({color[0]:.0f}, {color[1]:.0f}, {color[2]:.0f})")
            else:
                self.trace("   ❌ No cell colors found")
                
            if point_colors:
                self.trace(f"   ✅ Found point colors: {point_colors.GetNumberOfTuples()} tuples")
            else:
                self.trace("   ⚠️ No point colors found")
            
            # Step 8: Create VTK actor and mapper
            self.trace("🎭 STEP 8: CREATING VTK ACTOR AND MAPPER")
            
            if self.step_actor:
                self.renderer.RemoveActor(self.step_actor)
                self.trace("   ✅ Removed old actor")
                
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputData(polydata)
            self.trace("   ✅ Mapper created and polydata set")
            
            self.step_actor = vtk.vtkActor()
            self.step_actor.SetMapper(mapper)
            self.trace("   ✅ Actor created and mapper set")
            
            # Step 9: Apply colors to mapper
            self.trace("🎨 STEP 9: APPLYING COLORS TO MAPPER")
            
            if cell_colors:
                self.trace("   Applying cell colors...")
                mapper.SetScalarVisibility(True)
                mapper.SetScalarModeToUseCellData()
                mapper.SetColorModeToDirectScalars()
                self.trace("   ✅ Mapper configured for cell colors")
                
                # Set lighting
                self.step_actor.GetProperty().SetAmbient(0.3)
                self.step_actor.GetProperty().SetDiffuse(0.7)
                self.trace("   ✅ Lighting properties set")
                
            else:
                self.trace("   No colors - using default gray")
                self.step_actor.GetProperty().SetColor(0.7, 0.7, 0.7)
                mapper.SetScalarVisibility(False)
                self.trace("   ✅ Default gray color applied")
            
            # Step 10: Add actor to renderer
            self.trace("🎬 STEP 10: ADDING ACTOR TO RENDERER")
            self.renderer.AddActor(self.step_actor)
            self.trace("   ✅ Actor added to renderer")
            
            # Step 11: Reset camera and render
            self.trace("📷 STEP 11: RESET CAMERA AND RENDER")
            self.renderer.ResetCamera()
            self.trace("   ✅ Camera reset")
            
            self.vtk_widget.GetRenderWindow().Render()
            self.trace("   ✅ Window rendered")
            
            # Step 12: Initialize rotation tracking
            self.trace("🔄 STEP 12: INITIALIZE ROTATION TRACKING")
            self.last_orientation = list(self.step_actor.GetOrientation())
            self.trace(f"   ✅ Initial orientation: {self.last_orientation}")
            
            # Step 13: Update text display
            self.trace("📝 STEP 13: UPDATE TEXT DISPLAY")
            self.update_text_display()
            
            # Step 14: Update state
            self.trace("✅ STEP 14: COMPLETE - MODEL LOADED SUCCESSFULLY")
            self.state_label.setText(f"Model loaded: {os.path.basename(filename)}")
            
            self.trace("=" * 60)
            self.trace("🎉 COMPLETE STEP FILE LOADING TRACE FINISHED")
            
        except Exception as e:
            self.trace(f"❌ CRITICAL ERROR in step loading: {e}")
            import traceback
            self.trace(f"   Traceback: {traceback.format_exc()}")
            
    def trace_color_loading_only(self):
        """Test only the color loading part"""
        self.trace("🎨 TESTING COLOR LOADING ONLY")
        
        if not self.step_actor:
            self.trace("❌ No model loaded - load a STEP file first")
            return
            
        mapper = self.step_actor.GetMapper()
        polydata = mapper.GetInput()
        
        self.trace("🔍 Checking current color state:")
        self.trace(f"   Scalar visibility: {mapper.GetScalarVisibility()}")
        self.trace(f"   Scalar mode: {mapper.GetScalarMode()}")
        
        cell_colors = polydata.GetCellData().GetScalars()
        if cell_colors:
            self.trace(f"   ✅ Cell colors available: {cell_colors.GetNumberOfTuples()} colors")
            
            # Sample colors
            for i in range(min(5, cell_colors.GetNumberOfTuples())):
                color = cell_colors.GetTuple3(i)
                self.trace(f"      Color {i}: RGB({color[0]:.0f}, {color[1]:.0f}, {color[2]:.0f})")
        else:
            self.trace("   ❌ No cell colors available")
            
    def trace_button_rotation(self, axis, degrees):
        """Trace button rotation with complete flow"""
        self.trace(f"🔘 TRACING BUTTON ROTATION: {axis.upper()}{'+' if degrees > 0 else ''}{degrees}°")
        
        if not self.step_actor:
            self.trace("❌ No model loaded - cannot test rotation")
            return
            
        # Step 1: Update rotation values
        old_value = self.current_rot[axis]
        self.current_rot[axis] += degrees
        self.trace(f"   STEP 1: Updated {axis.upper()} rotation: {old_value:.1f}° → {self.current_rot[axis]:.1f}°")
        
        # Step 2: Apply VTK rotation
        self.trace(f"   STEP 2: Applying VTK rotation...")
        self.step_actor.RotateWXYZ(degrees,
            1 if axis == 'x' else 0,
            1 if axis == 'y' else 0,
            1 if axis == 'z' else 0)
        
        new_orientation = list(self.step_actor.GetOrientation())
        self.trace(f"      New VTK orientation: {[f'{x:.1f}' for x in new_orientation]}")
        
        # Step 3: Calculate axis and angle
        self.trace(f"   STEP 3: Calculating axis and angle...")
        self.calculate_axis_and_angle()
        
        # Step 4: Update displays
        self.trace(f"   STEP 4: Updating displays...")
        self.update_text_display()
        
        # Step 5: Render
        self.trace(f"   STEP 5: Rendering...")
        self.vtk_widget.GetRenderWindow().Render()
        
        self.trace(f"   ✅ Button rotation complete")
        
    def trace_mouse_rotation(self):
        """Trace mouse rotation detection"""
        if not self.step_actor:
            return
            
        try:
            current_orientation = list(self.step_actor.GetOrientation())
            
            # Check for change
            changed = False
            for i in range(3):
                if abs(current_orientation[i] - self.last_orientation[i]) > 1.0:
                    changed = True
                    break
                    
            if changed:
                self.trace(f"🖱️ MOUSE ROTATION DETECTED:")
                self.trace(f"   Old: {[f'{x:.1f}' for x in self.last_orientation]}")
                self.trace(f"   New: {[f'{x:.1f}' for x in current_orientation]}")
                
                # Update rotation values
                self.current_rot['x'] = current_orientation[0]
                self.current_rot['y'] = current_orientation[1]
                self.current_rot['z'] = current_orientation[2]
                
                # Calculate axis
                self.calculate_axis_and_angle()
                
                # Update display
                self.update_text_display()
                
                self.last_orientation = current_orientation
                
        except Exception as e:
            pass
            
    def calculate_axis_and_angle(self):
        """Calculate axis and angle from rotation"""
        try:
            rot_mag = math.sqrt(self.current_rot['x']**2 + self.current_rot['y']**2 + self.current_rot['z']**2)
            self.trace(f"      Rotation magnitude: {rot_mag:.3f}")
            
            if rot_mag > 0.001:
                self.current_axis = {
                    'x': self.current_rot['x'] / rot_mag,
                    'y': self.current_rot['y'] / rot_mag,
                    'z': self.current_rot['z'] / rot_mag
                }
                self.current_angle = rot_mag
                self.trace(f"      Calculated axis: {self.current_axis}")
                self.trace(f"      Calculated angle: {self.current_angle:.1f}°")
            else:
                self.current_axis = {'x': 0.0, 'y': 0.0, 'z': 1.0}
                self.current_angle = 0.0
                self.trace(f"      Zero rotation - using default axis")
                
        except Exception as e:
            self.trace(f"❌ Axis calculation error: {e}")
            
    def update_text_display(self):
        """Update text display with tracing"""
        try:
            text_content = (
                f"ROT: X={self.current_rot['x']:.1f}° Y={self.current_rot['y']:.1f}° Z={self.current_rot['z']:.1f}°\n"
                f"AXIS: X={self.current_axis['x']:.3f} Y={self.current_axis['y']:.3f} Z={self.current_axis['z']:.3f}\n"
                f"ANGLE: {self.current_angle:.1f}°"
            )
            
            self.text_actor.SetInput(text_content)
            self.trace(f"      Text updated: {text_content.replace(chr(10), ' | ')}")
            
        except Exception as e:
            self.trace(f"❌ Text update error: {e}")
            
    def trace_mouse_detection_test(self):
        """Test mouse detection"""
        self.trace("🖱️ MOUSE DETECTION TEST")
        self.trace("   Rotate the model with your mouse...")
        self.trace("   Watch for 'MOUSE ROTATION DETECTED' messages")
        
    def trace_text_display_update(self):
        """Test text display update"""
        self.trace("📝 TESTING TEXT DISPLAY UPDATE")
        
        # Set test values
        self.current_rot = {'x': 45.0, 'y': 30.0, 'z': 15.0}
        self.current_axis = {'x': 0.707, 'y': 0.500, 'z': 0.500}
        self.current_angle = 60.0
        
        self.trace("   Set test values")
        self.update_text_display()
        self.vtk_widget.GetRenderWindow().Render()
        self.trace("   ✅ Text display updated")
        
    def trace(self, message):
        """Add message to trace log"""
        self.step_count += 1
        timestamp = time.strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}"
        print(full_message)
        self.trace_log.append(full_message)
        
        # Auto-scroll to bottom
        scrollbar = self.trace_log.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

def main():
    app = QApplication(sys.argv)
    window = CompleteFlowDebug()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
