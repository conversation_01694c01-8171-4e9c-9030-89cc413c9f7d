#!/usr/bin/env python3
"""Detailed test of OpenCASCADE imports"""

print("Testing OCC import step by step...")

try:
    print("1. Importing OCC...")
    import OCC
    print(f"   ✅ OCC imported: {OCC}")
    
    print("2. Importing OCC.Core...")
    import OCC.Core
    print(f"   ✅ OCC.Core imported: {OCC.Core}")
    
    print("3. Listing OCC.Core contents...")
    import os
    core_path = os.path.join(OCC.__path__[0], 'Core')
    files = [f for f in os.listdir(core_path) if f.startswith('STEP')]
    print(f"   STEP-related files: {files[:10]}...")  # Show first 10
    
    print("4. Importing STEPControl_Reader...")
    from OCC.Core.STEPControl_Reader import STEPControl_Reader
    print("   ✅ STEPControl_Reader imported successfully!")
    
    print("5. Testing STEPControl_Reader...")
    reader = STEPControl_Reader()
    print("   ✅ STEPControl_Reader instance created!")
    
except Exception as e:
    print(f"   ❌ Error at step: {e}")
    import traceback
    traceback.print_exc()

print("\nTesting FreeCAD import step by step...")

try:
    print("1. Importing FreeCAD...")
    import FreeCAD
    print(f"   ✅ FreeCAD imported: {FreeCAD}")
    
    print("2. Getting FreeCAD version...")
    version = FreeCAD.Version()
    print(f"   ✅ FreeCAD version: {version}")
    
    print("3. Importing Part...")
    import Part
    print(f"   ✅ Part imported: {Part}")
    
except Exception as e:
    print(f"   ❌ FreeCAD error: {e}")
    import traceback
    traceback.print_exc()
