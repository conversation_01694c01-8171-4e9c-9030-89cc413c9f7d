#!/usr/bin/env python3
"""
Test Bounding Box Fix - Quick test to see if bounding box recreation fixes the reset issue
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def test_bounding_box_fix():
    """Quick test of the bounding box fix"""
    
    print("🔧 TEST BOUNDING BOX FIX")
    print("=" * 40)
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    # Process events to ensure GUI is ready
    app.processEvents()
    time.sleep(1)
    
    # Find a STEP file to test with
    test_files = [
        "AMPHENOL_U77-A1118-200T.STEP",
        "test.step",
        "sample.stp"
    ]
    
    test_file = None
    for filename in test_files:
        if os.path.exists(filename):
            test_file = filename
            break
    
    if not test_file:
        print("❌ No STEP file found for testing")
        return False
    
    print(f"📁 Using test file: {test_file}")
    
    # Step 1: Load STEP file
    print(f"\n📋 LOADING {test_file}...")
    success = viewer.load_step_file_direct(test_file)
    app.processEvents()
    time.sleep(2)
    
    if not success:
        print("❌ Failed to load STEP file")
        return False
    
    print("✅ Model loaded successfully")
    print("👁️ Initial model position (3 seconds)")
    time.sleep(3)
    
    # Step 2: Apply transformation
    print(f"\n📋 APPLYING TRANSFORMATION...")
    viewer.active_viewer = "top"
    
    print("🔧 Moving +50mm X and rotating +45° Z")
    viewer.move_shape("x", 50)
    viewer.rotate_shape("z", 45)
    app.processEvents()
    time.sleep(1)
    
    print("👁️ Model after transformation (3 seconds)")
    time.sleep(3)
    
    # Step 3: Test reset with bounding box fix
    print(f"\n📋 TESTING RESET WITH BOUNDING BOX FIX...")
    
    print("🔧 Calling reset_to_original()...")
    viewer.reset_to_original()
    app.processEvents()
    time.sleep(2)
    
    print("👁️ Model after reset - Did it move back visually? (5 seconds)")
    time.sleep(5)
    
    # Step 4: Check actor states
    print(f"\n📋 CHECKING ACTOR STATES...")
    renderer = viewer.vtk_renderer_left
    
    if hasattr(renderer, 'step_actors') and renderer.step_actors:
        for i, actor in enumerate(renderer.step_actors):
            pos = actor.GetPosition()
            orient = actor.GetOrientation()
            print(f"  Multi-actor {i}: Pos={pos}, Orient={orient}")
    
    if hasattr(renderer, 'step_actor') and renderer.step_actor:
        pos = renderer.step_actor.GetPosition()
        orient = renderer.step_actor.GetOrientation()
        print(f"  Single-actor: Pos={pos}, Orient={orient}")
    
    # Check total actors in renderer
    all_actors = []
    if hasattr(renderer, 'renderer') and renderer.renderer:
        actor_collection = renderer.renderer.GetActors()
        actor_collection.InitTraversal()
        
        actor = actor_collection.GetNextActor()
        while actor:
            all_actors.append(actor)
            actor = actor_collection.GetNextActor()
    
    print(f"  Total actors in renderer: {len(all_actors)}")
    
    print(f"\n🎯 RESULT:")
    print(f"If the model moved back visually, the bounding box fix worked!")
    print(f"If not, there may be another issue.")
    
    # Keep window open
    print(f"\n👁️ Window will stay open for final inspection...")
    QTimer.singleShot(10000, app.quit)
    app.exec_()
    
    return True

if __name__ == "__main__":
    test_bounding_box_fix()
