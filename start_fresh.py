#!/usr/bin/env python3
"""
Fresh startup script that forces complete module reload
This ensures all cached modules are cleared before starting
"""

import sys
import os
import importlib

# Clear ALL cached modules related to our application
modules_to_clear = [
    'step_viewer_tdk_modular',
    'gui_components', 
    'step_loader',
    'vtk_renderer',
    'step_transformer'
]

print("🧹 CLEARING PYTHON MODULE CACHE...")
for module_name in modules_to_clear:
    if module_name in sys.modules:
        print(f"   Clearing cached module: {module_name}")
        del sys.modules[module_name]

# Clear any .pyc files
print("🧹 CLEARING .pyc FILES...")
for root, dirs, files in os.walk('.'):
    for file in files:
        if file.endswith('.pyc'):
            pyc_path = os.path.join(root, file)
            try:
                os.remove(pyc_path)
                print(f"   Removed: {pyc_path}")
            except:
                pass

# Remove __pycache__ directories
for root, dirs, files in os.walk('.'):
    if '__pycache__' in dirs:
        pycache_path = os.path.join(root, '__pycache__')
        try:
            import shutil
            shutil.rmtree(pycache_path)
            print(f"   Removed directory: {pycache_path}")
        except:
            pass

print("✅ CACHE CLEARED - Starting fresh program...")

# Now import and start the program
from step_viewer_tdk_modular import StepViewerTDK
from PyQt5.QtWidgets import QApplication

if __name__ == "__main__":
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.show()
    print("🚀 FRESH PROGRAM STARTED - All debug messages should now appear!")
    sys.exit(app.exec_())
