#!/usr/bin/env python3
"""
AUTOMATED ISSUE TEST - Test all issues programmatically
"""

import sys
import os
import time
import math
import vtk

def test_step_loading():
    """Test STEP file loading and colors"""
    print("🔍 TESTING STEP FILE LOADING AND COLORS")
    print("=" * 50)
    
    try:
        # Find a STEP file
        step_files = [f for f in os.listdir('.') if f.endswith('.step') or f.endswith('.stp')]
        if not step_files:
            print("❌ No STEP files found")
            return False
            
        filename = step_files[0]
        print(f"Testing with: {filename}")
        
        # Load with step_loader
        from step_loader import STEPLoader
        loader = STEPLoader()
        success, message = loader.load_step_file(filename)
        
        print(f"Load result: {success}, {message}")
        
        if not success or not loader.current_polydata:
            print("❌ STEP loading failed")
            return False
            
        polydata = loader.current_polydata
        print(f"Polydata: {polydata.GetNumberOfCells()} cells, {polydata.GetNumberOfPoints()} points")
        
        # Test VTK rendering
        mapper = vtk.vtkPolyDataMapper()
        mapper.SetInputData(polydata)
        
        actor = vtk.vtkActor()
        actor.SetMapper(mapper)
        
        # Check colors
        cell_colors = polydata.GetCellData().GetScalars()
        if cell_colors:
            print(f"✅ Colors found: {cell_colors.GetNumberOfTuples()} colors")
            # Apply colors
            mapper.SetScalarVisibility(True)
            mapper.SetScalarModeToUseCellData()
            mapper.SetColorModeToDirectScalars()
            actor.GetProperty().SetAmbient(0.3)
            actor.GetProperty().SetDiffuse(0.7)
            print("✅ STEP file colors applied")
            return True
        else:
            print("⚠️ No colors in STEP file - applying default")
            # Apply default color
            actor.GetProperty().SetColor(0.7, 0.7, 0.7)
            mapper.SetScalarVisibility(False)
            print("✅ Default gray color applied")
            return True
            
    except Exception as e:
        print(f"❌ STEP loading error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_button_rotation():
    """Test button rotation and axis calculation"""
    print("\n🔘 TESTING BUTTON ROTATION AND AXIS CALCULATION")
    print("=" * 50)
    
    try:
        # Simulate rotation values
        current_rot = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        
        # Test X+15° rotation
        axis = 'x'
        degrees = 15.0
        
        print(f"Testing {axis.upper()}{'+' if degrees > 0 else ''}{degrees}° rotation")
        
        # Update rotation values (like button click)
        old_value = current_rot[axis]
        current_rot[axis] += degrees
        print(f"ROT updated: {axis.upper()} {old_value:.1f}° → {current_rot[axis]:.1f}°")
        
        # Calculate axis and angle from rotation
        rot_mag = math.sqrt(current_rot['x']**2 + current_rot['y']**2 + current_rot['z']**2)
        print(f"Rotation magnitude: {rot_mag:.3f}")
        
        if rot_mag > 0.001:
            current_axis = {
                'x': current_rot['x'] / rot_mag,
                'y': current_rot['y'] / rot_mag,
                'z': current_rot['z'] / rot_mag
            }
            current_angle = rot_mag
            print(f"Calculated axis: {current_axis}")
            print(f"Calculated angle: {current_angle:.1f}°")
            
            # Check if axis changed from default
            if abs(current_axis['x']) > 0.1 or abs(current_axis['y']) > 0.1:
                print("✅ Button rotation working - axis values changed")
                return True
            else:
                print("❌ Button rotation broken - axis values didn't change")
                return False
        else:
            print("❌ Button rotation broken - zero magnitude")
            return False
            
    except Exception as e:
        print(f"❌ Button rotation error: {e}")
        return False

def test_vtk_mouse_rotation():
    """Test VTK mouse rotation detection"""
    print("\n🖱️ TESTING VTK MOUSE ROTATION DETECTION")
    print("=" * 50)
    
    try:
        # Create VTK actor to test orientation changes
        sphere = vtk.vtkSphereSource()
        mapper = vtk.vtkPolyDataMapper()
        mapper.SetInputConnection(sphere.GetOutputPort())
        
        actor = vtk.vtkActor()
        actor.SetMapper(mapper)
        
        # Get initial orientation
        initial_orientation = list(actor.GetOrientation())
        print(f"Initial orientation: {initial_orientation}")
        
        # Simulate rotation (like mouse drag)
        actor.RotateWXYZ(15, 1, 0, 0)  # 15° around X axis
        
        # Get new orientation
        new_orientation = list(actor.GetOrientation())
        print(f"New orientation: {new_orientation}")
        
        # Check if orientation changed
        changed = False
        for i in range(3):
            if abs(new_orientation[i] - initial_orientation[i]) > 1.0:
                changed = True
                break
                
        if changed:
            print("✅ VTK orientation change detected")
            
            # Test if we can extract rotation values
            current_rot = {
                'x': new_orientation[0],
                'y': new_orientation[1],
                'z': new_orientation[2]
            }
            print(f"Extracted rotation: {current_rot}")
            
            # Calculate axis from rotation
            rot_mag = math.sqrt(current_rot['x']**2 + current_rot['y']**2 + current_rot['z']**2)
            if rot_mag > 0.001:
                current_axis = {
                    'x': current_rot['x'] / rot_mag,
                    'y': current_rot['y'] / rot_mag,
                    'z': current_rot['z'] / rot_mag
                }
                print(f"Calculated axis: {current_axis}")
                print("✅ Mouse rotation axis calculation working")
                return True
            else:
                print("❌ Mouse rotation axis calculation failed")
                return False
        else:
            print("❌ VTK orientation change not detected")
            return False
            
    except Exception as e:
        print(f"❌ VTK mouse rotation error: {e}")
        return False

def test_main_program_integration():
    """Test if the main program has the required methods"""
    print("\n🔧 TESTING MAIN PROGRAM INTEGRATION")
    print("=" * 50)
    
    try:
        import step_viewer_tdk_modular
        
        # Check if rotate_shape method exists and has axis calculation
        if hasattr(step_viewer_tdk_modular.StepViewerTDK, 'rotate_shape'):
            print("✅ rotate_shape method exists")
            
            import inspect
            method = getattr(step_viewer_tdk_modular.StepViewerTDK, 'rotate_shape')
            source_lines = inspect.getsourcelines(method)
            source_text = ''.join(source_lines[0])
            
            # Check for axis calculation
            if 'current_axis_left' in source_text and 'math.sqrt' in source_text:
                print("✅ rotate_shape has axis calculation")
            else:
                print("❌ rotate_shape missing axis calculation")
                return False
                
            # Check for text overlay update
            if 'update_vtk_text_overlays' in source_text:
                print("✅ rotate_shape calls text overlay update")
            else:
                print("❌ rotate_shape doesn't call text overlay update")
                return False
                
        else:
            print("❌ rotate_shape method not found")
            return False
            
        # Check text overlay method
        if hasattr(step_viewer_tdk_modular.StepViewerTDK, 'update_vtk_text_overlays'):
            print("✅ update_vtk_text_overlays method exists")
        else:
            print("❌ update_vtk_text_overlays method not found")
            return False
            
        print("✅ Main program integration looks correct")
        return True
        
    except Exception as e:
        print(f"❌ Main program integration error: {e}")
        return False

def main():
    """Run all tests and identify issues"""
    print("AUTOMATED ISSUE TESTING")
    print("=" * 60)
    
    tests = [
        ("STEP File Loading & Colors", test_step_loading),
        ("Button Rotation & Axis Calculation", test_button_rotation),
        ("VTK Mouse Rotation Detection", test_vtk_mouse_rotation),
        ("Main Program Integration", test_main_program_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("AUTOMATED TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    failed_tests = []
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
        else:
            failed_tests.append(test_name)
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if failed_tests:
        print(f"\n⚠️ FAILED TESTS NEED FIXING:")
        for test in failed_tests:
            print(f"   - {test}")
    else:
        print(f"\n🎉 All tests passed! Issues should be resolved.")

if __name__ == "__main__":
    main()
