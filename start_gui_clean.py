#!/usr/bin/env python3
"""
Clean GUI launcher for the 3D STEP viewer
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """Run the interactive GUI"""
    print("🚀 Starting 3D STEP Viewer GUI...")
    
    # Import here to avoid any conflicts
    from step_viewer_tdk_modular_fixed import StepViewerTDK
    
    # Create Qt application
    app = QApplication(sys.argv)
    app.setApplicationName("TDK STEP Viewer")
    app.setApplicationVersion("3.0")
    
    # Create and show the viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    print("✅ GUI is now open!")
    print("You can now test the rotation fix:")
    print("1. Load a STEP file in TOP viewer")
    print("2. Apply rotation (e.g., X+45°)")
    print("3. Save the rotated file")
    print("4. Load the saved file in BOTTOM viewer")
    print("5. Check if BOTTOM viewer shows correct rotation values")
    
    # Run the application event loop
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
