TESTING OPENCASCADE COLOR EXTRACTION
Basic imports OK
STEP file read OK
S<PERSON>pe extracted OK
Color tools imported OK
CAF STEP file read OK
ERROR: Wrong number or type of arguments for overloaded function 'XCAFDoc_ColorTool_GetColor'.
  Possible C/C++ prototypes are:
    XCAFDoc_ColorTool::GetColor(TDF_Label const &,Quantity_Color &)
    XCAFDoc_ColorTool::GetColor(TDF_Label const &,Quantity_ColorRGBA &)
    XCAFDoc_ColorTool::GetColor(TDF_Label const &,XCAFDoc_ColorType const,TDF_Label &)
    XCAFDoc_ColorTool::GetColor(TDF_Label const &,XCAFDoc_ColorType const,Quantity_Color &)
    XCAFDoc_ColorTool::GetColor(TDF_Label const &,XCAFDoc_ColorType const,Quantity_ColorRGBA &)
    XCAFDoc_ColorTool::GetColor(TopoDS_Shape const &,XCAFDoc_ColorType const,TDF_Label &)
    XCAFDoc_ColorTool::GetColor(TopoDS_Shape const &,XCAFDoc_ColorType const,Quantity_Color &)
    XCAFDoc_ColorTool::GetColor(TopoDS_Shape const &,XCAFDoc_ColorType const,Quantity_ColorRGBA &)

TEST COMPLETE
