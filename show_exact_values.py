#!/usr/bin/env python3
"""
Show EXACT values as requested:
1. Original coordinates in STEP file
2. What gets written after rotation
3. What gets read back from saved file
4. Verification they match
"""

import re
import numpy as np
from simple_step_modifier import SimpleSTEPModifier
import vtk

def show_step_file_lines(filename, title):
    """Show the exact lines from STEP file for root coordinate system"""
    print(f"\n🔍 {title}: {filename}")
    print("-" * 60)
    
    with open(filename, 'r') as f:
        content = f.read()
    
    # Find and show the exact lines
    lines = content.split('\n')
    
    for i, line in enumerate(lines):
        if '#12 = CARTESIAN_POINT' in line:
            print(f"   Line {i+1}: {line}")
        elif '#13 = DIRECTION' in line:
            print(f"   Line {i+1}: {line}")
        elif '#14 = DIRECTION' in line:
            print(f"   Line {i+1}: {line}")

def main():
    print("=" * 80)
    print("🔧 EXACT VALUES TEST - SHOW ORIGINAL, WRITTEN, AND READ BACK")
    print("=" * 80)
    
    original_file = "test.step"
    test_file = "exact_values_test.step"
    
    # STEP 1: Show original coordinates
    print("📋 STEP 1: ORIGINAL COORDINATES IN STEP FILE")
    show_step_file_lines(original_file, "ORIGINAL FILE")
    
    # STEP 2: Apply 45° Z rotation and save
    print(f"\n📋 STEP 2: APPLYING 45° Z ROTATION AND SAVING")
    print("-" * 60)
    
    modifier = SimpleSTEPModifier()
    modifier.load_step_file(original_file)
    
    # Create 45° Z rotation matrix
    transform = vtk.vtkTransform()
    transform.RotateZ(45.0)
    matrix = transform.GetMatrix()
    
    print(f"🔧 Applying 45° Z rotation...")
    
    # Transform geometry
    modifier.transform_geometry_coordinates(matrix)
    
    # Set coordinate system (45° Z rotation)
    modifier.modify_placement(0.0, 0.0, 0.0, 0.0, 0.0, 45.0)
    
    # Save file
    modifier.save_step_file(test_file)
    
    # STEP 3: Show what was written to file
    print(f"\n📋 STEP 3: WHAT WAS WRITTEN TO SAVED FILE")
    show_step_file_lines(test_file, "SAVED FILE")
    
    # STEP 4: Read back and verify
    print(f"\n📋 STEP 4: READING BACK FROM SAVED FILE")
    print("-" * 60)
    
    # Read the saved file again to verify
    modifier2 = SimpleSTEPModifier()
    modifier2.load_step_file(test_file)
    
    # Extract values from saved file
    with open(test_file, 'r') as f:
        saved_content = f.read()
    
    # Parse the exact values
    point_match = re.search(r'#12\s*=\s*CARTESIAN_POINT\s*\([^;]+\)\s*;', saved_content)
    z_dir_match = re.search(r'#13\s*=\s*DIRECTION\s*\([^;]+\)\s*;', saved_content)
    x_dir_match = re.search(r'#14\s*=\s*DIRECTION\s*\([^;]+\)\s*;', saved_content)
    
    if point_match and z_dir_match and x_dir_match:
        print(f"✅ Successfully read back all coordinate values")
        
        # Extract numerical values
        point_line = point_match.group(0)
        z_dir_line = z_dir_match.group(0)
        x_dir_line = x_dir_match.group(0)
        
        # Parse coordinates
        point_coords = re.search(r'\(\s*[^,]+,\s*\(\s*([^,]+),\s*([^,]+),\s*([^)]+)\s*\)', point_line)
        z_dir_coords = re.search(r'\(\s*[^,]+,\s*\(\s*([^,]+),\s*([^,]+),\s*([^)]+)\s*\)', z_dir_line)
        x_dir_coords = re.search(r'\(\s*[^,]+,\s*\(\s*([^,]+),\s*([^,]+),\s*([^)]+)\s*\)', x_dir_line)
        
        if point_coords and z_dir_coords and x_dir_coords:
            saved_pos = [float(point_coords.group(1)), float(point_coords.group(2)), float(point_coords.group(3))]
            saved_z_dir = [float(z_dir_coords.group(1)), float(z_dir_coords.group(2)), float(z_dir_coords.group(3))]
            saved_x_dir = [float(x_dir_coords.group(1)), float(x_dir_coords.group(2)), float(x_dir_coords.group(3))]
            
            print(f"📊 READ BACK VALUES:")
            print(f"   Position: ({saved_pos[0]:.6f}, {saved_pos[1]:.6f}, {saved_pos[2]:.6f})")
            print(f"   Z Direction: ({saved_z_dir[0]:.6f}, {saved_z_dir[1]:.6f}, {saved_z_dir[2]:.6f})")
            print(f"   X Direction: ({saved_x_dir[0]:.6f}, {saved_x_dir[1]:.6f}, {saved_x_dir[2]:.6f})")
            
            # STEP 5: Verification
            print(f"\n📋 STEP 5: VERIFICATION - DO THEY MATCH?")
            print("-" * 60)
            
            # Expected values for 45° Z rotation
            expected_pos = [0.0, 0.0, 0.0]
            expected_z_dir = [0.0, 0.0, 1.0]
            expected_x_dir = [np.cos(np.radians(45)), np.sin(np.radians(45)), 0.0]
            
            print(f"📊 EXPECTED VALUES FOR 45° Z ROTATION:")
            print(f"   Position: ({expected_pos[0]:.6f}, {expected_pos[1]:.6f}, {expected_pos[2]:.6f})")
            print(f"   Z Direction: ({expected_z_dir[0]:.6f}, {expected_z_dir[1]:.6f}, {expected_z_dir[2]:.6f})")
            print(f"   X Direction: ({expected_x_dir[0]:.6f}, {expected_x_dir[1]:.6f}, {expected_x_dir[2]:.6f})")
            
            # Check matches
            pos_match = np.allclose(saved_pos, expected_pos, atol=1e-5)
            z_match = np.allclose(saved_z_dir, expected_z_dir, atol=1e-5)
            x_match = np.allclose(saved_x_dir, expected_x_dir, atol=1e-5)
            
            print(f"\n🔍 MATCH VERIFICATION:")
            print(f"   Position matches: {'✅ YES' if pos_match else '❌ NO'}")
            print(f"   Z Direction matches: {'✅ YES' if z_match else '❌ NO'}")
            print(f"   X Direction matches: {'✅ YES' if x_match else '❌ NO'}")
            
            if pos_match and z_match and x_match:
                print(f"\n🎉 PERFECT SUCCESS: All values match exactly!")
                print(f"   ✅ Original coordinates were read correctly")
                print(f"   ✅ Rotation was applied and written correctly")
                print(f"   ✅ Saved values were read back correctly")
                print(f"   ✅ All values match expected 45° Z rotation")
            else:
                print(f"\n❌ MISMATCH: Some values don't match expected results")
        else:
            print(f"❌ Failed to parse coordinate values from saved file")
    else:
        print(f"❌ Failed to find coordinate lines in saved file")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
