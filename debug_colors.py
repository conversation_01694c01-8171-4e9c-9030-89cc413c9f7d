#!/usr/bin/env python3
"""
Debug program to analyze STEP file colors and see what's actually being applied
"""

import sys
from step_loader import <PERSON><PERSON><PERSON>oader

def debug_step_colors():
    print("=== STEP COLOR DEBUG ANALYSIS ===")
    
    # Load the STEP file
    loader = STEPLoader()
    filename = "E:/Python/3d-models/SOIC16P127_1270X940X610L89X51.STEP"
    
    print(f"Loading STEP file: {filename}")
    success, message = loader.load_step_file(filename)
    
    if not success:
        print(f"Failed to load: {message}")
        return
    
    print(f"Load result: {success}, Message: {message}")
    
    # Check polydata
    if loader.current_polydata:
        num_cells = loader.current_polydata.GetNumberOfCells()
        num_points = loader.current_polydata.GetNumberOfPoints()
        print(f"Polydata: {num_cells} cells, {num_points} points")
        
        # Check if colors are applied
        cell_data = loader.current_polydata.GetCellData()
        if cell_data.GetScalars():
            colors_array = cell_data.GetScalars()
            num_color_tuples = colors_array.GetNumberOfTuples()
            print(f"Colors applied: {num_color_tuples} color tuples")
            
            # Sample first 20 colors to see the pattern
            print("First 20 cell colors:")
            unique_colors = set()
            for i in range(min(20, num_color_tuples)):
                color = colors_array.GetTuple3(i)
                unique_colors.add(color)
                print(f"  Cell {i}: RGB{color}")
            
            print(f"Unique colors found in first 20 cells: {len(unique_colors)}")
            for color in unique_colors:
                print(f"  Unique color: RGB{color}")
                
            # Check all colors to see total unique colors
            all_unique_colors = set()
            for i in range(num_color_tuples):
                color = colors_array.GetTuple3(i)
                all_unique_colors.add(color)
            
            print(f"Total unique colors in entire model: {len(all_unique_colors)}")
            for color in all_unique_colors:
                # Count how many cells use this color
                count = 0
                for i in range(num_color_tuples):
                    if colors_array.GetTuple3(i) == color:
                        count += 1
                print(f"  Color RGB{color}: used by {count} cells ({count/num_color_tuples*100:.1f}%)")
                
        else:
            print("No colors applied to cell data")
            
        # Check point colors too
        point_data = loader.current_polydata.GetPointData()
        if point_data.GetScalars():
            print("Point colors are also present")
        else:
            print("No point colors")
    else:
        print("No polydata available")

if __name__ == "__main__":
    debug_step_colors()
