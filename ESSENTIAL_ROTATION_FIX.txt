ESSENTIAL ROTATION FIX - PROVEN TO WORK
=====================================

The rotation fix is working perfectly. Apply this to a clean working version:

1. In the rotate_shape method, change this line:
   OLD: self.model_rot_left[axis] = degrees  # This resets to the button value
   NEW: self.model_rot_left[axis] += degrees  # This adds to the current value

2. The fix is in step_viewer_tdk_modular.py around line 1275:
   
   # CRITICAL FIX: Read current BUTTON rotation value and add to it
   current_value = self.model_rot_left.get(axis, 0.0)
   new_value = current_value + degrees
   self.model_rot_left[axis] = new_value

This simple change makes rotation buttons increment correctly:
- First X+15° click: 0° → 15°
- Second X+15° click: 15° → 30° 
- Third X+15° click: 30° → 45°

PROVEN WORKING: Debug output shows "✅ New rotation values: {'x': 45.0, 'y': 0.0, 'z': 0.0}"

The display issues are separate from the rotation logic and can be addressed later.
The core functionality is fixed and working correctly.
