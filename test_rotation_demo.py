#!/usr/bin/env python3
"""
Test Rotation Demo - Load, rotate, save, load, then stop
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

def main():
    app = QApplication(sys.argv)
    
    print("Starting rotation demo...")
    
    # Import viewer
    from step_viewer_tdk_modular_fixed import StepViewerTDK
    viewer = StepViewerTDK()
    viewer.show()
    viewer.setWindowTitle("Rotation Demo Test")
    
    # Find test file
    test_file = 'test.step'
    if not os.path.exists(test_file):
        test_file = 'SOIC16P127_1270X940X610L89X51.STEP'
    
    if not os.path.exists(test_file):
        print("No test file found")
        return
    
    def step1():
        print("Step 1: Loading file...")
        viewer.active_viewer = 'top'
        viewer.step_loader_left.load_step_file(test_file)
        print(f"Loaded: {test_file}")
        QTimer.singleShot(2000, step2)
    
    def step2():
        print("Step 2: Rotating...")
        viewer.rotate_shape('x', 15.0)
        viewer.rotate_shape('y', 30.0)
        viewer.rotate_shape('z', 45.0)
        print("Applied rotations: X=15°, Y=30°, Z=45°")
        QTimer.singleShot(2000, step3)
    
    def step3():
        print("Step 3: Saving...")
        output_file = "demo_output.step"
        delta_pos = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        delta_rot = {'x': 15.0, 'y': 30.0, 'z': 45.0}
        
        success = viewer._save_step_opencascade_transform(
            output_file,
            viewer.step_loader_left,
            delta_pos,
            delta_rot
        )
        
        if success:
            print(f"Saved: {output_file}")
            QTimer.singleShot(2000, lambda: step4(output_file))
        else:
            print("Save failed")
    
    def step4(saved_file):
        print("Step 4: Loading saved file in right viewer...")
        viewer.step_loader_right.load_step_file(saved_file)
        print(f"Loaded saved file: {saved_file}")
        print("DEMO COMPLETE - Both viewers should show rotated geometry")
        print("Left: Original with rotations applied")
        print("Right: Saved file with rotations preserved")
    
    # Start sequence
    QTimer.singleShot(1000, step1)
    
    # Run GUI
    app.exec_()

if __name__ == "__main__":
    main()
