#!/usr/bin/env python3
"""
Debug how STEP file maps colors to geometry
"""

print("=== DEBUGGING STEP COLOR TO GEOMETRY MAPPING ===")

# Read the STEP file and analyze color-to-geometry relationships
with open('SOIC16P127_1270X940X610L89X51.STEP', 'r', encoding='utf-8', errors='ignore') as f:
    content = f.read()

lines = content.split('\n')

print("Looking for color-to-surface relationships...")

# Find all COLOUR_RGB entries with their IDs
color_entries = {}
for line in lines:
    if 'COLOUR_RGB' in line and line.startswith('#'):
        parts = line.split('=')
        if len(parts) >= 2:
            color_id = parts[0].strip()
            color_line = parts[1].strip()
            color_entries[color_id] = color_line
            print(f"Color entry: {color_id} = {color_line}")

print(f"\nFound {len(color_entries)} color entries")

# Find FILL_AREA_STYLE_COLOUR entries that reference colors
fill_style_entries = {}
for line in lines:
    if 'FILL_AREA_STYLE_COLOUR' in line and line.startswith('#'):
        parts = line.split('=')
        if len(parts) >= 2:
            style_id = parts[0].strip()
            style_line = parts[1].strip()
            fill_style_entries[style_id] = style_line
            print(f"Fill style: {style_id} = {style_line}")

print(f"\nFound {len(fill_style_entries)} fill style entries")

# Find SURFACE_STYLE entries that reference fill styles
surface_style_entries = {}
for line in lines:
    if 'SURFACE_STYLE' in line and line.startswith('#'):
        parts = line.split('=')
        if len(parts) >= 2:
            surface_id = parts[0].strip()
            surface_line = parts[1].strip()
            surface_style_entries[surface_id] = surface_line
            print(f"Surface style: {surface_id} = {surface_line}")

print(f"\nFound {len(surface_style_entries)} surface style entries")

# Look for geometry that references these surface styles
print("\nLooking for geometry references...")
geometry_with_colors = []
for line in lines:
    if any(surface_id in line for surface_id in surface_style_entries.keys()):
        if line.startswith('#') and ('FACE' in line or 'SHELL' in line or 'SOLID' in line):
            geometry_with_colors.append(line.strip())

print(f"Found {len(geometry_with_colors)} geometry entries with color references:")
for entry in geometry_with_colors[:5]:  # Show first 5
    print(f"  {entry}")

print("\n=== ANALYSIS COMPLETE ===")
print("The STEP file has a complex color-to-geometry mapping")
print("Need to parse this structure to apply colors correctly")
