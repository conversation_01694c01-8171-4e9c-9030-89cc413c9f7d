"""
Simple STEP file parser to extract basic geometry and color information
"""
import re
import numpy as np

class SimpleStepParser:
    def __init__(self):
        self.entities = {}
        self.colors = {}
        self.surfaces = []
        
    def parse_step_file(self, filename):
        """Parse a STEP file and extract basic information"""
        try:
            with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            print(f"Parsing STEP file: {filename}")
            
            # Extract entities (lines starting with #)
            entity_pattern = r'#(\d+)\s*=\s*([^;]+);'
            matches = re.findall(entity_pattern, content)
            
            for entity_id, entity_data in matches:
                self.entities[int(entity_id)] = entity_data.strip()
            
            print(f"Found {len(self.entities)} entities")
            
            # Look for color information
            self.extract_colors()
            
            # Look for surface information
            self.extract_surfaces()
            
            return True
            
        except Exception as e:
            print(f"Error parsing STEP file: {e}")
            return False
    
    def extract_colors(self):
        """Extract color information from STEP entities"""
        color_patterns = [
            r'COLOUR_RGB\s*\(\s*[^,]+,\s*([\d.]+),\s*([\d.]+),\s*([\d.]+)\s*\)',
            r'SURFACE_STYLE_FILL_AREA.*COLOUR_RGB.*\(\s*[^,]+,\s*([\d.]+),\s*([\d.]+),\s*([\d.]+)\s*\)'
        ]
        
        for entity_id, entity_data in self.entities.items():
            for pattern in color_patterns:
                matches = re.findall(pattern, entity_data, re.IGNORECASE)
                if matches:
                    for match in matches:
                        r, g, b = float(match[0]), float(match[1]), float(match[2])
                        self.colors[entity_id] = (r, g, b)
                        print(f"Found color in entity {entity_id}: RGB({r:.3f}, {g:.3f}, {b:.3f})")
        
        print(f"Extracted {len(self.colors)} colors")
    
    def extract_surfaces(self):
        """Extract surface/face information"""
        surface_patterns = [
            r'ADVANCED_FACE',
            r'FACE_SURFACE',
            r'PLANE',
            r'CYLINDRICAL_SURFACE',
            r'SPHERICAL_SURFACE'
        ]
        
        for entity_id, entity_data in self.entities.items():
            for pattern in surface_patterns:
                if re.search(pattern, entity_data, re.IGNORECASE):
                    self.surfaces.append(entity_id)
                    break
        
        print(f"Found {len(self.surfaces)} surfaces")
    
    def get_default_colors(self):
        """Return default colors if none found in STEP file"""
        if self.colors:
            # Use found colors
            unique_colors = list(set(self.colors.values()))
            return unique_colors
        else:
            # Return default gray colors similar to what we found in trimesh
            return [
                (0.525, 0.525, 0.525),  # Light gray (134/255)
                (0.051, 0.051, 0.051),  # Dark gray (13/255)
                (0.7, 0.7, 0.7),        # Medium gray
                (0.3, 0.3, 0.3)         # Darker gray
            ]
    
    def get_material_info(self):
        """Return material information for the STEP file"""
        colors = self.get_default_colors()
        
        return {
            'num_materials': len(colors),
            'colors': colors,
            'surfaces': len(self.surfaces),
            'entities': len(self.entities)
        }

# Test function
if __name__ == "__main__":
    parser = SimpleStepParser()
    if parser.parse_step_file("test.step"):
        info = parser.get_material_info()
        print(f"Material info: {info}")
