#!/usr/bin/env python3
"""
Test the fixed delta transformation logic
"""

import sys
import os

# Add current directory to path for imports
sys.path.append('.')

from step_transformer import STEPTransformer

def test_delta_transformation():
    """Test delta transformation with a clear example"""
    print("🧪 TESTING DELTA TRANSFORMATION FIX")
    print("=" * 50)
    
    # Simulate the scenario:
    # - Original file has some rotation (e.g., 9°)
    # - User rotates it 45° more
    # - We should apply only the 45° delta, not the total 54°
    
    print("📝 Scenario: User rotates model 45° more from original position")
    print("   Original rotation: 9°")
    print("   Current rotation: 54°")
    print("   Delta rotation: 45° (what we should apply)")
    
    # Test with delta values only
    transformer = STEPTransformer()
    if not transformer.load_step_file("test.step"):
        print("❌ Failed to load test.step")
        return False
    
    # Apply ONLY the delta (45° rotation, no translation)
    success = transformer.apply_transformation(
        rotation_x=0.0,
        rotation_y=0.0,
        rotation_z=45.0,  # Only the delta rotation
        translation_x=0.0,  # No translation change
        translation_y=0.0,
        translation_z=0.0
    )
    
    if not success:
        print("❌ Delta transformation failed")
        return False
    
    # Save the result
    output_file = "test_delta_45deg_only.step"
    if transformer.save_step_file(output_file):
        print(f"✅ Delta transformation successful: {output_file}")
        
        # Analyze the result
        analyze_transformation_result(output_file)
        return True
    else:
        print("❌ Save failed")
        return False

def analyze_transformation_result(filename):
    """Analyze the transformation result"""
    print(f"\n🔍 ANALYZING RESULT: {filename}")
    
    if not os.path.exists(filename):
        print("❌ File not found")
        return
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find coordinate points
        import re
        coord_pattern = r'CARTESIAN_POINT\s*\(\s*\'[^\']*\'\s*,\s*\(\s*([-\d.E+-]+)\s*,\s*([-\d.E+-]+)\s*,\s*([-\d.E+-]+)\s*\)'
        coords = re.findall(coord_pattern, content)
        
        print(f"   Found {len(coords)} coordinate points")
        print(f"   First 3 transformed coordinates:")
        
        for i, (x, y, z) in enumerate(coords[:3]):
            print(f"     Point {i+1}: ({float(x):10.6f}, {float(y):10.6f}, {float(z):10.6f})")
        
        # Compare with original
        print(f"\n   Comparison with original:")
        original_coords = [
            (0.000000, 0.000000, 0.000000),
            (-1.109997, -1.612300, 0.491400),
            (-0.889997, -1.612300, 0.491400)
        ]
        
        for i, ((orig_x, orig_y, orig_z), (new_x, new_y, new_z)) in enumerate(zip(original_coords, coords[:3])):
            new_x, new_y, new_z = float(new_x), float(new_y), float(new_z)
            print(f"     Point {i+1}:")
            print(f"       Original: ({orig_x:10.6f}, {orig_y:10.6f}, {orig_z:10.6f})")
            print(f"       Delta 45°: ({new_x:10.6f}, {new_y:10.6f}, {new_z:10.6f})")
            
            # Check if transformation looks reasonable for 45° rotation
            if i == 0:  # Origin should stay at origin for pure rotation
                if abs(new_x) < 0.001 and abs(new_y) < 0.001 and abs(new_z) < 0.001:
                    print(f"       ✅ Origin correctly unchanged")
                else:
                    print(f"       ❌ Origin moved unexpectedly")
            else:
                # Other points should be rotated
                distance_orig = (orig_x**2 + orig_y**2 + orig_z**2)**0.5
                distance_new = (new_x**2 + new_y**2 + new_z**2)**0.5
                if abs(distance_orig - distance_new) < 0.01:
                    print(f"       ✅ Distance preserved (rotation only)")
                else:
                    print(f"       ⚠️ Distance changed: {distance_orig:.6f} → {distance_new:.6f}")
                    
    except Exception as e:
        print(f"❌ Error analyzing file: {e}")

def main():
    """Main test function"""
    print("🔧 DELTA TRANSFORMATION FIX TEST")
    print("=" * 60)
    
    success = test_delta_transformation()
    
    print("\n" + "=" * 60)
    if success:
        print("🎯 DELTA TRANSFORMATION TEST COMPLETE")
        print("✅ Check the coordinate analysis above to verify the fix works correctly!")
    else:
        print("❌ DELTA TRANSFORMATION TEST FAILED")

if __name__ == "__main__":
    main()
