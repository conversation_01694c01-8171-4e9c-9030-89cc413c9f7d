#!/usr/bin/env python3
"""
Manual Test Capture - Isolate and capture manual save operations
This script will help us see what happens during your actual manual save
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from step_viewer_tdk_modular import StepViewerTDK

def main():
    print("🔧 MANUAL TEST CAPTURE - Starting clean viewer")
    print("=" * 60)
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    
    # Override the save method to add debug output
    original_save_method = viewer._save_step_with_transformations
    
    def debug_save_method(filename, loader, current_pos, current_rot, orig_pos, orig_rot):
        # Log to file instead of terminal (terminal seems corrupted)
        import time
        log_msg = f"""
=== MANUAL SAVE OPERATION DETECTED ===
Time: {time.strftime('%Y-%m-%d %H:%M:%S')}
Filename: {filename}
Current Rotation: {current_rot}
Original Rotation: {orig_rot}
Current Position: {current_pos}
Original Position: {orig_pos}
========================================
"""

        with open('manual_save_debug.log', 'a') as f:
            f.write(log_msg)

        print("🚨 MANUAL SAVE DETECTED - Check manual_save_debug.log")

        # Call the original method
        result = original_save_method(filename, loader, current_pos, current_rot, orig_pos, orig_rot)

        result_msg = f"Save Result: {result}\n=== SAVE COMPLETE ===\n\n"
        with open('manual_save_debug.log', 'a') as f:
            f.write(result_msg)

        print(f"💾 Save completed - Result logged")

        return result
    
    # Replace the method
    viewer._save_step_with_transformations = debug_save_method
    
    print("✅ Debug wrapper installed")
    print("📋 Instructions:")
    print("   1. Load test.step in TOP viewer")
    print("   2. Rotate it to any angle")
    print("   3. Click the green save button")
    print("   4. Save as 'manual_debug_test.step'")
    print("   5. Load saved file in BOTTOM viewer")
    print("   6. Compare angles")
    print("=" * 60)
    
    viewer.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
