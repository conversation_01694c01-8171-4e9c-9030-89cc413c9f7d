#!/usr/bin/env python3
"""
Debug Reset Final Fix - Find exactly why reset doesn't work and fix it
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular import Step<PERSON><PERSON>werTD<PERSON>

def debug_reset_final_fix():
    """Final debug to fix reset issue once and for all"""
    
    print("🔧 DEBUG RESET FINAL FIX")
    print("=" * 60)
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    # Process events to ensure GUI is ready
    app.processEvents()
    time.sleep(1)
    
    # Find a STEP file to test with
    test_files = [
        "AMPHENOL_U77-A1118-200T.STEP",
        "test.step",
        "sample.stp"
    ]
    
    test_file = None
    for filename in test_files:
        if os.path.exists(filename):
            test_file = filename
            break
    
    if not test_file:
        print("❌ No STEP file found for testing")
        return False
    
    print(f"📁 Using test file: {test_file}")
    
    # Step 1: Load STEP file
    print(f"\n📋 STEP 1: LOADING {test_file}...")
    success = viewer.load_step_file_direct(test_file)
    app.processEvents()
    time.sleep(2)
    
    if not success:
        print("❌ Failed to load STEP file")
        return False
    
    renderer = viewer.vtk_renderer_left
    
    # Get all actors
    all_actors = []
    if hasattr(renderer, 'renderer') and renderer.renderer:
        actor_collection = renderer.renderer.GetActors()
        actor_collection.InitTraversal()
        
        actor = actor_collection.GetNextActor()
        while actor:
            all_actors.append(actor)
            actor = actor_collection.GetNextActor()
    
    print(f"\n🔍 INITIAL STATE:")
    multi_actors = []
    bbox_actor = None
    
    for i, actor in enumerate(all_actors):
        pos = actor.GetPosition()
        orient = actor.GetOrientation()
        visible = actor.GetVisibility()
        
        print(f"Actor {i}: Pos={pos}, Orient={orient}, Visible={visible}")
        
        # Identify actor types
        if hasattr(renderer, 'step_actors') and renderer.step_actors:
            for j, multi_actor in enumerate(renderer.step_actors):
                if multi_actor == actor:
                    multi_actors.append(actor)
                    print(f"  *** MULTI-ACTOR {j} ***")
                    
        if hasattr(renderer, 'bbox_actor') and renderer.bbox_actor == actor:
            bbox_actor = actor
            print(f"  *** BOUNDING BOX ACTOR ***")
    
    print(f"\n👁️ INITIAL MODEL - Note position (3 seconds)")
    time.sleep(3)
    
    # Step 2: Apply transformation
    print(f"\n📋 STEP 2: APPLYING TRANSFORMATION...")
    
    viewer.active_viewer = "top"
    
    print(f"🔧 Applying transformation: +100mm X, +90° Z")
    viewer.move_shape("x", 100)
    viewer.rotate_shape("z", 90)
    app.processEvents()
    time.sleep(2)
    
    # Check state after transformation
    print(f"\n🔍 STATE AFTER TRANSFORMATION:")
    for i, actor in enumerate(multi_actors):
        pos = actor.GetPosition()
        orient = actor.GetOrientation()
        print(f"Multi-actor {i}: Pos={pos}, Orient={orient}")
    
    if bbox_actor:
        pos = bbox_actor.GetPosition()
        orient = bbox_actor.GetOrientation()
        bounds = bbox_actor.GetBounds()
        print(f"Bounding box: Pos={pos}, Orient={orient}")
        print(f"  Bounds: {bounds}")
    
    print(f"\n👁️ AFTER TRANSFORMATION - Note new position (3 seconds)")
    time.sleep(3)
    
    # Step 3: Manual reset test
    print(f"\n📋 STEP 3: MANUAL RESET TEST...")
    
    print(f"🔧 Manually resetting ALL actors to (0,0,0)...")
    
    for i, actor in enumerate(multi_actors):
        print(f"  Resetting multi-actor {i}...")
        actor.SetPosition(0, 0, 0)
        actor.SetOrientation(0, 0, 0)
        actor.SetUserTransform(None)
        actor.Modified()
    
    if bbox_actor:
        print(f"  Resetting bounding box...")
        bbox_actor.SetPosition(0, 0, 0)
        bbox_actor.SetOrientation(0, 0, 0)
        bbox_actor.SetUserTransform(None)
        bbox_actor.Modified()
    
    # Force render
    renderer.render_window.Render()
    app.processEvents()
    time.sleep(1)
    
    print(f"\n👁️ AFTER MANUAL RESET - Did it move back visually? (5 seconds)")
    time.sleep(5)
    
    # Step 4: Re-apply transformation for GUI reset test
    print(f"\n📋 STEP 4: RE-APPLYING TRANSFORMATION FOR GUI RESET TEST...")
    
    print(f"🔧 Re-applying transformation...")
    viewer.move_shape("x", 100)
    viewer.rotate_shape("z", 90)
    app.processEvents()
    time.sleep(2)
    
    print(f"\n👁️ RE-TRANSFORMED - Note position again (3 seconds)")
    time.sleep(3)
    
    # Step 5: GUI reset with detailed analysis
    print(f"\n📋 STEP 5: GUI RESET WITH DETAILED ANALYSIS...")
    
    print(f"\n🔧 BEFORE GUI RESET:")
    for i, actor in enumerate(multi_actors):
        pos = actor.GetPosition()
        orient = actor.GetOrientation()
        print(f"Multi-actor {i}: Pos={pos}, Orient={orient}")
    
    if bbox_actor:
        pos = bbox_actor.GetPosition()
        orient = bbox_actor.GetOrientation()
        print(f"Bounding box: Pos={pos}, Orient={orient}")
    
    # Check original transforms storage
    print(f"\n🔧 CHECKING ORIGINAL TRANSFORMS STORAGE:")
    if hasattr(viewer, 'original_actor_transforms_left'):
        print(f"Original transforms stored: {len(viewer.original_actor_transforms_left)}")
        for i, orig_state in enumerate(viewer.original_actor_transforms_left):
            print(f"  Original {i}:")
            print(f"    Position: {orig_state['position']}")
            print(f"    Orientation: {orig_state['orientation']}")
    else:
        print("❌ No original transforms stored!")
    
    print(f"\n🔧 Calling GUI reset...")
    viewer.reset_to_original()
    app.processEvents()
    time.sleep(2)
    
    print(f"\n🔧 AFTER GUI RESET:")
    for i, actor in enumerate(multi_actors):
        pos = actor.GetPosition()
        orient = actor.GetOrientation()
        print(f"Multi-actor {i}: Pos={pos}, Orient={orient}")
    
    if bbox_actor:
        pos = bbox_actor.GetPosition()
        orient = bbox_actor.GetOrientation()
        print(f"Bounding box: Pos={pos}, Orient={orient}")
    
    print(f"\n👁️ AFTER GUI RESET - Did it move back visually? (5 seconds)")
    time.sleep(5)
    
    # Step 6: Test if the issue is with bounding box recreation
    print(f"\n📋 STEP 6: TESTING BOUNDING BOX RECREATION ISSUE...")
    
    # Re-apply transformation
    print(f"🔧 Re-applying transformation one more time...")
    viewer.move_shape("x", 100)
    app.processEvents()
    time.sleep(1)
    
    print(f"🔧 Current state after move:")
    for i, actor in enumerate(multi_actors):
        pos = actor.GetPosition()
        print(f"Multi-actor {i}: Pos={pos}")
    
    if bbox_actor:
        pos = bbox_actor.GetPosition()
        print(f"Bounding box: Pos={pos}")
    
    # Manual reset without bounding box recreation
    print(f"\n🔧 Manual reset WITHOUT bounding box recreation...")
    for i, actor in enumerate(multi_actors):
        actor.SetPosition(0, 0, 0)
        actor.SetOrientation(0, 0, 0)
        actor.Modified()
    
    # Don't recreate bounding box, just move it
    if bbox_actor:
        bbox_actor.SetPosition(0, 0, 0)
        bbox_actor.SetOrientation(0, 0, 0)
        bbox_actor.Modified()
    
    renderer.render_window.Render()
    app.processEvents()
    time.sleep(1)
    
    print(f"\n👁️ AFTER MANUAL RESET (no bbox recreation) - Did it work? (5 seconds)")
    time.sleep(5)
    
    # Step 7: Final analysis
    print(f"\n📋 STEP 7: FINAL ANALYSIS")
    print("=" * 60)
    
    print(f"🎯 KEY FINDINGS:")
    print(f"1. Did manual reset work? (Step 3)")
    print(f"2. Did GUI reset work? (Step 5)")
    print(f"3. Did manual reset without bbox recreation work? (Step 6)")
    
    print(f"\n🔍 DIAGNOSIS:")
    print(f"- If manual reset worked: The reset logic is correct")
    print(f"- If GUI reset didn't work: GUI reset has a bug")
    print(f"- If manual without bbox recreation worked: Bbox recreation is the issue")
    print(f"- If nothing worked: There's a deeper rendering issue")
    
    # Keep window open
    print(f"\n👁️ Window will stay open for final inspection...")
    QTimer.singleShot(15000, app.quit)
    app.exec_()
    
    return True

if __name__ == "__main__":
    debug_reset_final_fix()
