#!/usr/bin/env python3
"""
Test rotation consistency between viewers
This will help understand the difference between button and mouse rotations
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from step_viewer_tdk_modular import <PERSON><PERSON>iewerTDK
from PyQt5.QtWidgets import QApplication
import tempfile

def test_rotation_consistency():
    """Test if rotation values are consistent between viewers"""
    
    print("🚀 Testing rotation consistency between TOP and BOTTOM viewers...")
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create viewer
    viewer = StepViewerTDK()
    
    # Load test file in both viewers
    test_file = "test.step"
    if not os.path.exists(test_file):
        print(f"❌ Test file {test_file} not found")
        return
    
    print(f"📁 Loading {test_file} in both viewers...")
    
    # Load in TOP viewer
    viewer.set_active_viewer("top")
    viewer.step_loader_left.load_step_file(test_file)
    
    # Load in BOTTOM viewer  
    viewer.set_active_viewer("bottom")
    viewer.step_loader_right.load_step_file(test_file)
    
    print("✅ Files loaded in both viewers")
    
    # Test 1: Initial state (no rotations)
    print("\n🔧 Test 1: Initial state (no rotations)")
    top_rot_initial = viewer._extract_rotation_from_vtk_actor("top")
    bottom_rot_initial = viewer._extract_rotation_from_vtk_actor("bottom")
    
    print(f"   TOP initial:    {top_rot_initial}")
    print(f"   BOTTOM initial: {bottom_rot_initial}")
    
    # Test 2: Apply button rotation to TOP viewer
    print("\n🔧 Test 2: Apply button rotation to TOP viewer (X=45°)")
    viewer.set_active_viewer("top")
    viewer.current_rot_left = {'x': 45.0, 'y': 0.0, 'z': 0.0}
    
    top_rot_after_button = viewer._extract_rotation_from_vtk_actor("top")
    bottom_rot_after_button = viewer._extract_rotation_from_vtk_actor("bottom")
    
    print(f"   TOP after button:    {top_rot_after_button}")
    print(f"   BOTTOM after button: {bottom_rot_after_button}")
    
    # Test 3: Apply button rotation to BOTTOM viewer
    print("\n🔧 Test 3: Apply button rotation to BOTTOM viewer (X=30°)")
    viewer.set_active_viewer("bottom")
    viewer.current_rot_right = {'x': 30.0, 'y': 0.0, 'z': 0.0}
    
    top_rot_after_bottom_button = viewer._extract_rotation_from_vtk_actor("top")
    bottom_rot_after_bottom_button = viewer._extract_rotation_from_vtk_actor("bottom")
    
    print(f"   TOP after bottom button:    {top_rot_after_bottom_button}")
    print(f"   BOTTOM after bottom button: {bottom_rot_after_bottom_button}")
    
    # Analysis
    print("\n📊 ANALYSIS:")
    print("✅ Button rotations should be tracked independently per viewer")
    print("✅ Each viewer should maintain its own rotation state")
    print("❌ Mouse rotations are currently NOT captured (this is the bug)")
    
    print("\n🎯 CONCLUSION:")
    print("The rotation extraction method should:")
    print("1. ✅ Track button rotations per viewer (WORKING)")
    print("2. ❌ Capture mouse rotations per viewer (NOT WORKING)")
    print("3. ✅ Be consistent within each viewer (WORKING)")
    
    app.quit()

if __name__ == "__main__":
    test_rotation_consistency()
