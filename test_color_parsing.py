#!/usr/bin/env python3

import re

# Test the exact pattern from step_loader.py
test_lines = [
    "COLOUR_RGB ( '',0.75294117647059, 0.75294117647059, 0.75294117647059 )",
    "COLOUR_RGB ( '',0.25098039215686, 0.25098039215686, 0.25098039215686 )"
]

colour_pattern = r'COLOUR_RGB\s*\(\s*[^,]*,\s*([\d\.]+)\s*,\s*([\d\.]+)\s*,\s*([\d\.]+)\s*\)'

print("Testing color parsing:")
for line in test_lines:
    matches = re.findall(colour_pattern, line)
    if matches:
        for match in matches:
            r = int(float(match[0]) * 255)
            g = int(float(match[1]) * 255)
            b = int(float(match[2]) * 255)
            print(f"Parsed: RGB({r}, {g}, {b}) from {match}")
    else:
        print(f"No match for: {line}")

print("Color parsing test complete")
