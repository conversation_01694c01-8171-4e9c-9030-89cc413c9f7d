#!/usr/bin/env python3
"""
STEP File Transformer - Apply transformations to STEP files by modifying text content
This creates proper STEP files with transformations applied when OpenCASCADE writing is not available
"""

import re
import math
import os
import shutil

class STEPTransformer:
    def __init__(self):
        self.step_content = ""
        self.original_filename = ""
        
    def load_step_file(self, filename):
        """Load STEP file content"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                self.step_content = f.read()
            self.original_filename = filename
            print(f"✅ STEP TRANSFORMER: Loaded {filename} ({len(self.step_content)} chars)")
            return True
        except Exception as e:
            print(f"❌ STEP TRANSFORMER: Failed to load {filename}: {e}")
            return False
    
    def apply_transformation(self, rotation_x=0, rotation_y=0, rotation_z=0, 
                           translation_x=0, translation_y=0, translation_z=0):
        """Apply rotation and translation transformations to all geometry points"""
        print(f"🔧 STEP TRANSFORMER: Applying transformations:")
        print(f"   Rotation: X={rotation_x}°, Y={rotation_y}°, Z={rotation_z}°")
        print(f"   Translation: X={translation_x}, Y={translation_y}, Z={translation_z}")
        
        # Convert rotations to radians
        rx = math.radians(rotation_x)
        ry = math.radians(rotation_y)
        rz = math.radians(rotation_z)
        
        # Create rotation matrices
        cos_x, sin_x = math.cos(rx), math.sin(rx)
        cos_y, sin_y = math.cos(ry), math.sin(ry)
        cos_z, sin_z = math.cos(rz), math.sin(rz)
        
        # Combined rotation matrix (Z * Y * X order)
        r11 = cos_y * cos_z
        r12 = -cos_y * sin_z
        r13 = sin_y
        r21 = sin_x * sin_y * cos_z + cos_x * sin_z
        r22 = -sin_x * sin_y * sin_z + cos_x * cos_z
        r23 = -sin_x * cos_y
        r31 = -cos_x * sin_y * cos_z + sin_x * sin_z
        r32 = cos_x * sin_y * sin_z + sin_x * cos_z
        r33 = cos_x * cos_y
        
        def transform_point(x, y, z):
            """Apply rotation and translation to a point"""
            # Apply rotation
            x_rot = r11 * x + r12 * y + r13 * z
            y_rot = r21 * x + r22 * y + r23 * z
            z_rot = r31 * x + r32 * y + r33 * z
            
            # Apply translation
            x_final = x_rot + translation_x
            y_final = y_rot + translation_y
            z_final = z_rot + translation_z
            
            return x_final, y_final, z_final
        
        # Find and transform all CARTESIAN_POINT entries
        point_pattern = r'(#\d+\s*=\s*CARTESIAN_POINT\s*\(\s*\'[^\']*\'\s*,\s*\(\s*)([-\d.E+-]+)\s*,\s*([-\d.E+-]+)\s*,\s*([-\d.E+-]+)(\s*\)\s*\)\s*;)'
        
        def replace_point(match):
            prefix = match.group(1)
            x = float(match.group(2))
            y = float(match.group(3))
            z = float(match.group(4))
            suffix = match.group(5)
            
            # Transform the point
            x_new, y_new, z_new = transform_point(x, y, z)
            
            # Format with appropriate precision
            return f"{prefix}{x_new:.12f}, {y_new:.12f}, {z_new:.12f}{suffix}"
        
        # Count points before transformation
        matches = list(re.finditer(point_pattern, self.step_content))
        print(f"🔧 STEP TRANSFORMER: Found {len(matches)} CARTESIAN_POINT entries to transform")
        
        if len(matches) == 0:
            print("❌ STEP TRANSFORMER: No CARTESIAN_POINT entries found")
            return False
        
        # Apply transformation to all points
        self.step_content = re.sub(point_pattern, replace_point, self.step_content)

        print(f"✅ STEP TRANSFORMER: Transformed {len(matches)} points")

        # Also transform DIRECTION vectors in coordinate systems
        direction_pattern = r'(#\d+\s*=\s*DIRECTION\s*\(\s*\'[^\']*\'\s*,\s*\(\s*)([-\d.E+-]+)\s*,\s*([-\d.E+-]+)\s*,\s*([-\d.E+-]+)(\s*\)\s*\)\s*;)'

        def replace_direction(match):
            prefix = match.group(1)
            x = float(match.group(2))
            y = float(match.group(3))
            z = float(match.group(4))
            suffix = match.group(5)

            # Transform the direction vector (rotation only, no translation)
            x_new = r11 * x + r12 * y + r13 * z
            y_new = r21 * x + r22 * y + r23 * z
            z_new = r31 * x + r32 * y + r33 * z

            # Normalize the direction vector
            length = math.sqrt(x_new*x_new + y_new*y_new + z_new*z_new)
            if length > 0.0001:  # Avoid division by zero
                x_new /= length
                y_new /= length
                z_new /= length

            # Format with appropriate precision
            return f"{prefix}{x_new:.12f}, {y_new:.12f}, {z_new:.12f}{suffix}"

        # Count and transform direction vectors
        direction_matches = list(re.finditer(direction_pattern, self.step_content))
        print(f"🔧 STEP TRANSFORMER: Found {len(direction_matches)} DIRECTION entries to transform")

        if len(direction_matches) > 0:
            self.step_content = re.sub(direction_pattern, replace_direction, self.step_content)
            print(f"✅ STEP TRANSFORMER: Transformed {len(direction_matches)} direction vectors")

        return True
    
    def save_step_file(self, filename):
        """Save the transformed STEP file"""
        try:
            # Ensure we have content to save
            if not self.step_content:
                print("❌ STEP TRANSFORMER: No content to save")
                return False
            
            # Write the transformed content
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.step_content)
            
            # Verify the file was created
            if os.path.exists(filename) and os.path.getsize(filename) > 1000:
                file_size = os.path.getsize(filename)
                print(f"✅ STEP TRANSFORMER: Saved transformed STEP file: {filename}")
                print(f"✅ File size: {file_size} bytes")
                return True
            else:
                print("❌ STEP TRANSFORMER: File not created or too small")
                return False
                
        except Exception as e:
            print(f"❌ STEP TRANSFORMER: Save failed: {e}")
            return False

def test_step_transformer():
    """Test the STEP transformer"""
    print("🧪 TESTING STEP TRANSFORMER")
    print("=" * 40)
    
    # Test with the test.step file
    transformer = STEPTransformer()
    
    if not transformer.load_step_file("test.step"):
        print("❌ Failed to load test file")
        return False
    
    # Apply a 45-degree Z rotation and some translation
    success = transformer.apply_transformation(
        rotation_z=45.0,
        translation_x=10.0,
        translation_y=5.0,
        translation_z=2.0
    )
    
    if not success:
        print("❌ Transformation failed")
        return False
    
    # Save the transformed file
    output_file = "test_transformed_step.step"
    if transformer.save_step_file(output_file):
        print(f"✅ Test successful! Created: {output_file}")
        
        # Compare file sizes
        original_size = os.path.getsize("test.step")
        new_size = os.path.getsize(output_file)
        print(f"   Original: {original_size} bytes")
        print(f"   Transformed: {new_size} bytes")
        
        return True
    else:
        print("❌ Save failed")
        return False

if __name__ == "__main__":
    test_step_transformer()
