import os
import sys

class STEPLoader:
    def __init__(self):
        self.shape = None
        self.current_polydata = None
        self.original_position = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.original_orientation = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.original_colors = None
        
    def load_step_file(self, filename):
        """Load STEP file and convert to VTK polydata"""
        if not os.path.exists(filename):
            return False, f"File not found: {filename}"
        
        try:
            # Try OpenCASCADE method first
            success = self._load_with_opencascade(filename)
            if success:
                return True, "Loaded with OpenCASCADE"
                
            # Try FreeCAD method as fallback
            success = self._load_with_freecad(filename)
            if success:
                return True, "Loaded with FreeCAD"
                
            return False, "Failed to load with any method"
            
        except Exception as e:
            return False, f"Error loading file: {str(e)}"
    
    def _load_with_opencascade(self, filename):
        """Load STEP file using OpenCASCADE"""
        try:
            from OCC.Core.STEPControl_Reader import STEPControl_Reader
            from OCC.Core.IFSelect_ReturnStatus import IFSelect_RetDone
            import vtk
            
            reader = STEPControl_Reader()
            status = reader.ReadFile(filename)
            
            if status != IFSelect_RetDone:
                print("Failed to read STEP file with OpenCASCADE")
                return False
                
            reader.TransferRoots()
            self.shape = reader.OneShape()
            
            # Convert to VTK polydata
            self.current_polydata = self._shape_to_polydata(self.shape)
            
            print(f"Successfully loaded STEP file with OpenCASCADE: {filename}")
            return True
            
        except ImportError:
            print("OpenCASCADE not available")
            return False
        except Exception as e:
            print(f"OpenCASCADE loading error: {e}")
            return False
    
    def _load_with_freecad(self, filename):
        """Load STEP file using FreeCAD"""
        try:
            import FreeCAD
            import Part
            import vtk
            
            # Create temporary document
            doc = FreeCAD.newDocument("temp")
            
            # Import STEP file
            Part.insert(filename, doc.Name)
            
            # Get the imported object
            if len(doc.Objects) == 0:
                FreeCAD.closeDocument(doc.Name)
                return False
                
            obj = doc.Objects[0]
            shape = obj.Shape
            
            # Convert to mesh
            mesh = shape.tessellate(0.1)  # Tolerance
            vertices = mesh[0]
            faces = mesh[1]
            
            # Create VTK polydata
            points = vtk.vtkPoints()
            for vertex in vertices:
                points.InsertNextPoint(vertex)
            
            polys = vtk.vtkCellArray()
            for face in faces:
                polys.InsertNextCell(len(face))
                for vertex_id in face:
                    polys.InsertCellPoint(vertex_id)
            
            self.current_polydata = vtk.vtkPolyData()
            self.current_polydata.SetPoints(points)
            self.current_polydata.SetPolys(polys)
            
            FreeCAD.closeDocument(doc.Name)
            print(f"Successfully loaded STEP file with FreeCAD: {filename}")
            return True
            
        except ImportError:
            print("FreeCAD not available")
            return False
        except Exception as e:
            print(f"FreeCAD loading error: {e}")
            return False
    
    def _shape_to_polydata(self, shape):
        """Convert OpenCASCADE shape to VTK polydata"""
        try:
            import vtk
            from OCC.Core.BRepMesh_IncrementalMesh import BRepMesh_IncrementalMesh
            from OCC.Core.TopExp_Explorer import TopExp_Explorer
            from OCC.Core.TopAbs import TopAbs_FACE
            from OCC.Core.BRep_Tool import BRep_Tool
            from OCC.Core.TopLoc import TopLoc_Location
            
            # Mesh the shape
            mesh = BRepMesh_IncrementalMesh(shape, 0.1)
            mesh.Perform()
            
            points = vtk.vtkPoints()
            polys = vtk.vtkCellArray()
            
            # Extract triangles from faces
            explorer = TopExp_Explorer(shape, TopAbs_FACE)
            point_id = 0
            
            while explorer.More():
                face = explorer.Current()
                location = TopLoc_Location()
                triangulation = BRep_Tool.Triangulation(face, location)
                
                if triangulation:
                    # Add points
                    for i in range(1, triangulation.NbNodes() + 1):
                        pnt = triangulation.Node(i)
                        points.InsertNextPoint(pnt.X(), pnt.Y(), pnt.Z())
                    
                    # Add triangles
                    for i in range(1, triangulation.NbTriangles() + 1):
                        triangle = triangulation.Triangle(i)
                        n1, n2, n3 = triangle.Get()
                        
                        polys.InsertNextCell(3)
                        polys.InsertCellPoint(point_id + n1 - 1)
                        polys.InsertCellPoint(point_id + n2 - 1)
                        polys.InsertCellPoint(point_id + n3 - 1)
                    
                    point_id += triangulation.NbNodes()
                
                explorer.Next()
            
            polydata = vtk.vtkPolyData()
            polydata.SetPoints(points)
            polydata.SetPolys(polys)
            
            return polydata
            
        except Exception as e:
            print(f"Error converting shape to polydata: {e}")
            return None
    
    def save_step_file(self, filename):
        """Save the current shape to STEP file"""
        if not self.shape:
            return False
            
        try:
            from OCC.Core.STEPControl_Writer import STEPControl_Writer
            from OCC.Core.Interface_Static import Interface_Static
            
            writer = STEPControl_Writer()
            Interface_Static.SetCVal("write.step.schema", "AP203")
            
            writer.Transfer(self.shape, 1)
            status = writer.Write(filename)
            
            return status == 1
            
        except Exception as e:
            print(f"Error saving STEP file: {e}")
            return False