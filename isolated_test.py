#!/usr/bin/env python3
"""
Completely isolated test - no imports from local files
"""

print("ISOLATED TEST STARTING")
print("This should NOT trigger any STEP loading")

try:
    import vtk
    print("VTK imported successfully")
    
    # Create a simple sphere
    sphere = vtk.vtkSphereSource()
    sphere.SetRadius(1.0)
    
    print("Sphere created")
    print("TEST COMPLETED - NO STEP LOADING TRIGGERED")
    
except Exception as e:
    print(f"Error: {e}")

print("ISOLATED TEST FINISHED")
