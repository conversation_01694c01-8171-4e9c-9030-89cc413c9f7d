#!/usr/bin/env python3

print("INVESTIGATING COLOR EXTRACTION ROOT CAUSE")
print("=" * 60)

# Test 1: Check raw STEP file color values
print("Test 1: Raw STEP file color parsing...")
import re

try:
    with open('SOIC16P127_1270X940X610L89X51.STEP', 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
    
    # Find all COLOUR_RGB entries
    colour_pattern = r'COLOUR_RGB\s*\(\s*[^,]*,\s*([\d\.]+)\s*,\s*([\d\.]+)\s*,\s*([\d\.]+)\s*\)'
    matches = re.findall(colour_pattern, content)
    
    print(f"Found {len(matches)} COLOUR_RGB entries in STEP file:")
    for i, match in enumerate(matches):
        r_val, g_val, b_val = float(match[0]), float(match[1]), float(match[2])
        r, g, b = int(r_val * 255), int(g_val * 255), int(b_val * 255)
        print(f"  Color {i}: ({r_val:.6f}, {g_val:.6f}, {b_val:.6f}) → RGB({r}, {g}, {b})")
        
        # Check if this matches FreeCAD values
        if (r, g, b) == (25, 25, 25):
            print(f"    ✅ MATCHES FreeCAD dark gray!")
        elif (r, g, b) == (192, 192, 192):
            print(f"    ✅ MATCHES FreeCAD light gray!")
        elif (r, g, b) == (13, 13, 13):
            print(f"    ❌ This is our extracted dark (too dark)")
        elif (r, g, b) == (134, 134, 134):
            print(f"    ❌ This is our extracted light (too dark)")

except Exception as e:
    print(f"Error reading STEP file: {e}")

print("\n" + "-" * 40)

# Test 2: Check OpenCASCADE color extraction precision
print("Test 2: OpenCASCADE color extraction...")

try:
    from step_loader import STEPLoader
    
    loader = STEPLoader()
    loader.current_filename = 'SOIC16P127_1270X940X610L89X51.STEP'
    
    # Load with OpenCASCADE and check intermediate values
    success = loader._load_with_opencascade('SOIC16P127_1270X940X610L89X51.STEP')
    
    if success and hasattr(loader, 'xcaf_shape_colors'):
        print(f"XCAF extracted {len(loader.xcaf_shape_colors)} shape colors")
        
        # Get unique colors and their raw float values
        unique_colors = list(set(loader.xcaf_shape_colors.values()))
        print(f"Unique colors: {unique_colors}")
        
        # Check if we can access the raw Quantity_Color values
        if hasattr(loader, 'color_tool') and hasattr(loader, 'root_label'):
            print("Checking raw Quantity_Color values...")
            
            from OCC.Core.TDF import TDF_ChildIterator
            from OCC.Core.Quantity import Quantity_Color
            from OCC.Core.XCAFDoc import XCAFDoc_ColorType
            
            # Try to get raw color values before conversion
            child_iter = TDF_ChildIterator(loader.root_label)
            color_count = 0
            while child_iter.More() and color_count < 5:
                label = child_iter.Value()
                if loader.shape_tool.IsShape(label):
                    shape = loader.shape_tool.GetShape(label)
                    color = Quantity_Color()
                    
                    if loader.color_tool.GetColor(shape, XCAFDoc_ColorType.XCAFDoc_ColorSurf, color):
                        # Get raw float values
                        r_raw = color.Red()
                        g_raw = color.Green()
                        b_raw = color.Blue()
                        
                        # Convert to RGB
                        r, g, b = int(r_raw * 255), int(g_raw * 255), int(b_raw * 255)
                        
                        print(f"  Raw color {color_count}: ({r_raw:.6f}, {g_raw:.6f}, {b_raw:.6f}) → RGB({r}, {g}, {b})")
                        
                        # Check for precision issues
                        if abs(r_raw - 0.098039) < 0.001:  # 25/255 = 0.098039
                            print(f"    ✅ This should be RGB(25, 25, 25) - FreeCAD dark!")
                        elif abs(r_raw - 0.752941) < 0.001:  # 192/255 = 0.752941
                            print(f"    ✅ This should be RGB(192, 192, 192) - FreeCAD light!")
                        
                        color_count += 1
                
                child_iter.Next()
    
except Exception as e:
    print(f"Error in OpenCASCADE test: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "-" * 40)

# Test 3: Check color space or gamma issues
print("Test 3: Color space analysis...")

# Check if our values could be gamma-corrected versions
our_dark = 13
our_light = 134
freecad_dark = 25
freecad_light = 192

print(f"Our values: Dark={our_dark}, Light={our_light}")
print(f"FreeCAD values: Dark={freecad_dark}, Light={freecad_light}")

# Check gamma correction (common values: 2.2, 1.8)
for gamma in [1.8, 2.0, 2.2, 2.4]:
    corrected_dark = int((our_dark / 255.0) ** (1/gamma) * 255)
    corrected_light = int((our_light / 255.0) ** (1/gamma) * 255)
    
    print(f"Gamma {gamma}: Dark={corrected_dark}, Light={corrected_light}")
    
    if abs(corrected_dark - freecad_dark) <= 2 and abs(corrected_light - freecad_light) <= 10:
        print(f"  ✅ GAMMA {gamma} CORRECTION MATCHES FREECAD!")

print("\n" + "=" * 60)
print("INVESTIGATION COMPLETE")
