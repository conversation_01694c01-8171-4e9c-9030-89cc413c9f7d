@echo off
if "%1"=="" (
    echo Usage: test_version.bat [version_number]
    echo Example: test_version.bat 37
    exit /b 1
)

set VERSION=%1
echo Testing version %VERSION%...

echo Copying step_viewer_tdk_modular_rev%VERSION%.py to step_viewer_tdk_modular.py
if exist step_viewer_tdk_modular_rev%VERSION%.py (
    copy step_viewer_tdk_modular_rev%VERSION%.py step_viewer_tdk_modular.py
) else (
    echo WARNING: step_viewer_tdk_modular_rev%VERSION%.py not found!
)

echo Copying vtk_renderer_rev%VERSION%.py to vtk_renderer.py
if exist vtk_renderer_rev%VERSION%.py (
    copy vtk_renderer_rev%VERSION%.py vtk_renderer.py
) else (
    echo WARNING: vtk_renderer_rev%VERSION%.py not found!
)

echo Copying gui_components_rev%VERSION%.py to gui_components.py
if exist gui_components_rev%VERSION%.py (
    copy gui_components_rev%VERSION%.py gui_components.py
) else (
    echo WARNING: gui_components_rev%VERSION%.py not found!
)

echo Copying step_loader_rev%VERSION%.py to step_loader.py
if exist step_loader_rev%VERSION%.py (
    copy step_loader_rev%VERSION%.py step_loader.py
) else (
    echo WARNING: step_loader_rev%VERSION%.py not found!
)

echo Copying requirements_rev%VERSION%.txt to requirements.txt
if exist requirements_rev%VERSION%.txt (
    copy requirements_rev%VERSION%.txt requirements.txt
) else (
    echo WARNING: requirements_rev%VERSION%.txt not found!
)

echo All available files copied for version %VERSION%
echo Starting program...
python step_viewer_tdk_modular.py
