@echo off
echo === Augment Diagnostic ===

:: Check VS Code extension
echo Checking Augment extension...
code --list-extensions | findstr "augment.vscode-augment"
IF %ERRORLEVEL% NEQ 0 (
    echo [!] Augment extension not found. Install via VS Code Marketplace.
) ELSE (
    echo [+] Augment extension is installed.
)

:: Check workspace trust
echo Checking workspace trust...
IF EXIST "%APPDATA%\Code\User\workspaceTrustState.json" (
    echo [+] Workspace trust file found.
    type "%APPDATA%\Code\User\workspaceTrustState.json" | findstr /i "trusted"
) ELSE (
    echo [!] Workspace trust file not found. Open VS Code and trust the workspace manually.
)

:: Check for Augment config
echo Checking for Augment config files...
IF EXIST ".augment" (
    echo [+] .augment folder exists.
) ELSE (
    echo [!] .augment folder missing. Agent mode may not be initialized.
)

:: Check mode toggle visibility
echo NOTE: If Agent mode toggle is missing, try:
echo - Opening the exact project folder (not parent directory)
echo - Reloading VS Code window (Ctrl+Shift+P ? Reload Window)
echo - Trusting the workspace
echo - Reinstalling Augment extension

echo === Done ===
pause
