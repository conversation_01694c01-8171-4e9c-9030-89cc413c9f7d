#!/usr/bin/env python3
"""
Simple launcher for the STEP viewer with mouse rotation fix
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    print("🎯 STEP Viewer with Mouse Rotation Fix")
    print("=" * 50)
    print("Starting the 3D STEP viewer...")
    print("")
    print("📋 How to test the mouse rotation fix:")
    print("1. Load a STEP file using 'Open STEP File' button")
    print("2. Rotate with mouse by dragging the model")
    print("3. Use rotation buttons (X+15°, Y+15°, Z+15°) if desired")
    print("4. Save using green 'Save STEP File (Improved Method)' button")
    print("5. Mouse rotations will now be saved correctly!")
    print("=" * 50)
    
    # Import and run the viewer
    from step_viewer_tdk_modular_fixed import StepViewerTDK
    
    app = QApplication(sys.argv)
    app.setApplicationName("TDK STEP Viewer - Mouse Rotation Fixed")
    app.setApplicationVersion("3.1")
    
    viewer = StepViewerTDK()
    viewer.show()
    
    print("✅ Viewer is now running!")
    print("🖱️  Mouse rotation save fix is active!")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
