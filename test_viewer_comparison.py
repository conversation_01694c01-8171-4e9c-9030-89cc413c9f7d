#!/usr/bin/env python3
"""
Test to compare how the same file appears in both viewers
This will help identify coordinate system differences
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from step_viewer_tdk_modular import Step<PERSON>iewerTDK
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
import tempfile

def test_viewer_comparison():
    """Test how the same file appears in both viewers"""
    
    print("🚀 Testing viewer comparison...")
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    # Test files
    original_file = "test.step"
    if not os.path.exists(original_file):
        print(f"❌ Test file {original_file} not found")
        return
    
    # Create a rotated file first
    temp_rotated_file = tempfile.mktemp(suffix='_rotated.step')
    
    def step1_create_rotated_file():
        """Step 1: Create a rotated file"""
        print(f"\n🔧 Step 1: Creating rotated file...")
        
        # Load original in bottom viewer
        viewer.set_active_viewer("bottom")
        viewer.step_loader_right.load_step_file(original_file)
        
        # Apply 45° X rotation
        viewer.current_rot_right = {'x': 45.0, 'y': 0.0, 'z': 0.0}
        
        # Save the rotated file
        try:
            loader = viewer.step_loader_right
            current_rot = {'x': 45.0, 'y': 0.0, 'z': 0.0}
            current_pos = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            orig_rot = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            orig_pos = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            
            success = viewer._save_step_with_transformations(
                temp_rotated_file, loader, current_pos, current_rot, orig_pos, orig_rot
            )
            
            if success:
                print(f"   ✅ Rotated file created: {temp_rotated_file}")
            else:
                print(f"   ❌ Failed to create rotated file")
                return
        except Exception as e:
            print(f"   ❌ Error creating rotated file: {e}")
            return
        
        QTimer.singleShot(2000, step2_load_in_both_viewers)
    
    def step2_load_in_both_viewers():
        """Step 2: Load the rotated file in both viewers"""
        print(f"\n📂 Step 2: Loading rotated file in both viewers...")
        
        # Load in TOP viewer
        print("   Loading in TOP viewer...")
        viewer.set_active_viewer("top")
        viewer.step_loader_left.load_step_file(temp_rotated_file)
        
        # Get TOP viewer camera info
        if viewer.vtk_renderer_left and viewer.vtk_renderer_left.renderer:
            camera_top = viewer.vtk_renderer_left.renderer.GetActiveCamera()
            if camera_top:
                pos_top = camera_top.GetPosition()
                focal_top = camera_top.GetFocalPoint()
                up_top = camera_top.GetViewUp()
                print(f"   TOP camera - Pos: {pos_top}, Focal: {focal_top}, Up: {up_top}")
        
        QTimer.singleShot(1000, step3_load_in_bottom)
    
    def step3_load_in_bottom():
        """Step 3: Load in bottom viewer"""
        print("   Loading in BOTTOM viewer...")
        viewer.set_active_viewer("bottom")
        viewer.step_loader_right.load_step_file(temp_rotated_file)
        
        # Get BOTTOM viewer camera info
        if viewer.vtk_renderer_right and viewer.vtk_renderer_right.renderer:
            camera_bottom = viewer.vtk_renderer_right.renderer.GetActiveCamera()
            if camera_bottom:
                pos_bottom = camera_bottom.GetPosition()
                focal_bottom = camera_bottom.GetFocalPoint()
                up_bottom = camera_bottom.GetViewUp()
                print(f"   BOTTOM camera - Pos: {pos_bottom}, Focal: {focal_bottom}, Up: {up_bottom}")
        
        QTimer.singleShot(2000, step4_compare_orientations)
    
    def step4_compare_orientations():
        """Step 4: Compare how the model appears in both viewers"""
        print(f"\n📊 Step 4: Comparing model orientations...")
        
        # Check bounding boxes
        print("   Checking bounding boxes...")
        
        # TOP viewer bounding box
        if hasattr(viewer.vtk_renderer_left, 'step_actor') and viewer.vtk_renderer_left.step_actor:
            bounds_top = viewer.vtk_renderer_left.step_actor.GetBounds()
            print(f"   TOP bounds: {bounds_top}")
        else:
            print("   TOP: No step actor found")
        
        # BOTTOM viewer bounding box  
        if hasattr(viewer.vtk_renderer_right, 'step_actor') and viewer.vtk_renderer_right.step_actor:
            bounds_bottom = viewer.vtk_renderer_right.step_actor.GetBounds()
            print(f"   BOTTOM bounds: {bounds_bottom}")
        else:
            print("   BOTTOM: No step actor found")
        
        # Check actor orientations
        print("   Checking actor orientations...")
        
        # TOP viewer actor orientation
        if hasattr(viewer.vtk_renderer_left, 'step_actor') and viewer.vtk_renderer_left.step_actor:
            orient_top = viewer.vtk_renderer_left.step_actor.GetOrientation()
            print(f"   TOP actor orientation: {orient_top}")
        
        # BOTTOM viewer actor orientation
        if hasattr(viewer.vtk_renderer_right, 'step_actor') and viewer.vtk_renderer_right.step_actor:
            orient_bottom = viewer.vtk_renderer_right.step_actor.GetOrientation()
            print(f"   BOTTOM actor orientation: {orient_bottom}")
        
        QTimer.singleShot(2000, step5_analyze_coordinate_systems)
    
    def step5_analyze_coordinate_systems():
        """Step 5: Analyze coordinate systems in the file"""
        print(f"\n🔍 Step 5: Analyzing coordinate systems...")
        
        if os.path.exists(temp_rotated_file):
            with open(temp_rotated_file, 'r') as f:
                content = f.read()
            
            # Look for coordinate system definitions
            import re
            
            # Find AXIS2_PLACEMENT_3D entries
            axis_matches = re.findall(r'AXIS2_PLACEMENT_3D\([^)]+\)', content)
            print(f"   Found {len(axis_matches)} coordinate systems")
            
            # Find DIRECTION entries (these define the coordinate axes)
            direction_matches = re.findall(r'DIRECTION\([^)]+\)', content)
            print(f"   Found {len(direction_matches)} direction vectors")
            
            # Show first few directions to see the coordinate system
            for i, match in enumerate(direction_matches[:10]):
                print(f"   Direction {i+1}: {match}")
        
        print(f"\n🎯 ANALYSIS:")
        print(f"   The issue is likely that the coordinate system transformation")
        print(f"   in the STEP file doesn't match the VTK viewer coordinate system.")
        print(f"   STEP files use different coordinate conventions than VTK.")
        
        QTimer.singleShot(1000, cleanup_and_exit)
    
    def cleanup_and_exit():
        """Cleanup and exit"""
        print(f"\n🧹 Cleaning up...")
        if os.path.exists(temp_rotated_file):
            os.remove(temp_rotated_file)
            print(f"   Removed temp file: {temp_rotated_file}")
        
        app.quit()
    
    # Start the test sequence
    QTimer.singleShot(3000, step1_create_rotated_file)
    
    # Run the application
    app.exec_()

if __name__ == "__main__":
    test_viewer_comparison()
