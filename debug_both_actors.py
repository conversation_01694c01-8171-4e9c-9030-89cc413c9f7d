#!/usr/bin/env python3
"""
Debug Both Actors - Find out which actor is actually being displayed
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular import Step<PERSON>iewerTDK

def debug_which_actor_is_visible():
    """Debug to find out which actor is actually being displayed"""
    
    print("🔧 WHICH ACTOR IS VISIBLE DEBUG")
    print("=" * 60)
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    # Process events to ensure GUI is ready
    app.processEvents()
    time.sleep(1)
    
    # Find a STEP file to test with
    test_files = [
        "AMPHENOL_U77-A1118-200T.STEP",
        "test.step",
        "sample.stp"
    ]
    
    test_file = None
    for filename in test_files:
        if os.path.exists(filename):
            test_file = filename
            break
    
    if not test_file:
        print("❌ No STEP file found for testing")
        return False
    
    print(f"📁 Using test file: {test_file}")
    
    # Step 1: Load STEP file
    print(f"\n📋 STEP 1: LOADING {test_file}...")
    success = viewer.load_step_file_direct(test_file)
    app.processEvents()
    time.sleep(2)
    
    if not success:
        print("❌ Failed to load STEP file")
        return False
    
    renderer = viewer.vtk_renderer_left
    
    print(f"\n🔍 INITIAL STATE:")
    print(f"Multi-actors exist: {hasattr(renderer, 'step_actors') and bool(renderer.step_actors)}")
    print(f"Single-actor exists: {hasattr(renderer, 'step_actor') and bool(renderer.step_actor)}")
    
    if hasattr(renderer, 'step_actors') and renderer.step_actors:
        print(f"Multi-actors count: {len(renderer.step_actors)}")
        for i, actor in enumerate(renderer.step_actors):
            print(f"  Multi-actor {i}: Visible={actor.GetVisibility()}, Pos={actor.GetPosition()}")
    
    if hasattr(renderer, 'step_actor') and renderer.step_actor:
        print(f"Single-actor: Visible={renderer.step_actor.GetVisibility()}, Pos={renderer.step_actor.GetPosition()}")
    
    # Step 2: Test visibility by hiding one actor at a time
    print(f"\n📋 STEP 2: TESTING VISIBILITY...")
    
    # First, hide multi-actors and see if model disappears
    if hasattr(renderer, 'step_actors') and renderer.step_actors:
        print("🔧 Hiding multi-actors...")
        for actor in renderer.step_actors:
            actor.SetVisibility(False)
        renderer.render_window.Render()
        app.processEvents()
        time.sleep(2)
        
        print("👁️ Multi-actors hidden - can you still see the model? (5 seconds)")
        time.sleep(5)
        
        # Show them again
        print("🔧 Showing multi-actors again...")
        for actor in renderer.step_actors:
            actor.SetVisibility(True)
        renderer.render_window.Render()
        app.processEvents()
        time.sleep(1)
    
    # Now hide single-actor and see if model disappears
    if hasattr(renderer, 'step_actor') and renderer.step_actor:
        print("🔧 Hiding single-actor...")
        renderer.step_actor.SetVisibility(False)
        renderer.render_window.Render()
        app.processEvents()
        time.sleep(2)
        
        print("👁️ Single-actor hidden - can you still see the model? (5 seconds)")
        time.sleep(5)
        
        # Show it again
        print("🔧 Showing single-actor again...")
        renderer.step_actor.SetVisibility(True)
        renderer.render_window.Render()
        app.processEvents()
        time.sleep(1)
    
    # Step 3: Test transformations on each actor separately
    print(f"\n📋 STEP 3: TESTING TRANSFORMATIONS SEPARATELY...")
    
    viewer.active_viewer = "top"
    
    # Test multi-actor transformation
    if hasattr(renderer, 'step_actors') and renderer.step_actors:
        print("🔧 Rotating ONLY multi-actors by 45°...")
        for actor in renderer.step_actors:
            actor.RotateWXYZ(45, 0, 0, 1)
            actor.Modified()
        renderer.render_window.Render()
        app.processEvents()
        
        print("👁️ Multi-actors rotated - can you see rotation? (5 seconds)")
        time.sleep(5)
        
        # Reset multi-actors
        print("🔧 Resetting multi-actors...")
        for actor in renderer.step_actors:
            actor.SetOrientation(0, 0, 0)
            actor.Modified()
        renderer.render_window.Render()
        app.processEvents()
        time.sleep(1)
    
    # Test single-actor transformation
    if hasattr(renderer, 'step_actor') and renderer.step_actor:
        print("🔧 Rotating ONLY single-actor by 45°...")
        renderer.step_actor.RotateWXYZ(45, 0, 0, 1)
        renderer.step_actor.Modified()
        renderer.render_window.Render()
        app.processEvents()
        
        print("👁️ Single-actor rotated - can you see rotation? (5 seconds)")
        time.sleep(5)
        
        # Reset single-actor
        print("🔧 Resetting single-actor...")
        renderer.step_actor.SetOrientation(0, 0, 0)
        renderer.step_actor.Modified()
        renderer.render_window.Render()
        app.processEvents()
        time.sleep(1)
    
    # Step 4: Test the actual GUI buttons
    print(f"\n📋 STEP 4: TESTING GUI BUTTONS...")
    
    print("🔧 Using GUI rotation button...")
    viewer.rotate_shape("z", 45)
    app.processEvents()
    time.sleep(2)
    
    print("👁️ GUI rotation applied - can you see rotation? (5 seconds)")
    time.sleep(5)
    
    print("🔧 Using GUI reset button...")
    viewer.reset_to_original()
    app.processEvents()
    time.sleep(2)
    
    print("👁️ GUI reset applied - can you see reset? (5 seconds)")
    time.sleep(5)
    
    # Final state check
    print(f"\n🔍 FINAL STATE:")
    if hasattr(renderer, 'step_actors') and renderer.step_actors:
        for i, actor in enumerate(renderer.step_actors):
            print(f"  Multi-actor {i}: Visible={actor.GetVisibility()}, Pos={actor.GetPosition()}, Orient={actor.GetOrientation()}")
    
    if hasattr(renderer, 'step_actor') and renderer.step_actor:
        print(f"Single-actor: Visible={renderer.step_actor.GetVisibility()}, Pos={renderer.step_actor.GetPosition()}, Orient={renderer.step_actor.GetOrientation()}")
    
    print(f"\n🎯 CONCLUSION:")
    print("Based on the visibility tests above, we can determine:")
    print("- If model disappeared when multi-actors were hidden: MULTI-ACTORS are displayed")
    print("- If model disappeared when single-actor was hidden: SINGLE-ACTOR is displayed")
    print("- If model stayed visible in both cases: BOTH are displayed (overlapping)")
    
    # Keep window open
    print(f"\n👁️ Window will stay open for final inspection...")
    QTimer.singleShot(10000, app.quit)
    app.exec_()
    
    return True

if __name__ == "__main__":
    debug_which_actor_is_visible()
