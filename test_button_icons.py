#!/usr/bin/env python3
"""
Test script to verify the SVG button icons are rendering correctly
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QHBoxLayout, QWidget, QPushButton
from PyQt5.QtCore import QSize
from PyQt5.QtGui import QIcon, QPixmap, QPainter
from PyQt5.QtSvg import QSvgRenderer
from PyQt5.QtCore import Qt, QByteArray

class IconTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Button Icon Test - Fixed SVG Icons")
        self.setGeometry(200, 200, 800, 100)
        
        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QHBoxLayout(central_widget)
        
        # Create test buttons with all 7 view icons
        view_types = ["axonometric", "front", "top", "right", "left", "bottom", "perspective"]
        
        for view_type in view_types:
            btn = QPushButton(view_type.title())
            btn.setIcon(self.create_iso_icon(view_type))
            btn.setIconSize(QSize(28, 28))
            btn.setStyleSheet("QPushButton { background-color: #FFFFFF; border: 1px solid #A0A0A0; padding: 2px; min-width: 36px; min-height: 36px; } QPushButton:hover { background-color: #F0F8FF; border-color: #0080C0; } QPushButton:pressed { background-color: #E0F0FF; }")
            btn.setToolTip(f"{view_type.title()} View")
            layout.addWidget(btn)
    
    def create_iso_icon(self, view_type):
        """Create EXACT FreeCAD view icons using the fixed SVG data"""
        try:
            from PyQt5.QtCore import Qt, QByteArray
            from PyQt5.QtGui import QPixmap, QPainter, QIcon
            from PyQt5.QtSvg import QSvgRenderer
            print(f"🎯 Creating FreeCAD icon for: {view_type}")
        except ImportError as e:
            print(f"❌ SVG import failed: {e}")
            # Fallback to simple colored rectangles
            from PyQt5.QtCore import Qt
            from PyQt5.QtGui import QPixmap, QPainter, QPen, QIcon, QColor
            pixmap = QPixmap(32, 32)
            pixmap.fill(Qt.transparent)
            painter = QPainter(pixmap)
            painter.setPen(QPen(QColor(0, 180, 180), 2))
            painter.drawRect(4, 4, 24, 24)
            painter.end()
            return QIcon(pixmap)

        # Fixed SVG icons with proper centering and padding
        svg_data = {
            "axonometric": '''<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
                <!-- Centered isometric cube with proper padding -->
                <path fill="none" stroke="#042a2a" stroke-width="3" d="M 16,28 4,21 4,9 16,16 z"/>
                <path fill="none" stroke="#042a2a" stroke-width="3" d="M 16,28 28,21 28,9 16,16 z"/>
                <path fill="none" stroke="#042a2a" stroke-width="3" d="M 28,9 16,16 4,9 16,4 z"/>
                <path fill="none" stroke="#16d0d2" stroke-width="2" d="M 16,28 4,21 4,9 16,16 z"/>
                <path fill="none" stroke="#16d0d2" stroke-width="2" d="M 16,28 28,21 28,9 16,16 z"/>
                <path fill="none" stroke="#34e0e2" stroke-width="2" d="M 28,9 16,16 4,9 16,4 z"/>
            </svg>''',

            "front": '''<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
                <!-- Centered front view cube with proper padding -->
                <path fill="none" stroke="#042a2a" stroke-width="3" d="M 4,11 20,14 27,7 11,4 z"/>
                <path fill="none" stroke="#042a2a" stroke-width="3" d="M 27,7 27,23 20,28 20,14 z"/>
                <path fill="none" stroke="#042a2a" stroke-width="2" stroke-dasharray="2,2" d="M 11,4 11,20 4,25 4,11 z"/>
                <path fill="none" stroke="#042a2a" stroke-width="3" d="M 4,11 20,14 20,28 4,25 z"/>
                <path fill="#34e0e2" stroke="#34e0e2" stroke-width="2" d="M 4,11 11,4 27,7 20,14 z"/>
                <path fill="none" stroke="#34e0e2" stroke-width="2" d="M 27,7 27,23 20,28 20,14 z"/>
                <path fill="none" stroke="#34e0e2" stroke-width="1.5" stroke-dasharray="2,2" d="M 11,4 11,20 4,25 4,11 z"/>
                <path fill="none" stroke="#34e0e2" stroke-width="2" d="M 4,11 20,14 20,28 4,25 z"/>
            </svg>''',

            "top": '''<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
                <!-- Centered top view cube with proper padding -->
                <path fill="none" stroke="#042a2a" stroke-width="2" stroke-dasharray="2,2" d="M 11,4 11,20 4,25 4,11 z"/>
                <path fill="none" stroke="#042a2a" stroke-width="2" stroke-dasharray="2,2" d="M 11,4 27,7 27,23 11,20 z"/>
                <path fill="none" stroke="#042a2a" stroke-width="3" d="M 4,11 20,14 27,7 11,4 z"/>
                <path fill="none" stroke="#042a2a" stroke-width="3" d="M 27,7 27,23 20,28 20,14 z"/>
                <path fill="none" stroke="#042a2a" stroke-width="3" d="M 4,11 20,14 20,28 4,25 z"/>
                <path fill="#34e0e2" stroke="#042a2a" stroke-width="2" d="M 11,20 4,25 20,28 27,23 z"/>
                <path fill="none" stroke="#34e0e2" stroke-width="2" d="M 4,11 11,4 27,7 20,14 z"/>
                <path fill="none" stroke="#34e0e2" stroke-width="1.5" stroke-dasharray="2,2" d="M 11,4 11,20 4,25 4,11 z"/>
                <path fill="none" stroke="#34e0e2" stroke-width="1.5" stroke-dasharray="2,2" d="M 11,4 27,7 27,23 11,20 z"/>
            </svg>''',

            "right": '''<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
                <!-- Centered right view cube with proper padding -->
                <path fill="none" stroke="#042a2a" stroke-width="3" d="M 4,11 20,14 27,7 11,4 z"/>
                <path fill="none" stroke="#042a2a" stroke-width="3" d="M 27,7 27,23 20,28 20,14 z"/>
                <path fill="none" stroke="#042a2a" stroke-width="2" stroke-dasharray="2,2" d="M 11,4 11,20 4,25 4,11 z"/>
                <path fill="none" stroke="#042a2a" stroke-width="3" d="M 4,11 20,14 20,28 4,25 z"/>
                <path fill="#06989a" stroke="#34e0e2" stroke-width="2" d="M 27,7 27,23 20,28 20,14 z"/>
                <path fill="none" stroke="#34e0e2" stroke-width="2" d="M 4,11 11,4 27,7 20,14 z"/>
                <path fill="none" stroke="#34e0e2" stroke-width="1.5" stroke-dasharray="2,2" d="M 11,4 11,20 4,25 4,11 z"/>
                <path fill="none" stroke="#34e0e2" stroke-width="2" d="M 4,11 20,14 20,28 4,25 z"/>
            </svg>''',

            "left": '''<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
                <!-- Centered left view cube with proper padding -->
                <path fill="none" stroke="#042a2a" stroke-width="2" stroke-dasharray="2,2" d="M 11,4 11,20 4,25 4,11 z"/>
                <path fill="none" stroke="#042a2a" stroke-width="2" stroke-dasharray="2,2" d="M 11,4 27,7 27,23 11,20 z"/>
                <path fill="none" stroke="#042a2a" stroke-width="3" d="M 4,11 20,14 27,7 11,4 z"/>
                <path fill="none" stroke="#042a2a" stroke-width="3" d="M 27,7 27,23 20,28 20,14 z"/>
                <path fill="none" stroke="#042a2a" stroke-width="3" d="M 4,11 20,14 20,28 4,25 z"/>
                <path fill="#06989a" stroke="#042a2a" stroke-width="2" d="M 11,4 11,21 4,26 4,11 z"/>
                <path fill="none" stroke="#34e0e2" stroke-width="2" d="M 4,11 11,4 27,7 20,14 z"/>
                <path fill="none" stroke="#34e0e2" stroke-width="1.5" stroke-dasharray="2,2" d="M 11,4 11,20 4,25 4,11 z"/>
                <path fill="none" stroke="#34e0e2" stroke-width="1.5" stroke-dasharray="2,2" d="M 11,4 27,7 27,23 11,20 z"/>
            </svg>''',

            "bottom": '''<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
                <!-- Centered bottom view cube with proper padding -->
                <path fill="none" stroke="#042a2a" stroke-width="2" stroke-dasharray="2,2" d="M 11,4 11,20 4,25 4,11 z"/>
                <path fill="none" stroke="#042a2a" stroke-width="2" stroke-dasharray="2,2" d="M 11,4 27,7 27,23 11,20 z"/>
                <path fill="none" stroke="#042a2a" stroke-width="3" d="M 4,11 20,14 27,7 11,4 z"/>
                <path fill="none" stroke="#042a2a" stroke-width="3" d="M 27,7 27,23 20,28 20,14 z"/>
                <path fill="none" stroke="#042a2a" stroke-width="3" d="M 4,11 20,14 20,28 4,25 z"/>
                <path fill="none" stroke="#34e0e2" stroke-width="2" d="M 4,11 11,4 27,7 20,14 z"/>
                <path fill="none" stroke="#34e0e2" stroke-width="1.5" stroke-dasharray="2,2" d="M 11,4 11,20 4,25 4,11 z"/>
                <path fill="none" stroke="#34e0e2" stroke-width="1.5" stroke-dasharray="2,2" d="M 11,4 27,7 27,23 11,20 z"/>
                <path fill="none" stroke="#34e0e2" stroke-width="2" d="M 27,7 27,23 20,28 20,14 z"/>
                <path fill="none" stroke="#34e0e2" stroke-width="2" d="M 4,11 20,14 20,28 4,25 z"/>
            </svg>''',

            "perspective": '''<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
                <!-- Centered perspective cube with proper padding (similar to axonometric but slightly different) -->
                <path fill="none" stroke="#042a2a" stroke-width="3" d="M 16,28 4,21 4,9 16,16 z"/>
                <path fill="none" stroke="#042a2a" stroke-width="3" d="M 16,28 28,21 28,9 16,16 z"/>
                <path fill="none" stroke="#042a2a" stroke-width="3" d="M 28,9 16,16 4,9 16,4 z"/>
                <path fill="none" stroke="#16d0d2" stroke-width="2" d="M 16,28 4,21 4,9 16,16 z"/>
                <path fill="none" stroke="#16d0d2" stroke-width="2" d="M 16,28 28,21 28,9 16,16 z"/>
                <path fill="none" stroke="#34e0e2" stroke-width="2" d="M 28,9 16,16 4,9 16,4 z"/>
            </svg>'''
        }

        # Get the SVG data for the requested view type
        if view_type not in svg_data:
            print(f"⚠️ View type '{view_type}' not found, using axonometric")
            view_type = "axonometric"  # fallback

        svg_string = svg_data[view_type]
        print(f"✅ Got SVG data for {view_type}, length: {len(svg_string)}")

        # Create SVG renderer and render to pixmap
        svg_bytes = QByteArray(svg_string.encode('utf-8'))
        svg_renderer = QSvgRenderer(svg_bytes)

        if not svg_renderer.isValid():
            print(f"❌ SVG renderer invalid for {view_type}")
            # Fallback to simple icon
            from PyQt5.QtGui import QPen, QColor
            pixmap = QPixmap(32, 32)
            pixmap.fill(Qt.transparent)
            painter = QPainter(pixmap)
            painter.setPen(QPen(QColor(255, 0, 0), 2))
            painter.drawRect(4, 4, 24, 24)
            painter.end()
            return QIcon(pixmap)

        print(f"✅ SVG renderer valid for {view_type}")

        # Create standard 32x32 pixmap for icons
        pixmap = QPixmap(32, 32)
        pixmap.fill(Qt.transparent)

        # Render SVG with normal scaling - let the SVG itself be big
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        # Simple 1:1 rendering - the SVG should be designed to fill the space
        svg_renderer.render(painter)
        painter.end()

        print(f"🎨 Rendered {view_type} icon successfully")
        return QIcon(pixmap)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = IconTestWindow()
    window.show()
    sys.exit(app.exec_())
