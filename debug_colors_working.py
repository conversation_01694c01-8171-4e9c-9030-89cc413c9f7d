#!/usr/bin/env python3

print("DEBUGGING COLORS TO MAKE SURE THEY ARE WORKING")

from step_loader import STEPLoader

# Load STEP file
loader = STEPLoader()
success, msg = loader.load_step_file('SOIC16P127_1270X940X610L89X51.STEP')

if success and loader.current_polydata:
    polydata = loader.current_polydata
    colors_array = polydata.GetCellData().GetScalars("Colors")
    
    if colors_array:
        num_colors = colors_array.GetNumberOfTuples()
        
        # Count actual colors applied
        color_counts = {}
        for i in range(num_colors):
            r = int(colors_array.GetComponent(i, 0))
            g = int(colors_array.GetComponent(i, 1))
            b = int(colors_array.GetComponent(i, 2))
            color = (r, g, b)
            color_counts[color] = color_counts.get(color, 0) + 1
        
        print(f"COLORS APPLIED TO {num_colors} CELLS:")
        for color, count in color_counts.items():
            percentage = (count / num_colors) * 100
            print(f"  RGB{color}: {count} cells ({percentage:.1f}%)")
        
        # Check if we have the expected STEP file colors
        expected_light = (192, 192, 192)
        expected_dark = (63, 63, 63)
        
        if expected_light in color_counts and expected_dark in color_counts:
            print("SUCCESS: Both expected colors found")
            print(f"Light silver: {color_counts[expected_light]} cells")
            print(f"Dark silver: {color_counts[expected_dark]} cells")
            print("COLORS ARE WORKING CORRECTLY")
        else:
            print("FAILURE: Expected colors not found")
            print(f"Expected light silver {expected_light}: {'FOUND' if expected_light in color_counts else 'MISSING'}")
            print(f"Expected dark silver {expected_dark}: {'FOUND' if expected_dark in color_counts else 'MISSING'}")
            print("COLORS ARE NOT WORKING")
    else:
        print("FAILURE: No colors found in polydata")
        print("COLORS ARE NOT WORKING")
else:
    print("FAILURE: STEP file loading failed")
    print("COLORS ARE NOT WORKING")

print("COLOR DEBUG COMPLETE")
