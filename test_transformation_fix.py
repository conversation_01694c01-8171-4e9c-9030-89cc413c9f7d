#!/usr/bin/env python3
"""
Test and fix the transformation issues in Option 1 and Option 2
"""

import os
import sys
import shutil
import time
from datetime import datetime

def get_file_info(filename):
    """Get detailed file information"""
    if os.path.exists(filename):
        stat = os.stat(filename)
        return {
            'exists': True,
            'size': stat.st_size,
            'mtime': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S.%f')[:-3],
            'readable': os.access(filename, os.R_OK)
        }
    else:
        return {'exists': False, 'size': 0, 'mtime': 'N/A', 'readable': False}

def test_timestamp_fix():
    """Test the timestamp fix for Original Save"""
    print("=" * 80)
    print("🧪 TESTING TIMESTAMP FIX FOR ORIGINAL SAVE")
    print("=" * 80)
    
    # Check if test.step exists
    test_file = "test.step"
    if not os.path.exists(test_file):
        print(f"❌ ERROR: {test_file} not found!")
        return False
    
    original_info = get_file_info(test_file)
    print(f"📁 Original file: {test_file}")
    print(f"   Size: {original_info['size']} bytes")
    print(f"   Modified: {original_info['mtime']}")
    
    # Test files
    test_files = {
        'original_new': 'test_timestamp_original_new.step',
        'original_existing': 'test_timestamp_original_existing.step'
    }
    
    # Remove any existing test files
    for filename in test_files.values():
        if os.path.exists(filename):
            os.remove(filename)
    
    # Create existing file
    with open(test_files['original_existing'], 'w') as f:
        f.write("DUMMY EXISTING FILE FOR TIMESTAMP TEST\n")
    
    before_info = get_file_info(test_files['original_existing'])
    print(f"\n📝 Test 1: Original Save to New File")
    print(f"   Target: {test_files['original_new']}")
    
    # Test new file save
    try:
        shutil.copy(test_file, test_files['original_new'])  # copy() updates timestamp
        new_info = get_file_info(test_files['original_new'])
        
        print(f"   Result: Size={new_info['size']} bytes, Time={new_info['mtime']}")
        
        if new_info['size'] == original_info['size']:
            print(f"   ✅ SUCCESS: New file created with correct size")
        else:
            print(f"   ❌ FAILED: Size mismatch")
            
    except Exception as e:
        print(f"   ❌ EXCEPTION: {e}")
    
    print(f"\n📝 Test 2: Original Save to Existing File (Overwrite)")
    print(f"   Target: {test_files['original_existing']}")
    print(f"   Before: Size={before_info['size']} bytes, Time={before_info['mtime']}")
    
    time.sleep(1)  # Ensure timestamp difference
    
    # Test existing file overwrite
    try:
        shutil.copy(test_file, test_files['original_existing'])  # copy() updates timestamp
        after_info = get_file_info(test_files['original_existing'])
        
        print(f"   After:  Size={after_info['size']} bytes, Time={after_info['mtime']}")
        
        if (after_info['size'] == original_info['size'] and 
            after_info['mtime'] != before_info['mtime']):
            print(f"   ✅ SUCCESS: File overwritten with updated timestamp")
            print(f"   ✅ SIZE: {before_info['size']} → {after_info['size']} bytes")
            print(f"   ✅ TIME: Updated from {before_info['mtime']} to {after_info['mtime']}")
        else:
            print(f"   ❌ FAILED: Timestamp not updated or size wrong")
            print(f"      Size match: {after_info['size'] == original_info['size']}")
            print(f"      Time updated: {after_info['mtime'] != before_info['mtime']}")
            
    except Exception as e:
        print(f"   ❌ EXCEPTION: {e}")
    
    # Cleanup
    print(f"\n🧹 Cleaning up test files...")
    for filename in test_files.values():
        if os.path.exists(filename):
            os.remove(filename)
            print(f"   Removed: {filename}")
    
    print(f"\n✅ Timestamp fix test completed!")
    return True

def test_transformation_understanding():
    """Test what the current transformations actually mean"""
    print("\n" + "=" * 80)
    print("🧪 TESTING TRANSFORMATION UNDERSTANDING")
    print("=" * 80)
    
    try:
        # Import the main program
        from step_viewer_tdk_modular import StepViewerTDK
        from PyQt5.QtWidgets import QApplication
        
        # Create QApplication if needed
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("✅ Successfully imported StepViewerTDK")
        
        # Create viewer instance
        viewer = StepViewerTDK()
        
        # Load the test file
        test_file = "test.step"
        print(f"\n📝 Loading test file: {test_file}")
        success, msg = viewer.step_loader_left.load_step_file(test_file)
        
        if not success:
            print(f"❌ Failed to load {test_file}: {msg}")
            return False
        
        print(f"✅ Loaded {test_file} successfully")
        
        # Check current transformation values
        viewer.active_viewer = "top"
        
        # Get current values
        current_pos = getattr(viewer, 'current_pos_left', {'x': 0, 'y': 0, 'z': 0})
        current_rot = getattr(viewer, 'current_rot_left', {'x': 0, 'y': 0, 'z': 0})
        
        print(f"\n🔧 Current transformation values:")
        print(f"   Position: {current_pos}")
        print(f"   Rotation: {current_rot}")
        
        # Set some test values
        viewer.current_pos_left = {'x': 10.0, 'y': 5.0, 'z': 2.0}
        viewer.current_rot_left = {'x': 45.0, 'y': 30.0, 'z': 15.0}
        
        print(f"\n🔧 Set test transformation values:")
        print(f"   Position: {viewer.current_pos_left}")
        print(f"   Rotation: {viewer.current_rot_left}")
        
        print(f"\n💡 ANALYSIS:")
        print(f"   These values represent the GUI display transformations")
        print(f"   Position: Translation in mm (X, Y, Z)")
        print(f"   Rotation: Rotation in degrees (X, Y, Z)")
        print(f"   Problem: SimpleSTEPModifier can't parse this STEP file's coordinate system")
        print(f"   Solution: Need alternative transformation method")
        
        return True
        
    except Exception as e:
        print(f"❌ FAILED TO TEST TRANSFORMATIONS: {e}")
        return False

if __name__ == "__main__":
    print("🧪 COMPREHENSIVE TRANSFORMATION AND TIMESTAMP FIX TEST")
    
    # Test timestamp fix
    test_timestamp_fix()
    
    # Test transformation understanding
    test_transformation_understanding()
    
    print(f"\n📊 SUMMARY:")
    print(f"✅ Timestamp fix: Use shutil.copy() instead of shutil.copy2()")
    print(f"⚠️  Transformation issue: SimpleSTEPModifier can't parse this STEP file format")
    print(f"💡 Next step: Implement alternative transformation method")
