#!/usr/bin/env python3
"""
STEP File Placement Editor
Directly modifies STEP file placement/transformation values without changing geometry
"""

import re
import math
import os

class STEPPlacementEditor:
    def __init__(self):
        self.step_content = ""
        self.placement_entities = []
        
    def load_step_file(self, filename):
        """Load STEP file content"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                self.step_content = f.read()
            print(f"✅ Loaded STEP file: {filename}")
            return True
        except Exception as e:
            print(f"❌ Failed to load STEP file: {e}")
            return False
    
    def find_placement_entities(self):
        """Find all placement-related entities in STEP file"""
        self.placement_entities = []

        # Look for AXIS2_PLACEMENT_3D entities - corrected pattern
        axis2_pattern = r'#(\d+)\s*=\s*AXIS2_PLACEMENT_3D\s*\(\s*\'([^\']*)\'\s*,\s*#(\d+)\s*,\s*#(\d+)\s*,\s*#(\d+)\s*\)\s*;'
        axis2_matches = re.findall(axis2_pattern, self.step_content)

        # Look for CARTESIAN_POINT entities - corrected pattern with spaces
        point_pattern = r'#(\d+)\s*=\s*CARTESIAN_POINT\s*\(\s*\'([^\']*)\'\s*,\s*\(\s*([-\d\.E\+\-]+)\s*,\s*([-\d\.E\+\-]+)\s*,\s*([-\d\.E\+\-]+)\s*\)\s*\)\s*;'
        point_matches = re.findall(point_pattern, self.step_content)

        # Look for DIRECTION entities - corrected pattern with spaces
        direction_pattern = r'#(\d+)\s*=\s*DIRECTION\s*\(\s*\'([^\']*)\'\s*,\s*\(\s*([-\d\.E\+\-]+)\s*,\s*([-\d\.E\+\-]+)\s*,\s*([-\d\.E\+\-]+)\s*\)\s*\)\s*;'
        direction_matches = re.findall(direction_pattern, self.step_content)

        print(f"🔍 Found {len(axis2_matches)} AXIS2_PLACEMENT_3D entities")
        print(f"🔍 Found {len(point_matches)} CARTESIAN_POINT entities")
        print(f"🔍 Found {len(direction_matches)} DIRECTION entities")

        # Store entities for modification
        self.axis2_entities = {match[0]: match for match in axis2_matches}
        self.point_entities = {match[0]: match for match in point_matches}
        self.direction_entities = {match[0]: match for match in direction_matches}

        return len(axis2_matches) > 0
    
    def create_transformation_matrix(self, rotation_deg, translation):
        """Create transformation matrix from rotation (degrees) and translation"""
        rx, ry, rz = math.radians(rotation_deg['x']), math.radians(rotation_deg['y']), math.radians(rotation_deg['z'])
        
        # Create rotation matrices
        cos_x, sin_x = math.cos(rx), math.sin(rx)
        cos_y, sin_y = math.cos(ry), math.sin(ry)
        cos_z, sin_z = math.cos(rz), math.sin(rz)
        
        # Combined rotation matrix (Z * Y * X order)
        r11 = cos_y * cos_z
        r12 = -cos_y * sin_z
        r13 = sin_y
        
        r21 = sin_x * sin_y * cos_z + cos_x * sin_z
        r22 = -sin_x * sin_y * sin_z + cos_x * cos_z
        r23 = -sin_x * cos_y
        
        r31 = -cos_x * sin_y * cos_z + sin_x * sin_z
        r32 = cos_x * sin_y * sin_z + sin_x * cos_z
        r33 = cos_x * cos_y
        
        return {
            'origin': (translation['x'], translation['y'], translation['z']),
            'z_axis': (r13, r23, r33),  # Z direction
            'x_axis': (r11, r21, r31)   # X direction (Y is computed as Z cross X)
        }
    
    def find_main_coordinate_system(self):
        """Find the main coordinate system referenced in ADVANCED_BREP_SHAPE_REPRESENTATION"""
        # Look for ADVANCED_BREP_SHAPE_REPRESENTATION - more flexible pattern
        shape_rep_pattern = r'#(\d+)\s*=\s*ADVANCED_BREP_SHAPE_REPRESENTATION\s*\([^;]+\)\s*;'
        shape_rep_matches = re.findall(shape_rep_pattern, self.step_content)

        if not shape_rep_matches:
            print("❌ No ADVANCED_BREP_SHAPE_REPRESENTATION found")
            # Try to find the last AXIS2_PLACEMENT_3D in the file (often the global one)
            axis2_ids = list(self.axis2_entities.keys())
            if axis2_ids:
                # Sort by ID number and take the highest (last in file)
                axis2_ids_int = [(int(id_str), id_str) for id_str in axis2_ids]
                axis2_ids_int.sort()
                main_axis2_id = axis2_ids_int[-1][1]  # Get the string ID
                print(f"🔍 Using last AXIS2_PLACEMENT_3D as main coordinate system: #{main_axis2_id}")
                return main_axis2_id
            return None

        print(f"🔍 Found {len(shape_rep_matches)} ADVANCED_BREP_SHAPE_REPRESENTATION entities")

        # Find the last AXIS2_PLACEMENT_3D referenced in the shape representation
        # This is typically the global coordinate system
        axis2_refs = []
        for match in shape_rep_matches:
            # Find the line with this entity - more flexible pattern
            line_pattern = f"#{match}\\s*=\\s*ADVANCED_BREP_SHAPE_REPRESENTATION[^;]+;"
            line_match = re.search(line_pattern, self.step_content)
            if line_match:
                line = line_match.group(0)
                print(f"🔍 Shape representation line: {line[:100]}...")
                # Find all #numbers in this line
                refs = re.findall(r'#(\d+)', line)
                # Check which ones are AXIS2_PLACEMENT_3D
                for ref in refs:
                    if ref in self.axis2_entities:
                        axis2_refs.append(ref)
                        print(f"🔍 Found AXIS2_PLACEMENT_3D reference: #{ref}")

        if axis2_refs:
            # Use the last one (typically the global coordinate system)
            main_axis2_id = axis2_refs[-1]
            print(f"🔍 Selected main coordinate system: AXIS2_PLACEMENT_3D #{main_axis2_id}")
            return main_axis2_id

        print("❌ No AXIS2_PLACEMENT_3D found in shape representation")
        # Fallback: use the last AXIS2_PLACEMENT_3D in the file
        axis2_ids = list(self.axis2_entities.keys())
        if axis2_ids:
            axis2_ids_int = [(int(id_str), id_str) for id_str in axis2_ids]
            axis2_ids_int.sort()
            main_axis2_id = axis2_ids_int[-1][1]
            print(f"🔍 Fallback: using last AXIS2_PLACEMENT_3D: #{main_axis2_id}")
            return main_axis2_id

        return None

    def modify_placement_from_matrix(self, transform_matrix, translation):
        """Modify placement values in STEP file using transformation matrix directly"""
        if not self.axis2_entities:
            print("❌ No AXIS2_PLACEMENT_3D entities found to modify")
            return False

        # Find the main coordinate system
        main_axis2_id = self.find_main_coordinate_system()
        if not main_axis2_id:
            print("❌ Could not find main coordinate system, using first AXIS2_PLACEMENT_3D")
            main_axis2_id = list(self.axis2_entities.keys())[0]

        # Extract transformation vectors directly from matrix
        # Z-axis (normal) is the 3rd column of rotation matrix
        z_axis = [
            transform_matrix.GetElement(0, 2),
            transform_matrix.GetElement(1, 2),
            transform_matrix.GetElement(2, 2)
        ]

        # X-axis (reference direction) is the 1st column of rotation matrix
        x_axis = [
            transform_matrix.GetElement(0, 0),
            transform_matrix.GetElement(1, 0),
            transform_matrix.GetElement(2, 0)
        ]

        # Origin is the translation part
        origin = [
            translation['x'],
            translation['y'],
            translation['z']
        ]

        transform = {
            'origin': origin,
            'z_axis': z_axis,
            'x_axis': x_axis
        }

        print(f"🔧 Applying transformation:")
        print(f"   Origin: {transform['origin']}")
        print(f"   Z-axis: {transform['z_axis']}")
        print(f"   X-axis: {transform['x_axis']}")

        modifications_made = 0

        # Modify the main AXIS2_PLACEMENT_3D
        if main_axis2_id in self.axis2_entities:
            entity_data = self.axis2_entities[main_axis2_id]
            entity_id, name, point_id, z_dir_id, x_dir_id = entity_data

            print(f"🔧 Modifying main coordinate system AXIS2_PLACEMENT_3D #{entity_id}")

            # Modify the origin point
            if point_id in self.point_entities:
                old_point = self.point_entities[point_id]
                # Match the exact format with proper spacing
                old_pattern = f"#{point_id} = CARTESIAN_POINT ( '{old_point[1]}',  ( {old_point[2]}, {old_point[3]}, {old_point[4]} ) ) ;"
                new_pattern = f"#{point_id} = CARTESIAN_POINT ( '{old_point[1]}',  ( {transform['origin'][0]:.15f}, {transform['origin'][1]:.15f}, {transform['origin'][2]:.15f} ) ) ;"

                if old_pattern in self.step_content:
                    self.step_content = self.step_content.replace(old_pattern, new_pattern)
                    print(f"   ✅ Updated main origin point #{point_id}")
                    modifications_made += 1
                else:
                    print(f"   ❌ Pattern not found for main origin point #{point_id}")
                    print(f"      Looking for: {old_pattern}")

            # Modify Z direction
            if z_dir_id in self.direction_entities:
                old_dir = self.direction_entities[z_dir_id]
                # Match the exact format with proper spacing
                old_pattern = f"#{z_dir_id} = DIRECTION ( '{old_dir[1]}',  ( {old_dir[2]}, {old_dir[3]}, {old_dir[4]} ) ) ;"
                new_pattern = f"#{z_dir_id} = DIRECTION ( '{old_dir[1]}',  ( {transform['z_axis'][0]:.15f}, {transform['z_axis'][1]:.15f}, {transform['z_axis'][2]:.15f} ) ) ;"

                if old_pattern in self.step_content:
                    self.step_content = self.step_content.replace(old_pattern, new_pattern)
                    print(f"   ✅ Updated main Z direction #{z_dir_id}")
                    modifications_made += 1
                else:
                    print(f"   ❌ Pattern not found for main Z direction #{z_dir_id}")
                    print(f"      Looking for: {old_pattern}")

            # Modify X direction
            if x_dir_id in self.direction_entities:
                old_dir = self.direction_entities[x_dir_id]
                # Match the exact format with proper spacing
                old_pattern = f"#{x_dir_id} = DIRECTION ( '{old_dir[1]}',  ( {old_dir[2]}, {old_dir[3]}, {old_dir[4]} ) ) ;"
                new_pattern = f"#{x_dir_id} = DIRECTION ( '{old_dir[1]}',  ( {transform['x_axis'][0]:.15f}, {transform['x_axis'][1]:.15f}, {transform['x_axis'][2]:.15f} ) ) ;"

                if old_pattern in self.step_content:
                    self.step_content = self.step_content.replace(old_pattern, new_pattern)
                    print(f"   ✅ Updated main X direction #{x_dir_id}")
                    modifications_made += 1
                else:
                    print(f"   ❌ Pattern not found for main X direction #{x_dir_id}")
                    print(f"      Looking for: {old_pattern}")
        else:
            print(f"❌ Main coordinate system #{main_axis2_id} not found in entities")

        print(f"✅ Made {modifications_made} modifications to STEP file")
        return modifications_made > 0
    
    def save_step_file(self, filename):
        """Save modified STEP file"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.step_content)
            print(f"✅ Saved modified STEP file: {filename}")
            return True
        except Exception as e:
            print(f"❌ Failed to save STEP file: {e}")
            return False
    
    def read_current_placement(self):
        """Read current placement values from STEP file"""
        if not self.axis2_entities:
            return None
        
        # Get first AXIS2_PLACEMENT_3D
        entity_id, entity_data = list(self.axis2_entities.items())[0]
        entity_id, name, point_id, z_dir_id, x_dir_id = entity_data
        
        placement = {}
        
        # Get origin
        if point_id in self.point_entities:
            point_data = self.point_entities[point_id]
            placement['origin'] = (float(point_data[2]), float(point_data[3]), float(point_data[4]))
        
        # Get Z direction
        if z_dir_id in self.direction_entities:
            dir_data = self.direction_entities[z_dir_id]
            placement['z_axis'] = (float(dir_data[2]), float(dir_data[3]), float(dir_data[4]))
        
        # Get X direction
        if x_dir_id in self.direction_entities:
            dir_data = self.direction_entities[x_dir_id]
            placement['x_axis'] = (float(dir_data[2]), float(dir_data[3]), float(dir_data[4]))
        
        return placement
