#!/usr/bin/env python3
"""
Simple test program to verify camera reading logic
Tests VTK camera orientation reading without the complex GUI
"""

import vtk
import sys

def test_camera_orientation():
    """Test basic VTK camera orientation reading"""
    print("=== VTK Camera Orientation Test ===")
    
    # Create basic VTK setup
    renderer = vtk.vtkRenderer()
    render_window = vtk.vtkRenderWindow()
    render_window.AddRenderer(renderer)
    
    # Create a simple cube for testing
    cube_source = vtk.vtkCubeSource()
    mapper = vtk.vtkPolyDataMapper()
    mapper.SetInputConnection(cube_source.GetOutputPort())
    
    actor = vtk.vtkActor()
    actor.SetMapper(mapper)
    renderer.AddActor(actor)
    
    # Get the camera
    camera = renderer.GetActiveCamera()
    
    print("1. INITIAL CAMERA STATE:")
    print(f"   Position: {camera.GetPosition()}")
    print(f"   Focal Point: {camera.GetFocalPoint()}")
    print(f"   View Up: {camera.GetViewUp()}")
    print(f"   Orientation: {camera.GetOrientation()}")
    
    # Test camera positioning like VTK does for model viewing
    print("\n2. POSITIONING CAMERA FOR MODEL VIEW:")
    camera.SetPosition(0, 0, 10)
    camera.SetFocalPoint(0, 0, 0)
    camera.SetViewUp(0, 1, 0)
    
    print(f"   Position: {camera.GetPosition()}")
    print(f"   Focal Point: {camera.GetFocalPoint()}")
    print(f"   View Up: {camera.GetViewUp()}")
    print(f"   Orientation: {camera.GetOrientation()}")
    
    # Test some rotations
    print("\n3. TESTING CAMERA ROTATIONS:")
    
    # Rotate around Y axis
    camera.Azimuth(30)
    print(f"   After Azimuth(30): {camera.GetOrientation()}")
    
    # Rotate around X axis  
    camera.Elevation(45)
    print(f"   After Elevation(45): {camera.GetOrientation()}")
    
    # More rotation
    camera.Roll(15)
    print(f"   After Roll(15): {camera.GetOrientation()}")
    
    # Test storing and restoring camera state
    print("\n4. TESTING CAMERA STATE STORAGE:")
    stored_position = camera.GetPosition()
    stored_focal = camera.GetFocalPoint()
    stored_up = camera.GetViewUp()
    stored_orientation = camera.GetOrientation()
    
    print(f"   STORED - Position: {stored_position}")
    print(f"   STORED - Focal: {stored_focal}")
    print(f"   STORED - Up: {stored_up}")
    print(f"   STORED - Orientation: {stored_orientation}")
    
    # Change camera
    camera.SetPosition(5, 5, 5)
    camera.SetFocalPoint(1, 1, 1)
    print(f"   CHANGED - Orientation: {camera.GetOrientation()}")
    
    # Restore camera
    camera.SetPosition(*stored_position)
    camera.SetFocalPoint(*stored_focal)
    camera.SetViewUp(*stored_up)
    print(f"   RESTORED - Orientation: {camera.GetOrientation()}")
    
    # Verify restoration worked
    restored_orientation = camera.GetOrientation()
    if abs(restored_orientation[0] - stored_orientation[0]) < 0.001 and \
       abs(restored_orientation[1] - stored_orientation[1]) < 0.001 and \
       abs(restored_orientation[2] - stored_orientation[2]) < 0.001:
        print("   ✅ CAMERA RESTORATION SUCCESSFUL!")
        return True
    else:
        print("   ❌ CAMERA RESTORATION FAILED!")
        print(f"   Expected: {stored_orientation}")
        print(f"   Got: {restored_orientation}")
        return False

def test_actor_vs_camera():
    """Test the difference between actor transforms and camera orientation"""
    print("\n=== ACTOR vs CAMERA Test ===")
    
    renderer = vtk.vtkRenderer()
    render_window = vtk.vtkRenderWindow()
    render_window.AddRenderer(renderer)
    
    # Create cube
    cube_source = vtk.vtkCubeSource()
    mapper = vtk.vtkPolyDataMapper()
    mapper.SetInputConnection(cube_source.GetOutputPort())
    
    actor = vtk.vtkActor()
    actor.SetMapper(mapper)
    renderer.AddActor(actor)
    
    camera = renderer.GetActiveCamera()
    
    print("1. INITIAL STATE:")
    print(f"   Actor Position: {actor.GetPosition()}")
    print(f"   Actor Orientation: {actor.GetOrientation()}")
    print(f"   Camera Orientation: {camera.GetOrientation()}")
    
    print("\n2. ROTATING ACTOR:")
    actor.SetOrientation(30, 45, 15)
    print(f"   Actor Orientation: {actor.GetOrientation()}")
    print(f"   Camera Orientation: {camera.GetOrientation()}")
    print("   → Actor rotation does NOT change camera orientation")
    
    print("\n3. ROTATING CAMERA:")
    camera.Azimuth(30)
    camera.Elevation(45)
    print(f"   Actor Orientation: {actor.GetOrientation()}")
    print(f"   Camera Orientation: {camera.GetOrientation()}")
    print("   → Camera rotation does NOT change actor orientation")
    
    print("\n✅ CONCLUSION: Actor and Camera orientations are INDEPENDENT")

if __name__ == "__main__":
    print("VTK Camera Debug Test Program")
    print("=" * 50)
    
    try:
        # Test basic camera functionality
        success = test_camera_orientation()
        
        # Test actor vs camera difference
        test_actor_vs_camera()
        
        if success:
            print("\n🎯 ALL TESTS PASSED - Camera logic is working correctly!")
            sys.exit(0)
        else:
            print("\n❌ TESTS FAILED - Camera logic needs fixing!")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 ERROR: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
