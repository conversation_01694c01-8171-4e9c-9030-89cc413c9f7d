#!/usr/bin/env python3
"""
DEBUG PROGRAM - Test rotation tracking and color handling
This program will systematically test and fix:
1. Mouse rotation should update axis values in display
2. Rotation buttons should update axis values in display  
3. Colors should be light silver and dark silver for body parts
"""

import sys
import os
from PyQt5.QtWidgets import <PERSON>A<PERSON>lication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QLabel
from PyQt5.QtCore import QTimer
import vtk
from vtk.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor

class DebugRotationColors(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("DEBUG: Rotation & Colors Test")
        self.setGeometry(100, 100, 1000, 700)
        
        # Initialize tracking variables
        self.current_rot = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_axis = {'x': 0.0, 'y': 0.0, 'z': 1.0}
        self.current_angle = 0.0
        
        # VTK components
        self.vtk_widget = None
        self.renderer = None
        self.step_actor = None
        self.interactor = None
        
        self.init_ui()
        self.setup_vtk()
        
        # Timer to track mouse rotation
        self.rotation_timer = QTimer()
        self.rotation_timer.timeout.connect(self.update_rotation_from_mouse)
        self.rotation_timer.start(100)  # Update every 100ms
        
    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QHBoxLayout(central_widget)
        
        # Left panel - controls
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_panel.setFixedWidth(300)
        
        # Load button
        self.load_btn = QPushButton("Load STEP File")
        self.load_btn.clicked.connect(self.load_step_file)
        left_layout.addWidget(self.load_btn)
        
        # Rotation buttons
        left_layout.addWidget(QLabel("ROTATION BUTTONS:"))
        
        rot_layout = QHBoxLayout()
        self.x_plus_btn = QPushButton("X+15°")
        self.x_plus_btn.clicked.connect(lambda: self.rotate_button('x', 15))
        self.y_plus_btn = QPushButton("Y+15°")
        self.y_plus_btn.clicked.connect(lambda: self.rotate_button('y', 15))
        self.z_plus_btn = QPushButton("Z+15°")
        self.z_plus_btn.clicked.connect(lambda: self.rotate_button('z', 15))
        
        rot_layout.addWidget(self.x_plus_btn)
        rot_layout.addWidget(self.y_plus_btn)
        rot_layout.addWidget(self.z_plus_btn)
        left_layout.addLayout(rot_layout)
        
        # Color test buttons
        left_layout.addWidget(QLabel("COLOR TESTS:"))
        
        self.color_silver_btn = QPushButton("Apply Silver Colors")
        self.color_silver_btn.clicked.connect(self.apply_silver_colors)
        left_layout.addWidget(self.color_silver_btn)
        
        self.color_debug_btn = QPushButton("Debug Colors")
        self.color_debug_btn.clicked.connect(self.debug_colors)
        left_layout.addWidget(self.color_debug_btn)
        
        # Display values
        left_layout.addWidget(QLabel("CURRENT VALUES:"))
        self.rot_label = QLabel("ROT: X=0.0° Y=0.0° Z=0.0°")
        self.axis_label = QLabel("AXIS: X=0.0 Y=0.0 Z=1.0")
        self.angle_label = QLabel("ANGLE: 0.0°")
        
        left_layout.addWidget(self.rot_label)
        left_layout.addWidget(self.axis_label)
        left_layout.addWidget(self.angle_label)
        
        # Debug output
        left_layout.addWidget(QLabel("DEBUG OUTPUT:"))
        self.debug_label = QLabel("Ready...")
        self.debug_label.setWordWrap(True)
        self.debug_label.setStyleSheet("background-color: #f0f0f0; padding: 5px;")
        left_layout.addWidget(self.debug_label)
        
        layout.addWidget(left_panel)
        
        # Right panel - VTK viewer
        self.vtk_widget = QVTKRenderWindowInteractor()
        layout.addWidget(self.vtk_widget)
        
    def setup_vtk(self):
        """Setup VTK renderer"""
        self.renderer = vtk.vtkRenderer()
        self.renderer.SetBackground(0.1, 0.1, 0.2)  # Dark blue background
        
        self.vtk_widget.GetRenderWindow().AddRenderer(self.renderer)
        self.interactor = self.vtk_widget.GetRenderWindow().GetInteractor()
        
        # Add interactor observer for mouse rotation tracking
        self.interactor.AddObserver("InteractionEvent", self.on_mouse_interaction)
        
        self.vtk_widget.Initialize()
        self.vtk_widget.Start()
        
    def load_step_file(self):
        """Load a STEP file for testing"""
        from PyQt5.QtWidgets import QFileDialog
        filename, _ = QFileDialog.getOpenFileName(self, "Load STEP File", "", "STEP Files (*.step *.stp)")
        
        if filename:
            self.debug_output(f"Loading: {filename}")
            
            # Use our step_loader
            try:
                from step_loader import STEPLoader
                loader = STEPLoader()
                success, message = loader.load_step_file(filename)
                
                if success and loader.current_polydata:
                    self.display_polydata(loader.current_polydata)
                    self.debug_output(f"✅ Loaded successfully: {message}")
                else:
                    self.debug_output(f"❌ Load failed: {message}")
                    
            except Exception as e:
                self.debug_output(f"❌ Load error: {e}")
                
    def display_polydata(self, polydata):
        """Display polydata in VTK viewer"""
        try:
            # Remove existing actor
            if self.step_actor:
                self.renderer.RemoveActor(self.step_actor)
                
            # Create mapper and actor
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputData(polydata)
            
            self.step_actor = vtk.vtkActor()
            self.step_actor.SetMapper(mapper)
            
            # Check for existing colors
            cell_colors = polydata.GetCellData().GetScalars()
            if cell_colors:
                self.debug_output(f"✅ Found {cell_colors.GetNumberOfTuples()} cell colors")
                mapper.SetScalarVisibility(True)
            else:
                self.debug_output("⚠️ No colors found, using default")
                self.step_actor.GetProperty().SetColor(0.7, 0.7, 0.7)
                
            self.renderer.AddActor(self.step_actor)
            self.renderer.ResetCamera()
            self.vtk_widget.GetRenderWindow().Render()
            
            self.debug_output("✅ Model displayed")
            
        except Exception as e:
            self.debug_output(f"❌ Display error: {e}")
            
    def rotate_button(self, axis, degrees):
        """Handle rotation button clicks"""
        self.debug_output(f"🔄 Button: {axis.upper()}{'+' if degrees > 0 else ''}{degrees}°")
        
        # Update rotation values
        self.current_rot[axis] += degrees
        
        # Apply VTK rotation
        if self.step_actor:
            self.step_actor.RotateWXYZ(degrees,
                1 if axis == 'x' else 0,
                1 if axis == 'y' else 0,
                1 if axis == 'z' else 0)
            self.vtk_widget.GetRenderWindow().Render()
            
        # Update axis/angle from rotation
        self.update_axis_angle_from_rotation()
        self.update_display()
        
        self.debug_output(f"✅ New ROT: {self.current_rot}")
        
    def on_mouse_interaction(self, obj, event):
        """Handle mouse interaction events"""
        # This should update rotation values when user rotates with mouse
        try:
            if self.step_actor:
                # Get current actor orientation
                orientation = self.step_actor.GetOrientation()
                
                # Convert to our rotation format
                old_rot = self.current_rot.copy()
                self.current_rot['x'] = orientation[0]
                self.current_rot['y'] = orientation[1] 
                self.current_rot['z'] = orientation[2]
                
                # Check if rotation changed
                if (abs(self.current_rot['x'] - old_rot['x']) > 0.1 or
                    abs(self.current_rot['y'] - old_rot['y']) > 0.1 or
                    abs(self.current_rot['z'] - old_rot['z']) > 0.1):
                    
                    self.update_axis_angle_from_rotation()
                    self.update_display()
                    self.debug_output(f"🖱️ Mouse rotation: {self.current_rot}")
                    
        except Exception as e:
            pass  # Ignore errors during mouse tracking
            
    def update_rotation_from_mouse(self):
        """Timer-based rotation tracking"""
        if self.step_actor:
            try:
                orientation = self.step_actor.GetOrientation()
                old_rot = self.current_rot.copy()
                
                self.current_rot['x'] = orientation[0]
                self.current_rot['y'] = orientation[1]
                self.current_rot['z'] = orientation[2]
                
                # Check if changed significantly
                if (abs(self.current_rot['x'] - old_rot['x']) > 1.0 or
                    abs(self.current_rot['y'] - old_rot['y']) > 1.0 or
                    abs(self.current_rot['z'] - old_rot['z']) > 1.0):
                    
                    self.update_axis_angle_from_rotation()
                    self.update_display()
                    
            except:
                pass
                
    def update_axis_angle_from_rotation(self):
        """Calculate axis and angle from rotation values"""
        import math
        
        # Calculate rotation magnitude
        rot_mag = math.sqrt(self.current_rot['x']**2 + self.current_rot['y']**2 + self.current_rot['z']**2)
        
        if rot_mag > 0.001:
            # Normalize to get axis
            self.current_axis = {
                'x': self.current_rot['x'] / rot_mag,
                'y': self.current_rot['y'] / rot_mag,
                'z': self.current_rot['z'] / rot_mag
            }
            self.current_angle = rot_mag
        else:
            self.current_axis = {'x': 0.0, 'y': 0.0, 'z': 1.0}
            self.current_angle = 0.0
            
    def update_display(self):
        """Update display labels"""
        self.rot_label.setText(f"ROT: X={self.current_rot['x']:.1f}° Y={self.current_rot['y']:.1f}° Z={self.current_rot['z']:.1f}°")
        self.axis_label.setText(f"AXIS: X={self.current_axis['x']:.3f} Y={self.current_axis['y']:.3f} Z={self.current_axis['z']:.3f}")
        self.angle_label.setText(f"ANGLE: {self.current_angle:.1f}°")
        
    def apply_silver_colors(self):
        """Apply silver colors to the model"""
        if not self.step_actor:
            self.debug_output("❌ No model loaded")
            return
            
        try:
            # Get polydata
            mapper = self.step_actor.GetMapper()
            polydata = mapper.GetInput()
            
            # Create silver colors
            import vtk
            colors = vtk.vtkUnsignedCharArray()
            colors.SetNumberOfComponents(3)
            colors.SetName("Colors")
            
            # Define silver colors
            light_silver = (192, 192, 192)  # Light silver
            dark_silver = (128, 128, 128)   # Dark silver
            
            num_cells = polydata.GetNumberOfCells()
            
            for i in range(num_cells):
                # Alternate between light and dark silver
                if i % 2 == 0:
                    colors.InsertNextTuple3(*light_silver)
                else:
                    colors.InsertNextTuple3(*dark_silver)
                    
            polydata.GetCellData().SetScalars(colors)
            polydata.GetCellData().SetActiveScalars("Colors")
            
            # Enable scalar visibility
            mapper.SetScalarVisibility(True)
            mapper.SetScalarModeToUseCellData()
            
            self.vtk_widget.GetRenderWindow().Render()
            self.debug_output(f"✅ Applied silver colors to {num_cells} cells")
            
        except Exception as e:
            self.debug_output(f"❌ Color error: {e}")
            
    def debug_colors(self):
        """Debug color information"""
        if not self.step_actor:
            self.debug_output("❌ No model loaded")
            return
            
        try:
            mapper = self.step_actor.GetMapper()
            polydata = mapper.GetInput()
            
            # Check for colors
            cell_colors = polydata.GetCellData().GetScalars()
            point_colors = polydata.GetPointData().GetScalars()
            
            info = []
            info.append(f"Cells: {polydata.GetNumberOfCells()}")
            info.append(f"Points: {polydata.GetNumberOfPoints()}")
            
            if cell_colors:
                info.append(f"Cell colors: {cell_colors.GetNumberOfTuples()} tuples")
                info.append(f"Color components: {cell_colors.GetNumberOfComponents()}")
            else:
                info.append("No cell colors")
                
            if point_colors:
                info.append(f"Point colors: {point_colors.GetNumberOfTuples()} tuples")
            else:
                info.append("No point colors")
                
            info.append(f"Scalar visibility: {mapper.GetScalarVisibility()}")
            
            self.debug_output("🔍 " + " | ".join(info))
            
        except Exception as e:
            self.debug_output(f"❌ Debug error: {e}")
            
    def debug_output(self, message):
        """Output debug message"""
        print(message)
        self.debug_label.setText(message)

def main():
    app = QApplication(sys.argv)
    window = DebugRotationColors()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
