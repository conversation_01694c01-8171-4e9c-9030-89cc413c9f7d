#!/usr/bin/env python3

print("READ STEP TEXT FILE FOR ACTUAL SHAPE-TO-COLOR ASSIGNMENTS")

# Read STEP file as text
with open('SOIC16P127_1270X940X610L89X51.STEP', 'r', encoding='utf-8', errors='ignore') as f:
    step_text = f.read()

lines = step_text.split('\n')

print("Looking for shape-to-color assignments in STEP text...")

# Find COLOUR_RGB entries
colors = {}
for line in lines:
    if 'COLOUR_RGB' in line and '#' in line:
        print(f"Color line: {line}")
        # Extract color ID and RGB values
        import re
        match = re.search(r'#(\d+).*?([\d\.]+)\s*,\s*([\d\.]+)\s*,\s*([\d\.]+)', line)
        if match:
            color_id = match.group(1)
            r = int(float(match.group(2)) * 255)
            g = int(float(match.group(3)) * 255)
            b = int(float(match.group(4)) * 255)
            colors[color_id] = (r, g, b)
            print(f"  Color #{color_id} = RGB({r}, {g}, {b})")

print(f"\nFound {len(colors)} colors")

# Find ADVANCED_FACE entries
faces = {}
for line in lines:
    if 'ADVANCED_FACE' in line and '#' in line:
        print(f"Face line: {line}")
        # Extract face ID
        match = re.search(r'#(\d+)=ADVANCED_FACE', line)
        if match:
            face_id = match.group(1)
            faces[face_id] = line
            print(f"  Face #{face_id}")

print(f"\nFound {len(faces)} faces")

# Find STYLED_ITEM entries that connect faces to colors
styled_items = []
for line in lines:
    if 'STYLED_ITEM' in line and '#' in line:
        print(f"Styled item line: {line}")
        styled_items.append(line)

print(f"\nFound {len(styled_items)} styled items")

# Show first few to understand the pattern
print("\nFirst 5 styled items:")
for i, item in enumerate(styled_items[:5]):
    print(f"{i}: {item}")

print("\nSTEP TEXT READING COMPLETE")
