#!/usr/bin/env python3
"""
START 3D STEP VIEWER GUI
This script starts the fixed 3D STEP viewer with working rotation save
"""

import subprocess
import sys

print('Starting 3D STEP Viewer GUI...')
print('The GUI will open with:')
print('- Dual 3D viewers (TOP and BOTTOM)')
print('- File loading capabilities')
print('- Rotation controls')
print('- Working OpenCASCADE save functionality')
print()

subprocess.run([sys.executable, 'step_viewer_tdk_modular_fixed.py'])
