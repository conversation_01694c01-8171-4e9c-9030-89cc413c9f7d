#!/usr/bin/env python3
"""
Test Option 1 Save: Verify that rotations applied in GUI are saved to STEP file
"""

import os
import sys
import time
from PyQt5.QtWidgets import QApplication

def test_option1_rotation_save():
    """Test that Option 1 save applies GUI rotations to the STEP file"""
    
    print("🧪 TESTING OPTION 1 ROTATION SAVE")
    print("=" * 60)
    
    # Test files
    original_file = "test.step"
    rotated_file = "test_rotated_option1.step"
    
    if not os.path.exists(original_file):
        print(f"❌ Test file not found: {original_file}")
        return False
    
    # Clean up previous test file
    if os.path.exists(rotated_file):
        os.remove(rotated_file)
        print(f"🗑️ Removed previous test file: {rotated_file}")
    
    try:
        # Import the main program
        from step_viewer_tdk_modular import StepViewerTDK
        
        # Create QApplication if needed
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("✅ Successfully imported StepViewerTDK")
        
        # Create viewer instance
        viewer = StepViewerTDK()
        
        print(f"\n📋 STEP 1: LOAD ORIGINAL FILE INTO TOP VIEWER")
        print("-" * 50)
        
        # Load original file into TOP viewer
        viewer.active_viewer = "top"
        viewer.update_viewer_highlights()
        success = viewer.load_step_file_direct(original_file)
        
        if not success:
            print(f"❌ Failed to load {original_file}")
            return False
        
        print(f"✅ Loaded {original_file} into TOP viewer")
        
        print(f"\n📋 STEP 2: APPLY ROTATIONS TO TOP VIEWER")
        print("-" * 50)
        
        # Apply test rotations (simulate user rotating the model)
        test_rotations = {'x': 45.0, 'y': 30.0, 'z': 15.0}
        test_position = {'x': 10.0, 'y': 5.0, 'z': 2.0}
        
        # Set the transformations in the viewer (simulate GUI changes)
        if hasattr(viewer, 'current_rot_left'):
            viewer.current_rot_left = test_rotations.copy()
            print(f"✅ Set TOP viewer rotations: {test_rotations}")
        
        if hasattr(viewer, 'current_pos_left'):
            viewer.current_pos_left = test_position.copy()
            print(f"✅ Set TOP viewer position: {test_position}")
        
        print(f"\n📋 STEP 3: SAVE WITH OPTION 1 (APPLY TRANSFORMATIONS)")
        print("-" * 50)
        
        # Test Option 1 save function directly
        success = viewer.save_step_file_option1(rotated_file)
        
        if not success:
            print(f"❌ Option 1 save failed")
            return False
        
        print(f"✅ Option 1 save successful: {rotated_file}")
        
        # Check file was created
        if not os.path.exists(rotated_file):
            print(f"❌ Saved file not found: {rotated_file}")
            return False
        
        original_size = os.path.getsize(original_file)
        rotated_size = os.path.getsize(rotated_file)
        
        print(f"📊 File sizes:")
        print(f"   Original: {original_size:,} bytes")
        print(f"   Rotated:  {rotated_size:,} bytes")
        print(f"   Difference: {rotated_size - original_size:+,} bytes")
        
        print(f"\n📋 STEP 4: LOAD SAVED FILE INTO BOTTOM VIEWER")
        print("-" * 50)
        
        # Load the saved file into BOTTOM viewer
        viewer.active_viewer = "bottom"
        viewer.update_viewer_highlights()
        success = viewer.load_step_file_direct(rotated_file)
        
        if not success:
            print(f"❌ Failed to load saved file {rotated_file}")
            return False
        
        print(f"✅ Loaded saved file {rotated_file} into BOTTOM viewer")
        
        print(f"\n📋 STEP 5: VERIFY TRANSFORMATIONS WERE APPLIED")
        print("-" * 50)
        
        # The saved file should now contain the transformations
        # When loaded, it should appear in the rotated position
        
        if rotated_size > original_size:
            print(f"✅ File size increased - transformations likely applied")
        else:
            print(f"⚠️ File size same/smaller - may be fallback copy")
        
        print(f"\n🎯 MANUAL VERIFICATION NEEDED:")
        print(f"   1. Check that TOP viewer shows rotated model")
        print(f"   2. Check that BOTTOM viewer shows model in same orientation")
        print(f"   3. Both viewers should show the same rotated position")
        
        print(f"\n✅ OPTION 1 ROTATION SAVE TEST COMPLETED")
        print(f"   Original file: {original_file}")
        print(f"   Saved file: {rotated_file}")
        print(f"   Test rotations: {test_rotations}")
        print(f"   Test position: {test_position}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_option1_rotation_save()
    if success:
        print(f"\n🎉 TEST PASSED")
    else:
        print(f"\n❌ TEST FAILED")
