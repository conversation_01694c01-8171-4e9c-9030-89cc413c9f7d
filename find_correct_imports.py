#!/usr/bin/env python3
"""
Find correct OCC import paths
"""

import os

def find_occ_modules():
    """Find what OCC modules are actually available"""
    try:
        import OCC.Core
        core_dir = os.path.dirname(OCC.Core.__file__)
        
        # Look for specific modules we need
        needed_modules = [
            'BRepMesh',
            'TopExp', 
            'TopAbs',
            'BRep',
            'TopLoc',
            'STEPControl',
            'IFSelect'
        ]
        
        print("Checking OCC.Core modules:")
        for module in needed_modules:
            module_file = os.path.join(core_dir, f"{module}.py")
            if os.path.exists(module_file):
                print(f"OK {module}.py exists")

                # Test import
                try:
                    exec(f"from OCC.Core import {module}")
                    print(f"OK from OCC.Core import {module} - WORKS")
                    
                    # Check what's inside
                    mod = __import__(f"OCC.Core.{module}", fromlist=[module])
                    attrs = [attr for attr in dir(mod) if not attr.startswith('_')]
                    print(f"   Contains: {attrs[:5]}...")  # Show first 5 attributes
                    
                except Exception as e:
                    print(f"FAIL from OCC.Core import {module} - FAILED: {e}")
            else:
                print(f"FAIL {module}.py does not exist")
        
        # Test specific classes we need
        specific_tests = [
            ("BRepMesh_IncrementalMesh", "from OCC.Core.BRepMesh import BRepMesh_IncrementalMesh"),
            ("TopExp_Explorer", "from OCC.Core.TopExp import TopExp_Explorer"),
            ("TopAbs_FACE", "from OCC.Core.TopAbs import TopAbs_FACE"),
            ("BRep_Tool", "from OCC.Core.BRep import BRep_Tool"),
            ("TopLoc_Location", "from OCC.Core.TopLoc import TopLoc_Location"),
            ("STEPControl_Reader", "from OCC.Core.STEPControl import STEPControl_Reader"),
            ("IFSelect_RetDone", "from OCC.Core.IFSelect import IFSelect_RetDone"),
        ]
        
        print("\nTesting specific class imports:")
        for class_name, import_cmd in specific_tests:
            try:
                exec(import_cmd)
                print(f"OK {class_name}: {import_cmd}")
            except Exception as e:
                print(f"FAIL {class_name}: {import_cmd} - FAILED: {e}")
                
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    find_occ_modules()
