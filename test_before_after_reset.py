#!/usr/bin/env python3
"""
Test Before After Reset - Show exact numbers before rotation, after rotation, after reset
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular import Step<PERSON>iewerTDK

def test_before_after_reset():
    """Test showing exact numbers before rotation, after rotation, after reset"""
    
    print("🔧 TEST BEFORE/AFTER RESET - EXACT NUMBERS")
    print("=" * 60)
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    # Process events to ensure GUI is ready
    app.processEvents()
    time.sleep(2)
    
    # Find a STEP file to test with
    test_files = [
        "SOIC16P127_1270X940X610L89X51.STEP",
        "AMPHENOL_U77-A1118-200T.STEP",
        "test.step",
        "sample.stp"
    ]
    
    test_file = None
    for filename in test_files:
        if os.path.exists(filename):
            test_file = filename
            break
    
    if not test_file:
        print("❌ No STEP file found for testing")
        return False
    
    print(f"📁 Using test file: {test_file}")
    
    def show_exact_numbers(label):
        """Show exact numbers for all actors"""
        print(f"\n🔍 {label}")
        print("-" * 60)
        
        renderer = viewer.vtk_renderer_left
        if hasattr(renderer, 'renderer') and renderer.renderer:
            actor_collection = renderer.renderer.GetActors()
            actor_collection.InitTraversal()
            
            actor_count = 0
            actor = actor_collection.GetNextActor()
            while actor:
                actor_count += 1
                pos = actor.GetPosition()
                orient = actor.GetOrientation()
                visible = actor.GetVisibility()
                
                # Identify actor type
                actor_type = "UNKNOWN"
                if hasattr(renderer, 'step_actors') and renderer.step_actors:
                    for j, multi_actor in enumerate(renderer.step_actors):
                        if multi_actor == actor:
                            actor_type = f"MULTI-ACTOR_{j}"
                            
                if hasattr(renderer, 'bbox_actor') and renderer.bbox_actor == actor:
                    actor_type = "BOUNDING_BOX"
                    
                if hasattr(renderer, 'step_actor') and renderer.step_actor == actor:
                    actor_type = "SINGLE_ACTOR"
                
                print(f"  {actor_type}: Position={pos}, Orientation={orient}, Visible={visible}")
                
                actor = actor_collection.GetNextActor()
            
            print(f"  Total actors: {actor_count}")
        else:
            print("❌ No renderer found")
    
    # Step 1: Load model and show BEFORE values
    print(f"\n📋 STEP 1: LOADING MODEL...")
    success = viewer.load_step_file_direct(test_file)
    app.processEvents()
    time.sleep(3)
    
    if not success:
        print("❌ Failed to load STEP file")
        return False
    
    show_exact_numbers("BEFORE VALUES (Initial Load)")
    
    # Step 2: Apply 3 random rotations and show values
    print(f"\n📋 STEP 2: APPLYING 3 RANDOM ROTATIONS...")

    viewer.active_viewer = "top"

    print(f"🔧 Rotating +37° X")
    viewer.rotate_shape("x", 37)
    app.processEvents()
    time.sleep(1)

    show_exact_numbers("AFTER ROTATION 1 VALUES (+37° X)")

    print(f"🔧 Rotating +123° Y")
    viewer.rotate_shape("y", 123)
    app.processEvents()
    time.sleep(1)

    show_exact_numbers("AFTER ROTATION 2 VALUES (+37° X, +123° Y)")

    print(f"🔧 Rotating +67° Z")
    viewer.rotate_shape("z", 67)
    app.processEvents()
    time.sleep(1)

    show_exact_numbers("AFTER ROTATION 3 VALUES (+37° X, +123° Y, +67° Z)")
    
    # Step 3: Reset and show AFTER reset values
    print(f"\n📋 STEP 3: RESETTING...")
    
    print(f"🔧 Calling reset...")
    viewer.reset_to_original()
    app.processEvents()
    time.sleep(3)
    
    show_exact_numbers("AFTER RESET VALUES")
    
    # Step 4: Analysis
    print(f"\n📋 STEP 4: ANALYSIS")
    print("=" * 60)
    
    print(f"🎯 EXPECTED RESULTS:")
    print(f"1. BEFORE: Position=(0,0,0), Orientation=(0,0,0)")
    print(f"2. AFTER ROTATIONS: Position=(0,0,0), Orientation=(37,123,67)")
    print(f"3. AFTER RESET: Position=(0,0,0), Orientation=(0,0,0)")

    print(f"\n🔍 CHECK:")
    print(f"- Did the orientation change to (37,123,67) after all rotations?")
    print(f"- Did the orientation change back to (0,0,0) after reset?")
    print(f"- Are both model and bounding box showing the same values?")
    
    # Keep window open for visual verification
    print(f"\n👁️ Window staying open for visual verification...")
    QTimer.singleShot(15000, app.quit)
    app.exec_()
    
    return True

if __name__ == "__main__":
    test_before_after_reset()
