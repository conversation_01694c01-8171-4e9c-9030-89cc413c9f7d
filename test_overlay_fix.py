#!/usr/bin/env python3
"""
Test program to verify overlay VTK content sharing fix
This will test the overlay functionality independently
"""

import sys
import vtk
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel
from PyQt5.QtCore import Qt
from vtk.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor

class OverlayTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Overlay VTK Content Test")
        self.setGeometry(100, 100, 1200, 800)
        
        # Create main widget and layout
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QVBoxLayout(main_widget)
        
        # Create button to test overlay
        self.overlay_btn = QPushButton("Test Overlay")
        self.overlay_btn.clicked.connect(self.test_overlay)
        layout.addWidget(self.overlay_btn)
        
        # Create container for VTK widgets
        vtk_container = QWidget()
        vtk_layout = QHBoxLayout(vtk_container)
        layout.addWidget(vtk_container)
        
        # Create original VTK widget
        self.original_vtk = QVTKRenderWindowInteractor(vtk_container)
        vtk_layout.addWidget(self.original_vtk)
        
        # Create original renderer and add a simple cube
        self.original_renderer = vtk.vtkRenderer()
        self.original_render_window = vtk.vtkRenderWindow()
        self.original_render_window.AddRenderer(self.original_renderer)
        self.original_vtk.SetRenderWindow(self.original_render_window)
        
        # Add a cube to original renderer
        cube_source = vtk.vtkCubeSource()
        cube_mapper = vtk.vtkPolyDataMapper()
        cube_mapper.SetInputConnection(cube_source.GetOutputPort())
        
        self.cube_actor = vtk.vtkActor()
        self.cube_actor.SetMapper(cube_mapper)
        self.cube_actor.GetProperty().SetColor(0.8, 0.2, 0.2)  # Red cube
        
        self.original_renderer.AddActor(self.cube_actor)
        self.original_renderer.SetBackground(0.1, 0.1, 0.2)  # Dark blue background
        
        # Create duplicate VTK widget (like overlay)
        self.duplicate_vtk = QVTKRenderWindowInteractor(vtk_container)
        vtk_layout.addWidget(self.duplicate_vtk)
        
        # Create duplicate renderer with copied content
        self.duplicate_renderer = vtk.vtkRenderer()
        self.duplicate_render_window = vtk.vtkRenderWindow()
        self.duplicate_render_window.AddRenderer(self.duplicate_renderer)
        self.duplicate_vtk.SetRenderWindow(self.duplicate_render_window)
        
        # Copy the cube to duplicate renderer
        duplicate_actor = vtk.vtkActor()
        duplicate_actor.SetMapper(cube_mapper)  # Share the same mapper
        duplicate_actor.GetProperty().DeepCopy(self.cube_actor.GetProperty())
        duplicate_actor.SetPosition(self.cube_actor.GetPosition())
        duplicate_actor.SetOrientation(self.cube_actor.GetOrientation())
        
        self.duplicate_renderer.AddActor(duplicate_actor)
        self.duplicate_renderer.SetBackground(0.0, 0.0, 0.0)  # Black background
        
        # Copy camera settings
        original_camera = self.original_renderer.GetActiveCamera()
        duplicate_camera = self.duplicate_renderer.GetActiveCamera()
        duplicate_camera.DeepCopy(original_camera)
        
        # Initialize VTK
        self.original_vtk.Initialize()
        self.duplicate_vtk.Initialize()
        
        # Render both
        self.original_render_window.Render()
        self.duplicate_render_window.Render()
        
        print("✅ Overlay test window created with original and duplicate VTK content")
        
        # Overlay widget (initially hidden)
        self.overlay_widget = None
        
    def test_overlay(self):
        """Test creating an overlay widget with VTK content"""
        if self.overlay_widget is None:
            print("🔧 Creating overlay widget...")
            
            # Create overlay widget
            self.overlay_widget = QWidget(self.centralWidget())
            self.overlay_widget.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint)
            self.overlay_widget.setAttribute(Qt.WA_TranslucentBackground)
            
            # Set semi-transparent background
            self.overlay_widget.setStyleSheet("""
                QWidget {
                    background-color: rgba(0, 0, 0, 50);
                    border: 2px solid yellow;
                    border-radius: 5px;
                }
            """)
            
            # Create layout for overlay
            overlay_layout = QVBoxLayout(self.overlay_widget)
            
            # Add label
            overlay_label = QLabel("OVERLAY TEST")
            overlay_label.setStyleSheet("color: yellow; font-weight: bold; background-color: rgba(0,0,0,150); padding: 5px;")
            overlay_label.setAlignment(Qt.AlignCenter)
            overlay_layout.addWidget(overlay_label)
            
            # Create overlay VTK widget with independent scene
            self.overlay_vtk_widget = QVTKRenderWindowInteractor(self.overlay_widget)
            
            # Create independent render window and renderer
            overlay_render_window = vtk.vtkRenderWindow()
            overlay_renderer = vtk.vtkRenderer()
            overlay_render_window.AddRenderer(overlay_renderer)
            self.overlay_vtk_widget.SetRenderWindow(overlay_render_window)
            
            # Copy cube to overlay renderer
            overlay_actor = vtk.vtkActor()
            overlay_actor.SetMapper(self.cube_actor.GetMapper())  # Share mapper
            overlay_actor.GetProperty().DeepCopy(self.cube_actor.GetProperty())
            overlay_actor.SetPosition(self.cube_actor.GetPosition())
            overlay_actor.SetOrientation(self.cube_actor.GetOrientation())
            
            # Make overlay cube green to distinguish it
            overlay_actor.GetProperty().SetColor(0.2, 0.8, 0.2)  # Green
            
            overlay_renderer.AddActor(overlay_actor)
            overlay_renderer.SetBackground(0.0, 0.0, 0.0)  # Black background
            
            # Copy camera settings
            overlay_camera = overlay_renderer.GetActiveCamera()
            overlay_camera.DeepCopy(self.original_renderer.GetActiveCamera())
            
            overlay_layout.addWidget(self.overlay_vtk_widget)
            
            # Size and position overlay
            self.overlay_widget.resize(400, 300)
            self.overlay_widget.move(50, 50)
            
            # Initialize and render
            self.overlay_vtk_widget.Initialize()
            overlay_render_window.Render()
            
            self.overlay_widget.show()
            self.overlay_widget.raise_()
            
            self.overlay_btn.setText("Hide Overlay")
            print("✅ Overlay widget created and shown")
            
        else:
            # Toggle overlay visibility
            if self.overlay_widget.isVisible():
                self.overlay_widget.hide()
                self.overlay_btn.setText("Show Overlay")
                print("🔧 Overlay hidden")
            else:
                self.overlay_widget.show()
                self.overlay_widget.raise_()
                self.overlay_btn.setText("Hide Overlay")
                print("🔧 Overlay shown")

def main():
    app = QApplication(sys.argv)
    window = OverlayTestWindow()
    window.show()
    
    print("🎯 Overlay test program started")
    print("📋 Instructions:")
    print("   1. You should see two VTK viewers side by side with red cubes")
    print("   2. Click 'Test Overlay' to create an overlay with a green cube")
    print("   3. The overlay should show the cube properly (not black)")
    print("   4. This tests the VTK content duplication fix")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
