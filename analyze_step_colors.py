#!/usr/bin/env python3
"""
Analyze actual STEP file to understand correct color mapping
"""

import os

step_file = 'SOIC16P127_1270X940X610L89X51.STEP'

print("=== ANALYZING STEP FILE FOR CORRECT COLORS ===")

if not os.path.exists(step_file):
    print(f"STEP file not found: {step_file}")
    exit(1)

try:
    with open(step_file, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
    
    print(f"STEP file size: {len(content)} characters")
    
    # Look for color information in STEP file
    color_lines = []
    lines = content.split('\n')
    
    for i, line in enumerate(lines):
        if 'COLOUR' in line.upper() or 'COLOR' in line.upper():
            color_lines.append((i+1, line.strip()))
    
    print(f"\nFound {len(color_lines)} color-related lines:")
    for line_num, line in color_lines:
        print(f"Line {line_num}: {line}")
    
    # Look for surface style information
    surface_lines = []
    for i, line in enumerate(lines):
        if 'SURFACE_STYLE' in line.upper():
            surface_lines.append((i+1, line.strip()))
    
    print(f"\nFound {len(surface_lines)} surface style lines:")
    for line_num, line in surface_lines[:5]:  # Show first 5
        print(f"Line {line_num}: {line}")
    
    # Look for shape information
    shape_lines = []
    for i, line in enumerate(lines):
        if 'SHAPE' in line.upper() and ('REPRESENTATION' in line.upper() or 'DEFINITION' in line.upper()):
            shape_lines.append((i+1, line.strip()))
    
    print(f"\nFound {len(shape_lines)} shape definition lines:")
    for line_num, line in shape_lines[:3]:  # Show first 3
        print(f"Line {line_num}: {line}")
    
    # Analyze the structure to understand pin vs body mapping
    print("\n=== STEP FILE STRUCTURE ANALYSIS ===")
    
    # Count different types of geometric entities
    entity_counts = {}
    for line in lines:
        if line.startswith('#') and '=' in line:
            parts = line.split('=', 1)
            if len(parts) > 1:
                entity_type = parts[1].strip().split('(')[0].strip()
                entity_counts[entity_type] = entity_counts.get(entity_type, 0) + 1
    
    print("Top geometric entities:")
    sorted_entities = sorted(entity_counts.items(), key=lambda x: x[1], reverse=True)
    for entity, count in sorted_entities[:10]:
        print(f"  {entity}: {count}")
    
    print("\n=== RECOMMENDED COLOR MAPPING ===")
    print("Based on STEP file analysis:")
    print("- Package body should be light silver")
    print("- Pins/leads should be dark silver") 
    print("- Need to map colors based on geometry position, not cell index")
    
except Exception as e:
    print(f"Error analyzing STEP file: {e}")
    import traceback
    traceback.print_exc()

print("=== ANALYSIS COMPLETE ===")
