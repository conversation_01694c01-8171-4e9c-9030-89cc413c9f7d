#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
STEP Viewer TDK Modular - Dual Viewer Version
Enhanced with top/bottom viewer selection and compact transform display
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QSplitter, QPushButton, QLabel, 
                            QFileDialog, QComboBox, QSpinBox, QGroupBox)
from PyQt5.QtCore import Qt, QTimer

# Import custom modules
from step_loader import STEPLoader
from vtk_renderer import VTKRenderer
from gui_components import create_tool_dock

class StepViewerTDK(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("VTK TDK STEP Viewer (Dual View) - Enhanced")
        self.setGeometry(100, 100, 1400, 900)
        
        # Initialize dual components
        self.step_loader_left = STEPLoader()
        self.step_loader_right = STEPLoader()
        self.vtk_renderer_left = VTKRenderer(self)
        self.vtk_renderer_right = VTKRenderer(self)
        
        # Data tracking for both viewers
        self.bbox_visible_left = True
        self.bbox_visible_right = True
        
        # Transform data for left (top) viewer
        self.orig_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.orig_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        
        # Transform data for right (bottom) viewer
        self.orig_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.orig_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        
        # Active viewer tracking
        self.active_viewer = "top"
        
        # Setup UI
        self.init_ui()
        
        # Mouse tracking timer
        self.mouse_timer = QTimer()
        self.mouse_timer.timeout.connect(self.update_camera_display)
        self.mouse_timer.start(200)
        
        self.statusBar().showMessage("Ready - Select TOP or BOTTOM viewer, then load STEP files")

    def init_ui(self):
        # Central widget with splitter
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QHBoxLayout(central_widget)
        
        # Create splitter for dual view
        splitter = QSplitter(Qt.Vertical)
        
        # Top viewer container
        top_container = QWidget()
        top_layout = QVBoxLayout(top_container)
        
        # Top file label
        self.top_file_label = QLabel("TOP VIEWER - No file loaded")
        self.top_file_label.setStyleSheet("color: blue; font-size: 12px; padding: 5px; background-color: lightgray;")
        top_layout.addWidget(self.top_file_label)
        
        # Top VTK widget
        self.vtk_widget_left = self.vtk_renderer_left.vtk_widget
        if self.vtk_widget_left:
            top_layout.addWidget(self.vtk_widget_left)
        
        # Bottom viewer container
        bottom_container = QWidget()
        bottom_layout = QVBoxLayout(bottom_container)
        
        # Bottom file label
        self.bottom_file_label = QLabel("BOTTOM VIEWER - No file loaded")
        self.bottom_file_label.setStyleSheet("color: blue; font-size: 12px; padding: 5px; background-color: lightgray;")
        bottom_layout.addWidget(self.bottom_file_label)
        
        # Bottom VTK widget
        self.vtk_widget_right = self.vtk_renderer_right.vtk_widget
        if self.vtk_widget_right:
            bottom_layout.addWidget(self.vtk_widget_right)
        
        # Add containers to splitter
        splitter.addWidget(top_container)
        splitter.addWidget(bottom_container)
        splitter.setSizes([400, 400])
        
        main_layout.addWidget(splitter)
        
        # Create tool dock
        dock = create_tool_dock(self)
        self.addDockWidget(Qt.LeftDockWidgetArea, dock)
        
        # Set initial active viewer
        self.update_viewer_highlights()

    def set_active_viewer(self, viewer):
        """Set the active viewer (top or bottom)"""
        self.active_viewer = viewer
        self.update_viewer_highlights()
        self.update_transform_display()
        self.statusBar().showMessage(f"Active viewer: {viewer.upper()}")

    def update_viewer_highlights(self):
        """Update button highlights to show active viewer"""
        if hasattr(self, 'top_btn') and hasattr(self, 'bottom_btn'):
            if self.active_viewer == "top":
                self.top_btn.setStyleSheet("QPushButton { background-color: lightgreen; font-weight: bold; padding: 8px; }")
                self.bottom_btn.setStyleSheet("QPushButton { padding: 8px; }")
            else:
                self.top_btn.setStyleSheet("QPushButton { padding: 8px; }")
                self.bottom_btn.setStyleSheet("QPushButton { background-color: lightgreen; font-weight: bold; padding: 8px; }")

    def load_step_file(self):
        """Load STEP file into active viewer"""
        filename, _ = QFileDialog.getOpenFileName(
            self, "Open STEP File", "", "STEP Files (*.step *.stp);;All Files (*)"
        )
        
        if filename:
            if self.active_viewer == "top":
                success, message = self.step_loader_left.load_step_file(filename)
                if success:
                    self.vtk_renderer_left.display_polydata(self.step_loader_left.current_polydata)
                    self.top_file_label.setText(f"TOP: {os.path.basename(filename)}")
                    self.extract_step_transformation_data("top")
                else:
                    self.top_file_label.setText("TOP: Load failed")
            else:
                success, message = self.step_loader_right.load_step_file(filename)
                if success:
                    self.vtk_renderer_right.display_polydata(self.step_loader_right.current_polydata)
                    self.bottom_file_label.setText(f"BOTTOM: {os.path.basename(filename)}")
                    self.extract_step_transformation_data("bottom")
                else:
                    self.bottom_file_label.setText("BOTTOM: Load failed")
            
            self.statusBar().showMessage(f"{self.active_viewer.title()}: {message}")
            self.update_transform_display()

    def extract_step_transformation_data(self, viewer):
        """Extract transformation data from loaded STEP file"""
        if viewer == "top":
            self.orig_pos_left = self.step_loader_left.original_position.copy()
            self.orig_rot_left = self.step_loader_left.original_orientation.copy()
            self.current_pos_left = self.orig_pos_left.copy()
            self.current_rot_left = self.orig_rot_left.copy()
        else:
            self.orig_pos_right = self.step_loader_right.original_position.copy()
            self.orig_rot_right = self.step_loader_right.original_orientation.copy()
            self.current_pos_right = self.orig_pos_right.copy()
            self.current_rot_right = self.orig_rot_right.copy()

    def clear_view(self):
        """Clear the active viewer"""
        if self.active_viewer == "top":
            self.vtk_renderer_left.clear_view()
            self.top_file_label.setText("TOP VIEWER - No file loaded")
        else:
            self.vtk_renderer_right.clear_view()
            self.bottom_file_label.setText("BOTTOM VIEWER - No file loaded")
        self.statusBar().showMessage(f"{self.active_viewer.title()} view cleared")

    def fit_view(self):
        """Fit view in active viewer"""
        if self.active_viewer == "top":
            self.vtk_renderer_left.fit_view()
        else:
            self.vtk_renderer_right.fit_view()

    def save_transformed_step(self):
        """Save transformed STEP file from active viewer"""
        filename, _ = QFileDialog.getSaveFileName(
            self, "Save STEP File", "", "STEP Files (*.step);;All Files (*)"
        )
        
        if filename:
            if self.active_viewer == "top":
                success = self.step_loader_left.save_step_file(filename)
            else:
                success = self.step_loader_right.save_step_file(filename)
            
            if success:
                self.statusBar().showMessage(f"Saved: {filename}")
            else:
                self.statusBar().showMessage("Save failed")

    def reset_to_original(self):
        """Reset active viewer to original transform"""
        if self.active_viewer == "top":
            self.current_pos_left = self.orig_pos_left.copy()
            self.current_rot_left = self.orig_rot_left.copy()
        else:
            self.current_pos_right = self.orig_pos_right.copy()
            self.current_rot_right = self.orig_rot_right.copy()
        
        self.update_transform_display()
        self.statusBar().showMessage(f"{self.active_viewer.title()} reset to original")

    def align_bottom_center(self):
        """Align model to bottom-center"""
        self.statusBar().showMessage(f"{self.active_viewer.title()}: Align bottom-center not implemented")

    def rotate_shape(self, axis, degrees):
        """Rotate shape in active viewer"""
        if self.active_viewer == "top":
            self.current_rot_left[axis] += degrees
        else:
            self.current_rot_right[axis] += degrees
        
        self.update_transform_display()
        self.statusBar().showMessage(f"{self.active_viewer.title()}: Rotated {degrees}° around {axis}")

    def change_model_color(self, color_name):
        """Change model color in active viewer"""
        self.statusBar().showMessage(f"{self.active_viewer.title()}: Color changed to {color_name}")

    def toggle_bbox_overlay(self):
        """Toggle bounding box in active viewer"""
        if self.active_viewer == "top":
            self.bbox_visible_left = not self.bbox_visible_left
            status = "shown" if self.bbox_visible_left else "hidden"
        else:
            self.bbox_visible_right = not self.bbox_visible_right
            status = "shown" if self.bbox_visible_right else "hidden"
        
        self.statusBar().showMessage(f"{self.active_viewer.title()}: Bounding box {status}")

    def force_view_update(self):
        """Force update of transform display"""
        self.update_transform_display()
        self.statusBar().showMessage("Transform display updated")

    def update_transform_display(self):
        """Update the transform display labels based on active viewer"""
        if self.active_viewer == "top":
            current_pos = self.current_pos_left
            current_rot = self.current_rot_left
            orig_pos = self.orig_pos_left
            orig_rot = self.orig_rot_left
        else:
            current_pos = self.current_pos_right
            current_rot = self.current_rot_right
            orig_pos = self.orig_pos_right
            orig_rot = self.orig_rot_right
        
        # Update original transform labels (compact format)
        if hasattr(self, 'lbl_orig_pos_x'):
            self.lbl_orig_pos_x.setText(f"X: {orig_pos['x']:.1f}")
            self.lbl_orig_pos_y.setText(f"Y: {orig_pos['y']:.1f}")
            self.lbl_orig_pos_z.setText(f"Z: {orig_pos['z']:.1f}")
            self.lbl_orig_rot_x.setText(f"X: {orig_rot['x']:.0f}°")
            self.lbl_orig_rot_y.setText(f"Y: {orig_rot['y']:.0f}°")
            self.lbl_orig_rot_z.setText(f"Z: {orig_rot['z']:.0f}°")
        
        # Update current transform labels (compact format)
        if hasattr(self, 'lbl_curr_pos_x'):
            self.lbl_curr_pos_x.setText(f"X: {current_pos['x']:.1f}")
            self.lbl_curr_pos_y.setText(f"Y: {current_pos['y']:.1f}")
            self.lbl_curr_pos_z.setText(f"Z: {current_pos['z']:.1f}")
            self.lbl_curr_rot_x.setText(f"X: {current_rot['x']:.0f}°")
            self.lbl_curr_rot_y.setText(f"Y: {current_rot['y']:.0f}°")
            self.lbl_curr_rot_z.setText(f"Z: {current_rot['z']:.0f}°")

    def update_camera_display(self):
        """Update camera orientation display"""
        if hasattr(self, 'lbl_curr_rot_x'):
            if self.active_viewer == "top":
                orientation = self.vtk_renderer_left.get_camera_orientation()
                self.current_rot_left = orientation
            else:
                orientation = self.vtk_renderer_right.get_camera_orientation()
                self.current_rot_right = orientation
            self.update_transform_display()

def main():
    app = QApplication(sys.argv)
    app.setApplicationName("TDK STEP Viewer Dual")
    app.setApplicationVersion("3.0")
    
    viewer = StepViewerTDK()
    viewer.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
