#!/usr/bin/env python3
"""
Debug Reset Step by Step - Trace exactly what happens during reset
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular import Step<PERSON>iewerTDK

def debug_reset_step_by_step():
    """Debug reset step by step to find exactly what's wrong"""
    
    print("🔧 DEBUG RESET STEP BY STEP")
    print("=" * 60)
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    # Process events to ensure GUI is ready
    app.processEvents()
    time.sleep(1)
    
    # Find a STEP file to test with
    test_files = [
        "AMPHENOL_U77-A1118-200T.STEP",
        "test.step",
        "sample.stp"
    ]
    
    test_file = None
    for filename in test_files:
        if os.path.exists(filename):
            test_file = filename
            break
    
    if not test_file:
        print("❌ No STEP file found for testing")
        return False
    
    print(f"📁 Using test file: {test_file}")
    
    # Step 1: Load STEP file
    print(f"\n📋 STEP 1: LOADING {test_file}...")
    success = viewer.load_step_file_direct(test_file)
    app.processEvents()
    time.sleep(2)
    
    if not success:
        print("❌ Failed to load STEP file")
        return False
    
    renderer = viewer.vtk_renderer_left
    
    # Get initial state
    print(f"\n🔍 INITIAL STATE:")
    all_actors = []
    if hasattr(renderer, 'renderer') and renderer.renderer:
        actor_collection = renderer.renderer.GetActors()
        actor_collection.InitTraversal()
        
        actor = actor_collection.GetNextActor()
        while actor:
            all_actors.append(actor)
            actor = actor_collection.GetNextActor()
    
    for i, actor in enumerate(all_actors):
        pos = actor.GetPosition()
        orient = actor.GetOrientation()
        bounds = actor.GetBounds()
        visible = actor.GetVisibility()
        
        print(f"  Actor {i}: Pos={pos}, Orient={orient}, Visible={visible}")
        print(f"    Bounds: {bounds}")
    
    print(f"\n👁️ INITIAL MODEL - Note position (3 seconds)")
    time.sleep(3)
    
    # Step 2: Apply LARGE transformation
    print(f"\n📋 STEP 2: APPLYING LARGE TRANSFORMATION...")
    
    viewer.active_viewer = "top"
    
    print(f"🔧 Applying LARGE transformation: +100mm X, +90° Z")
    viewer.move_shape("x", 100)
    viewer.rotate_shape("z", 90)
    app.processEvents()
    time.sleep(2)
    
    # Check state after transformation
    print(f"\n🔍 STATE AFTER TRANSFORMATION:")
    for i, actor in enumerate(all_actors):
        pos = actor.GetPosition()
        orient = actor.GetOrientation()
        bounds = actor.GetBounds()
        
        print(f"  Actor {i}: Pos={pos}, Orient={orient}")
        print(f"    Bounds: {bounds}")
    
    print(f"\n👁️ AFTER TRANSFORMATION - Note the new position (3 seconds)")
    time.sleep(3)
    
    # Step 3: Manual actor reset test
    print(f"\n📋 STEP 3: MANUAL ACTOR RESET TEST...")
    
    print(f"🔧 Manually resetting ALL actors to (0,0,0)...")
    
    for i, actor in enumerate(all_actors):
        print(f"  Resetting Actor {i}...")
        actor.SetPosition(0, 0, 0)
        actor.SetOrientation(0, 0, 0)
        actor.SetUserTransform(None)
        actor.Modified()
    
    # Force render
    renderer.render_window.Render()
    app.processEvents()
    time.sleep(1)
    
    print(f"\n👁️ AFTER MANUAL RESET - Did it move back visually? (5 seconds)")
    time.sleep(5)
    
    # Step 4: Re-apply transformation for GUI reset test
    print(f"\n📋 STEP 4: RE-APPLYING TRANSFORMATION FOR GUI RESET TEST...")
    
    print(f"🔧 Re-applying transformation...")
    viewer.move_shape("x", 100)
    viewer.rotate_shape("z", 90)
    app.processEvents()
    time.sleep(2)
    
    print(f"\n👁️ RE-TRANSFORMED - Note position again (3 seconds)")
    time.sleep(3)
    
    # Step 5: GUI reset with detailed tracing
    print(f"\n📋 STEP 5: GUI RESET WITH DETAILED TRACING...")
    
    print(f"🔧 BEFORE GUI RESET - Actor states:")
    for i, actor in enumerate(all_actors):
        pos = actor.GetPosition()
        orient = actor.GetOrientation()
        print(f"  Actor {i}: Pos={pos}, Orient={orient}")
    
    print(f"\n🔧 Calling viewer.reset_to_original()...")
    
    # Patch the reset method to add more debugging
    original_reset = viewer.reset_to_original
    
    def debug_reset():
        print(f"🔧 DEBUG: Inside reset_to_original()")
        print(f"🔧 DEBUG: active_viewer = {viewer.active_viewer}")
        
        # Call original reset
        try:
            original_reset()
            print(f"✅ DEBUG: reset_to_original() completed")
        except Exception as e:
            print(f"❌ DEBUG: reset_to_original() failed: {e}")
            import traceback
            traceback.print_exc()
    
    # Replace with debug version
    viewer.reset_to_original = debug_reset
    
    # Call the debug reset
    viewer.reset_to_original()
    app.processEvents()
    time.sleep(2)
    
    print(f"\n🔧 AFTER GUI RESET - Actor states:")
    for i, actor in enumerate(all_actors):
        pos = actor.GetPosition()
        orient = actor.GetOrientation()
        print(f"  Actor {i}: Pos={pos}, Orient={orient}")
    
    print(f"\n👁️ AFTER GUI RESET - Did it move back visually? (5 seconds)")
    time.sleep(5)
    
    # Step 6: Check what's actually being displayed
    print(f"\n📋 STEP 6: CHECKING WHAT'S ACTUALLY BEING DISPLAYED...")
    
    print(f"🔧 Testing visibility by hiding each actor...")
    
    for i, actor in enumerate(all_actors):
        print(f"\n🔧 Hiding Actor {i}...")
        actor.SetVisibility(False)
        renderer.render_window.Render()
        app.processEvents()
        time.sleep(2)
        
        print(f"👁️ Actor {i} hidden - Can you see a difference? (3 seconds)")
        time.sleep(3)
        
        # Show it again
        actor.SetVisibility(True)
        renderer.render_window.Render()
        app.processEvents()
        time.sleep(1)
    
    # Step 7: Final analysis
    print(f"\n📋 STEP 7: FINAL ANALYSIS")
    print("=" * 60)
    
    print(f"🎯 KEY FINDINGS:")
    print(f"1. Did manual reset work? (Step 3)")
    print(f"2. Did GUI reset work? (Step 5)")
    print(f"3. Which actor(s) are actually visible? (Step 6)")
    
    print(f"\n🔍 DIAGNOSIS:")
    print(f"- If manual reset worked but GUI reset didn't: GUI reset logic issue")
    print(f"- If neither worked: The visible actor is not being reset")
    print(f"- If wrong actor is visible: We're resetting the wrong actors")
    
    # Keep window open
    print(f"\n👁️ Window will stay open for final inspection...")
    QTimer.singleShot(15000, app.quit)
    app.exec_()
    
    return True

if __name__ == "__main__":
    debug_reset_step_by_step()
