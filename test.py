#!/usr/bin/env python3
"""
Complete STEP File Viewer Application
Integrates all modular components: VTK renderer, STEP loader, and GUI components
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QFileDialog, QMessageBox, QStatusBar)
from PyQt5.QtCore import Qt

# Import our modular components
from vtk_renderer import VTKRenderer
from step_loader import STEPLoader

class STEPViewerApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("STEP File Viewer - Complete Application")
        self.setGeometry(100, 100, 1200, 800)

        # Initialize components
        self.step_loader = STEPLoader()
        self.vtk_renderer = None
        self.current_file = None

        # Setup UI
        self.setup_ui()
        self.setup_status_bar()

        print("STEP Viewer Application initialized")

    def setup_ui(self):
        """Setup the main user interface"""
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Create main layout
        main_layout = QHBoxLayout(central_widget)

        # Create VTK renderer
        self.vtk_renderer = VTKRenderer(self)

        # Add VTK widget to layout
        if self.vtk_renderer.vtk_widget:
            main_layout.addWidget(self.vtk_renderer.vtk_widget, 3)  # 3/4 of space
            print("VTK widget added to main layout")
        else:
            print("ERROR: Failed to create VTK widget")

        # Create and add control panel
        try:
            control_dock = self.create_simple_control_panel()
            self.addDockWidget(Qt.RightDockWidgetArea, control_dock)
            print("Control panel added")
        except Exception as e:
            print(f"Error creating control panel: {e}")

    def create_simple_control_panel(self):
        """Create a simple control panel for the application"""
        from PyQt5.QtWidgets import QDockWidget, QWidget, QVBoxLayout, QGroupBox, QPushButton

        dock = QDockWidget("Controls", self)
        dock.setAllowedAreas(Qt.LeftDockWidgetArea | Qt.RightDockWidgetArea)

        panel = QWidget()
        layout = QVBoxLayout(panel)

        # File Operations Group
        file_group = QGroupBox("File Operations")
        file_layout = QVBoxLayout(file_group)

        load_btn = QPushButton("Load STEP File")
        load_btn.clicked.connect(self.load_step_file)
        file_layout.addWidget(load_btn)

        layout.addWidget(file_group)

        # View Operations Group
        view_group = QGroupBox("View Operations")
        view_layout = QVBoxLayout(view_group)

        fit_btn = QPushButton("Fit View")
        fit_btn.clicked.connect(self.fit_view)
        view_layout.addWidget(fit_btn)

        clear_btn = QPushButton("Clear View")
        clear_btn.clicked.connect(self.clear_view)
        view_layout.addWidget(clear_btn)

        layout.addWidget(view_group)

        dock.setWidget(panel)
        return dock

    def setup_status_bar(self):
        """Setup status bar"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready - Load a STEP file to begin")

    def load_step_file(self):
        """Load STEP file using file dialog"""
        print("Load button clicked")

        # Open file dialog
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Open STEP File",
            "",
            "STEP Files (*.step *.stp);;All Files (*)"
        )

        if not file_path:
            print("No file selected")
            return

        print(f"Selected file: {file_path}")
        self.current_file = file_path

        # Update status
        self.status_bar.showMessage(f"Loading: {os.path.basename(file_path)}...")

        try:
            # Load the STEP file
            success, message = self.step_loader.load_step_file(file_path)

            if success:
                print(f"Successfully loaded: {message}")

                # Display in VTK renderer
                if self.step_loader.current_polydata and self.vtk_renderer:
                    display_success = self.vtk_renderer.display_polydata(self.step_loader.current_polydata)

                    if display_success:
                        self.vtk_renderer.fit_view()
                        self.status_bar.showMessage(f"Loaded: {os.path.basename(file_path)} - {message}")
                        print("File displayed successfully in VTK renderer")
                    else:
                        self.status_bar.showMessage(f"Error displaying file: {os.path.basename(file_path)}")
                        print("Error displaying file in VTK renderer")
                else:
                    self.status_bar.showMessage("Error: No polydata generated")
                    print("Error: No polydata was generated from the STEP file")

            else:
                print(f"Failed to load file: {message}")
                self.status_bar.showMessage(f"Error: {message}")
                QMessageBox.warning(self, "Load Error", f"Failed to load STEP file:\n{message}")

        except Exception as e:
            error_msg = f"Unexpected error loading file: {str(e)}"
            print(error_msg)
            self.status_bar.showMessage(f"Error: {str(e)}")
            QMessageBox.critical(self, "Error", error_msg)

    def fit_view(self):
        """Fit the view to show the entire model"""
        if self.vtk_renderer:
            self.vtk_renderer.fit_view()
            print("View fitted to model")

    def clear_view(self):
        """Clear the current view"""
        if self.vtk_renderer:
            self.vtk_renderer.clear_view()
            self.status_bar.showMessage("View cleared")
            print("View cleared")

def main():
    """Main application entry point"""
    print("Starting STEP Viewer Application...")

    # Create QApplication
    app = QApplication(sys.argv)
    app.setApplicationName("STEP File Viewer")

    # Create and show main window
    viewer = STEPViewerApp()
    viewer.show()

    print("Application window shown, entering event loop...")

    # Start event loop
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()