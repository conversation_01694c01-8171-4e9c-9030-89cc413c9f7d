#!/usr/bin/env python3
"""
Test rotation extraction from the running viewer
"""

import sys
import os

def test_rotation_extraction():
    """Test if we can extract rotation from the running viewer"""
    print("🔍 TESTING ROTATION EXTRACTION")
    print("=" * 40)
    
    try:
        # Import the main program
        import step_viewer_tdk_modular
        
        # Try to find the running instance
        from PyQt5.QtWidgets import QApplication
        app = QApplication.instance()
        
        if app:
            # Find the main window
            for widget in app.topLevelWidgets():
                if isinstance(widget, step_viewer_tdk_modular.StepViewerTDK):
                    viewer = widget
                    print(f"✅ Found running viewer instance")
                    
                    # Test extraction for both viewers
                    print(f"\n🔧 Testing TOP viewer rotation extraction:")
                    top_rot = viewer._extract_rotation_from_vtk_actor("top")
                    print(f"TOP rotation: {top_rot}")
                    
                    print(f"\n🔧 Testing BOTTOM viewer rotation extraction:")
                    bottom_rot = viewer._extract_rotation_from_vtk_actor("bottom")
                    print(f"BOTTOM rotation: {bottom_rot}")
                    
                    # Check which viewer is active
                    print(f"\n🎯 Active viewer: {viewer.active_viewer}")
                    
                    return True
        
        print("❌ No running viewer found")
        return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_rotation_extraction()
